## skylab-service-failover

能力兜底组件（配置与 SPI）

### 1. 项目概述
- 项目名称：skylab-service-failover
- 项目描述：当引擎不可用或能力异常时，通过 FailoverController 路由到兜底实现
- 版本信息：2.0.9-SNAPSHOT（开发中）

### 2. 技术架构
- 核心：FailoverController + Diagnose/Sort/StudentLog/Feature 兜底 Service
- 入口：由前置服务根据错误码或策略触发兜底

### 3. 业务处理逻辑
- 解析 SceneInfo / functionCode -> 分流到对应兜底 Service -> 统一封装 ApiResponse 并附带 failover=true

### 4. 接口
- 内部调用，不对外直接暴露 HTTP

### 5. 开发指南
- 重要类：com.iflytek.skylab.service.failover.controller.FailoverController
- 建议：完善各兜底路径单元测试，模拟 EngineException

