package com.iflytek.skylab.service.failover.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.hy.rec.errtopsort.interfaces.param.ErrorTopicGraphRecommendRequest;
import com.iflytek.hy.rec.simple.ISimpleGraphRecommend;
import com.iflytek.hy.rec.sort.interfaces.param.*;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.MasterDiagnoseParam;
import com.iflytek.skylab.core.data.RecTraceNodeParam;
import com.iflytek.skylab.core.data.adapter.sort.AbstractParamAdapter;
import com.iflytek.skylab.core.data.adapter.sort.SortParamAdapterSelector;
import com.iflytek.skylab.core.data.adapter.sort.errortopic.ErrorTopicAbstractParamAdapter;
import com.iflytek.skylab.core.data.adapter.sort.errortopic.ErrorTopicSortParamAdapterSelector;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.RecLog;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.data.TraceHeaderUtils;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.feign.SkylineRouterFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/5/9 15:40
 */
@Slf4j
public class SortFailoverService implements ApplicationContextAware {

    private static final CopyOptions COPY_OPTIONS = CopyOptions.create()
            .ignoreNullValue()
            .ignoreError();

    @Value("${spring.application.name:skylab-failover}")
    private String springApplicationName;

    private ApplicationContext applicationContext;
    private final SkylineRouterFeign skylineRouterFeign;
    private final TraceUtils traceUtils;
    private final ISimpleGraphRecommend simpleGraphRecommend;

    private final SortParamAdapterSelector adapterSelector;
    private final ErrorTopicSortParamAdapterSelector errorTopicSortParamAdapterSelector;

    public SortFailoverService(SortParamAdapterSelector adapterSelector,
                               ISimpleGraphRecommend simpleGraphRecommend,
                               SkylineRouterFeign skylineRouterFeign,
                               TraceUtils traceUtils, ErrorTopicSortParamAdapterSelector errorTopicSortParamAdapterSelector) {

        this.adapterSelector = adapterSelector;
        this.simpleGraphRecommend = simpleGraphRecommend;
        this.skylineRouterFeign = skylineRouterFeign;
        this.traceUtils = traceUtils;
        this.errorTopicSortParamAdapterSelector = errorTopicSortParamAdapterSelector;
    }

    //知识簇终止测评funcCode
    private static final List<String> KC_EVAL_END_FUNCODES = Arrays.asList(
            RecEvalEnum.KC_REC_EVAL4IN.name(),
            RecEvalEnum.KC_REC_EVAL4CTN.name(),
            RecEvalEnum.KC_REC_EVAL4OUT.name(),
            RecEvalEnum.KC_REC_EVAL4SYNC.name());
    //测评终止: 出门测 || 点排序场景 || 点推题场景，不更新用户画像
    private static final Set<String> NOT_EVAL_END_FUNCODES = new HashSet<>();

    static {
        // 初始化NOT_EVAL_END_FUNCODES，包含所有RecNodeEnum的值
        RecNodeEnum[] recNodeEnums = RecNodeEnum.values();
        for (RecNodeEnum value : recNodeEnums) {
            NOT_EVAL_END_FUNCODES.add(value.name());
        }

        RecTopicEnum[] recTopicEnums = RecTopicEnum.values();
        for (RecTopicEnum value : recTopicEnums) {
            NOT_EVAL_END_FUNCODES.add(value.name());
        }
        //出门测
        NOT_EVAL_END_FUNCODES.add(RecEvalEnum.REC_EVAL4OUT.name());
        NOT_EVAL_END_FUNCODES.add(RecEvalEnum.REC_EVAL4SYNC.name());
        NOT_EVAL_END_FUNCODES.add(RecEvalEnum.KC_REC_EVAL4OUT.name());
        NOT_EVAL_END_FUNCODES.add(RecEvalEnum.KC_REC_EVAL4SYNC.name());
    }

    public ApiResponse recommend(String traceId, SceneInfo sceneInfo, ApiRequest apiRequest) {
        if (log.isDebugEnabled()) {
            log.debug("traceId:{}, sceneInfo:{}, apiRequest:{}", traceId, sceneInfo, apiRequest);
        }
        MultiLayerGraphRecommendRequest engineRequest = new MultiLayerGraphRecommendRequest();
        //兜底重写错题本
        ErrorTopicAbstractParamAdapter<?, ?> errorTopicAbstractParamAdapter = null;
        AbstractParamAdapter<?, ?> paramAdapter = null;
        ErrorTopicGraphRecommendRequest errorTopicGraphRecommendRequest = null;
        if (StudyCodeEnum.MACROGRAPH_ERROR_BOOK.equals(sceneInfo.getStudyCode())) {
            //引擎参数转换
            errorTopicAbstractParamAdapter = errorTopicSortParamAdapterSelector.select(sceneInfo, apiRequest.getPayload());
            errorTopicGraphRecommendRequest = errorTopicAbstractParamAdapter.adapt(traceId, sceneInfo, apiRequest.getPayload());
            BeanUtil.copyProperties(errorTopicGraphRecommendRequest, engineRequest);

        } else {
            paramAdapter = adapterSelector.select(sceneInfo, apiRequest.getPayload());
            engineRequest = paramAdapter.adapt(apiRequest.getTraceId(), sceneInfo, apiRequest.getPayload());
        }
        if (log.isDebugEnabled()) {
            log.debug("SimpleGraphRecommend request:{}", engineRequest);
        }
        MultiLayerGraphRecommendResponse engineResponse = simpleGraphRecommend.recommend(engineRequest);
        if (log.isDebugEnabled()) {
            log.debug("SimpleGraphRecommend response:{}", engineResponse);
        }

        // 缓存推荐结果
        if (StrUtil.isNotBlank(engineResponse.getCacheKey())) {
            if (log.isDebugEnabled()) {
                log.debug("cache key:{}", engineResponse.getCacheKey());
            }
            JSONObject engineResponseJson = (JSONObject) JSON.toJSON(engineResponse);
            DataHub.getStudyLogService().saveRecommendRecordCache(traceId, engineResponse.getCacheKey(), engineResponseJson);
        }

        // outNodeInfos 至少是空数据，避免是 null
        List<NodeInfo> outNodeInfos = Optional.ofNullable(engineResponse.getOutNodeInfos()).orElse(Lists.newArrayList());
        engineResponse.setOutNodeInfos(outNodeInfos);
        // RecommendInfo，避免是 null
        if (engineResponse.getRecommendInfo() == null) {
            engineResponse.setRecommendInfo(new RecommendInfo());
        }

        if (!StudyCodeEnum.MACROGRAPH_ERROR_BOOK.equals(sceneInfo.getStudyCode())) {
            try {
                //推题场景，存储推荐记录
                storeRecommendLogs(traceId, sceneInfo, engineRequest, engineResponse);
            } catch (Exception e) {
                log.error("SortFailoverService failover storeRecommendLogs fail, e=" + e.getMessage());
            }

            try {
                //根据引擎推荐结果，更新用户画像
                updateUserMasterInfo(traceId, sceneInfo, engineRequest.getInNodeIdList(),  engineRequest.getInNodeChapterMap(),engineResponse);
            } catch (Exception e) {
                log.error("SortFailoverService failover updateUserMasterInfo fail, e=" + e.getMessage());
            }
        }

        // 组装返回结果
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setTraceId(traceId);
        apiResponse.setHeader(new ApiResponseHeader());

        FuncResult adapt = null;
        if (StudyCodeEnum.MACROGRAPH_ERROR_BOOK.equals(sceneInfo.getStudyCode())) {
            com.iflytek.hy.rec.errtopsort.interfaces.param.MultiLayerGraphRecommendResponse response = new com.iflytek.hy.rec.errtopsort.interfaces.param.MultiLayerGraphRecommendResponse();
            BeanUtil.copyProperties(engineResponse, response);
            if (errorTopicAbstractParamAdapter != null) {
                adapt = errorTopicAbstractParamAdapter.adapt(errorTopicGraphRecommendRequest, response);
            }
        } else {
            if (paramAdapter != null) {
                adapt = paramAdapter.adapt(engineRequest, engineResponse);
            }
        }
        apiResponse.setObjectPayload(adapt);

        return apiResponse;
    }


    private void storeRecommendLogs(String traceId,
                                    SceneInfo sceneInfo,
                                    MultiLayerGraphRecommendRequest request,
                                    MultiLayerGraphRecommendResponse response) {

        // 点排序不存推荐记录
        RecNodeEnum[] values = RecNodeEnum.values();
        for (RecNodeEnum value : values) {
            if (value.name().equals(sceneInfo.getFunctionCode())) {
                return;
            }
        }
//        if (RecNodeParam.FUNC_CODE.equals(sceneInfo.getFunctionCode())) {
//            return;
//        }
        if (RecTraceNodeParam.FUNC_CODE.equals(sceneInfo.getFunctionCode())) {
            return;
        }

        //多轮测评推题 集合仅有1个推荐资源
        List<NodeInfo> outNodeInfos = response.getOutNodeInfos();
        Optional<RecommendInfo> recommendInfo = Optional.ofNullable(response.getRecommendInfo());
        if (CollectionUtils.isEmpty(outNodeInfos)) {
            log.error("MultiLayerGraphRecommendResponse|推荐点列表为空，traceId={}", traceId);
        }

        // 组装recProps
        Long recTime = System.currentTimeMillis() / 1000;
        Boolean isTermination = recommendInfo.map(RecommendInfo::getIsTermination).orElse(false);
        Long recEndTime = isTermination ? recTime : null;
        Long unitTimes = recommendInfo.map(RecommendInfo::getCostTime).orElse(null);

        StudyLogRecordEntity.RecProps recProps = new StudyLogRecordEntity.RecProps();
        recProps.setRecTraceId(traceId);
        recProps.setRecTime(recTime);
        recProps.setRecContext(null);
        recProps.setRecEndTime(recEndTime);
        recProps.setUnitTimes(unitTimes);

        // 初始化RecLog， 填充场景信息数据
        RecLog source = generateRecLog(sceneInfo, request);

        List<RecLog> candidates = Lists.newArrayList();
        for (NodeInfo nodeInfo : outNodeInfos) {
            Optional<RelationNodeInfo> relationNodeInfo = Optional.ofNullable(nodeInfo.getRelationNodes());
            String nodeId = relationNodeInfo.map(RelationNodeInfo::getNodeId).orElse(null);
            NodeTypeEnum nodeType = relationNodeInfo.map(RelationNodeInfo::getNodeType).map(NodeTypeEnum::parse).orElse(null);

            RecLog recLog = new RecLog();
            BeanUtil.copyProperties(source, recLog, COPY_OPTIONS);
            recLog.setNodeId(nodeId);
            recLog.setNodeType(nodeType);
            recLog.setResNodeId(nodeInfo.getNodeId());
            recLog.setResNodeType(ResourceTypeEnum.TOPIC);

            candidates.add(recLog);
        }

        if (CollectionUtil.isNotEmpty(candidates)) {
            DataHub.getStudyLogService().saveRecLogList(traceId, candidates);
        }
    }


    /**
     * 根据引擎推荐结果，更新用户画像
     *
     * @param traceId    跟踪Id
     * @param sceneInfo  场景信息
     * @param catalogIds 当前测评的目录
     * @param response   引擎返回
     */
    public void updateUserMasterInfo(String traceId, SceneInfo sceneInfo, List<String> catalogIds, Map<String, String> inNodeChapterMap, MultiLayerGraphRecommendResponse response) {
        RecommendInfo recommendInfo = response.getRecommendInfo();
        // 测评未终止
        if (recommendInfo == null || !Boolean.TRUE.equals(recommendInfo.getIsTermination())) {
            return;
        }

        if (NOT_EVAL_END_FUNCODES.contains(sceneInfo.getFunctionCode())) {
            log.info("测评终止: 出门测 || 点排序场景 || 点推题场景，不更新用户画像");
            return;
        }

        //精准学os
        if (StudyCodeEnum.SYNC_OS.equals(sceneInfo.getStudyCode()) && MapUtil.isNotEmpty(inNodeChapterMap)) {
            //ai诊断 自场景 -比如 题包推荐
            catalogIds = Lists.newArrayList(inNodeChapterMap.values());
        }

        //调用路由服务开始更新用户画像
        MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();
        //设值 画像更新范围
        masterDiagnoseParam.setCatalogIds(catalogIds);
        //整理当前场景画像更新服务输入(全量更新参数为1)
        masterDiagnoseParam.setAllUpdate(true);

        if (KC_EVAL_END_FUNCODES.contains(sceneInfo.getFunctionCode())) {
            //知识簇终止测评
            masterDiagnoseParam.setMasterFuncEnum(MasterFuncEnum.KC_MASTER_DIAGNOSE);
        }
        sceneInfo.setFunctionCode(masterDiagnoseParam.getFuncCode());
        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setTraceId(traceId);
        apiRequest.setObjectPayload(masterDiagnoseParam);
        apiRequest.setObjectScene(sceneInfo);
        MultiValueMap<String, String> header = TraceHeaderUtils.buildHeader(traceId, springApplicationName);
        if (log.isDebugEnabled()) {
            log.debug("traceId={},ApiRequest={}", traceId, apiRequest);
        }
        ApiResponse apiResponse = skylineRouterFeign.routeCall(apiRequest, header);
        if (log.isDebugEnabled()) {
            log.debug("traceId={},ApiResponse= {}", traceId, apiResponse);
        }
    }


    private RecLog generateRecLog(SceneInfo sceneInfo, MultiLayerGraphRecommendRequest request) {
        RecLog recLog = new RecLog();
        recLog.setUserId(sceneInfo.getUserId());
        recLog.setPhaseCode(sceneInfo.getPhaseCode());
        recLog.setSchoolId(sceneInfo.getSchoolId());
        recLog.setGradeCode(sceneInfo.getGradeCode());
        recLog.setClassId(sceneInfo.getClassId());
        recLog.setSubjectCode(sceneInfo.getSubjectCode());
        recLog.setBookCode(sceneInfo.getBookCode());
        recLog.setGraphVersion(sceneInfo.getGraphVersion());
        recLog.setStudyCode(sceneInfo.getStudyCode());
        recLog.setBizCode(sceneInfo.getBizCode());
        recLog.setBizAction(sceneInfo.getBizAction());
        recLog.setFuncCode(sceneInfo.getFunctionCode());
        recLog.setRoundId(request.getSessionInfo().getRoundId());
        recLog.setRoundIndex(request.getSessionInfo().getRoundRecNum());
        recLog.setFuncCode(sceneInfo.getFunctionCode());
        recLog.setFrom(sceneInfo.getExt1());
        recLog.setCloneFlag(FlagEnum.FALSE.intValue());
        return recLog;
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws
            BeansException {
        this.applicationContext = applicationContext;
    }
}
