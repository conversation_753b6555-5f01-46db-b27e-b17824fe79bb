package com.iflytek.skylab.service.failover.controller;

import cn.hutool.core.util.StrUtil;
import com.iflytek.hy.rec.framework.exception.EngineException;
import com.iflytek.skylab.core.data.adapter.scene.SceneInfoSelector;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.failover.service.DiagnoseFailoverService;
import com.iflytek.skylab.service.failover.service.FeatureFailoverService;
import com.iflytek.skylab.service.failover.service.SortFailoverService;
import com.iflytek.skylab.service.failover.service.StudentLogFailoverService;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/7 17:55
 */
@Slf4j
@Component
public class FailoverController implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Autowired
    private SortFailoverService sortFailoverService;
    @Autowired
    private DiagnoseFailoverService diagnoseFailoverService;
    @Autowired
    private StudentLogFailoverService studentLogFailoverService;
    @Autowired
    private FeatureFailoverService featureFailoverService;

    public ApiResponse process(ApiRequest apiRequest) {
        if (log.isDebugEnabled()) {
            log.debug("apiRequest= {}", apiRequest);
        }
        String traceId = apiRequest.getTraceId();

        SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);
        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }
        //移除 场景信息
        if (apiRequest.getParameter().containsKey(DispatchApiRequest.SCENE_INFO_KEY)) {
            apiRequest.getParameter().remove(DispatchApiRequest.SCENE_INFO_KEY);
        }

        ApiResponse apiResponse = null;
        String funcCode = sceneInfo.getFunctionCode();
        try {
            if (StringUtils.startsWith(funcCode, "REC_") ||StringUtils.startsWith(funcCode, "KC_REC_")) {
                apiResponse = sortFailoverService.recommend(traceId, sceneInfo, apiRequest);
            } else if (StringUtils.startsWith(funcCode, "MASTER_") || StringUtils.startsWith(funcCode, "KC_MASTER_") || StringUtils.startsWith(funcCode, "FAST_MASTER_FETCH")) {
                apiResponse = diagnoseFailoverService.diagnose(traceId, sceneInfo, apiRequest);
            } else if (StringUtils.startsWith(funcCode, "STUDY_") || StringUtils.startsWith(funcCode, "KC_STUDY_")) {
                apiResponse = studentLogFailoverService.studentLog(traceId, sceneInfo, apiRequest);
            } else if (StringUtils.startsWith(funcCode, "COM_FEA") || StringUtils.startsWith(funcCode, "FEA_ACQUIRE")) {
                apiResponse = featureFailoverService.selectFeature(traceId, sceneInfo, apiRequest);
            } else {
                throw new Exception(StrUtil.format("{}暂不支持兜底", funcCode));
            }
        } catch (EngineException e) {
            log.error("兜底服务调用引擎错误：{}", e.getMessage());
            apiResponse = new ApiResponse(new ApiResponseHeader(e.getErrorCode(), e.getMessage()));
            apiResponse.setTraceId(traceId);
        } catch (Exception e) {
            log.error("兜底服务错误：" + e.getMessage(), e);
            apiResponse = new ApiResponse(new ApiResponseHeader(-1, e.getMessage()));
            apiResponse.setTraceId(traceId);
        } finally {
            List<ApiResponseHeader.ErrorNode> errorNodes = apiResponse.getHeader().getErrorNodes();
            errorNodes = errorNodes == null ? new ArrayList<>() : errorNodes;
            errorNodes.add(new ApiResponseHeader.ErrorNode("failover", "true"));
            apiResponse.getHeader().setErrorNodes(errorNodes);
        }
        return apiResponse;
    }


    /**
     * 注入spring容器
     *
     * @param applicationContext
     * @throws BeansException
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
