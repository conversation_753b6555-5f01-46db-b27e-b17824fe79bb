package com.iflytek.skylab.service.failover.config;

import com.iflytek.hy.rec.feaacquire.FeaAcquire;
import com.iflytek.hy.rec.feaacquire.annotation.EnableFeaAcquire;
import com.iflytek.hy.rec.feaprocess.FeaPostProcess;
import com.iflytek.hy.rec.feaprocess.annotation.EnableFeaProcess;
import com.iflytek.hy.rec.simple.ISimpleGraphDiagnose;
import com.iflytek.hy.rec.simple.ISimpleGraphDtpmapDiagnose;
import com.iflytek.hy.rec.simple.ISimpleGraphRecommend;
import com.iflytek.hy.rec.simple.diagnose.SimpleGraphDiagnoseImpl;
import com.iflytek.hy.rec.simple.diagnose.SimpleGraphDtpmapDiagnoseImpl;
import com.iflytek.hy.rec.simple.recommend.SimpleRecommendImpl;
import com.iflytek.skylab.core.data.adapter.sort.SortParamAdapterSelector;
import com.iflytek.skylab.core.data.adapter.sort.errortopic.ErrorTopicSortParamAdapterSelector;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.service.failover.service.DiagnoseFailoverService;
import com.iflytek.skylab.service.failover.service.FeatureFailoverService;
import com.iflytek.skylab.service.failover.service.SortFailoverService;
import com.iflytek.skylab.service.failover.service.StudentLogFailoverService;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import com.iflytek.skyline.common.EnableSkylineFeign;
import com.iflytek.skyline.common.feign.SkylineRouterFeign;
import com.iflytek.skyline.resource.annotation.EnableSkylineResource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetLogging;

/**
 * <AUTHOR>
 * @date 2022/5/9 15:47
 */
@EnableSkynetLogging
@Configuration(proxyBeanMethods = false)
@EnableSkylineBrave
@EnableSkylineFeign
@EnableSkylineResource
@EnableDataHub
@EnableFeaProcess
@EnableFeaAcquire
public class FailoverConfiguration {

    public static final String SUFFIX_SERVICE = "_SERVICE";

    // @Bean({
    //         "REC_EVAL4IN" + SUFFIX_SERVICE,
    //         "REC_EVAL4OUT" + SUFFIX_SERVICE,
    //         "REC_EVAL4LIMIT" + SUFFIX_SERVICE,
    //         "REC_NODE" + SUFFIX_SERVICE,
    //         "REC_TOPIC" + SUFFIX_SERVICE
    // })
    @Bean
    public SortFailoverService sortFailoverService(SortParamAdapterSelector adapterSelector, ISimpleGraphRecommend simpleGraphRecommend, SkylineRouterFeign skylineRouterFeign, TraceUtils traceUtils, ErrorTopicSortParamAdapterSelector errorTopicSortParamAdapterSelector) {
        return new SortFailoverService(adapterSelector, simpleGraphRecommend, skylineRouterFeign, traceUtils, errorTopicSortParamAdapterSelector);
    }

    @Bean
    public StudentLogFailoverService studentLogFailoverService(SkylineRouterFeign skylineRouterFeign, TraceUtils traceUtils) {
        return new StudentLogFailoverService(skylineRouterFeign, traceUtils);
    }

    // @Bean({
    //         "MASTER_DIAGNOSE" + SUFFIX_SERVICE,
    //         "MASTER_FETCH" + SUFFIX_SERVICE
    // })
    @Bean
    public DiagnoseFailoverService diagnoseFailoverService(ISimpleGraphDiagnose simpleGraphDiagnose, ISimpleGraphDtpmapDiagnose simpleGraphDtpmapDiagnose, TraceUtils traceUtils) {
        return new DiagnoseFailoverService(simpleGraphDiagnose, simpleGraphDtpmapDiagnose, traceUtils);
    }

    @Bean
    public FeatureFailoverService acquireFeatureService(FeaPostProcess feaPostProcess, FeaAcquire feaAcquire) {
        return new FeatureFailoverService(feaPostProcess, feaAcquire);
    }

    @Bean
    public ISimpleGraphRecommend simpleGraphRecommend() {
        return new SimpleRecommendImpl();
    }

    @Bean
    public ISimpleGraphDiagnose simpleGraphDiagnose() {
        return new SimpleGraphDiagnoseImpl();
    }

    @Bean
    public ISimpleGraphDtpmapDiagnose simpleGraphDtpmapDiagnose() {
        return new SimpleGraphDtpmapDiagnoseImpl();
    }
}
