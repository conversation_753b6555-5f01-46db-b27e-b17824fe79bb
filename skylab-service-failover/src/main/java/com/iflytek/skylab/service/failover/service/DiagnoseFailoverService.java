package com.iflytek.skylab.service.failover.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseRequest;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.hy.rec.diag.interfaces.param.MasterInfo;
import com.iflytek.hy.rec.simple.ISimpleGraphDiagnose;
import com.iflytek.hy.rec.simple.ISimpleGraphDtpmapDiagnose;
import com.iflytek.skylab.core.constant.MasterFuncEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.constant.TraceConstant;
import com.iflytek.skylab.core.data.NodeInfo;
import com.iflytek.skylab.core.data.adapter.diag.DiagParamAdapter;
import com.iflytek.skylab.core.data.adapter.mapper.MasterItemMapper;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.MasterData;
import com.iflytek.skylab.core.dataapi.data.MasterItem;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.dataapi.util.MasteryUtil;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.service.failover.data.MasterUpdateTraceLog;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.data.TraceRecordGroup;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/9 15:39
 */
@Slf4j
public class DiagnoseFailoverService {


    private final ISimpleGraphDiagnose simpleGraphDiagnose;
    private final ISimpleGraphDtpmapDiagnose simpleGraphDtpmapDiagnose;
    private final DiagParamAdapter diagParamAdapter;
    private final TraceUtils traceUtils;

    public DiagnoseFailoverService(ISimpleGraphDiagnose simpleGraphDiagnose, ISimpleGraphDtpmapDiagnose simpleGraphDtpmapDiagnose, TraceUtils traceUtils) {
        this.simpleGraphDiagnose = simpleGraphDiagnose;
        this.simpleGraphDtpmapDiagnose = simpleGraphDtpmapDiagnose;
        this.diagParamAdapter = new DiagParamAdapter();
        this.traceUtils = traceUtils;
    }


    public ApiResponse diagnose(String traceId, SceneInfo sceneInfo, ApiRequest apiRequest) {
        if (log.isDebugEnabled()) {
            log.debug("traceId:{}, sceneInfo:{}, apiRequest:{}", traceId, sceneInfo, apiRequest);
        }
        // 组装返回结果
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setTraceId(traceId);
        apiResponse.setHeader(new ApiResponseHeader());

        // 转换参数，调用引擎诊断
        GraphDiagnoseRequest engineRequest = diagParamAdapter.adapt(traceId, sceneInfo, apiRequest.getPayload());
        GraphDiagnoseResponse engineResponse;

        if (StudyCodeEnum.AI_DIAG == sceneInfo.getStudyCode()) {
            com.iflytek.hy.rec.merdiag.interfaces.param.GraphSingleDiganoseRequest engineRequest2 = new com.iflytek.hy.rec.merdiag.interfaces.param.GraphSingleDiganoseRequest();
            engineRequest2.setSceneInfo(engineRequest.getSceneInfo());

            engineRequest2.setSessionInfo(engineRequest.getSessionInfo());
            engineRequest2.setNodeIds(engineRequest.getNodeIds());
            if (sceneInfo instanceof AiDiagSceneInfo) {
                AiDiagSceneInfo aiDiagSceneInfo = (AiDiagSceneInfo) sceneInfo;
                engineRequest2.setInNodeChapterMap(aiDiagSceneInfo.getNodeCatalogMap());
            }
            if (CollUtil.isEmpty(engineRequest2.getInNodeChapterMap())) {
                log.debug("sceneInfo 未获取到NodeCatalogMap，从nodeInfos中获取");
                List<NodeInfo> nodeInfos = apiRequest.getPayload().getList("nodeInfos", NodeInfo.class);
                if (CollUtil.isNotEmpty(nodeInfos)) {
                    Map<String, String> nodeCatalogMap = nodeInfos.stream().collect(Collectors.toMap(NodeInfo::getNodeId, NodeInfo::getCatalogId));
                    engineRequest2.setInNodeChapterMap(nodeCatalogMap);
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("SimpleGraphDiagnose(AIDIAG) request:{}", engineRequest2);
            }
            engineResponse = simpleGraphDiagnose.mergeDiagnose(engineRequest2);
            if (log.isDebugEnabled()) {
                log.debug("SimpleGraphDiagnose response:{}", engineResponse);
            }
            try {
                // 更新用户画像数据
                storeMasterLogs(traceId, sceneInfo, engineRequest, engineResponse);
            } catch (Exception e) {
                log.error("DiagnoseFailoverService failover storeMasterLogs fail, e={}", e.getMessage());
            }
            apiResponse.setObjectPayload(diagParamAdapter.adapt(engineRequest.getNodeIds(), engineResponse));
        } else if (StudyCodeEnum.SYNC_OS == sceneInfo.getStudyCode()) {
            com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseRequest engineRequest3 = new com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseRequest();
            engineRequest3.setSceneInfo(engineRequest.getSceneInfo());

            com.iflytek.hy.rec.dtpmapdiag.interfaces.param.SessionInfo sessionInfo3 = new com.iflytek.hy.rec.dtpmapdiag.interfaces.param.SessionInfo();
            sessionInfo3.setTraceId(engineRequest.getSessionInfo().getTraceId());
            sessionInfo3.setStrategyId(engineRequest.getSessionInfo().getStrategyId());
            engineRequest3.setSessionInfo(sessionInfo3);
            engineRequest3.setCatalogIds(engineRequest.getCatalogIds());
            engineRequest3.setNodeIds(engineRequest.getNodeIds());
            engineRequest3.setLearnPointUpdate(engineRequest.getLearnPointUpdate());
            engineRequest3.setAllUpdate(engineRequest.getAllUpdate());

            if (log.isDebugEnabled()) {
                log.debug("SimpleGraphDtpmapDiagnose request:{}", engineRequest);
            }
            com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseResponse engineResponse3;
            if (StrUtil.equals(sceneInfo.getFunctionCode(), MasterFuncEnum.FAST_MASTER_FETCH.name())) {
                engineResponse3 = simpleGraphDtpmapDiagnose.fastMasterFetch(engineRequest3);
            } else {
                engineResponse3 = simpleGraphDtpmapDiagnose.diagnose(engineRequest3);
            }
            if (log.isDebugEnabled()) {
                log.debug("SimpleGraphDiagnose response:{}", engineResponse3);
            }
            if (!StrUtil.equals(sceneInfo.getFunctionCode(), MasterFuncEnum.FAST_MASTER_FETCH.name())) {
                try {
                    // 更新用户画像数据
                    storeMasterLogs(traceId, sceneInfo, engineRequest, engineResponse3);
                } catch (Exception e) {
                    log.error("DiagnoseFailoverService failover storeMasterLogs fail, e={}", e.getMessage());
                }
            }
            apiResponse.setObjectPayload(diagParamAdapter.adapt(engineRequest.getNodeIds(), engineResponse3));
        } else {
            if (log.isDebugEnabled()) {
                log.debug("SimpleGraphDiagnose request:{}", engineRequest);
            }
            engineResponse = simpleGraphDiagnose.diagnose(engineRequest);
            if (log.isDebugEnabled()) {
                log.debug("SimpleGraphDiagnose response:{}", engineResponse);
            }
            try {
                // 更新用户画像数据
                storeMasterLogs(traceId, sceneInfo, engineRequest, engineResponse);
            } catch (Exception e) {
                log.error("DiagnoseFailoverService failover storeMasterLogs fail, e={}", e.getMessage());
            }
            apiResponse.setObjectPayload(diagParamAdapter.adapt(engineRequest.getNodeIds(), engineResponse));
        }
        return apiResponse;
    }


    public void storeMasterLogs(String traceId, SceneInfo sceneInfo, GraphDiagnoseRequest engineRequest, GraphDiagnoseResponse diagnoseResponse) {
        List<MasterInfo> masterInfoList = diagnoseResponse.getMasterInfoList();
        if (CollectionUtils.isEmpty(masterInfoList)) {
            log.warn("traceId={}; masterInfoList is empty.", traceId);
            return;
        }

        //设值 画像更新数据
        List<MasterItem> items = new ArrayList<>();
        for (MasterInfo info : masterInfoList) {
            if (info.getNeedUpdate()) {
                MasterItem masterItem = MasterItemMapper.INSTANCE.toMasterItem(traceId, sceneInfo, info);
                if(!MasteryUtil.validMastery(masterItem)){
//                    log.debug("3.validMastery traceId: {}, sceneInfo: {}, masterItem: {}", traceId, JSON.toJSONString(sceneInfo), JSON.toJSONString(masterItem));
                    continue;
                }

                items.add(masterItem);
            }
        }

        String userId = sceneInfo.getUserId();
        if (CollectionUtils.isEmpty(items)) {
            log.debug("本次处理暂无需要更新的用户画像数据，userId={}，traceId={}，", userId, traceId);
            return;
        }

        MasterData masterData = new MasterData().setUserId(userId).setItems(items);
        if (log.isDebugEnabled()) {
            log.debug("traceId={}; updateMasterData= {}", traceId, masterData);
        }
        // List<String> updated = DataHub.getMasterService().updateMasterData(traceId, masterData);
        List<UserMasteryRecord> updated = DataHub.getMasterService().updateMasterDataAndGet(traceId, masterData);
        if (log.isDebugEnabled()) {
            List<String> ids = Optional.ofNullable(updated)
                    .orElse(new ArrayList<>())
                    .stream()
                    .map(UserMasteryRecord::getId)
                    .collect(Collectors.toList());
            log.debug("traceId={}; updateMasterData.ids= {}", traceId, StrUtil.join(",", ids));
        }

        MasterUpdateTraceLog traceLog = new MasterUpdateTraceLog()
                .setRecords(updated)
                .setSessionInfo(engineRequest.getSessionInfo())
                .setSceneInfo(engineRequest.getSceneInfo());

        try {
            if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
                //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
            }else {
                traceUtils.record(TraceRecordGroup.FLOW, MasterUpdateTraceLog.TYPE, traceLog);
            }
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("MasterUpdateTraceLog:{}", traceLog);
            }
            log.error("MasterUpdateTraceLog埋点日志记录异常，message={}", e.getMessage());
        }
    }

    public void storeMasterLogs(String traceId, SceneInfo sceneInfo, GraphDiagnoseRequest engineRequest, com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseResponse diagnoseResponse) {
        List<com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo> masterInfoList = diagnoseResponse.getMasterInfoList();
        if (CollectionUtils.isEmpty(masterInfoList)) {
            log.warn("traceId={}; masterInfoList is empty.", traceId);
            return;
        }

        //设值 画像更新数据
        List<MasterItem> items = new ArrayList<>();
        for (com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo info : masterInfoList) {
            if (info.getNeedUpdate()) {
                MasterItem masterItem = MasterItemMapper.INSTANCE.toMasterItem(traceId, sceneInfo, info);
                if(!MasteryUtil.validMastery(masterItem)){
//                    log.debug("4.validMastery traceId: {}, sceneInfo: {}, masterItem: {}", traceId, JSON.toJSONString(sceneInfo), JSON.toJSONString(masterItem));
                    continue;
                }

                items.add(masterItem);
            }
        }

        String userId = sceneInfo.getUserId();
        if (CollectionUtils.isEmpty(items)) {
            log.debug("本次处理暂无需要更新的用户画像数据，userId={}，traceId={}，", userId, traceId);
            return;
        }

        MasterData masterData = new MasterData().setUserId(userId).setItems(items);
        if (log.isDebugEnabled()) {
            log.debug("traceId={}; updateMasterData= {}", traceId, masterData);
        }
        // List<String> updated = DataHub.getMasterService().updateMasterData(traceId, masterData);
        List<UserMasteryRecord> updated = DataHub.getMasterService().updateMasterDataAndGet(traceId, masterData);
        if (log.isDebugEnabled()) {
            List<String> ids = Optional.ofNullable(updated)
                    .orElse(new ArrayList<>())
                    .stream()
                    .map(UserMasteryRecord::getId)
                    .collect(Collectors.toList());
            log.debug("traceId={}; updateMasterData.ids= {}", traceId, StrUtil.join(",", ids));
        }

        MasterUpdateTraceLog traceLog = new MasterUpdateTraceLog()
                .setRecords(updated)
                .setSessionInfo(engineRequest.getSessionInfo())
                .setSceneInfo(engineRequest.getSceneInfo());

        try {
            if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
                //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
            }else {
                traceUtils.record(TraceRecordGroup.FLOW, MasterUpdateTraceLog.TYPE, traceLog);
            }
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("MasterUpdateTraceLog:{}", traceLog);
            }
            log.error("MasterUpdateTraceLog埋点日志记录异常，message={}", e.getMessage());
        }
    }


}
