package com.iflytek.skylab.service.failover.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.MasterFuncEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.StudyLogFuncEnum;
import com.iflytek.skylab.core.constant.TraceConstant;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.util.OdeonUtil;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.data.TraceHeaderUtils;
import com.iflytek.skyline.brave.data.TraceRecordGroup;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.common.feign.SkylineRouterFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/9 15:40
 */
@Slf4j
public class StudentLogFailoverService implements ApplicationContextAware {

    private final CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();
    public static final String CORRECT_FUNC_CODE = "STUDY_CORRECT_LOG";
    public static final String STUDY_LOG = "STUDY_LOG";
    public static final String STUDY_QUERY = "STUDY_QUERY";

    @Value("${spring.application.name:skylab-failover}")
    private String springApplicationName;

    private ApplicationContext applicationContext;
    private final SkylineRouterFeign skylineRouterFeign;
    private final TraceUtils traceUtils;


    public StudentLogFailoverService(SkylineRouterFeign skylineRouterFeign, TraceUtils traceUtils) {

        this.skylineRouterFeign = skylineRouterFeign;
        this.traceUtils = traceUtils;
    }

    public ApiResponse studentLog(String traceId, SceneInfo sceneInfo, ApiRequest apiRequest) {
        if (log.isDebugEnabled()) {
            log.debug("traceId:{}, sceneInfo:{}, apiRequest:{}", traceId, sceneInfo, apiRequest);
            log.debug("DispatchApiRequest= {}", apiRequest);
        }

        // 组装返回结果
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setTraceId(traceId);
        apiResponse.setHeader(new ApiResponseHeader());

        JSONObject apiRequestPayload = apiRequest.getPayload();

        if (apiRequestPayload == null) {
            throw new ParamNotExistException("apiRequest.getPayload() of ApiRequest");
        }

        if (STUDY_QUERY.equals(sceneInfo.getFunctionCode())) {
            StudyLogQueryResult studyLogQueryResult = new StudyLogQueryResult().setExistStudentLog(false);
            try {
                StudyLogQueryParam studyLogParam = apiRequestPayload.to(StudyLogQueryParam.class);
                studyLogQueryResult = query(apiRequest.getTraceId(), sceneInfo, studyLogParam);
                if (log.isDebugEnabled()) {
                    log.debug("DispatchApiResponse= {}", apiResponse);
                }
            } catch (Exception e) {
                log.error("traceId={},funcCode={},error={},failover error", traceId, sceneInfo.getFunctionCode(), e.toString());
            }
            apiResponse.setObjectPayload(studyLogQueryResult);
            return apiResponse;
        }

        StudyLogParam studyLogParam = apiRequestPayload.to(StudyLogParam.class);
        Assert.notNull(studyLogParam, "StudyLogParam must not null");

        if (CollUtil.isEmpty(studyLogParam.getItems())) {
            return apiResponse;
        }
        if (log.isDebugEnabled()) {
            log.debug("traceId={};StudyLogParam={}", traceId, studyLogParam);
        }

        //保存答题记录，同步诊断当前场景，异步诊断依赖的场景。（调用路由诊断）
        List<String> nodeIdList = new ArrayList<>(studyLogParam.getItems().size());
        List<FeedbackLog> feedbackLogList = new ArrayList<>(studyLogParam.getItems().size());
        for (StudyLogRecord studyLogRecord : studyLogParam.getItems()) {

            //学情答题记录中 不设值funcCode，由测评推题或者点推题设值。
            FeedbackLog feedbackLog = new FeedbackLog();
            BeanUtil.copyProperties(sceneInfo, feedbackLog, copyOptions);
            BeanUtil.copyProperties(studyLogRecord, feedbackLog, copyOptions);
            feedbackLog.setScoreRatio(repairScoreRatio(feedbackLog));
            //设置来源
            feedbackLog.setFrom(sceneInfo.getExt1());
            feedbackLogList.add(feedbackLog);
            nodeIdList.add(studyLogRecord.getNodeId());
        }
        if (log.isDebugEnabled()) {
            // 保存答题记录
            log.debug("saveFeedbackLogListAndGet= {}", feedbackLogList);
        }
        if (CORRECT_FUNC_CODE.equals(sceneInfo.getFunctionCode()) || StudyLogFuncEnum.KC_STUDY_CORRECT_LOG.name().equals(sceneInfo.getFunctionCode()) ) {
            // 保存批改记录
            log.debug("save StudyCorrectLogList ");
            List<FeedbackLog> feedbackLogs = DataHub.getStudyLogService().saveStudyCorrectLogList(traceId, feedbackLogList);
            StudyLogResult result = new StudyLogResult().setIdList(feedbackLogs.stream().map(FeedbackLog::getId).collect(Collectors.toList()));

            apiResponse.setObjectPayload(result);
            if (log.isDebugEnabled()) {
                log.debug("ApiResponse= {}", apiResponse);
            }
            return apiResponse;
        }
        //大图谱日志上报
        List<FeedbackLog> savedFeedbackLogList;
        if (STUDY_LOG.equals(sceneInfo.getFunctionCode()) && StrUtil.startWith("MACROGRAPH_", sceneInfo.getStudyCode().name())) {
            savedFeedbackLogList = DataHub.getStudyMacrographLogService().saveFeedbackLogListAndGet(traceId, feedbackLogList);
        } else {
            savedFeedbackLogList = DataHub.getStudyLogService().saveFeedbackLogListAndGet(traceId, feedbackLogList);
        }
        List<String> idList = savedFeedbackLogList.stream().map(FeedbackLog::getId).collect(Collectors.toList());
        log.debug("saveFeedbackLogListAndGet return idList= {}", idList);

        if (savedFeedbackLogList.size() != feedbackLogList.size()) {
            log.warn("Ooh, some things wrong happened saving feedbackLogs!");
        }
        // 推送到 Odeon 平台
        String userId = sceneInfo.getUserId();
        savedFeedbackLogList.forEach(feedbackLog -> {
            JSONObject json = OdeonUtil.wrappedOrNull(feedbackLog);
            if (json != null) {
                if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
                    //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
                }else {
                    traceUtils.record(TraceRecordGroup.EVAL, "FEEDBACK_LOG", json);
                }
            }
        });

        try {
            //本次场景下诊断（同步调用）直接使用 答题记录中的关联的点
            this.extracted(traceId, sceneInfo, nodeIdList);
        } catch (Exception e) {
            log.error("traceId=" + traceId + " [兜底]作答日志上报，更新点画像失败", e);
        }

        apiResponse.setObjectPayload(new StudyLogResult().setIdList(idList));
        return apiResponse;
    }

    /**
     * 诊断
     *
     * @param traceId
     * @param sceneInfo
     * @param nodeIdList 学习点（考点，锚点） 列表
     * @return
     */
    private ApiResponse extracted(String traceId, SceneInfo sceneInfo, List<String> nodeIdList) {
        if (log.isDebugEnabled()) {
            log.debug("TraceId= {}; NodeIdList= {}", nodeIdList);
        }

        //调用路由服务开始画像诊断
        MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();

        //FunctionCode-诊断场景知识簇分流
        if (StudyLogFuncEnum.KC_STUDY_LOG.name().equals(sceneInfo.getFunctionCode())) {
            masterDiagnoseParam.setMasterFuncEnum(MasterFuncEnum.KC_MASTER_DIAGNOSE);
        }

        masterDiagnoseParam.setNodeIds(nodeIdList);
        sceneInfo.setFunctionCode(masterDiagnoseParam.getFuncCode());
        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setTraceId(traceId);
        apiRequest.setObjectPayload(masterDiagnoseParam);
        apiRequest.setObjectScene(sceneInfo);
        MultiValueMap<String, String> header = TraceHeaderUtils.buildHeader(traceId, springApplicationName);
        if (log.isDebugEnabled()) {
            log.debug("ApiRequest= {}", apiRequest);
        }
        ApiResponse apiResponse = skylineRouterFeign.routeCall(apiRequest, header);
        if (log.isDebugEnabled()) {
            log.debug("ApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }

    /**
     * 修复得分率
     *
     * @param feedbackLog
     * @return
     */
    private double repairScoreRatio(FeedbackLog feedbackLog) {
        if (feedbackLog.getStandardScore() == null || feedbackLog.getStandardScore() == 0) {
            return 0;
        }
        double scoreRatio = feedbackLog.getScore() / feedbackLog.getStandardScore();
        if (scoreRatio > 1) {
            log.error("答题/批改记录得分率超标,feedbackLog = {}", JSON.toJSONString(feedbackLog));
        }
        return Math.min(scoreRatio, 1);
    }

    /**
     * 作答日志查询
     *
     * @param traceId
     * @param sceneInfo
     * @param studyLogParam
     * @return
     */
    public StudyLogQueryResult query(String traceId, SceneInfo sceneInfo, StudyLogQueryParam studyLogParam) {
        if (log.isDebugEnabled()) {
            log.debug("traceId={};StudyLogParam={}", traceId, studyLogParam);
        }
        //目录下锚点
        List<String> anchor = querySubGraphWithCata(traceId, sceneInfo.getGraphVersion(), studyLogParam.getCatalogId());
        StudyLogQuery query = new StudyLogQuery()
                .setUserId(sceneInfo.getUserId())
                .setSubjectCode(sceneInfo.getSubjectCode())
                .setPhaseCode(sceneInfo.getPhaseCode()).setNodeType(NodeTypeEnum.ANCHOR_POINT)
                .setNodeIdList(anchor).setBizCodeList(Lists.newArrayList(sceneInfo.getBizCode()));

        boolean existStudentLog = DataHub.getStudyLogService().existStudentLog(traceId, query);


        return new StudyLogQueryResult().setExistStudentLog(existStudentLog);
    }

    /**
     * 图谱查询-获取锚点下面题目
     *
     * @param traceId
     * @param graphVersion
     * @param cata
     * @return
     */
    private List<String> querySubGraphWithCata(String traceId, String graphVersion, String cata) {

        List<String> anchors = new ArrayList<>();

        GraphVertexesQuery query = new GraphVertexesQuery();
        query.setTraceId(traceId);
        query.setIds(Lists.newArrayList(cata));
        query.setGraphVersion(graphVersion);
        GraphData graphData = DataHub.getGraphService().queryVertexByIds(query);
        List<GraphData.GraphVertex> vertices = graphData.getVertices();

        if (CollectionUtil.isNotEmpty(vertices)) {
            for (GraphData.GraphVertex vertex : vertices) {
                String label = vertex.getLabel();

                SubGraphQuery subGraphQuery = new SubGraphQuery();
                subGraphQuery.setTraceId(traceId);
                subGraphQuery.setGraphVersion(graphVersion);
                subGraphQuery.setRootVertexLabel(label);
                subGraphQuery.setRootVertexIdList(Lists.newArrayList(cata));
                List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
                edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(label).target("ANCHOR_POINT").build());
                subGraphQuery.setEdgeLabels(edgeLabels);
                if (log.isDebugEnabled()) {
                    log.debug("GraphQuery={}", subGraphQuery);
                }
                GraphData subGraph = DataHub.getGraphService().querySubGraph(subGraphQuery);
                if (log.isDebugEnabled()) {
                    log.debug("GraphData={}", subGraph);
                }
                List<GraphData.GraphEdge> edges = subGraph.getEdges();
                if (CollUtil.isNotEmpty(edges)) {
                    List<String> collect = edges.stream().map(GraphData.GraphEdge::getTarget).collect(Collectors.toList());
                    anchors.addAll(collect);
                    if (log.isDebugEnabled()) {
                        log.debug("anchors={}", anchors.size());
                    }
                }
            }
        }
        return anchors;
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
