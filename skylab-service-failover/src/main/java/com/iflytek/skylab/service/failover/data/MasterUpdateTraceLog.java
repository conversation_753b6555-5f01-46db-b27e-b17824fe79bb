package com.iflytek.skylab.service.failover.data;

import com.iflytek.hy.rec.diag.interfaces.param.SessionInfo;
import com.iflytek.hy.rec.domain.model.valueobj.SceneInfo;
import com.iflytek.skylab.core.data.Jsonable;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/8 16:53
 */
@Getter
@Setter
@Accessors(chain = true)
public class MasterUpdateTraceLog extends Jsonable {

    public static final String TYPE = "MASTER_UPDATE_TRACE_LOG";

    private SceneInfo sceneInfo;

    private SessionInfo sessionInfo;

    private List<UserMasteryRecord> records;

    private Date sendTime = new Date();
}
