INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4274af92-4688-46fb-bc7f-f1206de985af": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "4274af92-4688-46fb-bc7f-f1206de985af": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "9755cb56-5a34-4872-8c96-173e1c10878d": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "9755cb56-5a34-4872-8c96-173e1c10878d": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "6d4877f5-5488-420b-a40a-0ab84757aaf4": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "6d4877f5-5488-420b-a40a-0ab84757aaf4": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4539a088-d2a5-441e-ac14-e2d616f9b384": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "4539a088-d2a5-441e-ac14-e2d616f9b384": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "6a38baf6-1402-475e-b4c4-9fc7e03bd75c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "6a38baf6-1402-475e-b4c4-9fc7e03bd75c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "50401070-1912-4269-b555-3c8e640a4bad": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "50401070-1912-4269-b555-3c8e640a4bad": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "373f6746-9cf6-4324-a14c-b08a8880509b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "373f6746-9cf6-4324-a14c-b08a8880509b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b271accc-f350-4d2c-b445-ebcb114274de": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "b271accc-f350-4d2c-b445-ebcb114274de": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c9f930a5-80ab-424b-b649-8247a83ee604": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "c9f930a5-80ab-424b-b649-8247a83ee604": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "3260841d-be39-4856-8877-c939911acee7": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ec4dd422-9e4e-46d9-a681-0701b02c08d3"  -> "3260841d-be39-4856-8877-c939911acee7": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "0037d23c-4262-48bb-9d4c-6f10252b6ac8": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "0037d23c-4262-48bb-9d4c-6f10252b6ac8": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "534186a5-2ddb-4cd1-aeae-bfa791969a08": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "534186a5-2ddb-4cd1-aeae-bfa791969a08": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "cc7d2ec7-4f2f-4492-bda4-f6c692bc9647": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "cc7d2ec7-4f2f-4492-bda4-f6c692bc9647": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "1305c275-01b9-4b1d-96ec-ea4a3cb340a1": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "1305c275-01b9-4b1d-96ec-ea4a3cb340a1": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "e75a9636-fbf7-482b-8074-2fe12a037f78": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "e75a9636-fbf7-482b-8074-2fe12a037f78": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "3de42181-d0f4-4c9e-a151-e49923d01cfb": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "3de42181-d0f4-4c9e-a151-e49923d01cfb": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "52d3e87c-2f48-4fed-9771-905383541062": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "52d3e87c-2f48-4fed-9771-905383541062": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "d23995dd-76be-413b-a291-0eb59444ce14": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "d23995dd-76be-413b-a291-0eb59444ce14": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "8a5b2ff8-afbe-4e15-b486-4a2ad07e101d": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "8a5b2ff8-afbe-4e15-b486-4a2ad07e101d": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "5b8f69c5-6ae9-414e-b5f6-6902d010890a": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "53afc663-2337-4af1-a917-c44426ca0d8f"  -> "5b8f69c5-6ae9-414e-b5f6-6902d010890a": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b8e7d045-09ca-4e34-906c-916fd5d68b36": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "b8e7d045-09ca-4e34-906c-916fd5d68b36": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "12b97d88-8f00-406c-b0fb-6959fc0979de": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "12b97d88-8f00-406c-b0fb-6959fc0979de": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4dc0668f-ada3-446e-be29-6635871746b0": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "4dc0668f-ada3-446e-be29-6635871746b0": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "d8a1fd87-c272-4a70-be32-6565332f177c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "d8a1fd87-c272-4a70-be32-6565332f177c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "fa8ed0bf-bb7c-4c85-9596-add7f9593aa4": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "fa8ed0bf-bb7c-4c85-9596-add7f9593aa4": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a46dcf73-a1e9-4b9f-9140-2abcfb8e2a6a": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "a46dcf73-a1e9-4b9f-9140-2abcfb8e2a6a": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c1e0f46a-006d-4534-aa31-3bd5d996d3ae": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "c1e0f46a-006d-4534-aa31-3bd5d996d3ae": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c0b177b0-5829-40a5-9133-6771b7b2bc5e": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "c0b177b0-5829-40a5-9133-6771b7b2bc5e": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "3e659c2b-fcb9-4c3e-8845-b531fbb68b91": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "3e659c2b-fcb9-4c3e-8845-b531fbb68b91": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "077bd9d6-b4f6-4de0-a1cb-c0bbd8a1ab14": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ad05227e-093c-4d3e-a2f9-c4c30823c822"  -> "077bd9d6-b4f6-4de0-a1cb-c0bbd8a1ab14": ("0", "");
DELETE EDGE ANCHOR_POINT_TOPIC "ad05227e-093c-4d3e-a2f9-c4c30823c822" -> "373f6746-9cf6-4324-a14c-b08a8880509b"@0;
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "78e464ae-15f7-4efe-9e30-287dc4b2adec": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "78e464ae-15f7-4efe-9e30-287dc4b2adec": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "56fcf31f-306d-4443-991e-5ce8ee651ea6": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "56fcf31f-306d-4443-991e-5ce8ee651ea6": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "86753e79-6c39-4ca4-b793-a8f62ddacb82": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "86753e79-6c39-4ca4-b793-a8f62ddacb82": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f3093c5b-5884-4f99-b6bb-32b6bb778484": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "f3093c5b-5884-4f99-b6bb-32b6bb778484": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "1c6b78bf-c766-4b71-9c14-394289d05213": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "1c6b78bf-c766-4b71-9c14-394289d05213": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "947b109e-b2a1-4a04-9e23-4550f14c8d92": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "947b109e-b2a1-4a04-9e23-4550f14c8d92": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c8b8e30a-0077-4cb9-8467-780101122101": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "c8b8e30a-0077-4cb9-8467-780101122101": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "682784b6-6e0b-427b-8c37-5cee29a472c5": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "682784b6-6e0b-427b-8c37-5cee29a472c5": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "31fd4c38-da56-41f2-93d6-2761466a9b07": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "31fd4c38-da56-41f2-93d6-2761466a9b07": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "946f070c-6ff0-43a3-a4ea-5df5ce851195": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "55e32607-ccc2-49bd-8346-336fe38472da"  -> "946f070c-6ff0-43a3-a4ea-5df5ce851195": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "36fed330-7137-49b3-967f-d4f229439115": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "36fed330-7137-49b3-967f-d4f229439115": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "06c6944f-fe12-40ce-9b6b-ee1ab0de0f68": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "06c6944f-fe12-40ce-9b6b-ee1ab0de0f68": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "cf69c036-cfc7-4bd7-8e62-9832a2e5dc6f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "cf69c036-cfc7-4bd7-8e62-9832a2e5dc6f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a6d9ed62-2a6f-49ce-8700-e31212050528": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "a6d9ed62-2a6f-49ce-8700-e31212050528": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7889914b-f4b6-40dc-be4f-65cae598bf42": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "7889914b-f4b6-40dc-be4f-65cae598bf42": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f3fe7e55-2f74-4b71-94cd-79f775376300": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "f3fe7e55-2f74-4b71-94cd-79f775376300": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "233ce387-ed6f-489d-accd-a20646b9de7e": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "233ce387-ed6f-489d-accd-a20646b9de7e": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "2bcfd3a1-326f-4478-95c1-864ef184ffa2": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "2bcfd3a1-326f-4478-95c1-864ef184ffa2": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "6a75c08c-27fe-407d-9f0c-f8e3c9b69551": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "6a75c08c-27fe-407d-9f0c-f8e3c9b69551": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "8af9bf78-ed1b-4dbc-9ee6-e39296046ee4": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "6568e40d-bf67-4538-825c-74791d9cd83f"  -> "8af9bf78-ed1b-4dbc-9ee6-e39296046ee4": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "88edb10b-21c4-410d-bd5f-ebfb34059173": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "88edb10b-21c4-410d-bd5f-ebfb34059173": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "484148f9-7ae3-4df3-a71f-f99aa91356f4": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "484148f9-7ae3-4df3-a71f-f99aa91356f4": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "ec1c33d1-cc8b-4a63-84f0-f0164d66d90b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "ec1c33d1-cc8b-4a63-84f0-f0164d66d90b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "191d9484-13e7-457d-ac13-1a85910a502f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "191d9484-13e7-457d-ac13-1a85910a502f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "cb0892b0-6a98-4089-a207-f451a199479c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "cb0892b0-6a98-4089-a207-f451a199479c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "3288efa0-beb8-400e-a05c-88c7266180ef": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "3288efa0-beb8-400e-a05c-88c7266180ef": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b4bb240c-f8fc-48d4-a5f6-acfc5c808d58": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "b4bb240c-f8fc-48d4-a5f6-acfc5c808d58": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4b5628d8-bcbc-4aa5-aa8b-a6f899429090": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "4b5628d8-bcbc-4aa5-aa8b-a6f899429090": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "177335b2-249a-4aa4-8959-f0520879ea5f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "177335b2-249a-4aa4-8959-f0520879ea5f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "594241d3-f29d-464b-ab52-61ced3173828": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "0bb770aa-c80f-4d6e-bd94-84a361b7205a"  -> "594241d3-f29d-464b-ab52-61ced3173828": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "38e7c615-fb50-42f8-b8c1-59153f27e817": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "38e7c615-fb50-42f8-b8c1-59153f27e817": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "85db53c0-9d16-40c4-9805-faffc9124de6": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "85db53c0-9d16-40c4-9805-faffc9124de6": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c5c1cd19-f99f-4d6e-a1ee-af08ef5d2180": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "c5c1cd19-f99f-4d6e-a1ee-af08ef5d2180": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "8d9124f5-981b-4bd7-8efb-004942ff4d44": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "8d9124f5-981b-4bd7-8efb-004942ff4d44": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c2e24cd1-124b-4464-8caf-e2b512635b3c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "c2e24cd1-124b-4464-8caf-e2b512635b3c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7822af2b-8673-4236-abe5-252908ae086b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "7822af2b-8673-4236-abe5-252908ae086b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "99b96e5a-7939-4e7d-9243-9504f48492f0": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "99b96e5a-7939-4e7d-9243-9504f48492f0": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "52fece2a-d3b0-4cb9-af83-00dc0e9cb4e6": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "52fece2a-d3b0-4cb9-af83-00dc0e9cb4e6": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "0ecbb95a-f098-4787-b0f7-406e2c65e404": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "0ecbb95a-f098-4787-b0f7-406e2c65e404": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a1fee120-62b0-4fa6-bb4c-0c1be4218033": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "1086a1a1-3f83-4988-b982-5024c1b1a2a8"  -> "a1fee120-62b0-4fa6-bb4c-0c1be4218033": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "232ff6fb-33a6-4386-8a15-3b4419dd1d17": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "232ff6fb-33a6-4386-8a15-3b4419dd1d17": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c2a599b1-c784-4cae-ab8a-b9163905c20d": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "c2a599b1-c784-4cae-ab8a-b9163905c20d": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "3d0218fd-2efe-4daa-b08f-5c595a6d329d": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "3d0218fd-2efe-4daa-b08f-5c595a6d329d": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "44c76f8f-e40d-4c93-8888-f0e1d92076fa": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "44c76f8f-e40d-4c93-8888-f0e1d92076fa": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "35e23951-a74f-4476-8fb7-853f1c8f2472": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "35e23951-a74f-4476-8fb7-853f1c8f2472": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "bea4d2d3-1077-436c-866f-36f550ec380b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "bea4d2d3-1077-436c-866f-36f550ec380b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "e64a797e-a5ff-4ad7-89df-ed40612ae449": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "e64a797e-a5ff-4ad7-89df-ed40612ae449": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "25d3f93c-9798-451f-8e40-9172f7cfd43c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "25d3f93c-9798-451f-8e40-9172f7cfd43c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4ab40bff-0dd3-4455-b1e3-7c32233085b3": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "4ab40bff-0dd3-4455-b1e3-7c32233085b3": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b1e1660a-1c01-44e8-a7e5-f5ed9d0eb933": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "cc6f58e0-bc6b-4384-8bc8-26cdb250fb30"  -> "b1e1660a-1c01-44e8-a7e5-f5ed9d0eb933": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "caa7e291-974c-498b-9957-b58c0f60fbdb": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "caa7e291-974c-498b-9957-b58c0f60fbdb": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b512c394-35fd-4987-9bcd-f4027e375652": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "b512c394-35fd-4987-9bcd-f4027e375652": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "0798ec7a-112e-4832-80df-8b01d23d3209": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "0798ec7a-112e-4832-80df-8b01d23d3209": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4c4f696c-7640-4b95-8ff1-a3f1c91af65c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "4c4f696c-7640-4b95-8ff1-a3f1c91af65c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "68fbd0e2-1c08-4b2a-869a-8fb6100b3ffa": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "68fbd0e2-1c08-4b2a-869a-8fb6100b3ffa": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "82dc2f87-8754-4435-a5a5-e151098717d1": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "82dc2f87-8754-4435-a5a5-e151098717d1": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "740e0b4f-12fe-4552-9920-2c114674049b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "740e0b4f-12fe-4552-9920-2c114674049b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "ddeb21e7-ac50-4bfb-aa94-058cf5d102de": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "ddeb21e7-ac50-4bfb-aa94-058cf5d102de": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "0231fce6-af78-451c-907d-24a6d5062830": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "0231fce6-af78-451c-907d-24a6d5062830": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "45856fbe-e78e-42a6-8428-aac11573b290": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "a7aa88c1-c8d4-4127-87b2-4dbb5b41529c"  -> "45856fbe-e78e-42a6-8428-aac11573b290": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "8f666e56-0c18-497a-ac1d-64c90d8f6f20": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "8f666e56-0c18-497a-ac1d-64c90d8f6f20": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a901e10a-09ee-471e-98c9-cbcee46c2377": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "a901e10a-09ee-471e-98c9-cbcee46c2377": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "d18ca7fb-1aae-42a2-8244-ba1e9c960ddd": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "d18ca7fb-1aae-42a2-8244-ba1e9c960ddd": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "d51a826a-fa35-4d26-9db9-15c42bda063c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "d51a826a-fa35-4d26-9db9-15c42bda063c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "ab3751b6-8845-4f7d-80f9-6b2157b42844": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "ab3751b6-8845-4f7d-80f9-6b2157b42844": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "9915126e-1f1b-4661-832b-14774bd8bfa4": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "9915126e-1f1b-4661-832b-14774bd8bfa4": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b95f7a56-f5e6-4913-8411-8be943b8a452": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "b95f7a56-f5e6-4913-8411-8be943b8a452": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "d21e41d3-6851-4245-bf92-91a888c70d50": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "d21e41d3-6851-4245-bf92-91a888c70d50": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "8429fbbe-ca57-470f-9c49-877a0ce0dfda": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "8429fbbe-ca57-470f-9c49-877a0ce0dfda": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a7e9f9ef-e1d2-4f47-a570-78afc7c1fbe2": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "52d7479d-7db9-4f15-92c2-9717dc584ffa"  -> "a7e9f9ef-e1d2-4f47-a570-78afc7c1fbe2": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "fb1d025e-c4e0-47d4-9d38-bee70caebb15": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "fb1d025e-c4e0-47d4-9d38-bee70caebb15": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "9b2983cc-9231-4b5c-bd4e-de68939b0226": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "9b2983cc-9231-4b5c-bd4e-de68939b0226": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a7c40a83-5849-4e2f-8f99-11722fe43e26": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "a7c40a83-5849-4e2f-8f99-11722fe43e26": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "6430a79d-6794-4875-97fc-9190b7e7860e": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "6430a79d-6794-4875-97fc-9190b7e7860e": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f936afad-3c36-41ba-9381-460d790e2d76": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "f936afad-3c36-41ba-9381-460d790e2d76": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "67ca1eb0-8894-42de-bb52-35ee2b02ecca": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "67ca1eb0-8894-42de-bb52-35ee2b02ecca": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "437e9255-66b0-4f6d-b797-7d1613c151ca": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "437e9255-66b0-4f6d-b797-7d1613c151ca": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "dd882d36-bfce-4cc7-86b2-18ed8e2d43e8": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "dd882d36-bfce-4cc7-86b2-18ed8e2d43e8": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f343010e-72ba-42f9-bd34-7da27e801177": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "f343010e-72ba-42f9-bd34-7da27e801177": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f092e60a-e800-4e56-a65a-a38d224d5177": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e4a25154-22f9-4131-b16f-b434e3da60e3"  -> "f092e60a-e800-4e56-a65a-a38d224d5177": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "3cabe0c1-3db2-4202-aea6-1a9f2d29e188": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "3cabe0c1-3db2-4202-aea6-1a9f2d29e188": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "427a7ed6-59de-4c69-a1c7-a822b0e076a4": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "427a7ed6-59de-4c69-a1c7-a822b0e076a4": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "19e71993-c955-4d0d-b7d4-ee6f4c0c660b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "19e71993-c955-4d0d-b7d4-ee6f4c0c660b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "978586cd-ed83-44a6-a88a-a548b555237a": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "978586cd-ed83-44a6-a88a-a548b555237a": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7bedc05d-5b2b-4bdb-8ee7-abc798639600": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "7bedc05d-5b2b-4bdb-8ee7-abc798639600": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b1761436-abf2-4b99-952d-03c3539cdc26": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "b1761436-abf2-4b99-952d-03c3539cdc26": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "57d31587-e971-4eed-b165-fad4ed02c962": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "57d31587-e971-4eed-b165-fad4ed02c962": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f764755a-f190-41e8-b809-3965b0175384": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "f764755a-f190-41e8-b809-3965b0175384": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "12852adc-41e2-4754-ac37-b4768c433528": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "12852adc-41e2-4754-ac37-b4768c433528": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "fa5d3972-2264-4c28-a784-5986abcd824a": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "2a303008-37b2-4801-bda0-0dcb2dd38c80"  -> "fa5d3972-2264-4c28-a784-5986abcd824a": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "43f17b98-25d3-45ad-b6ab-fe87ca53a8e3": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "43f17b98-25d3-45ad-b6ab-fe87ca53a8e3": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a97dc275-abd0-4fd7-b493-4461730450b4": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "a97dc275-abd0-4fd7-b493-4461730450b4": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c461b5e9-9de7-4069-a41b-d792bd8bd273": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "c461b5e9-9de7-4069-a41b-d792bd8bd273": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7f6ea2fd-623c-46f7-af3b-204052380fc1": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "7f6ea2fd-623c-46f7-af3b-204052380fc1": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7b6eb8ec-b914-438a-a4b3-dfd26cd5ef97": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "7b6eb8ec-b914-438a-a4b3-dfd26cd5ef97": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "575d1bb2-6365-4c42-9721-090fbb842fd3": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "575d1bb2-6365-4c42-9721-090fbb842fd3": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c456d827-62e1-4be5-a991-a39a76a80e90": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "c456d827-62e1-4be5-a991-a39a76a80e90": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "11eb2e30-e4c5-4f06-8cfe-9573e05bd234": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "11eb2e30-e4c5-4f06-8cfe-9573e05bd234": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "796778af-3de0-4559-b7b0-70cb8540b2c1": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "796778af-3de0-4559-b7b0-70cb8540b2c1": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "232655fc-1d0d-4094-9f76-71a4152f19ae": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "ba937c03-3ba9-42de-91af-0b82b4b83e85"  -> "232655fc-1d0d-4094-9f76-71a4152f19ae": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "80169e35-2d9d-4a1b-aea9-f69c30199564": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "80169e35-2d9d-4a1b-aea9-f69c30199564": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "50ac5bd4-54b7-4130-8f34-c77c7bbf8bd2": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "50ac5bd4-54b7-4130-8f34-c77c7bbf8bd2": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "0ba83af2-25ea-4875-bcde-db3b6a7089f6": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "0ba83af2-25ea-4875-bcde-db3b6a7089f6": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "69189785-0112-428b-accb-2b1b5cff24d3": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "69189785-0112-428b-accb-2b1b5cff24d3": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "6a50bb7c-a003-4451-b4e1-15c9d7ac68a8": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "6a50bb7c-a003-4451-b4e1-15c9d7ac68a8": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "1c406d9a-cb28-4bfd-9ba4-08ba8aba243c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "1c406d9a-cb28-4bfd-9ba4-08ba8aba243c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c39ccfba-8cb8-4f15-a1e1-7b33001eaefd": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "c39ccfba-8cb8-4f15-a1e1-7b33001eaefd": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "2dad4c4f-e16d-45d8-b51d-81e1fa1c2977": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "2dad4c4f-e16d-45d8-b51d-81e1fa1c2977": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b327c32f-d040-446f-a10b-b02453aed72e": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "b327c32f-d040-446f-a10b-b02453aed72e": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f872842d-f8c2-4e1e-817a-e0c91dcaa604": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "51977b30-3bd9-4b2f-8a2e-9ed912351350"  -> "f872842d-f8c2-4e1e-817a-e0c91dcaa604": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "e3e43e37-9c0e-4563-8375-a29f11810ac8": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "e3e43e37-9c0e-4563-8375-a29f11810ac8": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "eb222d9a-5ddd-44ac-a2a1-63b862d1755a": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "eb222d9a-5ddd-44ac-a2a1-63b862d1755a": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "9551fb5a-a38f-43ec-bea7-4e31971b6efd": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "9551fb5a-a38f-43ec-bea7-4e31971b6efd": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4e112b7d-64a4-4f57-b5f1-e9f7bceef84b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "4e112b7d-64a4-4f57-b5f1-e9f7bceef84b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "670665c0-92ea-4952-a4e5-6a6152c39e1c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "670665c0-92ea-4952-a4e5-6a6152c39e1c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "24bfc1d0-4b36-49a0-9a9f-853707e29cdd": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "24bfc1d0-4b36-49a0-9a9f-853707e29cdd": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "88eaf0ec-ee5b-4886-b31e-b9008c8eb760": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "88eaf0ec-ee5b-4886-b31e-b9008c8eb760": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "49b8ca0c-0e07-48d5-9d89-90db51ecea66": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "49b8ca0c-0e07-48d5-9d89-90db51ecea66": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "74671fea-e9fd-445a-acd0-62e56e3f492f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "74671fea-e9fd-445a-acd0-62e56e3f492f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c702dd5f-0333-4ad4-b092-ef1fd89b477d": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "389c61df-ce32-425c-b6e1-23004509728a"  -> "c702dd5f-0333-4ad4-b092-ef1fd89b477d": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "40739b39-21c2-48a5-ae8b-93667a33e674": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "40739b39-21c2-48a5-ae8b-93667a33e674": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "2b7f14f8-b95a-4b61-ab1f-e879e651275b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "2b7f14f8-b95a-4b61-ab1f-e879e651275b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7b6edc81-924f-40ec-9abb-d9639ecbb6e5": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "7b6edc81-924f-40ec-9abb-d9639ecbb6e5": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "eea278b4-09e9-49df-8a80-fddbaa170c16": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "eea278b4-09e9-49df-8a80-fddbaa170c16": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "465935c0-d3df-4903-8ef8-467a6d3e8e1c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "465935c0-d3df-4903-8ef8-467a6d3e8e1c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "2cfb80d7-10c2-46d1-b974-418ae7175a74": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "2cfb80d7-10c2-46d1-b974-418ae7175a74": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7bb2b8e6-fca6-4ce6-acb5-abea07f32c2a": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "7bb2b8e6-fca6-4ce6-acb5-abea07f32c2a": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a3a210e4-20b9-4123-bf2f-8af3de268149": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "a3a210e4-20b9-4123-bf2f-8af3de268149": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "9db00537-07e9-4ffd-9097-c6bf11f8313b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "9db00537-07e9-4ffd-9097-c6bf11f8313b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "ddedf0ee-a474-48d3-86bd-86cbf17fd5f7": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "df479220-1323-445c-b9a1-94c7a8f6dc86"  -> "ddedf0ee-a474-48d3-86bd-86cbf17fd5f7": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "369ba865-19a6-48a2-89a2-edc8cdbbfbea": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "369ba865-19a6-48a2-89a2-edc8cdbbfbea": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a0ff3282-1f6f-429e-a0d8-441ca44b9603": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "a0ff3282-1f6f-429e-a0d8-441ca44b9603": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "11bd2b2b-b588-4421-a3e9-175b76d88082": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "11bd2b2b-b588-4421-a3e9-175b76d88082": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a568eef1-d692-432f-97be-decd56c325a0": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "a568eef1-d692-432f-97be-decd56c325a0": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "535465ea-5fb1-437f-8c8a-60c069188a4c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "535465ea-5fb1-437f-8c8a-60c069188a4c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4ed1952e-f131-4df6-ae63-96ef803b763f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "4ed1952e-f131-4df6-ae63-96ef803b763f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4107238e-dc4b-409c-86dc-8da6c7f74948": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "4107238e-dc4b-409c-86dc-8da6c7f74948": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "33aaf2da-87f5-487a-ac2b-cbaa12fed5a2": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "33aaf2da-87f5-487a-ac2b-cbaa12fed5a2": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "32dc8692-b608-4107-96b6-b262892c24b8": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "32dc8692-b608-4107-96b6-b262892c24b8": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "44ef8eeb-14d8-4bf8-8fd8-64d82d362c7a": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "e5c6c530-fdd1-4d75-9cb0-215f41aebdfb"  -> "44ef8eeb-14d8-4bf8-8fd8-64d82d362c7a": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "9c1c7f98-cbdb-4d26-8a9f-72a501114e7d": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "9c1c7f98-cbdb-4d26-8a9f-72a501114e7d": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "692f859e-def8-4b27-9508-8a9b9efc1732": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "692f859e-def8-4b27-9508-8a9b9efc1732": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "202dcd78-f605-4ffa-b7ed-bf1d54657b44": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "202dcd78-f605-4ffa-b7ed-bf1d54657b44": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "cda381c2-79ea-4af0-b9bb-cf878f3e8353": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "cda381c2-79ea-4af0-b9bb-cf878f3e8353": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "d7ac445b-6feb-4b2a-b542-8532346d48fb": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "d7ac445b-6feb-4b2a-b542-8532346d48fb": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c0c4abfd-9ffb-41df-befa-3a8e409e9814": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "c0c4abfd-9ffb-41df-befa-3a8e409e9814": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "6a89dbe2-aed3-4061-8377-96963be5c5f1": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "6a89dbe2-aed3-4061-8377-96963be5c5f1": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "6aecc277-e0a0-41d1-911e-fc6fddda0745": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "6aecc277-e0a0-41d1-911e-fc6fddda0745": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "cd68c1fa-bb6a-452f-b631-1f1fa13ebb2f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "cd68c1fa-bb6a-452f-b631-1f1fa13ebb2f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "db606435-8529-45a8-9a4b-b67028b7f8d8": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "be4e4357-6d2d-42b9-86f2-a2cc98e236f4"  -> "db606435-8529-45a8-9a4b-b67028b7f8d8": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "0eafb5f0-4e71-483c-a449-59bbcc8066b2": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "0eafb5f0-4e71-483c-a449-59bbcc8066b2": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a1f9bd7a-e8c3-419e-94d4-c102fe4b5dea": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "a1f9bd7a-e8c3-419e-94d4-c102fe4b5dea": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "e4f2c9bb-5953-4006-b72b-fe2c20509167": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "e4f2c9bb-5953-4006-b72b-fe2c20509167": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f9a04b33-800a-47be-8049-69482f65cbfe": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "f9a04b33-800a-47be-8049-69482f65cbfe": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7cac7f21-150c-4ee5-9736-ccad9424f9fb": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "7cac7f21-150c-4ee5-9736-ccad9424f9fb": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "36075c77-fd84-47b7-a329-c9d1a516859f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "36075c77-fd84-47b7-a329-c9d1a516859f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "bffc43cf-6e46-4f49-a582-f3089b6ccef3": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "bffc43cf-6e46-4f49-a582-f3089b6ccef3": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "bc9b8b49-65ec-4a36-9ca8-edf2a2403743": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "bc9b8b49-65ec-4a36-9ca8-edf2a2403743": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "696b1c9b-45d7-4b9d-b48d-23c05ff5449b": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "696b1c9b-45d7-4b9d-b48d-23c05ff5449b": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f586ba1b-01c6-48f5-9f26-5a21a3369b16": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "b39f7ed0-cc5a-403a-a3d2-a53af333b5a1"  -> "f586ba1b-01c6-48f5-9f26-5a21a3369b16": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "96b7f45e-e883-4ff9-b682-d5a558cd0790": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "96b7f45e-e883-4ff9-b682-d5a558cd0790": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "3ec7ddf5-7832-4bea-a8f7-3c7e33f2d364": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "3ec7ddf5-7832-4bea-a8f7-3c7e33f2d364": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b9d98127-7f8e-4f8f-aae6-3e200149e966": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "b9d98127-7f8e-4f8f-aae6-3e200149e966": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a57d895d-ff54-4dfc-850d-1beb2240debd": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "a57d895d-ff54-4dfc-850d-1beb2240debd": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "1f24b3e6-b5e6-4a31-a131-b7d2a5026e05": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "1f24b3e6-b5e6-4a31-a131-b7d2a5026e05": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4574efc8-a62d-4701-a927-fc3b1585373a": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "4574efc8-a62d-4701-a927-fc3b1585373a": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "eef4211e-9838-4f02-90ac-5370cb0da7e9": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "eef4211e-9838-4f02-90ac-5370cb0da7e9": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "3f2dcace-8bb9-4fe4-b811-e8696c6cab17": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "3f2dcace-8bb9-4fe4-b811-e8696c6cab17": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c450c904-bf0b-4318-b288-4e0e31443c92": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "c450c904-bf0b-4318-b288-4e0e31443c92": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "5902a64f-307f-46fc-957e-b2ccf98c7755": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "fd05b7c1-c11e-4400-8d77-442abadb0af2"  -> "5902a64f-307f-46fc-957e-b2ccf98c7755": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "bd7efa61-6874-46e5-b212-8087c787be05": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "bd7efa61-6874-46e5-b212-8087c787be05": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "dfe5d575-173e-4b18-b67f-cf42c8438c24": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "dfe5d575-173e-4b18-b67f-cf42c8438c24": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "cc963864-edb5-44eb-b680-b4b11ab3def2": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "cc963864-edb5-44eb-b680-b4b11ab3def2": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "fa9ed7d0-6cdf-46f5-a5cb-6e12056b79ae": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "fa9ed7d0-6cdf-46f5-a5cb-6e12056b79ae": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "6ac7c8ae-33ef-4512-93e9-a33308fdbaf1": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "6ac7c8ae-33ef-4512-93e9-a33308fdbaf1": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "32164f38-3159-4fea-a31d-f3a6c3f96bb6": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "32164f38-3159-4fea-a31d-f3a6c3f96bb6": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "ddb14396-1c40-4471-9cb5-5effbac760fd": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "ddb14396-1c40-4471-9cb5-5effbac760fd": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "5e588dbb-3bf3-4610-b1fa-3c4eed751856": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "5e588dbb-3bf3-4610-b1fa-3c4eed751856": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "71251ccc-5f89-4d69-9378-46ff2ac2f18c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "71251ccc-5f89-4d69-9378-46ff2ac2f18c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "203a7208-99d6-4487-9b09-08aaf2d7d915": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "edcb58b3-4db3-4fe7-bef9-2f7e96b46df3"  -> "203a7208-99d6-4487-9b09-08aaf2d7d915": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c7fd6eb1-4e23-4570-b9ec-1221db69f53e": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "c7fd6eb1-4e23-4570-b9ec-1221db69f53e": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a90f7f44-60e9-4a98-be74-961c73847922": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "a90f7f44-60e9-4a98-be74-961c73847922": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4c1f513e-273f-4de7-985e-7c332bdf42c8": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "4c1f513e-273f-4de7-985e-7c332bdf42c8": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7a4667bc-bf87-47bb-8507-d017cad4cbd1": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "7a4667bc-bf87-47bb-8507-d017cad4cbd1": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f5fd449c-f737-42ad-8d66-87130e181015": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "f5fd449c-f737-42ad-8d66-87130e181015": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4c57fb6d-e1d2-4fbf-b359-9a91d6a81546": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "4c57fb6d-e1d2-4fbf-b359-9a91d6a81546": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "34d77560-3ccd-4b18-83b1-93b39a762d7f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "34d77560-3ccd-4b18-83b1-93b39a762d7f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "e9da8c3d-540e-4c26-bcc7-38e81c9d0aaf": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "e9da8c3d-540e-4c26-bcc7-38e81c9d0aaf": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f7f00fa2-3407-4c0c-a143-87341985d0f2": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "f7f00fa2-3407-4c0c-a143-87341985d0f2": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "66d9bf3c-5dde-4bfe-8beb-81b445ed3be9": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "f3495c99-9b96-4af2-bd6b-a8ecc8b49977"  -> "66d9bf3c-5dde-4bfe-8beb-81b445ed3be9": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "31b4ba1a-0fbc-4e7a-ae5f-d13e4aae8053": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "31b4ba1a-0fbc-4e7a-ae5f-d13e4aae8053": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "32fcfa9d-4ec0-4f3a-8a5c-380c56e19281": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "32fcfa9d-4ec0-4f3a-8a5c-380c56e19281": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "234335d1-4219-4f54-b400-36f80b19e90c": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "234335d1-4219-4f54-b400-36f80b19e90c": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4aab0edb-57bb-4cd9-a5ae-b38cae397ced": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "4aab0edb-57bb-4cd9-a5ae-b38cae397ced": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "3ac882cc-7b00-45b2-a073-aa4ac59d2a5f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "3ac882cc-7b00-45b2-a073-aa4ac59d2a5f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "45f5ca64-845a-4d3e-b7ab-45c4fb3ba674": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "45f5ca64-845a-4d3e-b7ab-45c4fb3ba674": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c5c3eb31-1518-4c28-8b3c-ee428cc800b2": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "c5c3eb31-1518-4c28-8b3c-ee428cc800b2": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "7e825c1f-d6dc-4038-a120-0b29c05cbfb1": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "7e825c1f-d6dc-4038-a120-0b29c05cbfb1": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "4e5ef421-2b6c-4cb4-aa88-63e69751c507": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "4e5ef421-2b6c-4cb4-aa88-63e69751c507": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "b4366803-2e57-43b7-93ea-be6eaaba937f": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "29927a73-737a-4c76-bd0c-89b6ca491a0a"  -> "b4366803-2e57-43b7-93ea-be6eaaba937f": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "c70b08c6-ca8b-4d2a-b9da-5887908a5a49": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "c70b08c6-ca8b-4d2a-b9da-5887908a5a49": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "89527b5c-9972-4d11-9d14-bc6b067e6756": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "89527b5c-9972-4d11-9d14-bc6b067e6756": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a6f156fc-f816-4d4b-b17e-11786e4dff03": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "a6f156fc-f816-4d4b-b17e-11786e4dff03": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "f19a0d14-8cc2-48ba-a76b-392726e0caa8": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "f19a0d14-8cc2-48ba-a76b-392726e0caa8": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "22fc78b0-a786-49d2-87e7-64dba23a1d11": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "22fc78b0-a786-49d2-87e7-64dba23a1d11": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "8c09a566-af96-492f-bd71-f88f02e8f164": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "8c09a566-af96-492f-bd71-f88f02e8f164": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "890a2464-37b3-4769-9001-a02db7a89f91": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "890a2464-37b3-4769-9001-a02db7a89f91": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "cde0f26b-37f3-4105-b0f9-56a95ab78db0": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "cde0f26b-37f3-4105-b0f9-56a95ab78db0": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "a6f56cee-b6b7-412a-ad3c-d20bd6a5c896": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "a6f56cee-b6b7-412a-ad3c-d20bd6a5c896": ("0", "");
INSERT VERTEX IF NOT EXISTS TOPIC() VALUES "587f0200-ce50-495a-bddc-9c8b8de16f2d": ();
INSERT EDGE IF NOT EXISTS ANCHOR_POINT_TOPIC(topicType, topicCategory) VALUES "15a59d7c-c0ce-4f6d-81bb-14f74739725f"  -> "587f0200-ce50-495a-bddc-9c8b8de16f2d": ("0", "");
