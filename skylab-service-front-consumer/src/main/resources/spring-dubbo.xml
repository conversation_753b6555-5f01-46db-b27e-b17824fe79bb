<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="epasConfig" class="com.iflytek.edu.epas.dubbo.config.EpasConfig">
        <!-- 在平台申请的appKey -->
        <property name="proxy" value="${epas.proxy}"/>
        <property name="appKey" value="${epas.appkey}"/>
        <property name="appSecret" value="${epas.appsecret}"/>
        <property name="addrServerUrl" value="${epas.addrserver.url}"/>
    </bean>



    <!--SkylabDiagnoseService-->
    <bean id="skylabDiagnoseService" class="com.iflytek.edu.epas.dubbo.common.EpasClientFactory" factory-method="createClient">
        <constructor-arg name="clazz" value="com.iflytek.skylab.core.contract.service.SkylabDiagnoseService"/>
        <constructor-arg name="serviceAppKey" value="${epas.appkey}"/>
    </bean>

    <!--SkylabRecommendService-->
    <bean id="skylabRecommendService" class="com.iflytek.edu.epas.dubbo.common.EpasClientFactory" factory-method="createClient">
        <constructor-arg name="clazz" value="com.iflytek.skylab.core.contract.service.SkylabRecommendService"/>
        <constructor-arg name="serviceAppKey" value="${epas.appkey}"/>
    </bean>

    <!--SkylabBehaviorService-->
    <bean id="skylabBehaviorService" class="com.iflytek.edu.epas.dubbo.common.EpasClientFactory" factory-method="createClient">
        <constructor-arg name="clazz" value="com.iflytek.skylab.core.contract.service.SkylabBehaviorService"/>
        <constructor-arg name="serviceAppKey" value="${epas.appkey}"/>
    </bean>

    <!--SkylabStandService-->
    <bean id="skylabStandService" class="com.iflytek.edu.epas.dubbo.common.EpasClientFactory"
          factory-method="createClient">
        <constructor-arg name="clazz"
                         value="com.iflytek.skylab.core.contract.service.SkylabStandService"/>
        <constructor-arg name="serviceAppKey" value="${epas.appkey}"/>
    </bean>

    <!--SkylabExtService-->
    <bean id="skylabExtService" class="com.iflytek.edu.epas.dubbo.common.EpasClientFactory"
          factory-method="createClient">
        <constructor-arg name="clazz"
                         value="com.iflytek.skylab.core.contract.service.SkylabExtService"/>
        <constructor-arg name="serviceAppKey" value="${epas.appkey}"/>
    </bean>

    <bean id="skylabClearMasteryService" class="com.iflytek.edu.epas.dubbo.common.EpasClientFactory"
          factory-method="createClient">
        <constructor-arg name="clazz"
                         value="com.iflytek.skylab.core.contract.service.SkylabClearMasteryService"/>
        <constructor-arg name="serviceAppKey" value="${epas.appkey}"/>
    </bean>

    <!--SkylabPrimaryMigrationService-->
    <bean id="skylabPrimaryMigrationService"
          class="com.iflytek.edu.epas.dubbo.common.EpasClientFactory" factory-method="createClient">
        <constructor-arg name="clazz"
                         value="com.iflytek.skylab.core.contract.service.SkylabPrimaryMigrationService"/>
        <constructor-arg name="serviceAppKey" value="${epas.appkey}"/>
    </bean>

</beans>
