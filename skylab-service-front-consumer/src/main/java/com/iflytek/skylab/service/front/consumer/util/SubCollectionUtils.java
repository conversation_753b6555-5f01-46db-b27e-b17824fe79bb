package com.iflytek.skylab.service.front.consumer.util;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-06
 * 分表工具
 */
@Slf4j
public class SubCollectionUtils {

    /**
     * c端默认画像表名
     */
    public static final String XXJ_USER_MASTERY_RECORD = "xxj_user_mastery_record";

    public static final int XXJ_USER_MASTERY_RECORD_SUB_COLLECTION_SIZE = 32;

    public static String getSubUserMasteryRecordCollectionName(String userId) {
        String index = "";
        // 生成32位小写md5值
        String encode = DigestUtil.md5Hex(userId);
        // 截取取后两位
        String subEncode = encode.substring(encode.length() - 2);
        // 分成32张表，转换成int后对31进行与操作
        index = String.valueOf(HexUtil.hexToInt(subEncode) & (XXJ_USER_MASTERY_RECORD_SUB_COLLECTION_SIZE - 1));
        return XXJ_USER_MASTERY_RECORD + index;
    }

    public static String getSubUserMasteryRecordCollectionName(String userId, BizCodeEnum bizCodeEnum) {
        return getSubUserMasteryRecordCollectionName(userId);
    }

    public static void main(String[] args) {
//        System.err.println(getSubUserMasteryRecordCollectionName("b3ac0b05-31b7-4837-a575-3fc4605572b7"));

//        String str = "07dd068c-121e-4bf2-a478-280b0596a987     01_09020101-002_001_001\n" +
//                "00c9c9d8-dadd-44ef-956c-81904c15bce4     30_08020213-002_005_002\n" +
//                "00df0587-2668-463a-988d-dbce0c5acea5     01_08020101-002_02_001\n" +
//                "00474e16-493c-4ffa-ae80-841fa45ca6f1     01_07020201-002_002_004\n" +
//                "006159e0-8318-487d-b329-e995770fd6ab     01_08020101-002_03_002\n" +
//                "ffebb42e-1787-4091-a0ca-44383f0f7583     19_07020207-003_003_002\n" +
//                "ffc3c8d4-dc29-4cec-88ea-a009f5052932     01_07020101-001_03_002_period20\n" +
//                "fdb63c2a-bba8-4a31-8545-04e558ded7b3     01_08020101-002_02_001_period10";
        String str = "ytxiong3     30_08020213-002_005";

        List<String> split = StrUtil.split(str, "\n");
        split.forEach(s -> {
            List<String> split1 = StrUtil.split(s, "     ");

            String format = String.format("db.getCollection(\"%s\").count({ $and: [ { \"user_id\": \"%s\" }, " +
                    "{ \"catalog_id\": \"%s\" } ] });", getSubUserMasteryRecordCollectionName(split1.get(0)), split1.get(0), split1.get(1));
            System.err.println(format);

            format = String.format("db.getCollection(\"%s\").find({ $and: [ { \"user_id\": \"%s\" }, " +
                    "{ \"catalog_id\": \"%s\" } ] }).limit(20).skip(0);", getSubUserMasteryRecordCollectionName(split1.get(0)), split1.get(0), split1.get(1));
            System.err.println(format);

            format = String.format("db.getCollection(\"%s\").find({ $and: [ { \"user_id\": \"%s\" }, " +
                    "{ \"catalog_id\": \"%s\" } ] }).limit(20).skip(20);", getSubUserMasteryRecordCollectionName(split1.get(0)), split1.get(0), split1.get(1));
            System.err.println(format);
            format = String.format("db.getCollection(\"%s\").find({ $and: [ { \"user_id\": \"%s\" }, " +
                    "{ \"catalog_id\": \"%s\" } ] }).limit(20).skip(40);", getSubUserMasteryRecordCollectionName(split1.get(0)), split1.get(0), split1.get(1));
            System.err.println(format);
            System.err.println();
        });

    }
}
