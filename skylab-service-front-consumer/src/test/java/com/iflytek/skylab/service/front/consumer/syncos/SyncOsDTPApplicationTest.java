package com.iflytek.skylab.service.front.consumer.syncos;

import cn.hutool.core.util.StrUtil;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.front.consumer.CheckResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 精准学os
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncOsDTPApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabStandService skylabStandService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    static SceneInfo sceneInfo = null;

    static HisStudyCodeEnum his = HisStudyCodeEnum.HOLIDAY_OS;
//    static HisStudyCodeEnum his = HisStudyCodeEnum.DTP_TRACE;

    static {
        sceneInfo = new SceneInfo();

//        sceneInfo.setGraphVersion("20240401_002");
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setHisStudyCode(his);

        sceneInfo.setAreaCode("340000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        sceneInfo.setPressCode("19");
        sceneInfo.setBookCode("19_07020207-003");
        sceneInfo.setCatalogCode("19_07020207-003_005_001");

//        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
//        sceneInfo.setLayerType("conventional");
    }

    /**
     * 溯源点推题
     */
    @Test
    public void DTP_SYD() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.DTP_SYD);

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("55601e80-e798-44bc-8933-daa878ffc056");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


    /**
     * 溯源点排序
     */
    @Test
    public void DTP_SEARCH_WEAK() {

        SkylabRequest<RecTraceNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.DTP_SEARCH_WEAK);
        sceneInfo.setCatalogCode("01_09020101-002_003_001");
        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        skylabRequest.setScene(sceneInfo);

        RecTraceNodeParam recTraceNodeParam = new RecTraceNodeParam();
        recTraceNodeParam.setNodeIds(Lists.newArrayList("c115c591-c683-4688-91b6-93c2f50cf384", "272a71db-f965-4a36-bfd2-3a08be1622e2", "0f5b4e23-ac8d-4fdc-b0d3-10f5845e147e"));
        recTraceNodeParam.setTargetNodeId("0f5b4e23-ac8d-4fdc-b0d3-10f5845e147e");

        skylabRequest.setPayload(recTraceNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


    /**
     * 目标点推题
     */
    @Test
    public void DTP_TARGET() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.DTP_TARGET);

        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_09020101-002");
        sceneInfo.setCatalogCode("01_09020101-002_003_001");

        sceneInfo.setLayerVersion("DTP_MAP_UPDATE");
//     * LayerVersionEnum.SYNC_LEARN_UPDATE.name() ：同步学新分层测评逻辑
//                * LayerVersionEnum.SENIOR_EXAM_UPDATE.name() :中考复习升级

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("0f5b4e23-ac8d-4fdc-b0d3-10f5845e147e");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


    @Test
    public void MASTERY_FETCH() {

        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
//
        sceneInfo.setBizAction(BizActionEnum.DTP_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setCatalogCode("01_07020101-001_02_002");
        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
        sceneInfo.setUserId("aaaa");
        skylabRequest.setScene(sceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();
        NodeInfo nodeInfo = new NodeInfo();
        nodeInfo.setNodeId("337873e6-95a3-4a60-b8ca-fe8d3ed99b56");
        nodeInfo.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        nodeInfo.setCatalogId("01_07020101-001_02_002");

        NodeInfo nodeInfo2 = new NodeInfo();
        nodeInfo2.setNodeId("34cc3cbb-2380-4f73-a462-99cff00ff7fa");
        nodeInfo2.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        nodeInfo2.setCatalogId("01_07020101-001_02_002");

        masterFetch5NodeParam.setNodeInfos(Arrays.asList(nodeInfo, nodeInfo2));

        skylabRequest.setPayload(masterFetch5NodeParam);

        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabRequest, skylabResponse);
    }


}
