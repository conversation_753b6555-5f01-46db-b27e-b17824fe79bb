package com.iflytek.skylab.service.front.consumer.syncos;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.service.front.consumer.CheckResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 寒暑专题
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncOsHolidayLinkApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabStandService skylabStandService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    static SceneInfo sceneInfo = null;

    static {
        sceneInfo = new SceneInfo();

//        sceneInfo.setGraphVersion("20240703_001");


        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);

        sceneInfo.setAreaCode("420000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        //课时
        sceneInfo.setCatalogCode("19_08020107-002_02_M-ff8e45e9_P-23cf3d60");
//        01_08020101-002_02_M-2ab72d82_P-10408039
        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        sceneInfo.setLayerType("conventional");

    }

    /**
     * 测评 -课时-单题
     */
    @Test
    public void sync_learn_rec_eval() {
        String roundId = UUID.randomUUID().toString();
        for (int i = 1; i <= 1; i++) {

            sceneInfo.setBizAction(BizActionEnum.HOLIDAY_OS_EVAL);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = UUID.randomUUID().toString();
            log.info("traceId 1: {}", traceId1);

            skylabRequest.setTraceId(traceId1);

            sceneInfo.setLayerVersion("highScoreAdvanced");
            //课时
            sceneInfo.setCatalogCode("01_07020101-001_03_M-28813d9e_P-94c2506e");

            List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
            sceneInfo.setPressCode(split.get(0));
            sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
            recEval4InOutParam.setTopicOrderNumber(i);
            recEval4InOutParam.setRoundId(roundId);

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

            skylabRequest.setPayload(recEval4InOutParam);

            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

            EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);
            if (skylabResponse.getPayload().isTerminationFlag()) {
                break;
            }
            //作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
        }
    }



    /**初中物理扩科
     * 测评 -课时-单题
     */
    @Test
    public void sync_learn_rec_eval0406() {
        String roundId = UUID.randomUUID().toString();
        for (int i = 1; i <= 1; i++) {

            sceneInfo.setBizAction(BizActionEnum.HOLIDAY_OS_EVAL);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = UUID.randomUUID().toString();
            log.info("traceId 1: {}", traceId1);

            skylabRequest.setTraceId(traceId1);

            sceneInfo.setSubjectCode("05");
            //学段
            sceneInfo.setPhaseCode("04");

            sceneInfo.setLayerVersion("conventional");
            //课时
            sceneInfo.setCatalogCode("341_08050313341-6477_08050313341-6477-192646_M-3c74f94a_P-08a07190");

            List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
            sceneInfo.setPressCode(split.get(0));
            sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
            recEval4InOutParam.setTopicOrderNumber(i);
            recEval4InOutParam.setRoundId(roundId);

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

            skylabRequest.setPayload(recEval4InOutParam);

            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

            EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);
            if (skylabResponse.getPayload().isTerminationFlag()) {
                break;
            }
            //作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
        }
    }

    /**
     * 同步习题入门测-5题-专题
     */
    /**
     * 同步练-5题-专题
     */
    @Test
    public void sync_learn_rec_eval_sync() {
        String roundId = UUID.randomUUID().toString();
        sceneInfo.setBizAction(BizActionEnum.KC_SYNC_LEARN_SYNC_PACK);

        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId1 = UUID.randomUUID().toString();
        log.info("traceId 1: {}", traceId1);

        skylabRequest.setTraceId(traceId1);

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.KC_REC_EVAL4SYNC);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId(roundId);

        recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));
        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
        CheckResultUtil.check(skylabRequest, skylabResponse, sceneInfo.getUserId());

        Assert.isTrue(skylabResponse.getPayload().getEvaluationItems().size() == 5, "同步习题入门测，题量不等于5");
        //批量作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
    }

    /**
     * 出门测-10题-专题
     */
    @Test
    public void sync_learn_rec_eval_out() {

        for (int i = 1; i <= 20; i++) {
            long start = System.currentTimeMillis();
            String roundId = UUID.randomUUID().toString();

            sceneInfo.setBizAction(BizActionEnum.HOLIDAY_OS_REVIEW);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = UUID.randomUUID().toString();
            log.info("traceId 1: {}", traceId1);

            skylabRequest.setTraceId(traceId1);

//        sceneInfo.setLayerVersion("highScoreAdvanced");
            //课时
            //课时
            sceneInfo.setCatalogCode("19_08020107-002_02_M-ff8e45e9");

            List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
            sceneInfo.setPressCode(split.get(0));
            sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4OUT);
            recEval4InOutParam.setTopicOrderNumber(1);
            recEval4InOutParam.setRoundId(roundId);

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

            skylabRequest.setPayload(recEval4InOutParam);

            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
//            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

            Assert.isTrue(skylabResponse.getPayload().getEvaluationItems().size() == 10, "出门测，题量不等于10");
            log.info("耗时：{}", System.currentTimeMillis() - start);
        }

    }

    @Test
    public void sync_learn_study_log() {
        long start = System.currentTimeMillis();
        List<StudyLogRecord> studyLogRecords = new ArrayList<>();

        sceneInfo.setBizAction(BizActionEnum.HOLIDAY_OS_EVAL);
        String roundId = UUID.randomUUID().toString();
        for (int i = 1; i <= 10; i++) {
            sceneInfo.setUserId(IdUtil.fastSimpleUUID());

            StudyLogRecord studyLogRecord = new StudyLogRecord();
            studyLogRecord.setNodeId("698ff5cb-fa27-44ba-b30d-b40a78b77fe0");
            studyLogRecord.setResNodeId("71ddd32e-73c9-4448-ba2d-332825d7bb6f");
            studyLogRecord.setRoundId(roundId);
            studyLogRecord.setRefTraceId(IdUtil.fastSimpleUUID());
            studyLogRecord.setRoundIndex("" + i);
            studyLogRecords.add(studyLogRecord);

        }
        sync_learn_study_log(sceneInfo, studyLogRecords);
        System.err.println("耗时：" + (System.currentTimeMillis() - start));
    }

    public void sync_learn_study_log(SceneInfo sceneInfo, List<StudyLogRecord> studyLogRecords) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        StudyLogParam studyLogParam = new StudyLogParam();

        for (StudyLogRecord studyLogRecord : studyLogRecords) {
            studyLogRecord.setFeedbackTime(new Date());
            studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
            studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
            studyLogRecord.setScore(5.0);
            studyLogRecord.setStandardScore(5.0);
            studyLogRecord.setTimeCost(650);
            studyLogRecord.setBookCode(sceneInfo.getBookCode());
            studyLogRecord.setCatalogCode(sceneInfo.getCatalogCode());
        }
        studyLogParam.setItems(studyLogRecords);
        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * 薄弱点推荐
     */
    @Test
    public void sync_learn_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.HOLIDAY_OS_SEARCH_WEAK);

        //课时或者专题
        sceneInfo.setCatalogCode("19_08020107-002_02_M-ff8e45e9");
//        sceneInfo.setCatalogCode("19_08020107-002_02_M-ff8e45e9_P-23cf3d60");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
        skylabRequest.setScene(sceneInfo);

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();

        recNodeParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

        skylabRequest.setPayload(recNodeParam);
        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }

    /**
     * 点推题
     */
    @Test
    public void sync_learn_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);


        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("698ff5cb-fa27-44ba-b30d-b40a78b77fe0");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);
        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }


    /**
     * 目录画像
     */
    @Test
    public void master_fetch_catalog() {

        SkylabRequest<MasterFetch5CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.HOLIDAY_OS);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        //课时或者专题
        sceneInfo.setCatalogCode(" 19_08020107-002_02_M-ff8e45e9_P-23cf3d60");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        skylabRequest.setScene(sceneInfo);

        //章节
        MasterFetch5CatalogParam masterFetch5CatalogParam = new MasterFetch5CatalogParam();
        masterFetch5CatalogParam.setCatalogIds(Arrays.asList(sceneInfo.getCatalogCode()));
        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT, NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
        skylabRequest.setPayload(masterFetch5CatalogParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


    /**
     * /**
     * 目录画像-压测 todo
     */
//    @Test
    public void master_fetch_catalog2() throws ExecutionException, InterruptedException {
        int numberOfTasks = 4;
        ExecutorService executorService = Executors.newFixedThreadPool(numberOfTasks);

        // 创建Future列表来收集异步执行的结果
        List<Future<Boolean>> futures = new ArrayList<>();

        for (int i = 0; i < 100000; i++) {
            int finalI = i;
            Future<Boolean> submit = executorService.submit(() -> {

                SkylabRequest<MasterDiagnoseParam> skylabRequest = new SkylabRequest<>();
                String a = "{\"allUpdate\":false,\"catalogIds\":[\"01_08020101-002_02_002\"],\"learnPointUpdate\":false,\"nodeIds\":[\"bce723cf-d4f7-4141-9ef9-a8afb79eeac4\"]" +
                        ",\"sceneInfo\":{\"areaCode\":\"010100\",\"bizAction\":\"SYNC_REC\",\"bizCode\":\"ZSY_XXJ\",\"bookCode\":\"01_08020101-002\",\"bookVersion\":\"01\",\"functionCode\":\"MASTER_DIAGNOSE\",\"graphVersion\":\"20240424_001\",\"phaseCode\":\"04\",\"studyCode\":\"SYNC_OS\",\"subjectCode\":\"02\",\"userId\":\"sync_os_use_pre_user_66\"},\"sessionInfo\":{\"strategyId\":\"ff9a3f68efd60b261d280315e5a59347\",\"traceId\":\"4ebaf6be-f21d-450e-b3fa-b93a9ba15e3f\"}}";

                SceneInfo sceneInfo1 = JSONUtil.parseObj(a).getJSONObject("sceneInfo").toBean(SceneInfo.class);
                MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();
                masterDiagnoseParam.setCatalogIds(Arrays.asList("01_08020101-002_02_002"));
//                masterDiagnoseParam.setCatalogIds(Arrays.asList("01_08020101-002_06"));
                masterDiagnoseParam.setNodeIds(Arrays.asList("bce723cf-d4f7-4141-9ef9-a8afb79eeac4"));
//                masterDiagnoseParam.setNodeIds(Arrays.asList("8e4af69d-5be2-48f2-bac6-badd22cf4fab"));

                masterDiagnoseParam.setAllUpdate(true);
                skylabRequest.setPayload(masterDiagnoseParam);
                sceneInfo1.setUserId("xiangzhang182_test");
//                sceneInfo1.setUserId(IdUtil.fastSimpleUUID());
                skylabRequest.setScene(sceneInfo1);
                skylabRequest.setTraceId(IdUtil.fastSimpleUUID());
                long bb = System.currentTimeMillis();
                SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
                CheckResultUtil.check(skylabResponse, sceneInfo1.getUserId());
                System.err.println(finalI + "耗时" + (System.currentTimeMillis() - bb));

                return true;
            });
            futures.add(submit);

        }
        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get(); // 等待任务完成，可以处理异常
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                e.printStackTrace();
            } finally {
                executorService.shutdown(); // 关闭线程池
            }
        }

    }

    /**
     * 点画像
     */
    @Test
    public void master_fetch_node() {

        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.HOLIDAY_OS);
        sceneInfo.setBizAction(BizActionEnum.NONE);

        skylabRequest.setScene(sceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();

        List<NodeInfo> nodeInfos = new ArrayList<>();
        nodeInfos.add(new NodeInfo("698ff5cb-fa27-44ba-b30d-b40a78b77fe0", NodeTypeEnum.ANCHOR_POINT, "19_08020107-002_02_M-ff8e45e9_P-23cf3d60", null));
        nodeInfos.add(new NodeInfo("9b435d8d-afd9-4a48-b860-91ed93ceaf40", NodeTypeEnum.ANCHOR_POINT, "19_08020107-002_02_M-ff8e45e9_P-23cf3d60", null));
        nodeInfos.add(new NodeInfo("b454ed07-1f77-4e28-92be-847e2edacf48", NodeTypeEnum.ANCHOR_POINT, "01_07020101-001_03_M-28813d9e_P-7fe6de31", null));

        masterFetch5NodeParam.setNodeInfos(nodeInfos);
        skylabRequest.setPayload(masterFetch5NodeParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }

    /**
     * 全书地图
     */
    @Test
    public void master_fetch_book() {

        SkylabRequest<MasterFetch5BookParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.HOLIDAY_OS);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setAreaCode("13000");
        skylabRequest.setScene(sceneInfo);

        MasterFetch5BookParam fetch5BookParam = new MasterFetch5BookParam();
        fetch5BookParam.setBookCode("19_08020107-002");
        fetch5BookParam.setCatalogIds(Arrays.asList("19_08020107-002_02"));
//        fetch5BookParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT));
//        fetch5BookParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.REVIEW_POINT));
        skylabRequest.setPayload(fetch5BookParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }

    /**
     * 分层特征
     */
    @Test
    public void acquire_feature() {

        SkylabRequest<AcquireFeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
        sceneInfo.setBizAction(BizActionEnum.HOLIDAY_OS_REVIEW);
        skylabRequest.setScene(sceneInfo);

        AcquireFeatureParam acquireFeatureParam = new AcquireFeatureParam();
        acquireFeatureParam.setFeatureNames(
                Arrays.asList(
                        "holiday_conventional_weak_predict_answer_time",
                        "holiday_thinkingExpansion_weak_predict_answer_time",
                        "holiday_highScoreAdvanced_weak_predict_answer_time"
                )
        );
        acquireFeatureParam.setParams(Arrays.asList("19_08020107-002_02_M-ff8e45e9_P-23cf3d60"));

        skylabRequest.setPayload(acquireFeatureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabRequest, skylabResponse);

    }

    @Test
    public void feature_catalog_predict_answer_time() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        //教材
        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();
        featureParamItem.setFeatureName("catalog_predict_answer_time");
        Map<String, String> params = new HashMap<>();
        params.put("catalog_code", "3dfcf4da-2825-4d54-b371-36996509d38c");
        params.put("study_code", "SENIOR_EXAM_1");
        params.put("user_level", "none");
        params.put("biz_code", "ZSY_XXJ");
        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));
        skylabRequest.setPayload(featureParam);

        params.put("user_level", "none");
        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

        params.put("user_level", "conventional");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

        params.put("user_level", "highScoreAdvanced");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

        params.put("user_level", "thinkingExpansion");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "conventional");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//          CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
//
//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "highScoreAdvanced");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//          CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
//
//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "thinkingExpansion");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//          CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    @Test
    public void feature_catalog_topic_count() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();
        featureParamItem.setFeatureName("catalog_topic_count");
        Map<String, String> params = new HashMap<>();
        params.put("catalog_code", "3dfcf4da-2825-4d54-b371-36996509d38c");
        params.put("study_code", "SENIOR_EXAM_1");
        params.put("user_level", "none");
        params.put("biz_code", "ZSY_XXJ");
        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));
        skylabRequest.setPayload(featureParam);

        params.put("user_level", "none");
        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

        params.put("user_level", "conventional");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

        params.put("user_level", "highScoreAdvanced");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

        params.put("user_level", "thinkingExpansion");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "conventional");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//          CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
//
//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "highScoreAdvanced");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//          CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
//
//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "thinkingExpansion");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//          CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }


}
