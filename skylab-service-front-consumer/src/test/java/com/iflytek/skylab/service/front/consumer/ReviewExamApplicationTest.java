package com.iflytek.skylab.service.front.consumer;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class ReviewExamApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;


    @Test
    public void review_exam_rec_eval(){

        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.MID_EXAM);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.EXAM_STAGE_DEALING);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v13");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("zhangxiang12");
        //教材
        sceneInfo.setPressCode("72");
        sceneInfo.setBookCode("72_09020154-002");
        sceneInfo.setCatalogCode("72_09020154-002_001");


        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4OUT);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recEval4InOutParam.setCatalogIds(Arrays.asList("72_09020154-002_001"));
        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void review_exam_rec_eval_limit(){
        SkylabRequest<RecEval4LimitParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.EXAM_UNIT_BASIS);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_09020101-002");
        sceneInfo.setCatalogCode("01_09020101-002_001");

        skylabRequest.setScene(sceneInfo);

        RecEval4LimitParam recEval4LimitParam = new RecEval4LimitParam();
        recEval4LimitParam.setTopicOrderNumber(1);
        recEval4LimitParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recEval4LimitParam.setCatalogIds(Arrays.asList("01_09020101-002_001"));
        skylabRequest.setPayload(recEval4LimitParam);

        SkylabResponse<RecEval4LimitResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4LimitResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }


    @Test
    public void review_exam_rec_node(){

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.EXAM_UNIT_SEARCH_WEAK);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xxj_hbase_es_pre_use_user_0");
        //教材
        sceneInfo.setPressCode("72");
        sceneInfo.setBookCode("72_08020154-001");
//        sceneInfo.setCatalogCode("72_08020154-001_05");

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("72_08020154-001_05"));

        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }


    @Test
    public void review_exam_rec_topic(){

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.EXAM_UNIT_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("ac935ac1-bbdf-4654-bddc-7a0a9e97beb3");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }


    @Test
    public void review_exam_master_fetch(){

        SkylabRequest<MasterFetch4CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();

        Map<String, Object> masterFetchExt  = new HashMap<>();
        masterFetchExt.put("evalNum",1);
        masterFetch4CatalogParam.setMasterFetchExt(masterFetchExt);
        masterFetch4CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_06_003"));
        skylabRequest.setPayload(masterFetch4CatalogParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void review_exam_study_log(){

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.MID_EXAM);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.EXAM_STAGE_DEALING);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v13");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("zhangxiang12");
        //教材
        sceneInfo.setPressCode("72");
        sceneInfo.setBookCode("72_09020154-002");
        sceneInfo.setCatalogCode("72_09020154-002_001");



        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("f16c7fc3-3fbf-4798-8b58-80c1e77a07fa");
        studyLogRecord.setNodeType(NodeTypeEnum.CHECK_POINT);
        studyLogRecord.setResNodeId("5810ddbd-b6c1-4d74-9a73-6dc9893974be");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("72_09020154-002");
        studyLogRecord.setCatalogCode("72_09020154-002_001");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }
}
