package com.iflytek.skylab.service.front.consumer;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.service.front.consumer.util.SubCollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/25 10:25
 */
@Slf4j
public class CheckResultUtil {

    public static void check(SkylabResponse skylabResponse, String userId, String method) {
        log.info("{}-出参：\r\n{}", method, JSONUtil.toJsonPrettyStr(skylabResponse));
        Assert.assertTrue(skylabResponse.getCode() == 0);

        String collectionName = SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId, null);
        log.info("collectionName = {}", collectionName);

        if (skylabResponse.getErrorNodes() != null) {
            for (int i = 0; i < skylabResponse.getErrorNodes().size(); i++) {
                JSONObject jsonObject = skylabResponse.getErrorNodes().getJSONObject(i);
                Assert.assertFalse("failover".equals(jsonObject.getString("code")) && "true".equals(jsonObject.getString("name")));
            }
        }
    }

    public static void check(SkylabResponse skylabResponse, String userId) {
        log.info("出参：\r\n" + JSONUtil.toJsonPrettyStr(skylabResponse));
        Assert.assertTrue(skylabResponse.getCode() == 0);

        String collectionName = SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId, null);
        log.info("collectionName = {}", collectionName);

        if (skylabResponse.getErrorNodes() != null) {
            for (int i = 0; i < skylabResponse.getErrorNodes().size(); i++) {
                JSONObject jsonObject = skylabResponse.getErrorNodes().getJSONObject(i);
                Assert.assertFalse("failover".equals(jsonObject.getString("code")) && "true".equals(jsonObject.getString("name")));
            }
        }
    }

    public static void check(SkylabRequest skylabRequest, SkylabResponse skylabResponse, String userId) {
        log.info("入参：\r\n" + JSONUtil.toJsonPrettyStr(skylabRequest));
        check(skylabResponse, userId);
    }


    public static void check(SkylabRequest skylabRequest, SkylabResponse skylabResponse) {
        log.info("入参：\r\n" + JSONUtil.toJsonPrettyStr(skylabRequest));
        check(skylabResponse, skylabRequest.getScene().getUserId());
    }
}
