package com.iflytek.skylab.service.front.consumer.syncos;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.service.front.consumer.CheckResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * 精准学os
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncOsAiDiagApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabStandService skylabStandService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    static SceneInfo sceneInfo = null;

    static {
        sceneInfo = new SceneInfo();

//        sceneInfo.setGraphVersion("20240401_002");
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setAreaCode("420000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        sceneInfo.setPressCode("01");
//        sceneInfo.setBookCode("01_08020101-002");
//        sceneInfo.setCatalogCode("01_08020101-002_06_002");

        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        sceneInfo.setLayerType("conventional");
    }

    /**
     * 测评 todo
     */
    @Test
    public void sync_learn_rec_eval() {

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);

        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId1 = UUID.randomUUID().toString();
        log.info("traceId 1: {}", traceId1);
        String roundId = UUID.randomUUID().toString();
        skylabRequest.setTraceId(traceId1);

        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId(roundId);

        recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

        EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);

        //作答
        sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), 1, traceId1, roundId);

//        String traceId2 = UUID.randomUUID().toString();
//        log.info("traceId 2: {}", traceId2);
//        recEval4InOutParam.setTopicOrderNumber(2);
//        skylabRequest.setTraceId(traceId2);
//        SkylabResponse<RecEval4InOutResult> skylabResponse2 = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
//        CheckResultUtil.check(skylabResponse2, sceneInfo.getUserId());
//
//        EvaluationItem evaluationItem2 = skylabResponse2.getPayload().getEvaluationItems().get(0);
//
//        //作答
//        sync_learn_study_log(sceneInfo, evaluationItem2.getNodeId(), evaluationItem2.getResNodeId(), 2, traceId2, roundId);

    }

    public void sync_learn_study_log(SceneInfo sceneInfo, String nodeId, String topic, int index, String refTraceId, String roundId) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId(nodeId);
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId(topic);
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);

        studyLogRecord.setRoundId(roundId);

        studyLogRecord.setRefTraceId(refTraceId);
        studyLogRecord.setRoundIndex("" + index);
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode(sceneInfo.getCatalogCode());
        studyLogRecord.setCorrectTraceId("c4016d98-2496-4808-ae28-4db2765cfa9a");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * 薄弱点推荐
     */
    @Test
    public void sync_learn_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();

        recNodeParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

        skylabRequest.setPayload(recNodeParam);
        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }

    /**
     * 点推题
     */
    @Test
    public void sync_learn_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);


        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("c4b2c778-ab97-4d2a-b747-cd6ccf01f7c8");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);
        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * 目录画像 todo
     */
    @Test
    public void master_fetch_catalog() {

        SkylabRequest<MasterFetch5CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

//        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.NONE);

        skylabRequest.setScene(sceneInfo);

        //章节
        MasterFetch5CatalogParam masterFetch5CatalogParam = new MasterFetch5CatalogParam();
        masterFetch5CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_06_001"));
        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT, NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
        skylabRequest.setPayload(masterFetch5CatalogParam);

//        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
//        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
//        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

        //章
        masterFetch5CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_02"));
        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.REVIEW_POINT));
//        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
        skylabRequest.setPayload(masterFetch5CatalogParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse2 = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse2, sceneInfo.getUserId());


        //章
//        masterFetch5CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_06"));
        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT));
//        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT, NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
//        skylabRequest.setPayload(masterFetch5CatalogParam);
//
//        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
//        SkylabResponse<MasterFetchResult> skylabResponse3 = diagnoseService.diagnose(skylabRequest);
//        CheckResultUtil.check(skylabResponse3, sceneInfo.getUserId());

    }

    /**
     * 点画像
     */
    @Test
    public void master_fetch_node() {

        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.NONE);

        skylabRequest.setScene(sceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();

        List<NodeInfo> nodeInfos = new ArrayList<>();
        nodeInfos.add(new NodeInfo("34e87921-cfcf-44aa-b1f6-49fc703cfcd7", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_06_003", null));
        nodeInfos.add(new NodeInfo("7cffb5c8-e7d4-4998-bc4e-4525f44c61f0", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_02_002", null));
        nodeInfos.add(new NodeInfo("090b7cb1-a052-4781-91a4-6a55cba94bba", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_04_002", null));
        nodeInfos.add(new NodeInfo("0a488b60-0db3-4bd5-8ff7-21e40ac627e5", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_02_001", null));
        nodeInfos.add(new NodeInfo("67bec164-6206-47ac-afef-639b7891302d", NodeTypeEnum.ANCHOR_POINT, "42_08020126-001_05_004", null));

        masterFetch5NodeParam.setNodeInfos(nodeInfos);
        skylabRequest.setPayload(masterFetch5NodeParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }

    @Test
    public void sync_learn_study_log() {
//        sync_learn_master_fetch();
        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);


        sceneInfo.setBizAction(BizActionEnum.NONE);

        skylabRequest.setScene(sceneInfo);
//        sceneInfo.setBookCode("01_08020101-002");
//        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord1 = new StudyLogRecord();
        studyLogRecord1.setFeedbackTime(new Date());
        studyLogRecord1.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        studyLogRecord1.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord1.setResNodeId("5aaf5a95-bffc-4cc1-a012-451313bcd581");
        studyLogRecord1.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord1.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord1.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord1.setRoundIndex("1");
        studyLogRecord1.setScore(5.0);
        studyLogRecord1.setStandardScore(5.0);
        studyLogRecord1.setTimeCost(650);
        studyLogRecord1.setBookCode("01_08020101-002");
        studyLogRecord1.setCatalogCode("01_08020101-002_06_003");

        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("67bec164-6206-47ac-afef-639b7891302d");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("bf0cea15-ae0b-4877-a9fb-17feefdbe4db");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("3423423432432462d47f0e50b");
        studyLogRecord.setRefTraceId("3423423424");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("42_08020126-001");
        studyLogRecord.setCatalogCode("42_08020126-001_05_004");

        studyLogParam.setItems(Arrays.asList(studyLogRecord1, studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }

    /**
     * 全书地图
     */
    @Test
    public void master_fetch_book() {

        SkylabRequest<MasterFetch5BookParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
        sceneInfo.setUserId("bdfb88da-a355-405d-a931-5f4e885a3c24");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setAreaCode("320000");
        skylabRequest.setScene(sceneInfo);

        MasterFetch5BookParam fetch5BookParam = new MasterFetch5BookParam();
        fetch5BookParam.setBookCode("01_08020101-002");
        fetch5BookParam.setCatalogIds(Arrays.asList("01_08020101-002_02"));
//        fetch5BookParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT));
        fetch5BookParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.REVIEW_POINT));
        skylabRequest.setPayload(fetch5BookParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


    /**
     * 学习轨迹
     */
    @Test
    public void path() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("aad78d71-7a63-4621-8f33-b10a82f2fd64");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();
        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

        featureParamItem.setFeatureName("user_node_mastery_path");
        Map<String, String> params = new HashMap<>();
        params.put("node_id", "1a3c210b-b592-4286-a5fa-0c18feec5d87");
//        params.put("user_id", "aad78d71-7a63-4621-8f33-b10a82f2fd64");
//        params.put("biz_code", "ZSY_XXJ");

        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));
        featureParam.setSimpleMode(true);
        skylabRequest.setPayload(featureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


    //    ai诊断题包
    @Test
    public void ai_diag_rec_topic_pack() {
        SkylabRequest<RecTopicPackParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("340100");
        aiDiagSceneInfo.setBizAction(BizActionEnum.AI_DIAGNOSIS_REC_PACK);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        aiDiagSceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        aiDiagSceneInfo.setHisStudyCode(HisStudyCodeEnum.AI_DIAG);
        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
        //教材
        aiDiagSceneInfo.setPressCode("01");
        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\",\n" +
                "\t\t\t\"ac935ac1-bbdf-4654-bddc-7a0a9e97beb3\":\"01_08020101-002_06_003\",\n" +
                "\t\t\t\"f6ecc8a2-00d7-4c5f-be7e-8a38245823ff\":\"01_07020101-001_02_001\"\n" +
                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        RecTopicPackParam recTopicPackParam = new RecTopicPackParam();
        recTopicPackParam.setNodeIds(Arrays.asList("34e87921-cfcf-44aa-b1f6-49fc703cfcd7", "ac935ac1-bbdf-4654-bddc-7a0a9e97beb3", "f6ecc8a2-00d7-4c5f-be7e-8a38245823ff"));
        skylabRequest.setPayload(recTopicPackParam);
        SkylabResponse<RecTopicPackResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicPackResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }


}
