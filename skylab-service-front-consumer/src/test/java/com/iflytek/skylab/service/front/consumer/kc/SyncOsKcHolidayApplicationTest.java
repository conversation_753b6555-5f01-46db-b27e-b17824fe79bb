package com.iflytek.skylab.service.front.consumer.kc;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.service.front.consumer.CheckResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 寒暑专题
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncOsKcHolidayApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabStandService skylabStandService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    static SceneInfo sceneInfo = null;

    static {
        sceneInfo = new SceneInfo();

        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.HOLIDAY_OS);

        sceneInfo.setAreaCode("420000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("111xiangzhang182_test");
        //教材
        //课时
//        sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188912_M-08492b9b");
        sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188912_M-08492b9b");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        sceneInfo.setLayerType("conventional");

    }

    /**
     * 寒暑-同步习题题包
     */
    @Test
    public void KC_HOLIDAY_OS_SYNC() {
        String roundId = UUID.randomUUID().toString();
        for (int i = 1; i <= 1; i++) {

            sceneInfo.setBizAction(BizActionEnum.KC_HOLIDAY_OS_REVIEW);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = UUID.randomUUID().toString();
            log.info("traceId 1: {}", traceId1);

            skylabRequest.setTraceId(traceId1);

//            sceneInfo.setLayerVersion("highScoreAdvanced");
//            List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
//            sceneInfo.setPressCode(split.get(0));
//            sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.KC_REC_EVAL4SYNC);
            recEval4InOutParam.setTopicOrderNumber(i);
            recEval4InOutParam.setRoundId(roundId);

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

            skylabRequest.setPayload(recEval4InOutParam);
            log.info("KC_HOLIDAY_OS_SYNC-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_HOLIDAY_OS_SYNC");

            EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);
            if (skylabResponse.getPayload().isTerminationFlag()) {
                break;
            }
            //作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
        }
    }
    /**
     * 寒暑-出门测题包
     * TODO  ERROR
     */
    @Test
    public void KC_HOLIDAY_OS_REVIEW() {
        String roundId = UUID.randomUUID().toString();
        for (int i = 1; i <= 1; i++) {

            sceneInfo.setBizAction(BizActionEnum.KC_HOLIDAY_OS_REVIEW);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = UUID.randomUUID().toString();
            log.info("traceId 1: {}", traceId1);

            skylabRequest.setTraceId(traceId1);

//            sceneInfo.setLayerVersion("highScoreAdvanced");
//            List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
//            sceneInfo.setPressCode(split.get(0));
//            sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.KC_REC_EVAL4OUT);
            recEval4InOutParam.setTopicOrderNumber(i);
            recEval4InOutParam.setRoundId(roundId);

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

            skylabRequest.setPayload(recEval4InOutParam);
            log.info("KC_HOLIDAY_OS_REVIEW-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_HOLIDAY_OS_REVIEW");

            EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);
            if (skylabResponse.getPayload().isTerminationFlag()) {
                break;
            }
            //作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
        }
    }
    /**
     * 寒暑-所有点找弱项
     */
    @Test
    public void KC_HOLIDAY_OS_EVAL() {
        String roundId = UUID.randomUUID().toString();
        for (int i = 1; i <= 1; i++) {

            sceneInfo.setBizAction(BizActionEnum.KC_HOLIDAY_OS_EVAL);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = UUID.randomUUID().toString();
            log.info("traceId 1: {}", traceId1);

            skylabRequest.setTraceId(traceId1);

//            sceneInfo.setLayerVersion("highScoreAdvanced");
            //课时

            sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188912_M-08492b9b_P-d7f66f81");
            List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
            sceneInfo.setPressCode(split.get(0));
            sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.KC_REC_EVAL4IN);
            recEval4InOutParam.setTopicOrderNumber(i);
            recEval4InOutParam.setRoundId(roundId);

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

            skylabRequest.setPayload(recEval4InOutParam);
            log.info("KC_HOLIDAY_OS_EVAL-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_HOLIDAY_OS_EVAL");

            EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);
            if (skylabResponse.getPayload().isTerminationFlag()) {
                break;
            }
            //作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
        }
    }



    /**
     * 寒暑-仅灰点找弱项
     */
    @Test
    public void KC_HOLIDAY_OS_EVAL_CTN() {
        for (int i = 1; i <= 1; i++) {
            String roundId = UUID.randomUUID().toString();

            sceneInfo.setBizAction(BizActionEnum.KC_HOLIDAY_OS_EVAL_CTN);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = UUID.randomUUID().toString();
            log.info("traceId 1: {}", traceId1);

            skylabRequest.setTraceId(traceId1);

            sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188912_M-08492b9b_P-d7f66f81");

            List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
            sceneInfo.setPressCode(split.get(0));
            sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.KC_REC_EVAL4CTN);
            recEval4InOutParam.setTopicOrderNumber(1);
            recEval4InOutParam.setRoundId(roundId);

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

            skylabRequest.setPayload(recEval4InOutParam);

            skylabRequest.setPayload(recEval4InOutParam);
            log.info("KC_HOLIDAY_OS_EVAL_CTN-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_HOLIDAY_OS_EVAL_CTN");

//            Assert.isTrue(skylabResponse.getPayload().getEvaluationItems().size() == 5, "同步习题入门测，题量不等于5");
        }
        //批量作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
    }

    /**
     * 寒暑-看板点排序
     */
    @Test
    public void KC_HOLIDAY_OS_SEARCH_WEAK() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.KC_HOLIDAY_OS_SEARCH_WEAK);

        //课时或者专题
        sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188912_M-08492b9b_P-c0c4048a");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
        skylabRequest.setScene(sceneInfo);

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setRecNodeEnum(RecNodeEnum.KC_REC_NODE);
        recNodeParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

        skylabRequest.setPayload(recNodeParam);
        log.info("KC_HOLIDAY_OS_SEARCH_WEAK-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_HOLIDAY_OS_SEARCH_WEAK");

    }
    /**
     * 寒暑-寒暑-自主学点排序
     */
    @Test
    public void KC_HOLIDAY_OS_SEARCH_WEAK_VARIANT() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.KC_HOLIDAY_OS_SEARCH_WEAK_VARIANT);

        //课时或者专题
//        sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188912_M-08492b9b_P-c0c4048a");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));
        skylabRequest.setScene(sceneInfo);

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
//        KC_REC_NODE_VARIANT
        recNodeParam.setRecNodeEnum(RecNodeEnum.KC_REC_NODE_VARIANT);
        recNodeParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

        skylabRequest.setPayload(recNodeParam);
        log.info("KC_HOLIDAY_OS_SEARCH_WEAK_VARIANT-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_HOLIDAY_OS_SEARCH_WEAK_VARIANT");

    }
    /**
     * 寒暑-自主学点推题
     */
    @Test
    public void KC_HOLIDAY_OS_REC() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.KC_HOLIDAY_OS_REC);

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("ed2250f2-5ffd-436b-980a-e37559dc1857");
        recTopicParam.setTopicOrderNumber(1);
        recTopicParam.setRecTopicEnum(RecTopicEnum.KC_REC_TOPIC);
        skylabRequest.setPayload(recTopicParam);
        log.info("KC_HOLIDAY_OS_REC-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_HOLIDAY_OS_REC");
    }

    @Test
    public void sync_learn_study_log() {
        long start = System.currentTimeMillis();
        List<StudyLogRecord> studyLogRecords = new ArrayList<>();

        sceneInfo.setBizAction(BizActionEnum.KC_HOLIDAY_OS_EVAL);
        String roundId = UUID.randomUUID().toString();
        for (int i = 1; i <= 1; i++) {
//            sceneInfo.setUserId(IdUtil.fastSimpleUUID());

            StudyLogRecord studyLogRecord = new StudyLogRecord();
            studyLogRecord.setNodeId("ed2250f2-5ffd-436b-980a-e37559dc1857");
            studyLogRecord.setResNodeId("7afa74eb-8859-40dd-b6bf-2a0bfd87edae");
            studyLogRecord.setRoundId(roundId);
            studyLogRecord.setRefTraceId(IdUtil.fastSimpleUUID());
            studyLogRecord.setRoundIndex("" + i);
            studyLogRecords.add(studyLogRecord);

        }
        sync_learn_study_log(sceneInfo, studyLogRecords);
        System.err.println("耗时：" + (System.currentTimeMillis() - start));
    }

    public void sync_learn_study_log(SceneInfo sceneInfo, List<StudyLogRecord> studyLogRecords) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        StudyLogParam studyLogParam = new StudyLogParam();
        studyLogParam.setStudyLogFuncEnum( StudyLogFuncEnum.KC_STUDY_LOG);

        for (StudyLogRecord studyLogRecord : studyLogRecords) {
            studyLogRecord.setFeedbackTime(new Date());
            studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
            studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
            studyLogRecord.setScore(5.0);
            studyLogRecord.setStandardScore(5.0);
            studyLogRecord.setTimeCost(650);
            studyLogRecord.setBookCode(sceneInfo.getBookCode());
            studyLogRecord.setCatalogCode(sceneInfo.getCatalogCode());
        }
        studyLogParam.setItems(studyLogRecords);
        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        skylabRequest.setPayload(studyLogParam);
        log.info("sync_learn_study_log-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "sync_learn_study_log");
    }

    /**
     * 目录画像
     */
    @Test
    public void master_fetch_catalog() {

        SkylabRequest<MasterFetch5CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

//        sceneInfo.setHisStudyCode(HisStudyCodeEnum.HOLIDAY_OS);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        //课时或者专题
//        sceneInfo.setCatalogCode(" 19_08020107-002_02_M-ff8e45e9_P-23cf3d60");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        skylabRequest.setScene(sceneInfo);

        //章节
        MasterFetch5CatalogParam masterFetch5CatalogParam = new MasterFetch5CatalogParam();
        masterFetch5CatalogParam.setCatalogIds(Arrays.asList(sceneInfo.getCatalogCode()));
//        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT, NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
        skylabRequest.setPayload(masterFetch5CatalogParam);

        log.info("master_fetch_catalog-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "master_fetch_catalog");

    }

//
//    /**
//     * /**
//     * 目录画像-压测 todo
//     */
////    @Test
//    public void master_fetch_catalog2() throws ExecutionException, InterruptedException {
//        int numberOfTasks = 4;
//        ExecutorService executorService = Executors.newFixedThreadPool(numberOfTasks);
//
//        // 创建Future列表来收集异步执行的结果
//        List<Future<Boolean>> futures = new ArrayList<>();
//
//        for (int i = 0; i < 100000; i++) {
//            int finalI = i;
//            Future<Boolean> submit = executorService.submit(() -> {
//
//                SkylabRequest<MasterDiagnoseParam> skylabRequest = new SkylabRequest<>();
//                String a = "{\"allUpdate\":false,\"catalogIds\":[\"01_08020101-002_02_002\"],\"learnPointUpdate\":false,\"nodeIds\":[\"bce723cf-d4f7-4141-9ef9-a8afb79eeac4\"]" +
//                        ",\"sceneInfo\":{\"areaCode\":\"010100\",\"bizAction\":\"SYNC_REC\",\"bizCode\":\"ZSY_XXJ\",\"bookCode\":\"01_08020101-002\",\"bookVersion\":\"01\",\"functionCode\":\"MASTER_DIAGNOSE\",\"graphVersion\":\"20240424_001\",\"phaseCode\":\"04\",\"studyCode\":\"SYNC_OS\",\"subjectCode\":\"02\",\"userId\":\"sync_os_use_pre_user_66\"},\"sessionInfo\":{\"strategyId\":\"ff9a3f68efd60b261d280315e5a59347\",\"traceId\":\"4ebaf6be-f21d-450e-b3fa-b93a9ba15e3f\"}}";
//
//                SceneInfo sceneInfo1 = JSONUtil.parseObj(a).getJSONObject("sceneInfo").toBean(SceneInfo.class);
//                MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();
//                masterDiagnoseParam.setCatalogIds(Arrays.asList("01_08020101-002_02_002"));
////                masterDiagnoseParam.setCatalogIds(Arrays.asList("01_08020101-002_06"));
//                masterDiagnoseParam.setNodeIds(Arrays.asList("bce723cf-d4f7-4141-9ef9-a8afb79eeac4"));
////                masterDiagnoseParam.setNodeIds(Arrays.asList("8e4af69d-5be2-48f2-bac6-badd22cf4fab"));
//
//                masterDiagnoseParam.setAllUpdate(true);
//                skylabRequest.setPayload(masterDiagnoseParam);
//                sceneInfo1.setUserId("xiangzhang182_test");
////                sceneInfo1.setUserId(IdUtil.fastSimpleUUID());
//                skylabRequest.setScene(sceneInfo1);
//                skylabRequest.setTraceId(IdUtil.fastSimpleUUID());
//                long bb = System.currentTimeMillis();
//                SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
//                CheckResultUtil.check(skylabResponse, sceneInfo1.getUserId());
//                System.err.println(finalI + "耗时" + (System.currentTimeMillis() - bb));
//
//                return true;
//            });
//            futures.add(submit);
//
//        }
//        // 等待所有任务完成
//        for (Future<?> future : futures) {
//            try {
//                future.get(); // 等待任务完成，可以处理异常
//            } catch (InterruptedException | ExecutionException e) {
//                Thread.currentThread().interrupt();
//                e.printStackTrace();
//            } finally {
//                executorService.shutdown(); // 关闭线程池
//            }
//        }
//
//    }

    /**
     * 点画像
     */
    @Test
    public void master_fetch_node() {

        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
        sceneInfo.setBizAction(BizActionEnum.NONE);

        skylabRequest.setScene(sceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();

        List<NodeInfo> nodeInfos = new ArrayList<>();
        nodeInfos.add(new NodeInfo("ed2250f2-5ffd-436b-980a-e37559dc1857", NodeTypeEnum.ANCHOR_POINT, sceneInfo.getCatalogCode(), null));
//        nodeInfos.add(new NodeInfo("9b435d8d-afd9-4a48-b860-91ed93ceaf40", NodeTypeEnum.ANCHOR_POINT, "19_08020107-002_02_M-ff8e45e9_P-23cf3d60", null));
//        nodeInfos.add(new NodeInfo("b454ed07-1f77-4e28-92be-847e2edacf48", NodeTypeEnum.ANCHOR_POINT, "01_07020101-001_03_M-28813d9e_P-7fe6de31", null));

        masterFetch5NodeParam.setNodeInfos(nodeInfos);
        skylabRequest.setPayload(masterFetch5NodeParam);

        log.info("master_fetch_node-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "master_fetch_node");

    }


}
