package com.iflytek.skylab.service.front.consumer;

import cn.hutool.json.JSONUtil;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.contract.service.SkylabClearMasteryService;
import com.iflytek.skylab.core.data.ClearMasteryParam;
import com.iflytek.skylab.core.data.ClearMasteryResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SkylabClearMasteryApplicationTest {
    @Autowired
    private SkylabClearMasteryService clearMasteryService;


    @Test
    public void clear() throws ExecutionException, InterruptedException {


        SkylabRequest<ClearMasteryParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("grayUserId");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);
        ClearMasteryParam queryParam = new ClearMasteryParam();

        skylabRequest.setPayload(queryParam);

        SkylabResponse<ClearMasteryResult> clearMastery = clearMasteryService.clearMastery(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(clearMastery));

    }

}
