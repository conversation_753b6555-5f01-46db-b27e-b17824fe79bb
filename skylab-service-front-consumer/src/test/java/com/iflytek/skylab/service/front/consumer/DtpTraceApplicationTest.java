package com.iflytek.skylab.service.front.consumer;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class DtpTraceApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    /**
     * 溯源点排序
     */
    @Test
    public void DTP_SEARCH_WEAK() {

        SkylabRequest<RecTraceNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.DTP_TRACE);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.DTP_SEARCH_WEAK);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-n");
        //教材
        sceneInfo.setPressCode("01");
//        sceneInfo.setBookCode("01_08020101-002");
//        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        RecTraceNodeParam recTraceNodeParam = new RecTraceNodeParam();
        recTraceNodeParam.setNodeIds(Lists.newArrayList("c115c591-c683-4688-91b6-93c2f50cf384", "272a71db-f965-4a36-bfd2-3a08be1622e2", "0f5b4e23-ac8d-4fdc-b0d3-10f5845e147e"));
        recTraceNodeParam.setTargetNodeId("0f5b4e23-ac8d-4fdc-b0d3-10f5845e147e");

//        recTraceNodeParam.getNodeIds().add(recTraceNodeParam.getTargetNodeId());

        skylabRequest.setPayload(recTraceNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }


    /**
     * 溯源点推题
     */
    @Test
    public void DTP_SYD() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.DTP_TRACE);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.DTP_SYD);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-n");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_09020101-002");
        sceneInfo.setCatalogCode("01_09020101-002_003_001");
        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("c115c591-c683-4688-91b6-93c2f50cf384");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    /**
     * 目标点推题
     */
    @Test
    public void DTP_TARGET() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.DTP_TRACE);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.DTP_TARGET);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-n");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_09020101-002");
        sceneInfo.setCatalogCode("01_09020101-002_003_001");



//        StudyCode = DTP_MAP

        sceneInfo.setLayerVersion("DTP_MAP_UPDATE");
//     * LayerVersionEnum.SYNC_LEARN_UPDATE.name() ：同步学新分层测评逻辑
//                * LayerVersionEnum.SENIOR_EXAM_UPDATE.name() :中考复习升级


        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("0f5b4e23-ac8d-4fdc-b0d3-10f5845e147e");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }


    @Test
    public void MASTERY_FETCH() {

        SkylabRequest<MasterFetch4NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.DTP_TRACE);
        sceneInfo.setAreaCode("110000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("20240124_001");
        sceneInfo.setGradeCode("08");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("stage_update_new_pre_user_45");
        //教材
        sceneInfo.setPressCode("94");

        skylabRequest.setScene(sceneInfo);

        MasterFetch4NodeParam masterFetch4NodeParam = new MasterFetch4NodeParam();
        masterFetch4NodeParam.setNodeIds(Arrays.asList(
                "528c2e6f-415f-4e1a-a71a-dd058b3a3668"));
        skylabRequest.setPayload(masterFetch4NodeParam);
        log.info(JSON.toJSONString(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void sync_learn_study_log() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.DTP_TRACE);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.DTP_TARGET);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-n");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_09020101-002");
        sceneInfo.setCatalogCode("01_09020101-002_003_001");

        skylabRequest.setScene(sceneInfo);


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("0f5b4e23-ac8d-4fdc-b0d3-10f5845e147e");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("8665d8ad-6a0e-4265-9390-4b8c204acdd5");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        sceneInfo.setBookCode("01_09020101-002");
        sceneInfo.setCatalogCode("01_09020101-002_003_001");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }

}
