package com.iflytek.skylab.service.front.consumer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.core.domain.extra.AiReviewSceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class ConsumerApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;



    //理科单品-测评
    @Test
    public void AI_test4(){
        String test = "{\n" +
                "\t\"payload\": {\n" +
                "\t\t\"catalogIds\": [\"273_1902050101-2316_1902050101-2316-79587_1902050101-2316-79588xyxtb01\"],\n" +
                "\t\t\"recEvalEnum\": \"REC_EVAL4IN\",\n" +
                "\t\t\"roundId\": \"tsp-engine-test7#273_1902050101-2316_1902050101-2316-79587_1902050101-2316-79588xyxtb01#1\",\n" +
                "\t\t\"topicOrderNumber\": 1\n" +
                "\t},\n" +
                "\t\"scene\": {\n" +
                "\t\t\"areaCode\": \"310000\",\n" +
                "\t\t\"bizAction\": \"AI_REVIEW_EVAL\",\n" +
                "\t\t\"bizCode\": \"ZSY_XXJ\",\n" +
                "        \"studentLevel\": \"good\",\n" +
                "        \"examType\":\"week\",\n" +
                "        \"planDiff\":\"base\",\n" +
                "        \"pointType\":\"CHECK_POINT\",\n" +
                "        \"nodeCatalogMap\":{\n" +
                "            \"xy01c91e707e-cc9e-4787-a5bf-7035dd2b3c6d\":\"273_1902050101-2316_1902050101-2316-79587_1902050101-2316-79588xyxtb01\"\n" +
                "        }\n" +
                "\t\t\"gradeCode\": \"08\",\n" +
                "\t\t\"graphVersion\": \"v10\",\n" +
                "\t\t\"phaseCode\": \"05\",\n" +
                "\t\t\"pressCode\": \"273\",\n" +
                "\t\t\"studyCode\": \"AI_REVIEW\",\n" +
                "\t\t\"subjectCode\": \"02\",\n" +
                "\t\t\"test\": false,\n" +
                "\t\t\"userId\": \"tsp-engine-test7\"\n" +
                "\t},\n" +
                "\t\"traceId\": \"9d5c26d9-2266-4c23-9828-480739c6c688\"\n" +
                "}";

        SkylabRequest<RecEval4InOutParam> skylabRequest = JSON.parseObject(test,new TypeReference<SkylabRequest<RecEval4InOutParam>>(){});
        AiReviewSceneInfo aiReviewSceneInfo = new AiReviewSceneInfo();
        BeanUtils.copyProperties(skylabRequest.getScene(), aiReviewSceneInfo);
        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "            \"xy01c91e707e-cc9e-4787-a5bf-7035dd2b3c6d\":\"273_1902050101-2316_1902050101-2316-79587_1902050101-2316-79588xyxtb01\"\n" +
                "        }";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>(){});

        aiReviewSceneInfo.setNodeCatalogMap(nodeCatalogMap);

        aiReviewSceneInfo.setStudentLevel(StudentLevelEnum.GOOD);
        aiReviewSceneInfo.setPointType(NodeTypeEnum.CHECK_POINT);
        aiReviewSceneInfo.setExamType(ExamType.WEEK_EXAM);
        // aiReviewSceneInfo.setPlanDiff("base");

        skylabRequest.setScene(aiReviewSceneInfo);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest,RecEval4InOutResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    //理科单品-题包推荐
    @Test
    public void AI_test5() {
        String test = "{\n" +
                "\t\"payload\": {\n" +
                "\t\t\"nodeIds\": [\"02_05_508\",\"4dd07c5d-c3da-4295-aa8b-1ba6317ce961\",\"05bcc2ca-05e1-431f-b065-9e1800bf99c1\",\"2c54fd69-21ff-4b93-8dc5-780d0b945583\",\"02_05_528\",\"f018460e-49ec-434b-b102-d6e49f97f546\",\"02_05_511\",\"df093370-5a37-45f9-94c9-f4aeb57c2b73\",\"02_05_508\",\"51643b7b-7588-40a8-82de-6e61839fc778\",\"5aa374dd-a283-4b66-9936-723ff3b7a814\",\"ed6acd0a-5941-4015-9060-961b78589e65\",\"02_05_530\",\"b7b14c64-20d9-4d2e-aab2-13c53ec71ec2\",\"02_05_526\",\"afef2a60-5f04-461f-9a30-f6794a5e2301\",\"02_05_510\",\"02_05_534\",\"c89f40a1-59a1-4e2f-b08c-df38a461c508\",\"02_05_515\",\"b844ef0a-9b58-4f51-a0af-771a65c6c2e6\",\"e4a58888-2fef-46ad-bf49-6398ec172921\",\"1b834624-fd39-446b-9a31-e281c8dfe080\",\"02_05_509\",\"02_05_532\",\"2b440216-6e9c-444b-b2c6-bbf942f98eb2\",\"a16366c6-82aa-4eeb-94d3-3eff95039fb9\",\"37b7ddd2-9aef-44d4-9199-650d8a6d68c5\",\"02_05_513\"]\n" +
                "\t},\n" +
                "\t\"scene\": {\n" +
                "\t\t\"areaCode\": \"310000\",\n" +
                "\t\t\"bizAction\": \"AI_REVIEW_WEAK\",\n" +
                "\t\t\"bizCode\": \"ZSY_XXJ\",\n" +
                "        \"studentLevel\": \"normal\",\n" +
                "        \"examType\":\"week\",\n" +
                "        \"planDiff\":\"base\",\n" +
                "        \"pointType\":\"ANCHOR_POINT\",\n" +
                "        \"nodeCatalogMap\":{\n" +
                "            \"02_05_515\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"ed6acd0a-5941-4015-9060-961b78589e65\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"37b7ddd2-9aef-44d4-9199-650d8a6d68c5\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"02_05_513\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"02_05_534\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"02_05_511\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "            \"02_05_510\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "            \"02_05_532\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"2b440216-6e9c-444b-b2c6-bbf942f98eb2\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"02_05_530\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"05bcc2ca-05e1-431f-b065-9e1800bf99c1\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"df093370-5a37-45f9-94c9-f4aeb57c2b73\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"51643b7b-7588-40a8-82de-6e61839fc778\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"a16366c6-82aa-4eeb-94d3-3eff95039fb9\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"f018460e-49ec-434b-b102-d6e49f97f546\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"b7b14c64-20d9-4d2e-aab2-13c53ec71ec2\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"02_05_509\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "            \"02_05_508\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"02_05_528\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"c89f40a1-59a1-4e2f-b08c-df38a461c508\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "            \"02_05_526\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"2c54fd69-21ff-4b93-8dc5-780d0b945583\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"5aa374dd-a283-4b66-9936-723ff3b7a814\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"e4a58888-2fef-46ad-bf49-6398ec172921\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"4dd07c5d-c3da-4295-aa8b-1ba6317ce961\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"1b834624-fd" +
                "39-446b-9a31-e281c8dfe080\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"afef2a60-5f04-461f-9a30-f6794a5e2301\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"b844ef0a-9b58-4f51-a0af-771a65c6c2e6\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\"\n" +
                "        }\n" +
                "\t\t\"gradeCode\": \"08\",\n" +
                "\t\t\"graphVersion\": \"v10\",\n" +
                "\t\t\"phaseCode\": \"05\",\n" +
                "\t\t\"pressCode\": \"273\",\n" +
                "\t\t\"studyCode\": \"AI_REVIEW\",\n" +
                "\t\t\"subjectCode\": \"02\",\n" +
                "\t\t\"test\": false,\n" +
                "\t\t\"userId\": \"tsp-engine-test7\"\n" +
                "\t},\n" +
                "\t\"traceId\": \"9d5c26d9-2266-4c23-9828-480739c6c688\"\n" +
                "}";

        SkylabRequest<RecTopicPackParam> skylabRequest = JSON.parseObject(test, new TypeReference<SkylabRequest<RecTopicPackParam>>() {
        });
        AiReviewSceneInfo aiReviewSceneInfo = new AiReviewSceneInfo();
        BeanUtils.copyProperties(skylabRequest.getScene(), aiReviewSceneInfo);
        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "            \"02_05_515\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"ed6acd0a-5941-4015-9060-961b78589e65\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"37b7ddd2-9aef-44d4-9199-650d8a6d68c5\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"02_05_513\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"02_05_534\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"02_05_511\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "            \"02_05_510\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "            \"02_05_532\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"2b440216-6e9c-444b-b2c6-bbf942f98eb2\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"02_05_530\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"05bcc2ca-05e1-431f-b065-9e1800bf99c1\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"df093370-5a37-45f9-94c9-f4aeb57c2b73\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"51643b7b-7588-40a8-82de-6e61839fc778\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"a16366c6-82aa-4eeb-94d3-3eff95039fb9\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"f018460e-49ec-434b-b102-d6e49f97f546\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"b7b14c64-20d9-4d2e-aab2-13c53ec71ec2\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"02_05_509\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "            \"02_05_508\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"02_05_528\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"c89f40a1-59a1-4e2f-b08c-df38a461c508\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "            \"02_05_526\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"2c54fd69-21ff-4b93-8dc5-780d0b945583\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"5aa374dd-a283-4b66-9936-723ff3b7a814\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"e4a58888-2fef-46ad-bf49-6398ec172921\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "            \"4dd07c5d-c3da-4295-aa8b-1ba6317ce961\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"1b834624-fd39-446b-9a31-e281c8dfe080\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "            \"afef2a60-5f04-461f-9a30-f6794a5e2301\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "            \"b844ef0a-9b58-4f51-a0af-771a65c6c2e6\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\"\n" +
                "        }";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });

        aiReviewSceneInfo.setNodeCatalogMap(nodeCatalogMap);

        aiReviewSceneInfo.setStudentLevel(StudentLevelEnum.NORMAL);
        aiReviewSceneInfo.setPointType(NodeTypeEnum.ANCHOR_POINT);
        aiReviewSceneInfo.setExamType(ExamType.WEEK_EXAM);
        // aiReviewSceneInfo.setPlanDiff("base");

        skylabRequest.setScene(aiReviewSceneInfo);

        SkylabResponse<RecTopicPackResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicPackResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    //理科单品-推点
    @Test
    public void AI_test6() {
        String test = "{\n" +
                "    \"payload\": {\n" +
                "\t\t\"catalogIds\": [\"273_1902050101-2316_1902050101-2316-79587_1902050101-2316-79588xyxtb01\"]\n" +
                "\t},\n" +
                "\t\"scene\": {\n" +
                "\t\t\"areaCode\": \"310000\",\n" +
                "\t\t\"bizAction\": \"AI_REVIEW_POINTS\",\n" +
                "\t\t\"bizCode\": \"ZSY_XXJ\",\n" +
                "        \"studentLevel\": \"normal\",\n" +
                "        \"examType\":\"week\",\n" +
                "        \"planDiff\":\"base\",\n" +
                "        \"pointType\":\"CHECK_POINT\",\n" +
                "\t\t\"gradeCode\": \"08\",\n" +
                "\t\t\"graphVersion\": \"v10\",\n" +
                "\t\t\"phaseCode\": \"05\",\n" +
                "\t\t\"pressCode\": \"273\",\n" +
                "\t\t\"studyCode\": \"AI_REVIEW\",\n" +
                "\t\t\"subjectCode\": \"02\",\n" +
                "\t\t\"test\": false,\n" +
                "\t\t\"userId\": \"tsp-engine-test7\"\n" +
                "\t},\n" +
                "\t\"traceId\": \"9d5c26d9-2266-4c23-9828-480739c6c688\"\n" +
                "}";

        SkylabRequest<RecNodeParam> skylabRequest = JSON.parseObject(test, new TypeReference<SkylabRequest<RecNodeParam>>() {
        });
        AiReviewSceneInfo aiReviewSceneInfo = new AiReviewSceneInfo();
        BeanUtils.copyProperties(skylabRequest.getScene(), aiReviewSceneInfo);
        //特殊字段处理



        aiReviewSceneInfo.setStudentLevel(StudentLevelEnum.NORMAL);
        aiReviewSceneInfo.setPointType(NodeTypeEnum.CHECK_POINT);
        aiReviewSceneInfo.setExamType(ExamType.WEEK_EXAM);
        // aiReviewSceneInfo.setPlanDiff("base");

        skylabRequest.setScene(aiReviewSceneInfo);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    //理科单品-推题
    @Test
    public void AI_test7() {
        String test = "{\n" +
                "\t\"payload\": {\n" +
                "\t\t\"nodeId\": \"xy01c91e707e-cc9e-4787-a5bf-7035dd2b3c6d\",\n" +
                "\t\t\"roundId\": \"AiReviewUser01_273_19028801273-3001_19028801273-3001-102083_19028801273-3001-102086_1\",\n" +
                "\t\t\"topicOrderNumber\": 1\n" +
                "\t},\n" +
                "\t\"scene\": {\n" +
                "\t\t\"areaCode\": \"310000\",\n" +
                "\t\t\"bizAction\": \"AI_REVIEW_REC\",\n" +
                "\t\t\"bizCode\": \"ZSY_XXJ\",\n" +
                "        \"studentLevel\": \"normal\",\n" +
                "        \"examType\":\"week\",\n" +
                "        \"planDiff\":\"base\",\n" +
                "        \"pointType\":\"CHECK_POINT\",\n" +
                "        \"catalogCode\":\"273_1902050101-2316_1902050101-2316-79587_1902050101-2316-79588xyxtb01\",\n" +
                "\t\t\"gradeCode\": \"08\",\n" +
                "\t\t\"graphVersion\": \"v10\",\n" +
                "\t\t\"phaseCode\": \"05\",\n" +
                "\t\t\"pressCode\": \"273\",\n" +
                "\t\t\"studyCode\": \"AI_REVIEW\",\n" +
                "\t\t\"subjectCode\": \"02\",\n" +
                "\t\t\"test\": false,\n" +
                "\t\t\"userId\": \"tsp-engine-test7\"\n" +
                "\t},\n" +
                "\t\"traceId\": \"07d118f1-f558-424f-be64-8646aa46ae4a\"\n" +
                "}";

        SkylabRequest<RecTopicParam> skylabRequest = JSON.parseObject(test, new TypeReference<SkylabRequest<RecTopicParam>>() {
        });
        AiReviewSceneInfo aiReviewSceneInfo = new AiReviewSceneInfo();
        BeanUtils.copyProperties(skylabRequest.getScene(), aiReviewSceneInfo);
        //特殊字段处理

        aiReviewSceneInfo.setStudentLevel(StudentLevelEnum.NORMAL);
        aiReviewSceneInfo.setPointType(NodeTypeEnum.CHECK_POINT);
        aiReviewSceneInfo.setExamType(ExamType.WEEK_EXAM);
        // aiReviewSceneInfo.setPlanDiff("base");

        skylabRequest.setScene(aiReviewSceneInfo);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    //理科单品-画像
    @Test
    public void AI_test8() {
        String test = "\n" +
                "{\n" +
                "\t\"traceId\": \"fdac1539-38ec-4fb6-96c1-00b563efa00a\",\n" +
                "\t\"scene\": {\n" +
                "\t\t\"areaCode\": \"310000\",\n" +
                "\t\t\"bizAction\": \"NONE\",\n" +
                "\t\t\"bizCode\": \"ZSY_XXJ\",\n" +
                "        \"studentLevel\": \"normal\",\n" +
                "        \"examType\":\"month\",\n" +
                "        \"planDiff\":\"base\",\n" +
                "        \"pointType\":\"CHECK_POINT\",\n" +
                "        \"catalogIds\":[\"273_19028801273-3001\"],\n" +
                "\t\t\"gradeCode\": \"08\",\n" +
                "\t\t\"graphVersion\": \"v10\",\n" +
                "\t\t\"phaseCode\": \"05\",\n" +
                "\t\t\"pressCode\": \"273\",\n" +
                "\t\t\"studyCode\": \"AI_REVIEW\",\n" +
                "\t\t\"subjectCode\": \"02\",\n" +
                "\t\t\"test\": false,\n" +
                "\t\t\"userId\": \"tsp-engine-test7\"\n" +
                "\t},\n" +
                "\t\"payload\": {\n" +
                "\t\t\"nodeIds\": [\"xy01c91e707e-cc9e-4787-a5bf-7035dd2b3c6d\"]\n" +
                "\t}\n" +
                "}";

        SkylabRequest<MasterFetch4NodeParam> skylabRequest = JSON.parseObject(test, new TypeReference<SkylabRequest<MasterFetch4NodeParam>>() {});
        AiReviewSceneInfo aiReviewSceneInfo = new AiReviewSceneInfo();
        BeanUtils.copyProperties(skylabRequest.getScene(), aiReviewSceneInfo);
        //特殊字段处理

        aiReviewSceneInfo.setStudentLevel(StudentLevelEnum.NORMAL);
        aiReviewSceneInfo.setPointType(NodeTypeEnum.CHECK_POINT);
        aiReviewSceneInfo.setCatalogIds(Arrays.asList("273_1902050101-2316_1902050101-2316-79587_1902050101-2316-79588xyxtb01"));
        aiReviewSceneInfo.setExamType(ExamType.MONTH_EXAM);
        // aiReviewSceneInfo.setPlanDiff("base");

        skylabRequest.setScene(aiReviewSceneInfo);

        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void test(){
        String test = "[{\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95199\",\n" +
                "\t\t\"pointId\": \"02_05_508\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "\t\t\"pointId\": \"4dd07c5d-c3da-4295-aa8b-1ba6317ce961\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "\t\t\"pointId\": \"05bcc2ca-05e1-431f-b065-9e1800bf99c1\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"2c54fd69-21ff-4b93-8dc5-780d0b945583\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"02_05_528\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "\t\t\"pointId\": \"f018460e-49ec-434b-b102-d6e49f97f546\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "\t\t\"pointId\": \"02_05_511\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"df093370-5a37-45f9-94c9-f4aeb57c2b73\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "\t\t\"pointId\": \"02_05_508\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"51643b7b-7588-40a8-82de-6e61839fc778\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"5aa374dd-a283-4b66-9936-723ff3b7a814\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "\t\t\"pointId\": \"ed6acd0a-5941-4015-9060-961b78589e65\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "\t\t\"pointId\": \"02_05_530\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"b7b14c64-20d9-4d2e-aab2-13c53ec71ec2\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"02_05_526\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "\t\t\"pointId\": \"afef2a60-5f04-461f-9a30-f6794a5e2301\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "\t\t\"pointId\": \"02_05_510\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "\t\t\"pointId\": \"02_05_534\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "\t\t\"pointId\": \"c89f40a1-59a1-4e2f-b08c-df38a461c508\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "\t\t\"pointId\": \"02_05_515\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"b844ef0a-9b58-4f51-a0af-771a65c6c2e6\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"e4a58888-2fef-46ad-bf49-6398ec172921\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "\t\t\"pointId\": \"1b834624-fd39-446b-9a31-e281c8dfe080\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95200\",\n" +
                "\t\t\"pointId\": \"02_05_509\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"02_05_532\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"2b440216-6e9c-444b-b2c6-bbf942f98eb2\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500\",\n" +
                "\t\t\"pointId\": \"a16366c6-82aa-4eeb-94d3-3eff95039fb9\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95202\",\n" +
                "\t\t\"pointId\": \"37b7ddd2-9aef-44d4-9199-650d8a6d68c5\"\n" +
                "\t}, {\n" +
                "\t\t\"catalogCode\": \"273_19020601273-2489_19020601273-2489-86497_19020601273-2489-86500_19020601273-2489-95201\",\n" +
                "\t\t\"pointId\": \"02_05_513\"\n" +
                "\t}]";
        List<JSONObject> list = JSON.parseArray(test,JSONObject.class);
        Map<String,String> map = new HashMap<>();
        List<String> alist = new ArrayList<>();
        list.forEach(t-> {
            map.put((String) t.get("pointId"),(String) t.get("catalogCode"));
            alist.add((String) t.get("pointId"));
        });
        log.info("list:{},map:{}",list.size(),map.size());
        log.info(JSON.toJSONString(map));
        log.info(JSON.toJSONString(alist));
    }
}