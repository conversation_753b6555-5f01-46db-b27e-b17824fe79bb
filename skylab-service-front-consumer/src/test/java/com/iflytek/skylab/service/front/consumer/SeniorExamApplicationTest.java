package com.iflytek.skylab.service.front.consumer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SeniorExamApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;


    @Test
    public void senior_exam_rec_eval(){

        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.FIRST_REVISE_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright");


        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recEval4InOutParam.setCatalogIds(Arrays.asList("db80eadd-f083-4ace-a74e-aa483d4370a5"));

        Map<String, Object> recExt  = new HashMap<>();
        recExt.put("evalNum",1);
        recEval4InOutParam.setRecExt(recExt);
        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void senior_exam_rec_node(){

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.FIRST_REVISE_SORT);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright");

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("db80eadd-f083-4ace-a74e-aa483d4370a5"));

        Map<String, Object> recExt  = new HashMap<>();
        recExt.put("evalNum",1);
        recNodeParam.setRecExt(recExt);
        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }


    @Test
    public void senior_exam_rec_topic(){

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.FIRST_REVISE_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright");

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recTopicParam.setNodeId("986669f4-11bb-4133-b566-d52c0bfdbbfd");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void senior_exam_master_fetch(){

        SkylabRequest<MasterFetch4CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright11");

        skylabRequest.setScene(sceneInfo);

        MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();
        masterFetch4CatalogParam.setCatalogIds(Arrays.asList("db80eadd-f083-4ace-a74e-aa483d4370a5"));

        Map<String, Object> masterFetchExt  = new HashMap<>();
        masterFetchExt.put("evalNum",1);
        masterFetch4CatalogParam.setMasterFetchExt(masterFetchExt);

        skylabRequest.setPayload(masterFetch4CatalogParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void senior_exam_study_log(){

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.FIRST_REVISE_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright11");



        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("986669f4-11bb-4133-b566-d52c0bfdbbfd");
        studyLogRecord.setNodeType(NodeTypeEnum.REVIEW_POINT);
        studyLogRecord.setResNodeId("19de314f-6677-4908-aafb-bfb7a09e4fc2");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setCatalogCode("3bbfb13c-10a5-41d4-b48f-c55ac69bcf99");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }
}