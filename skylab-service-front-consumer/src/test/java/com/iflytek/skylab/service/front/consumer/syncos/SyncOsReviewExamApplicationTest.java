package com.iflytek.skylab.service.front.consumer.syncos;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.service.front.consumer.CheckResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncOsReviewExamApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;
    static SceneInfo sceneInfo = null;

    static {
        sceneInfo = new SceneInfo();

//        sceneInfo.setGraphVersion("20240401_002");
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setAreaCode("420000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-1");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06");

    }

    @Test
    public void review_exam_rec_eval() {

        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.MID_EXAM);
        sceneInfo.setBizAction(BizActionEnum.EXAM_STAGE_DEALING);

        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4OUT);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recEval4InOutParam.setCatalogIds(Arrays.asList("01_08020101-002_06"));
        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * * 入门测
     */

    @Test
    public void review_exam_rec_eval_in() {
        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setBizAction(BizActionEnum.EXAM_UNIT_BASIS);

        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_09020101-002");
        sceneInfo.setCatalogCode("01_09020101-002_001");

        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recEval4InOutParam.setCatalogIds(Arrays.asList("01_09020101-002_001"));
        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


    @Test
    public void review_exam_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setBizAction(BizActionEnum.EXAM_UNIT_SEARCH_WEAK);
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06");

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("01_08020101-002_06"));

        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }


    @Test
    public void review_exam_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setBizAction(BizActionEnum.EXAM_UNIT_REC);
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06");

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("*************-402a-b4f8-e5707be73e9f");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    @Test
    public void review_exam_rec_topic2() {
        for (int i = 0; i < 10; i++) {


            SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
            String traceId = UUID.randomUUID().toString();
            log.info("traceId: {}", traceId);
            skylabRequest.setTraceId(traceId);

            sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_OS_MAP);
            sceneInfo.setBizAction(BizActionEnum.SYNC_OS_CHECK_REC);
            //教材
            sceneInfo.setPressCode("30");
            sceneInfo.setBookCode("30_08020213-002");
            sceneInfo.setCatalogCode("30_08020213-002_005");

            skylabRequest.setScene(sceneInfo);

            RecTopicParam recTopicParam = new RecTopicParam();
            recTopicParam.setNodeId("f5922888-bded-40ac-bc3a-468867aca38e");
            recTopicParam.setTopicOrderNumber(1);
            skylabRequest.setPayload(recTopicParam);

            SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
//        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
        }
    }

    @Test
    public void review_exam_rec_node2() {
        for (int i = 0; i < 1; i++) {
            SkylabRequest<RecUnitParam> skylabRequest = new SkylabRequest<>();
            String traceId = UUID.randomUUID().toString();
            log.info("traceId: {}", traceId);
            skylabRequest.setTraceId(traceId);

            SceneInfo sceneInfo = new SceneInfo();
//            sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
            sceneInfo.setStudyCode(StudyCodeEnum.MID_EXAM);
            sceneInfo.setHisStudyCode(HisStudyCodeEnum.MID_EXAM);
            sceneInfo.setAreaCode("000000");
            sceneInfo.setBizAction(BizActionEnum.EXAM_STAGE_UNITS);
            sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

            sceneInfo.setGradeCode("07");
            //学科
            sceneInfo.setSubjectCode("02");
            //学段
            sceneInfo.setPhaseCode("04");
            //用户id
            sceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
            //教材
            sceneInfo.setPressCode("19");
            sceneInfo.setBookCode("19_08020207-002");
//        sceneInfo.setCatalogCode("19_08020207-002");

            skylabRequest.setScene(sceneInfo);

            RecUnitParam recNodeParam = new RecUnitParam();
            recNodeParam.setCatalogIds(Arrays.asList("19_08020207-002_06", "19_08020207-002_03", "19_08020207-002_05", "19_08020207-002_07", "19_08020207-002_02"));
//        recNodeParam.setCatalogIds(Arrays.asList("01_08020101-002_06"));
            skylabRequest.setPayload(recNodeParam);

            SkylabResponse<RecUnitResult> skylabResponse = recommendService.recommend(skylabRequest, RecUnitResult.class);
            log.info(JSON.toJSONString(skylabResponse));
        }

    }

    @Test
    public void review_exam_rec_node3() {
        for (int i = 0; i < 1; i++) {
            SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
            String traceId = UUID.randomUUID().toString();
            log.info("traceId: {}", traceId);
            skylabRequest.setTraceId(traceId);

            SceneInfo sceneInfo = new SceneInfo();

            sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
            sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
            sceneInfo.setBizAction(BizActionEnum.NONE);
            sceneInfo.setHisStudyCode(HisStudyCodeEnum.MID_EXAM);

            sceneInfo.setAreaCode("340100");

            sceneInfo.setGradeCode("08");
            //学科
            sceneInfo.setSubjectCode("05");
            //学段
            sceneInfo.setPhaseCode("04");
            //用户id
            sceneInfo.setUserId("xiangzhang182_test");
            //教材
            sceneInfo.setCatalogCode("272_08050201272-8940_08050201272-8940-213717");

            List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
            sceneInfo.setPressCode(split.get(0));
            sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

            sceneInfo.setLayerType("conventional");

            skylabRequest.setScene(sceneInfo);

            RecNodeParam recNodeParam = new RecNodeParam();
            recNodeParam.setCatalogIds(Arrays.asList("272_08050201272-8940_08050201272-8940-213717_S-57ed831a-086a-4086-9038-c38568ef2893",
                    "272_08050201272-8940_08050201272-8940-213717_S-eeb29e59-bc9e-4b8a-82e9-3eef5f1cafbc",
                    "272_08050201272-8940_08050201272-8940-213717_S-2a5245da-1582-4fa5-a8ff-d426ea8ad127",
                    "272_08050201272-8940_08050201272-8940-213717_S-248fe20a-684c-4d77-8d72-2716a9e1c0af",
                    "272_08050201272-8940_08050201272-8940-213717_S-8adc615c-3ff7-42e5-a109-432dfd35d5ae"));
            skylabRequest.setPayload(recNodeParam);

            SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
            log.info(JSON.toJSONString(skylabResponse));
        }

    }


    @Test
    public void review_exam_master_fetchbook() {

        SkylabRequest<MasterFetch5BookParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06");
        sceneInfo.setUserId("Mastery040201");
        skylabRequest.setScene(sceneInfo);

        MasterFetch5BookParam bookParam = new MasterFetch5BookParam();

        bookParam.setBookCode("01-07020101-001");
//        bookParam.setCatalogIds(Arrays.asList("01_07020101-001_02"));
        skylabRequest.setPayload(bookParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }


    @Test
    public void review_exam_master_fetch() {

        SkylabRequest<MasterFetch5CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        //教材
        sceneInfo.setPressCode("19");
        sceneInfo.setBookCode("19_08020107-002");
        sceneInfo.setCatalogCode("19_08020107-002_02");
        sceneInfo.setUserId("b3ac0b05-31b7-4837-a575-3fc4605572b7");

        skylabRequest.setScene(sceneInfo);

        MasterFetch5CatalogParam masterFetch5CatalogParam = new MasterFetch5CatalogParam();

        masterFetch5CatalogParam.setCatalogIds(Arrays.asList("19_08020107-002_02"));
        skylabRequest.setPayload(masterFetch5CatalogParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    @Test
    public void review_exam_master_fetch_node() {

        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.MID_EXAM);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_04");
        sceneInfo.setUserId("Mastery040201");
        skylabRequest.setScene(sceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();

        List<NodeInfo> nodeInfos = new ArrayList<>();
        nodeInfos.add(new NodeInfo("676b545e-a750-40a6-8893-c58826fe2b69", NodeTypeEnum.CHECK_POINT, "01_08020101-002_04", null));

        masterFetch5NodeParam.setNodeInfos(nodeInfos);
        skylabRequest.setPayload(masterFetch5NodeParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));

        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    @Test
    public void review_exam_study_log() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.UNIT_REVIEW);
        sceneInfo.setBizAction(BizActionEnum.EXAM_STAGE_DEALING);
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("*************-402a-b4f8-e5707be73e9f");
        studyLogRecord.setNodeType(NodeTypeEnum.CHECK_POINT);
        studyLogRecord.setResNodeId("fa2c71c6-111c-4e47-9735-4b93af559e87");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode("01_08020101-002_06");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
        review_exam_master_fetch();
    }


}
