package com.iflytek.skylab.service.front.consumer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.ResourceTypeEnum;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class BYODAiDiagApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    String USERID = "xiangzhang182";

    @Test
    public void ai_diag_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.AI_DIAGNOSIS_SEARCH_WEAK);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
//        aiDiagSceneInfo.setGraphVersion("v7");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId(USERID);
        //教材
        aiDiagSceneInfo.setPressCode("01");
        aiDiagSceneInfo.setBookCode("01_08020101-002");
        aiDiagSceneInfo.setCatalogCode("01_08020101-002_06_003");

        //特殊字段处理
        String nodeCatalogMapString = "{" +
                "\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_04_002\"" +
                "}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        skylabRequest.setScene(aiDiagSceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();

        recNodeParam.setCatalogIds(Arrays.asList("34e87921-cfcf-44aa-b1f6-49fc703cfcd7"));

        JSONObject recExt = new JSONObject();
        recExt.fluentPut("anchorCntLimit", 1);
        recNodeParam.setRecExt(recExt);
        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, USERID);
    }

    @Test
    public void ai_diag_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.AI_DIAGNOSIS_REC);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId(USERID);
        //教材
        aiDiagSceneInfo.setPressCode("01");
        aiDiagSceneInfo.setBookCode("01_08020101-002");
        aiDiagSceneInfo.setCatalogCode("01_08020101-002_06_003");

        //特殊字段处理
        String nodeCatalogMapString = "{" +
                "\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\"" +
                "}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, USERID);
    }

    @Test
    public void ai_diag_rec_topic_pack() {
        SkylabRequest<RecTopicPackParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.AI_DIAGNOSIS_REC_PACK);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId(USERID);
        //教材
        aiDiagSceneInfo.setPressCode("01");
        //特殊字段处理
        String nodeCatalogMapString = "{" +
                "\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\"," +
                "\"ac935ac1-bbdf-4654-bddc-7a0a9e97beb3\":\"01_08020101-002_06_003\"," +
                "\"f6ecc8a2-00d7-4c5f-be7e-8a38245823ff\":\"01_07020101-001_02_001\"" +
                "}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        RecTopicPackParam recTopicPackParam = new RecTopicPackParam();
        recTopicPackParam.setNodeIds(Arrays.asList("34e87921-cfcf-44aa-b1f6-49fc703cfcd7", "ac935ac1-bbdf-4654-bddc-7a0a9e97beb3", "f6ecc8a2-00d7-4c5f-be7e-8a38245823ff"));
        skylabRequest.setPayload(recTopicPackParam);
        SkylabResponse<RecTopicPackResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicPackResult.class);
        CheckResultUtil.check(skylabResponse, USERID);
    }

    @Test
    public void ai_diag_master_fetch() {

        SkylabRequest<MasterFetch4NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.NONE);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId(USERID);
        //教材
        aiDiagSceneInfo.setPressCode("01");
        //特殊字段处理
        String nodeCatalogMapString = "{" +
                "\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\"," +
                "\"ac935ac1-bbdf-4654-bddc-7a0a9e97beb3\":\"01_08020101-002_06_003\"," +
                "\"f6ecc8a2-00d7-4c5f-be7e-8a38245823ff\":\"01_07020101-001_02_001\"" +
                "}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        MasterFetch4NodeParam masterFetch4NodeParam = new MasterFetch4NodeParam();
        masterFetch4NodeParam.setNodeIds(Arrays.asList("34e87921-cfcf-44aa-b1f6-49fc703cfcd7", "ac935ac1-bbdf-4654-bddc-7a0a9e97beb3", "f6ecc8a2-00d7-4c5f-be7e-8a38245823ff"));
        skylabRequest.setPayload(masterFetch4NodeParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, USERID);
    }

    /**
     * 学情中心对接
     * case1：拍搜多题上报
     * case2：普通单题上报
     */
    @Test
    public void ai_diag_study_log() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.PHOTO_INPUT);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId(USERID);
        //教材
        aiDiagSceneInfo.setPressCode("01");

        //特殊字段处理
        String nodeCatalogMapString = "{" +
                "\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\"" +
                "}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("5aaf5a95-bffc-4cc1-a012-451313bcd581");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode("01_08020101-002_06_003");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, USERID);
    }
}
