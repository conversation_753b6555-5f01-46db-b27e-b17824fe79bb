package com.iflytek.skylab.service.front.consumer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.LayerTypeEnum;
import com.iflytek.skylab.core.domain.LayerVersionEnum;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SeniorExamUpdateApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;
    @Autowired
    private SkylabStandService skylabStandService;

    String userId = "zx182-01";
    //    String graphVersion = "20231206_001_zkfx";
    String graphVersion = "20240112_006";

    public void check(SkylabResponse skylabResponse) {
        log.info(JSON.toJSONString(skylabResponse));
        Assert.assertTrue(skylabResponse.getCode() == 0);
        if (skylabResponse.getErrorNodes() != null) {
            for (int i = 0; i < skylabResponse.getErrorNodes().size(); i++) {
                JSONObject jsonObject = skylabResponse.getErrorNodes().getJSONObject(i);
                Assert.assertFalse("failover".equals(jsonObject.getString("code")) && "true".equals(jsonObject.getString("name")));
            }
        }
    }

    @Test
    public void enior_exam1() {
        //测评一轮 treeId
        senior_exam_rec_eval(StudyCodeEnum.SENIOR_EXAM_1, null, null, "db80eadd-f083-4ace-a74e-aa483d4370a5");
        senior_exam_rec_eval(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), null, "db80eadd-f083-4ace-a74e-aa483d4370a5");
        senior_exam_rec_eval(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.conventional.name(), "db80eadd-f083-4ace-a74e-aa483d4370a5");
        senior_exam_rec_eval(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.highScoreAdvanced.name(), "db80eadd-f083-4ace-a74e-aa483d4370a5");
        senior_exam_rec_eval(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.thinkingExpansion.name(), "db80eadd-f083-4ace-a74e-aa483d4370a5");

        //推点一轮
        senior_exam_rec_node(StudyCodeEnum.SENIOR_EXAM_1, null, null);
        senior_exam_rec_node(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), null);
        senior_exam_rec_node(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.conventional.name());
        senior_exam_rec_node(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.highScoreAdvanced.name());
        senior_exam_rec_node(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.thinkingExpansion.name());
        //推点二轮
        senior_exam_rec_node2(StudyCodeEnum.SENIOR_EXAM_2, null, null);
        //推题一轮
        senior_exam_rec_topic();

        //中考一轮复习一级点目录画像
        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_1, null, null, "a7b35c35-d2dc-4a8a-92ce-8b62672d32cc");
        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.conventional.name(), "a7b35c35-d2dc-4a8a-92ce-8b62672d32cc");
        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.highScoreAdvanced.name(), "a7b35c35-d2dc-4a8a-92ce-8b62672d32cc");
        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.thinkingExpansion.name(), "a7b35c35-d2dc-4a8a-92ce-8b62672d32cc");

        //中考一轮复习四级点画像
        senior_exam_master_node_fetch(StudyCodeEnum.SENIOR_EXAM_1, null, null, "9fe86d70-538d-46c0-93a1-5d4d1de5c4db");
        senior_exam_master_node_fetch(StudyCodeEnum.SENIOR_EXAM_1, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.conventional.name(), "9fe86d70-538d-46c0-93a1-5d4d1de5c4db");

        //作答
        senior_exam_study_log(StudyCodeEnum.SENIOR_EXAM_1);


    }

    @Test
    public void enior_exam2() {
//        //tree
//        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_2, null, null, "083242f1-251d-4fae-8e15-3642126e83f4");
//        //中考二轮复习多个一级点目录画像
//        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_2, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.conventional.name(),
//                "97f0dc12-ed38-4537-9bd6-053ee7390890",
//                "305b5bd5-b175-49e6-88b5-e398ac013e71",
//                "a75f0a48-cdc8-4eda-bd3e-47e8d69b5a14",
//                "1a861a74-d6e9-4539-d3e4-07c7d5e4ae3e",
//                "11c3c36f-c9c4-4b29-af6e-eaf3fe9f029c",
//                "9e9e810e-36b4-42db-8c68-b186e2f794b8",
//                "a005e8c4-f4da-4ae8-9f53-7750bd3d1dad",
//                "a81e9dd6-15a7-4be2-adb8-6f2d228c3b49");
//        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_2, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.conventional.name(), "2cac3d4a-464c-4f4b-8960-7548e80119b3", "a7b35c35-d2dc-4a8a-92ce-8b62672d32cc", "3f3c20d2-7a8e-48c7-924b-f591537d49ca", "6146142b-7c99-403f-a489-20f443672c57", "214b8df3-89ec-433f-80b8-57036b7e6392", "77d82bf4-6091-4493-8edd-47600204fd4e", "3434d520-c9bc-4951-936b-f7fd4ea40868", "c7447939-9d1b-4bfa-aca6-e7e81c2fd34a", "599902df-9bfe-4c19-a7b5-4251c798b6d2");
//        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_2, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.highScoreAdvanced.name(), "2cac3d4a-464c-4f4b-8960-7548e80119b3", "a7b35c35-d2dc-4a8a-92ce-8b62672d32cc", "3f3c20d2-7a8e-48c7-924b-f591537d49ca", "6146142b-7c99-403f-a489-20f443672c57", "214b8df3-89ec-433f-80b8-57036b7e6392", "77d82bf4-6091-4493-8edd-47600204fd4e", "3434d520-c9bc-4951-936b-f7fd4ea40868", "c7447939-9d1b-4bfa-aca6-e7e81c2fd34a", "599902df-9bfe-4c19-a7b5-4251c798b6d2");
//        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_2, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.thinkingExpansion.name(), "2cac3d4a-464c-4f4b-8960-7548e80119b3", "a7b35c35-d2dc-4a8a-92ce-8b62672d32cc", "3f3c20d2-7a8e-48c7-924b-f591537d49ca", "6146142b-7c99-403f-a489-20f443672c57", "214b8df3-89ec-433f-80b8-57036b7e6392", "77d82bf4-6091-4493-8edd-47600204fd4e", "3434d520-c9bc-4951-936b-f7fd4ea40868", "c7447939-9d1b-4bfa-aca6-e7e81c2fd34a", "599902df-9bfe-4c19-a7b5-4251c798b6d2");
//
//
//        //测评二轮old treeId
//        senior_exam_rec_eval2(StudyCodeEnum.SENIOR_EXAM_2, null, null, "083242f1-251d-4fae-8e15-3642126e83f4");
        //测评二轮new 一级点
        senior_exam_rec_eval2(StudyCodeEnum.SENIOR_EXAM_2, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), null,
                "ce3c3f7d-18bd-4028-a90e-105257d0fe76", "0427498a-7b9c-44a7-b155-f240a4b5350b"
        );
//        senior_exam_rec_eval2(StudyCodeEnum.SENIOR_EXAM_2, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.conventional.name());
//        senior_exam_rec_eval2(StudyCodeEnum.SENIOR_EXAM_2, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.highScoreAdvanced.name());
//        senior_exam_rec_eval2(StudyCodeEnum.SENIOR_EXAM_2, LayerVersionEnum.SENIOR_EXAM_UPDATE.name(), LayerTypeEnum.thinkingExpansion.name());
//        //推点二轮
//        senior_exam_rec_node2(StudyCodeEnum.SENIOR_EXAM_2, null, null);
//        //推题一轮
//        senior_exam_rec_topic();
//        //推题二轮-变式题
//        senior_exam_rec_topic2(BizActionEnum.SECOND_REVISE_VARY);
//        //推题二轮-典例题
//        senior_exam_rec_topic2(BizActionEnum.SECOND_REVISE_TYP);
//        //中考一轮复习一级点目录画像
//        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_1, BizActionEnum.NONE, "a7b35c35-d2dc-4a8a-92ce-8b62672d32cc");
//        //中考二轮复习树目录画像
//        senior_exam_master_fetch(StudyCodeEnum.SENIOR_EXAM_2, BizActionEnum.NONE, "808197df-767a-4c71-9770-0e45aa677c3a");
//        //中考一轮复习四级点画像
//        senior_exam_master_node_fetch(StudyCodeEnum.SENIOR_EXAM_1, BizActionEnum.NONE, "9fe86d70-538d-46c0-93a1-5d4d1de5c4db");
//        //中考二轮复习四级点画像
//        senior_exam_master_node_fetch(StudyCodeEnum.SENIOR_EXAM_2, BizActionEnum.NONE, "9fe86d70-538d-46c0-93a1-5d4d1de5c4db");
//        //作答
//        senior_exam_study_log();
    }


    public void senior_exam_rec_eval(StudyCodeEnum studyCode, String version, String layerType, String... treeId) {

        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(studyCode);
        sceneInfo.setAreaCode("000000");

        sceneInfo.setBizAction(BizActionEnum.FIRST_REVISE_EVAL);

        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);
        sceneInfo.setGraphVersion(graphVersion);

        sceneInfo.setLayerVersion(version);

        sceneInfo.setLayerType(layerType);

        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        //tree
        recEval4InOutParam.setCatalogIds(Arrays.asList(treeId));

        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
        log.info(JSON.toJSONString(skylabResponse));

        check(skylabResponse);
    }

    public void senior_exam_rec_eval2(StudyCodeEnum studyCode, String version, String layerType, String... nodeId) {

        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(studyCode);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SECOND_REVISE_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

//        sceneInfo.setGraphVersion(graphVersion);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);

        sceneInfo.setLayerVersion(version);

        sceneInfo.setLayerType(layerType);

        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recEval4InOutParam.setCatalogIds(Arrays.asList(nodeId));

        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
//        log.info(JSON.toJSONString(skylabResponse));

        check(skylabResponse);
    }

    public void senior_exam_rec_node(StudyCodeEnum studyCode, String version, String layerType) {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(studyCode);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.FIRST_REVISE_SORT);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion(graphVersion);
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);


        sceneInfo.setLayerVersion(version);

        sceneInfo.setLayerType(layerType);

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("db80eadd-f083-4ace-a74e-aa483d4370a5"));

        skylabRequest.setPayload(recNodeParam);
        log.info(JSON.toJSONString(skylabRequest));

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
        check(skylabResponse);
    }

    public void senior_exam_rec_node2(StudyCodeEnum studyCode, String version, String layerType) {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(studyCode);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SECOND_REVISE_SORT);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion(graphVersion);
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);


        sceneInfo.setLayerVersion(version);

        sceneInfo.setLayerType(layerType);

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("808197df-767a-4c71-9770-0e45aa677c3a"));

        skylabRequest.setPayload(recNodeParam);
        log.info(JSON.toJSONString(skylabRequest));

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
        check(skylabResponse);
    }

    @Test
    public void senior_exam_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.FIRST_REVISE_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);
        sceneInfo.setGraphVersion(graphVersion);

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recTopicParam.setNodeId("986669f4-11bb-4133-b566-d52c0bfdbbfd");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    public void senior_exam_rec_topic2(BizActionEnum bizActionEnum) {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_2);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(bizActionEnum);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);
        sceneInfo.setGraphVersion(graphVersion);

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recTopicParam.setNodeId("f8d83ca2-1d3d-4e93-92e9-e2bc7dd873a6");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
        check(skylabResponse);
    }

    public void senior_exam_master_fetch(StudyCodeEnum studyCodeEnum, String version, String layerType, String... node) {

        SkylabRequest<MasterFetch4CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(studyCodeEnum);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion(graphVersion);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);

        sceneInfo.setLayerVersion(version);

        sceneInfo.setLayerType(layerType);

//        sceneInfo.setCatalogCode(cata);
        skylabRequest.setScene(sceneInfo);

        MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();
        masterFetch4CatalogParam.setCatalogIds(Arrays.asList(node));

        skylabRequest.setPayload(masterFetch4CatalogParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
//        log.info(JSON.toJSONString(skylabResponse));
        check(skylabResponse);
    }

    public void senior_exam_master_node_fetch(StudyCodeEnum studyCodeEnum, String version, String layerType, String node) {

        SkylabRequest<MasterFetch4NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(studyCodeEnum);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion(graphVersion);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);
        sceneInfo.setLayerVersion(version);

        sceneInfo.setLayerType(layerType);
//        sceneInfo.setCatalogCode(cata);
        skylabRequest.setScene(sceneInfo);

        MasterFetch4NodeParam masterFetch4CatalogParam = new MasterFetch4NodeParam();
        masterFetch4CatalogParam.setNodeIds(Arrays.asList(node));

        skylabRequest.setPayload(masterFetch4CatalogParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
//        log.info(JSON.toJSONString(skylabResponse));
        check(skylabResponse);
    }

    //    @Test
    public void senior_exam_study_log(StudyCodeEnum studyCodeEnum) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(studyCodeEnum);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.FIRST_REVISE_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright11");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("986669f4-11bb-4133-b566-d52c0bfdbbfd");
        studyLogRecord.setNodeType(NodeTypeEnum.REVIEW_POINT);
        studyLogRecord.setResNodeId("19de314f-6677-4908-aafb-bfb7a09e4fc2");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setCatalogCode("3bbfb13c-10a5-41d4-b48f-c55ac69bcf99");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
//        log.info(JSON.toJSONString(skylabResponse));
        check(skylabResponse);
    }

    /**
     * 1、中考复习复习点时长（revise_predict_answer_time）：
     * 主键：revisepoint_code#study_code[SENIOR_EXAM_1/SENIOR_EXAM_2]#user_level
     * user_level说明["conventional","highScoreAdvanced","thinkingExpansion","none"]
     * 无计划user_level="none"
     * 基础user_level="conventional"
     * 进阶user_level="highScoreAdvanced"
     * 拔高user_level="thinkingExpansion"
     * <p>
     * 2、中考复习测评题量（catalog_predict_answer_time）
     * 主键：catalog_code#biz_code[ZSY_XXJ]#study_code[SENIOR_EXAM_1/SENIOR_EXAM_2]#user_level
     * <p>
     * 3、中考复习测评时长（catalog_topic_count）
     * 主键：catalog_code#biz_code[ZSY_XXJ]#study_code[SENIOR_EXAM_1/SENIOR_EXAM_2]#user_level
     * catalog_code=一级复习点目录
     * user_level同上
     */
    @Test
    public void feature_revise_predict_answer_time() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);
        //教材
//        sceneInfo.setPressCode("01");
//        sceneInfo.setBookCode("01_08020101-002");
//        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();
        featureParamItem.setFeatureName("revise_predict_answer_time");
        Map<String, String> params = new HashMap<>();
        params.put("revisepoint_code", "861a9329-75b9-4394-9cbe-a5b53693c329");
        params.put("study_code", "SENIOR_EXAM_1");
        params.put("user_level", "none");
        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));
        skylabRequest.setPayload(featureParam);


        params.put("revisepoint_code", "861a9329-75b9-4394-9cbe-a5b53693c329");
        params.put("user_level", "none");
        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("revisepoint_code", "861a9329-75b9-4394-9cbe-a5b53693c329");
        params.put("user_level", "conventional");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("revisepoint_code", "7ce8535b-abfa-4389-8283-181482937a56");
        params.put("user_level", "highScoreAdvanced");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("revisepoint_code", "7ce8535b-abfa-4389-8283-181482937a56");
        params.put("user_level", "thinkingExpansion");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("study_code", "SENIOR_EXAM_2");
        params.put("user_level", "conventional");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("study_code", "SENIOR_EXAM_2");
        params.put("user_level", "highScoreAdvanced");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("study_code", "SENIOR_EXAM_2");
        params.put("user_level", "thinkingExpansion");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);
    }


    @Test
    public void feature_catalog_predict_answer_time() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);
        //教材
        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();
        featureParamItem.setFeatureName("catalog_predict_answer_time");
        Map<String, String> params = new HashMap<>();
        params.put("catalog_code", "3dfcf4da-2825-4d54-b371-36996509d38c");
        params.put("study_code", "SENIOR_EXAM_1");
        params.put("user_level", "none");
        params.put("biz_code", "ZSY_XXJ");
        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));
        skylabRequest.setPayload(featureParam);

        params.put("user_level", "none");
        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("user_level", "conventional");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("user_level", "highScoreAdvanced");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("user_level", "thinkingExpansion");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "conventional");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//        check(skylabResponse);
//
//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "highScoreAdvanced");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//        check(skylabResponse);
//
//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "thinkingExpansion");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//        check(skylabResponse);
    }

    @Test
    public void feature_catalog_topic_count() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId(userId);
        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();
        featureParamItem.setFeatureName("catalog_topic_count");
        Map<String, String> params = new HashMap<>();
        params.put("catalog_code", "3dfcf4da-2825-4d54-b371-36996509d38c");
        params.put("study_code", "SENIOR_EXAM_1");
        params.put("user_level", "none");
        params.put("biz_code", "ZSY_XXJ");
        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));
        skylabRequest.setPayload(featureParam);

        params.put("user_level", "none");
        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("user_level", "conventional");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("user_level", "highScoreAdvanced");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

        params.put("user_level", "thinkingExpansion");
        skylabResponse = skylabStandService.featureFetch(skylabRequest);
        check(skylabResponse);

//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "conventional");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//        check(skylabResponse);
//
//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "highScoreAdvanced");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//        check(skylabResponse);
//
//        params.put("study_code", "SENIOR_EXAM_2");
//        params.put("user_level", "thinkingExpansion");
//        skylabResponse = skylabStandService.featureFetch(skylabRequest);
//        check(skylabResponse);
    }
}