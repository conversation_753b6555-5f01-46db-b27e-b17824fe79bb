package com.iflytek.skylab.service.front.consumer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.RecTopicParam;
import com.iflytek.skylab.core.data.RecTopicResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/28 16:08
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class DynamicPathTest {

    String a = "{\"traceId\":\"df36a326-222b-434f-a96e-c1ed86767601\",\"scene\":{\"userId\":\"xiangzhang182\",\"studyCode\":\"SYNC_LEARN\",\"bizAction\":\"SYNC_REC\",\"bizCode\":\"ZSY_XXJ\",\"subjectCode\":\"02\",\"phaseCode\":\"04\",\"gradeCode\":\"10\",\"areaCode\":\"010100\",\"graphVersion\":\"vtest\",\"catalogCode\":\"01_07020101-001_04_001\",\"pressCode\":\"01\",\"bookCode\":\"01_07020101-001\",\"test\":false},\"payload\":{\"nodeId\":\"liwang-xxj-achorpointid-bvt1\",\"roundId\":\"bvt1-request\",\"topicOrderNumber\":1,\"funcCode\":\"REC_TOPIC\"}}";

    @Autowired
    private SkylabRecommendService recommendService;

    @Test
    public void pressureTest() throws InterruptedException {

        for (int i = 0; i < 4; i++) {
            int finalI = i;
            new Thread(() -> {
                while (true) {
                    sync_learn_rec_eval();
                }
            }).start();
        }
        new CountDownLatch(1).await();
    }

    @Test
    public void sync_learn_rec_eval() {
        long currentTimeMillis = System.currentTimeMillis();
        SkylabRequest<RecTopicParam> skylabRequest = JSON.parseObject(a, new TypeReference<SkylabRequest<RecTopicParam>>() {
        });
        skylabRequest.setTraceId(UUID.randomUUID().toString());
//        log.info(skylabRequest.getTraceId());
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
//        log.info(JSON.toJSONString(skylabResponse));
        if (skylabResponse.getCode() != 0) {
            log.error("异常：{}", JSON.toJSONString(skylabResponse));
        } else {
            log.info("线程：{}，topicId:{},recTotalNum:{},traceId:{},耗时：{}ms", Thread.currentThread().getName(),
                    skylabResponse.getPayload().getTopicId(),
                    skylabResponse.getPayload().getRecTotalNum(), skylabRequest.getTraceId(),
                    System.currentTimeMillis() - currentTimeMillis);
        }
    }
}
