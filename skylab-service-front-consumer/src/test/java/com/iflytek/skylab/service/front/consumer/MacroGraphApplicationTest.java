package com.iflytek.skylab.service.front.consumer;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 错题本测试用例
 * @date 2023年7月11日11:09:23
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class MacroGraphApplicationTest {
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    /**
     * 错题本变式题推荐
     */
    @Test
    public void macrographErrorBookVary() {

        SkylabRequest<RecErrorTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_ERROR_BOOK);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.MACROGRAPH_ERROR_BOOK_VARY);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-11");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        RecErrorTopicParam recErrorTopicParam = new RecErrorTopicParam();
        recErrorTopicParam.setTopicId("0a860eb5-3324-4ff1-ba7a-7e00cec43bd2");
        recErrorTopicParam.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        recErrorTopicParam.setTopicSection("020401");

        skylabRequest.setPayload(recErrorTopicParam);


        SkylabResponse<RecErrorTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecErrorTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * 测试不重题，换一换
     */
    @Test
    public void macrographErrorBookVary2() {
        List<String> topics = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            SkylabRequest<RecErrorTopicParam> skylabRequest = new SkylabRequest<>();
            String traceId = UUID.randomUUID().toString();
            log.info("traceId: {}", traceId);
            skylabRequest.setTraceId(traceId);

            SceneInfo sceneInfo = new SceneInfo();
            sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_ERROR_BOOK);
            sceneInfo.setAreaCode("000000");
            sceneInfo.setBizAction(BizActionEnum.MACROGRAPH_ERROR_BOOK_VARY);
            sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

            sceneInfo.setGradeCode("07");
            //学科
            sceneInfo.setSubjectCode("02");
            //学段
            sceneInfo.setPhaseCode("04");
            //用户id
            sceneInfo.setUserId("xiangzhang182-11");
            //教材
            sceneInfo.setPressCode("01");
            sceneInfo.setBookCode("01_08020101-002");
            sceneInfo.setCatalogCode("01_08020101-002_06");

            skylabRequest.setScene(sceneInfo);

            RecErrorTopicParam recErrorTopicParam = new RecErrorTopicParam();
            recErrorTopicParam.setTopicId("0a860eb5-3324-4ff1-ba7a-7e00cec43bd2");
            recErrorTopicParam.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
            recErrorTopicParam.setTopicSection("020401");
            recErrorTopicParam.setChange(true);

            skylabRequest.setPayload(recErrorTopicParam);

            SkylabResponse<RecErrorTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecErrorTopicResult.class);
            log.info("i={}", i);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

            for (EvaluationItem item : skylabResponse.getPayload().getEvaluationItems()) {
                if (topics.contains(item.getResNodeId())) {
//                    Assert.isTrue(false, "第{}次发生重题", i);
                } else {
                    topics.add(item.getResNodeId());
                }
            }
        }

    }

    /**
     * 错题本溯源点推题
     */
    @Test
    public void macrographErrorBookTopic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_ERROR_BOOK);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.MACROGRAPH_ERROR_BOOK_TOPIC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-3");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setRecTopicEnum(RecTopicEnum.REC_TOPIC_TRACE_POINT);
        recTopicParam.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void macrographErrorBookStudyLog() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_SYNC_TEST);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.MACROGRAPH_ERROR_BOOK_VARY);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-3");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("6e373d4b-e349-4dfd-8b43-3a3cc361f54b");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode("01_08020101-002_06_003");
        studyLogRecord.setCorrectTraceId("c4016d98-2496-4808-ae28-4db2765cfa9a");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));

    }

    @Test
    public void macrograpStudyLog() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_SYNC_TEST);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-3");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("5aaf5a95-bffc-4cc1-a012-451313bcd581");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode("01_08020101-002_06_003");
        studyLogRecord.setCorrectTraceId("c4016d98-2496-4808-ae28-4db2765cfa9a");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));


        sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_QUALITY_TEST_PAPER);
        skylabRequest.setTraceId(IdUtil.fastSimpleUUID());
        SkylabResponse<StudyLogResult> skylabResponse2 = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse2));

        sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_ERROR_BOOK);
        sceneInfo.setBizAction(BizActionEnum.MACROGRAPH_ERROR_BOOK_VARY);
        skylabRequest.setTraceId(IdUtil.fastSimpleUUID());
        SkylabResponse<StudyLogResult> skylabResponse3 = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse3));


    }

}
