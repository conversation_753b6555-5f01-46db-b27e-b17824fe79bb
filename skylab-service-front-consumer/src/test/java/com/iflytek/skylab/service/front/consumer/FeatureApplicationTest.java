package com.iflytek.skylab.service.front.consumer;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.HisStudyCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class FeatureApplicationTest {
    @Autowired
    private SkylabStandService skylabStandService;


    @Test
    public void feature() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_2);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v20");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

//        e16eb7e4#xz182#02#04#ZSY_XXJ#34e87921-cfcf-44aa-b1f6-49fc703cfcd7#ANCHOR_POINT
//        featureParamItem.setGraphVersion("v20");
        featureParamItem.setFeatureVersion(1);
        featureParamItem.setFeatureName("user_anchor_exam_recently_answer_record");
        Map<String, String> params = new HashMap<>();
        params.put("user_id", "xz182");
        params.put("biz_code", "ZSY_XXJ");
        params.put("subject_code", "02");
        params.put("phase_code", "04");
        params.put("point_code", "34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        params.put("point_type", "ANCHOR_POINT");

        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));

        skylabRequest.setPayload(featureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }


    @Test
    public void acquire_feature() {

        SkylabRequest<AcquireFeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v20");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");

        skylabRequest.setScene(sceneInfo);

        AcquireFeatureParam acquireFeatureParam = new AcquireFeatureParam();

        acquireFeatureParam.setFeatureNames(Arrays.asList("catalog_predict_answer_time"));
        acquireFeatureParam.setParams(Arrays.asList("01_08020101-002_06_003"));


        skylabRequest.setPayload(acquireFeatureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));

    }


    @Test
    public void feature1User_recently_200_answer_record() throws ExecutionException, InterruptedException {


        ExecutorService executorService = ThreadUtil.newExecutor(1);
        for (int i = 0; i < 1; i++) {
            executorService.submit(() -> {
                feature2111();
            });
        }
        ThreadUtil.safeSleep(1000000000);
    }


    public void feature2111() {

        List<String> list = Arrays.asList("11b139e1-af27-4819-bd12-4743bc9fde5d",
                "13308d1b-3537-451d-beb8-506391ea5d09",
                "956a867d-08e0-4e82-990a-68686f72db5a",
                "5d9f8de3-26c7-46d1-9b9a-0f44d49b6b4f", "1500000100081139764", "1500000100050958060", "0c772eaf-7d00-452a-be89-ab39b55f189e", "1500000100119180875", "1500000100139776560");

        for (int i = 0; i < 100000000; i++) {

            for (String userId : list) {

                SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
                String traceId = UUID.randomUUID().toString();
                log.info("traceId: {}", traceId);
                skylabRequest.setTraceId(traceId);

                SceneInfo sceneInfo = new SceneInfo();
                sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
                sceneInfo.setAreaCode("000000");
                sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
                sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v20");

                sceneInfo.setGradeCode("07");
                //学科
                sceneInfo.setSubjectCode("02");
                //学段
                sceneInfo.setPhaseCode("04");
                //用户id
                sceneInfo.setUserId(userId);
                //教材
                sceneInfo.setPressCode("01");
                sceneInfo.setBookCode("01_08020101-002");
                sceneInfo.setCatalogCode("01_08020101-002_06_003");


                skylabRequest.setScene(sceneInfo);

                FeatureParam featureParam = new FeatureParam();

                FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

//        featureParamItem.setGraphVersion("v20");
//        featureParamItem.setFeatureVersion(1);
                featureParamItem.setFeatureName("user_recently_200_answer_record");
                Map<String, String> params = new HashMap<>();
                params.put("user_id", userId);
                params.put("biz_code", "ZSY_XXJ");
                params.put("subject_code", "02");
                params.put("phase_code", "04");

                if (userId.equals("1500000100081139764")) {
                    params.put("user_id", userId);
                    params.put("biz_code", "ZSY_BYOD");
                    params.put("subject_code", "05");
                    params.put("phase_code", "04");
                    sceneInfo.setPhaseCode("04");
                    sceneInfo.setSubjectCode("05");
//                    sceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
                }
                if (userId.equals("1500000100050958060")) {
                    params.put("user_id", userId);
                    params.put("biz_code", "ZSY_BYOD");
                    params.put("subject_code", "05");
                    params.put("phase_code", "04");
                    sceneInfo.setSubjectCode("05");
                }
                if (userId.equals("1500000100119180875")) {
                    params.put("user_id", userId);
                    params.put("biz_code", "ZSY_KHSDB");
                }
                if (userId.equals("1500000100139776560")) {
                    params.put("user_id", userId);
                    params.put("biz_code", "ZSY_KHSDB");
                }

                featureParamItem.setParams(Arrays.asList(params));
                featureParam.setItems(Arrays.asList(featureParamItem));

                skylabRequest.setPayload(featureParam);

                SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
//                log.info(JSON.toJSONString(skylabResponse));
            }
        }

    }


    @Test
    public void anchor_dkt_predict_mastery() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();

        sceneInfo = new SceneInfo();

        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("420000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("aaaaaxiangzhang182_test1111");
        //教材

        sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188912");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

//        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
//        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
//        sceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
//
//        sceneInfo.setGradeCode("07");
//        //学科
//        sceneInfo.setSubjectCode("02");
//        //学段
//        sceneInfo.setPhaseCode("04");
//        //用户id
//        sceneInfo.setUserId("1500000100277953028");
//        //教材
//        sceneInfo.setPressCode("01");
//        sceneInfo.setBookCode("01_07020101-001");
//        sceneInfo.setCatalogCode("01_07020101-001_02_002");


        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

        featureParamItem.setFeatureName("anchor_dkt_predict_mastery");
        Map<String, String> params = new HashMap<>();
//        params.put("user_id", "xz182_test5");
//        params.put("biz_code", "ZSY_XXJ");
//        params.put("subject_code", "02");
//        params.put("phase_code", "04");
//        params.put("study_code", "SYNC_LEARN");
//        params.put("catalog_code", "01_08020101-002_06_003");
//        params.put("anchorpoint_code", "4c1e22a6-9537-4596-b568-321e731e6996");
//        params.put("anchorpoint_code", "fcffe335-fe4b-49d4-a4cd-776566d0f370");
        params.put("anchorpoint_code", "58d6671e-757a-4a50-83c1-d0beaa1a2e47");


        featureParamItem.setParams(Lists.newArrayList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));
        featureParam.setSimpleMode(true);
        skylabRequest.setPayload(featureParam);
//        xz182_test5#ZSY_XXJ#SYNC_LEARN#02#04#01_08020101-002_06_003#1ff2e352-cc0b-41c6-84f8-ebf7d6d06ec0
        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    @Test
    public void user_level22() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-21");
        //教材
//        sceneInfo.setPressCode("01");
//        sceneInfo.setBookCode("01_08020101-002");
//        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

        featureParamItem.setFeatureName("anchorpoint_graph_layer");

        JSONArray objects = JSON.parseArray(("[{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"253847b3-6d4d-4660-8acc-0ac56afb574d\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"253847b3-6d4d-4660-8acc-0ac56afb574d\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"ae70514d-c78c-4cc2-b80b-aa679f8dac20\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"ae70514d-c78c-4cc2-b80b-aa679f8dac20\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"e034f57b-11b5-4073-92d0-c7f6113f5c58\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"e034f57b-11b5-4073-92d0-c7f6113f5c58\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"195f7487-8d99-4c6f-aa53-c1aaf150ab38\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"195f7487-8d99-4c6f-aa53-c1aaf150ab38\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"076378be-3b3a-4fa6-86c4-ba4f3451f2b5\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"076378be-3b3a-4fa6-86c4-ba4f3451f2b5\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"8a8be044-6743-49fb-858b-4f7de41780d6\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"8a8be044-6743-49fb-858b-4f7de41780d6\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"fc377c5f-848e-494f-b32b-df01a18e6593\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"fc377c5f-848e-494f-b32b-df01a18e6593\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"ed2250f2-5ffd-436b-980a-e37559dc1857\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"ed2250f2-5ffd-436b-980a-e37559dc1857\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"7f4f2863-abc1-4d2d-a337-d973a38c74fa\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"7f4f2863-abc1-4d2d-a337-d973a38c74fa\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"b4ece79c-131b-4d84-ba3d-67650a1c48bc\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"b4ece79c-131b-4d84-ba3d-67650a1c48bc\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"dd125b82-aa27-4bd3-8de4-fc7a71b6238d\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"dd125b82-aa27-4bd3-8de4-fc7a71b6238d\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"eabfb891-931a-45da-bc7a-ad4f385bc1de\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"eabfb891-931a-45da-bc7a-ad4f385bc1de\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"ef92862a-7f52-47d6-b653-f6d8efa07ff4\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"ef92862a-7f52-47d6-b653-f6d8efa07ff4\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"5560f240-ae3e-47ef-8d39-b6484c4d20f4\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"5560f240-ae3e-47ef-8d39-b6484c4d20f4\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"4fe9d4bb-587e-4613-8b0e-73d4daef9929\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"4fe9d4bb-587e-4613-8b0e-73d4daef9929\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"95a91f47-73f2-4cc2-a704-4f511af697f6\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"95a91f47-73f2-4cc2-a704-4f511af697f6\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"bc8196d9-424b-4556-a071-5505d613c676\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"bc8196d9-424b-4556-a071-5505d613c676\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"51e4fc84-4770-4fee-a4f1-2cf7cfd9e361\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"51e4fc84-4770-4fee-a4f1-2cf7cfd9e361\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"2364f351-fe5f-4813-a149-7ac62ab43151\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"2364f351-fe5f-4813-a149-7ac62ab43151\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"7304793b-646f-4734-b058-e33fb43f5ac5\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"7304793b-646f-4734-b058-e33fb43f5ac5\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"9e1741ab-9d3f-46cd-b32a-7b91e3bf8e4a\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"9e1741ab-9d3f-46cd-b32a-7b91e3bf8e4a\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"fc51ef64-41ba-4b5b-ae49-87e3f9146be5\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"fc51ef64-41ba-4b5b-ae49-87e3f9146be5\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"9c8b5e9f-f99f-4e42-a818-ef2b8b9e25af\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"9c8b5e9f-f99f-4e42-a818-ef2b8b9e25af\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"50cec173-3202-460c-bd8f-bd5917f7806f\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"50cec173-3202-460c-bd8f-bd5917f7806f\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"a750e90f-8557-492e-91ee-4a808f9981a9\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"a750e90f-8557-492e-91ee-4a808f9981a9\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"faa29fed-6d8c-4a45-aee7-e00939f7a292\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"faa29fed-6d8c-4a45-aee7-e00939f7a292\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"1bfbcf17-f593-4923-bccd-95829b4c616f\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"1bfbcf17-f593-4923-bccd-95829b4c616f\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"58d6671e-757a-4a50-83c1-d0beaa1a2e47\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"58d6671e-757a-4a50-83c1-d0beaa1a2e47\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"c5946ebb-3625-4354-afb8-17c0452de1ad\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"c5946ebb-3625-4354-afb8-17c0452de1ad\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"348b74a4-b77b-4c61-8f97-e0d1152a8715\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"348b74a4-b77b-4c61-8f97-e0d1152a8715\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"8bddbc1d-72e4-45da-959b-715748e888ed\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"8bddbc1d-72e4-45da-959b-715748e888ed\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"9c5231fa-439a-47cf-a2d7-b39a98172072\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"9c5231fa-439a-47cf-a2d7-b39a98172072\",\"study_code\":\"SYNC_OS\"}]"));

//        Map<String, String> params = new HashMap<>();
//        params.put("user_id", "xz182");
//        params.put("biz_code", "ZSY_XXJ");
//        params.put("subject_code", "02");
//        params.put("phase_code", "04");

        List<Map<String, String>> list = JSON.parseObject(objects.toString(), new TypeReference<List<Map<String, String>>>() {
        });
        featureParamItem.setParams(list);

        featureParam.setItems(Arrays.asList(featureParamItem));
        featureParam.setSimpleMode(false);
        skylabRequest.setPayload(featureParam);
        log.info(JSON.toJSONString(skylabRequest));
        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }


    @Test
    public void user_level223() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-21");
        //教材
//        sceneInfo.setPressCode("01");
//        sceneInfo.setBookCode("01_08020101-002");
//        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = JSONUtil.toBean(FileUtil.readUtf8String("D:\\Download\\历史作答入参.txt"), FeatureParam.class);
//
//        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();
//
////        featureParamItem.setFeatureName("anchorpoint_graph_layer");
//        featureParamItem.setFeatureName("chapter_study_time");
//
////        JSONArray objects = JSON.parseArray((" [\n" +
////                "                {\n" +
////                "                    \"subject_code\": \"02\",\n" +
////                "                    \"phase_code\": \"04\",\n" +
////                "                    \"biz_code\": \"ZSY_XXJ\",\n" +
////                "                    \"area_code\": \"000000\",\n" +
////                "                    \"item_identify_key\": \"2fae4060-35c1-436e-8442-6a2e3e182037\",\n" +
////                "                    \"catalog_code\": \"01_08020201-002_004_002_period20\",\n" +
////                "                    \"anchorpoint_code\": \"2fae4060-35c1-436e-8442-6a2e3e182037\",\n" +
////                "                    \"study_code\": \"SYNC_LEARN\"\n" +
////                "                }\n" +
////                "            ]"));
//
//        JSONArray objects = JSON.parseArray(("[\n" +
//                "\t\t\t\t{\n" +
//                "\t\t\t\t\t\"user_study_level\": \"good\",\n" +
//                "\t\t\t\t\t\"item_identify_key\": \"01_0602020101-1357_0602020101-1357-50293_0602020101-1357-50301\",\n" +
//                "\t\t\t\t\t\"chapter_id\": \"01_0602020101-1357_0602020101-1357-50293_0602020101-1357-50301\"\n" +
//                "\t\t\t\t}\n" +
//                "\t\t\t]"));
//
////        Map<String, String> params = new HashMap<>();
////        params.put("user_id", "xz182");
////        params.put("biz_code", "ZSY_XXJ");
////        params.put("subject_code", "02");
////        params.put("phase_code", "04");"
//
//        List<Map<String, String>> list = JSON.parseObject(objects.toString(), new TypeReference<List<Map<String, String>>>() {
//        });
//        featureParamItem.setParams(list);
//
//        featureParam.setItems(Arrays.asList(featureParamItem));
//        featureParam.setSimpleMode(false);
        skylabRequest.setPayload(featureParam);
//        log.info(JSON.toJSONString(skylabRequest));
        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }
}
