package com.iflytek.skylab.service.front.consumer;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class AiDiagApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;


    @Test
    public void ai_diag_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.NONE);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("zx21");
        //教材
        aiDiagSceneInfo.setPressCode("408");
        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"e9b0cec6-fc49-42a8-b421-e6512aef6322\":\"408_07020176408-6473_07020176408-6473-192459_07020176408-6473-192460_07020176408-6473-192461\",\n" +
                "\t\t\t\"cdd26b5f-f1d5-4ea1-9f72-864502f632ba\":\"408_07020176408-6473_07020176408-6473-192459_07020176408-6473-192460_07020176408-6473-192462\"\n" +

                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        skylabRequest.setScene(aiDiagSceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();

        recNodeParam.setCatalogIds(Arrays.asList("e9b0cec6-fc49-42a8-b421-e6512aef6322", "cdd26b5f-f1d5-4ea1-9f72-864502f632ba"));

        JSONObject recExt = new JSONObject();
        recExt.fluentPut("anchorCntLimit", 1);
        recNodeParam.setRecExt(recExt);
        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void ai_diag_rec_node2() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.NONE);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("zx21");
        //教材
        aiDiagSceneInfo.setPressCode("278");
        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"07e1572f-4f99-4cad-a8ec-57d7c1102d13\":\"278_08020107278-9309_08020107278-9309-231854\",\n" +
                "\t\t\t\"932389e6-5edb-4c1b-8034-94e8e97a0104\":\"278_08020107278-9309_08020107278-9309-231826\"\n" +

                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        skylabRequest.setScene(aiDiagSceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();

        recNodeParam.setCatalogIds(Arrays.asList("07e1572f-4f99-4cad-a8ec-57d7c1102d13", "932389e6-5edb-4c1b-8034-94e8e97a0104"));

        JSONObject recExt = new JSONObject();
        recExt.fluentPut("anchorCntLimit", 1);
        recNodeParam.setRecExt(recExt);
        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void ai_diag_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.NONE);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("zx21");
        //教材
        aiDiagSceneInfo.setPressCode("408");
        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"cdd26b5f-f1d5-4ea1-9f72-864502f632ba\":\"408_07020176408-6473_07020176408-6473-192459_07020176408-6473-192460_07020176408-6473-192462\"\n" +

                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("cdd26b5f-f1d5-4ea1-9f72-864502f632ba");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void ai_diag_rec_topic_pack() {
        SkylabRequest<RecTopicPackParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("340100");
        aiDiagSceneInfo.setBizAction(BizActionEnum.AI_DIAGNOSIS_REC_PACK);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
        //教材
        aiDiagSceneInfo.setPressCode("01");
        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\",\n" +
                "\t\t\t\"ac935ac1-bbdf-4654-bddc-7a0a9e97beb3\":\"01_08020101-002_06_003\",\n" +
                "\t\t\t\"f6ecc8a2-00d7-4c5f-be7e-8a38245823ff\":\"01_07020101-001_02_001\"\n" +
                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        RecTopicPackParam recTopicPackParam = new RecTopicPackParam();
        recTopicPackParam.setNodeIds(Arrays.asList("34e87921-cfcf-44aa-b1f6-49fc703cfcd7", "ac935ac1-bbdf-4654-bddc-7a0a9e97beb3", "f6ecc8a2-00d7-4c5f-be7e-8a38245823ff"));
        skylabRequest.setPayload(recTopicPackParam);
        SkylabResponse<RecTopicPackResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicPackResult.class);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    @Test
    public void ai_diag_master_fetchV2() {

        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo aiDiagSceneInfo = new SceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setStudyCode(StudyCodeEnum.AI_DIAG);
        aiDiagSceneInfo.setBizAction(BizActionEnum.NONE);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("zx5");
        //教材
        aiDiagSceneInfo.setPressCode("01");
        //特殊字段处理
//        String nodeCatalogMapString = "{\n" +
//                "\t\t\t\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\",\n" +
//                "\t\t\t\"ac935ac1-bbdf-4654-bddc-7a0a9e97beb3\":\"01_08020101-002_06_003\",\n" +
//                "\t\t\t\"f6ecc8a2-00d7-4c5f-be7e-8a38245823ff\":\"01_07020101-001_02_001\"\n" +
//                "\t\t}";
//        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
//        });
//        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();
        NodeInfo nodeInfo = new NodeInfo().setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7").setCatalogId("01_08020101-002_06_003").setNodeType(NodeTypeEnum.ANCHOR_POINT);
        NodeInfo nodeInfo2 = new NodeInfo().setNodeId("ac935ac1-bbdf-4654-bddc-7a0a9e97beb3").setCatalogId("01_08020101-002_06_003").setNodeType(NodeTypeEnum.ANCHOR_POINT);
        NodeInfo nodeInfo3 = new NodeInfo().setNodeId("f6ecc8a2-00d7-4c5f-be7e-8a38245823ff").setCatalogId("01_07020101-001_02_001").setNodeType(NodeTypeEnum.ANCHOR_POINT);

        masterFetch5NodeParam.setNodeInfos(Lists.newArrayList(nodeInfo, nodeInfo2, nodeInfo3));

//        MasterFetch4NodeParam masterFetch4NodeParam = new MasterFetch4NodeParam();
//        masterFetch4NodeParam.setNodeIds(Arrays.asList("34e87921-cfcf-44aa-b1f6-49fc703cfcd7", "ac935ac1-bbdf-4654-bddc-7a0a9e97beb3", "f6ecc8a2-00d7-4c5f-be7e-8a38245823ff"));
        skylabRequest.setPayload(masterFetch5NodeParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }



    @Test
    public void ai_diag_study_log() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.PHOTO_INPUT);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("zx1112aaaa");
        //教材
        aiDiagSceneInfo.setPressCode("278");

        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"932389e6-5edb-4c1b-8034-94e8e97a0104\":\"278_08020107278-9309_08020107278-9309-231826\"\n" +
                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("932389e6-5edb-4c1b-8034-94e8e97a0104");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("1b561815-aa96-48fa-bc9c-445c23deb1ac");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("278_08020107278-9309");
        studyLogRecord.setCatalogCode("278_08020107278-9309_08020107278-9309-231826");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, aiDiagSceneInfo.getUserId());
        log.info(JSON.toJSONString(skylabResponse));

        ai_diag_master_fetch();
    }
    @Test
    public void ai_diag_master_fetch() {
        SkylabRequest<MasterFetch4NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.NONE);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("zx1112aaaa");
        //教材
        aiDiagSceneInfo.setPressCode("278");
        //特殊字段处理
//        String nodeCatalogMapString = "{\n" +
//                "\t\t\t\"e9b0cec6-fc49-42a8-b421-e6512aef6322\":\"408_07020176408-6473_07020176408-6473-192459_07020176408-6473-192460_07020176408-6473-192461\",\n" +
//                "\t\t\t\"cdd26b5f-f1d5-4ea1-9f72-864502f632ba\":\"408_07020176408-6473_07020176408-6473-192459_07020176408-6473-192460_07020176408-6473-192462\"\n" +
//                "\t\t}";

        String nodeCatalogMapString = "{\n" +
//                "\t\t\t\"07e1572f-4f99-4cad-a8ec-57d7c1102d13\":\"278_08020107278-9309_08020107278-9309-231854\",\n" +
                "\t\t\t\"932389e6-5edb-4c1b-8034-94e8e97a0104\":\"278_08020107278-9309_08020107278-9309-231826\"\n" +
                "\t\t}";

        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        MasterFetch4NodeParam masterFetch4NodeParam = new MasterFetch4NodeParam();
        masterFetch4NodeParam.setNodeIds(Arrays.asList("932389e6-5edb-4c1b-8034-94e8e97a0104"));
        skylabRequest.setPayload(masterFetch4NodeParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));

    }


    @Test
    public void ai_diag_study_log2() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.PHOTO_INPUT);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("05");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("zx1112aaaa");
        //教材
        aiDiagSceneInfo.setPressCode("272");

        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"4918aa14-4830-494b-9669-56cf7f77f083\":\"272_09050301272-9343_09050301272-9343-233646\"\n" +
                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("4918aa14-4830-494b-9669-56cf7f77f083");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("22f996cb-6cc2-4062-b3f7-f3da38dae5c8");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("272_09050301272-9343");
        studyLogRecord.setCatalogCode("272_09050301272-9343_09050301272-9343-233646");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, aiDiagSceneInfo.getUserId());
        log.info(JSON.toJSONString(skylabResponse));

        ai_diag_master_fetch2();
    }
    @Test
    public void ai_diag_master_fetch2() {
        SkylabRequest<MasterFetch4NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.NONE);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("05");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("zx1112aaaa");
        //教材
        aiDiagSceneInfo.setPressCode("272");

        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"4918aa14-4830-494b-9669-56cf7f77f083\":\"272_09050301272-9343_09050301272-9343-233646\"\n" +
                "\t\t}";

        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        MasterFetch4NodeParam masterFetch4NodeParam = new MasterFetch4NodeParam();
        masterFetch4NodeParam.setNodeIds(Arrays.asList("4918aa14-4830-494b-9669-56cf7f77f083"));
        skylabRequest.setPayload(masterFetch4NodeParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));

    }

    @Test
    public void lxw_test_StudyLogParam() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.PHOTO_INPUT);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
        //教材
        aiDiagSceneInfo.setPressCode("01");

        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\"\n" +
                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("5aaf5a95-bffc-4cc1-a012-451313bcd581");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode("01_08020101-002_06_003");
        studyLogRecord.setCorrectTraceId("lxwCorrectTraceID01");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void lxw_test_StudyCorrectLogParam() {

        SkylabRequest<StudyCorrectLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("000000");
        aiDiagSceneInfo.setBizAction(BizActionEnum.PHOTO_INPUT);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        aiDiagSceneInfo.setGraphVersion("v10");

        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
        //教材
        aiDiagSceneInfo.setPressCode("01");

        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\"\n" +
                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        StudyCorrectLogParam studyCorrectLogParam = new StudyCorrectLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("5aaf5a95-bffc-4cc1-a012-451313bcd581");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode("01_08020101-002_06_003");

        studyCorrectLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setPayload(studyCorrectLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }
}