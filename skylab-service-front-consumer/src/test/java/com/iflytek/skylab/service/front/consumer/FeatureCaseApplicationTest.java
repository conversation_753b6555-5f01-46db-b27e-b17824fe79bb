package com.iflytek.skylab.service.front.consumer;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.FeatureParam;
import com.iflytek.skylab.core.data.FeatureResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class FeatureCaseApplicationTest {
    @Autowired
    private SkylabStandService skylabStandService;

    @Test
    public void featureByFile() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("ZX182");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

        List<File> aCase = FileUtil.loopFiles("case");
        for (File file : aCase) {
//            log.info("file={}", file.getName());
            List<String> list = FileUtil.readUtf8Lines(file);
            for (String s : list) {
//                log.info(s);
            }
            String featname = StrUtil.replace(file.getName(), ".txt", "");

            featureParamItem.setFeatureName(featname);

            List<Map<String, String>> listM = new ArrayList<>();

            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    continue;
                }
                Map<String, String> params = new HashMap<>();
                String s = list.get(0);
                List<String> key = StrUtil.split(s, ",");
                List<String> value = StrUtil.split(list.get(i), "#");

                for (int k = 0; k < key.size(); k++) {
                    params.put(key.get(k), value.get(k));
                }
                listM.add(params);
            }

            featureParamItem.setParams(listM);
            featureParam.setItems(Arrays.asList(featureParamItem));
            skylabRequest.setPayload(featureParam);
            System.err.println();
            System.err.println();
//            log.info("request={}", JSONUtil.toJsonStr(skylabRequest));
            SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
            System.err.println(file.getName() + "，response=" + JSONUtil.toJsonPrettyStr(skylabResponse));
            for (FeatureResult.FeatureResultItem item : skylabResponse.getPayload().getItems()) {
                Assert.isTrue(featname.equals(item.getFeatureName()), "特征名称不对");
                for (Map<String, String> value : item.getValues()) {
                    Assert.isTrue(StrUtil.isNotBlank(value.get(featname)), "特征结果不会null");
                }
            }
        }

    }

}
