package com.iflytek.skylab.service.front.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.service.front.consumer.util.SubCollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class PrimarySyncLearnApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    @Before
    public void get() {
        SubCollectionUtils.getSubUserMasteryRecordCollectionName("zx182-18", null);
    }

    @Test
    public void sync_learn_rec_eval() {
        String roundId = UUID.randomUUID().toString();

        for (int i = 0; i < 1; i++) {
            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId = UUID.randomUUID().toString();
            log.info("traceId: {}", traceId);
            skylabRequest.setTraceId(traceId);

            SceneInfo sceneInfo = new SceneInfo();
            sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
            sceneInfo.setAreaCode("000000");
            sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
            sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

            sceneInfo.setGradeCode("01");
            //学科
            sceneInfo.setSubjectCode("02");
            //学段
            sceneInfo.setPhaseCode("03");
            //用户id
            sceneInfo.setUserId("zx182-18");
            //教材
            sceneInfo.setPressCode("01");
            sceneInfo.setBookCode("01_01020101-001");
            sceneInfo.setCatalogCode("01_01020101-001_03_period10");

            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
            recEval4InOutParam.setTopicOrderNumber(i + 1);
            recEval4InOutParam.setRoundId(roundId);
            recEval4InOutParam.setCatalogIds(Arrays.asList("01_01020101-001_03_period10"));
            skylabRequest.setPayload(recEval4InOutParam);

            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
            RecEval4InOutResult payload = skylabResponse.getPayload();
            List<EvaluationItem> evaluationItems = payload.getEvaluationItems();
            EvaluationItem evaluationItem = evaluationItems.get(0);
            ThreadUtil.safeSleep(2000);
            sync_learn_study_log(recEval4InOutParam.getTopicOrderNumber(), evaluationItem.getNodeId(), evaluationItem.getResNodeId(), traceId, recEval4InOutParam.getRoundId());
            ThreadUtil.safeSleep(2000);

            sync_learn_master_fetch();
        }

    }

    @Test
    public void sync_learn_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-18");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_01020101-001");
        sceneInfo.setCatalogCode("01_01020101-001_03_period10");

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("01_01020101-001_03_period10"));

        Map<String, Object> recExt = new HashMap<>();
        recExt.put("anchorCntLimit", 1);
        recNodeParam.setRecExt(recExt);
        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }


    //ToDO 重新验证
    @Test
    public void sync_learn_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-18");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_01020101-001");
        sceneInfo.setCatalogCode("01_01020101-001_03_period10");

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("efcc67a5-633c-41a8-a741-01e36365a4ea");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    @Test
    public void sync_learn_master_fetch() {

        SkylabRequest<MasterFetch4CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("06");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("8d5fb5ad-0dbf-488a-b9e4-eee9a57f4597");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_06020101-003");
        sceneInfo.setCatalogCode("01_06020101-003_02_period10");

        skylabRequest.setScene(sceneInfo);

        MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();
        masterFetch4CatalogParam.setCatalogIds(Arrays.asList("01_06020101-003_02_period10"));
//        masterFetch4CatalogParam
        skylabRequest.setPayload(masterFetch4CatalogParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    //    @Test
    public void sync_learn_study_log(int idnex, String nodeId, String topicId, String refTraceId, String roundId) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-18");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_01020101-001");
        sceneInfo.setCatalogCode("01_01020101-001_03_period10");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
//        studyLogRecord.setNodeId("efcc67a5-633c-41a8-a741-01e36365a4ea");
        studyLogRecord.setNodeId(nodeId);
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId(topicId);
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId(roundId);
        studyLogRecord.setRefTraceId(refTraceId);
        studyLogRecord.setRoundIndex(idnex + "");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_01020101-001");
        studyLogRecord.setCatalogCode("01_01020101-001_03_period10");


        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }


    public void sync_learn_study_log1() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-18");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_04020101-003");
        sceneInfo.setCatalogCode("01_04020101-003_02_001_period10");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
//        studyLogRecord.setNodeId("efcc67a5-633c-41a8-a741-01e36365a4ea");
        studyLogRecord.setNodeId("a98e48f5-198f-4fe8-bade-949edbab49bf");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("bbbfe996-f8a3-4487-b981-084787801fb");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("11111");
        studyLogRecord.setRefTraceId("2232423");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(0.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_04020101-003");
        studyLogRecord.setCatalogCode("01_04020101-003_02_001_period10");


        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    public void sync_learn_study_log2() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-18");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_04020101-003");
        sceneInfo.setCatalogCode("01_04020101-003_02_001_period20");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
//        studyLogRecord.setNodeId("efcc67a5-633c-41a8-a741-01e36365a4ea");
        studyLogRecord.setNodeId("85de0a00-be77-4b02-8dc7-8d566a4772bd");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("1838204a-0bf0-44d4-8388-01132130a833");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("11111");
        studyLogRecord.setRefTraceId("2232423");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(0.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_04020101-003");
        studyLogRecord.setCatalogCode("01_04020101-003_02_001_period20");


        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    public void sync_learn_study_log3() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-18");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_06020101-003");
        sceneInfo.setCatalogCode("01_06020101-003_02_period10");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("4457aec0-2097-40ff-b6a2-b4fb33915625");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("dec9c950-eb29-4d8f-933f-4a23f6377e2f");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("11111");
        studyLogRecord.setRefTraceId("2232423");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(0.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_06020101-003");
        studyLogRecord.setCatalogCode("01_06020101-003_02_period10");


        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    String data = "8a536a7a-130f-49f7-860b-4296a11c5432=01_01020101-001_02_period20=b3b793ef-d1fc-43b3-8bd7-34e38335c464\n" +
            "d631c763-b6de-478f-b20b-d349fb3df824=01_01020101-001_06_period40=ec4dee4f-7172-4818-b288-ea1fda436faa\n" +
            "9bbd48b3-366b-4d12-909d-dd19d90616e8=01_01020101-001_10_period20=1c98f186-c260-4ed9-b7f9-f84bd65d62d1\n" +
            "a81a899b-682f-4c9e-ba44-f0268f02e686=01_06020101-003_02_period20=dc3234a7-7100-4c67-a6eb-603590778408\n" +
            "8fa1bc4b-d631-4ec9-aed1-e82a9a0abb6f=01_06020101-003_09_period10=7abf9f54-2ba1-49f7-9991-cbda049c8b43\n" +
            "7ab4d070-8846-4140-b812-4b6d5aeb286a=01_01020101-001_04_period30=de34f66c-2cbb-42f5-aad2-2adb28072927\n" +
            "d271d4d4-51c3-4c88-86ef-0de4b210af46=01_01020101-001_10_period40=513280cc-b3ea-492d-ba53-916c0536b946\n" +
            "efb6f2b1-9c86-4050-8174-3b1cc16f0109=01_04020101-003_08_period10=a70d80c6-2de2-43cd-ae17-eee131a2305d\n" +
            "6c941566-9e8f-4540-a1b2-35ebb7728dcd=01_05020101-003_04_period10=40d0e54e-9045-4def-aeda-f0af39f0030b\n" +
            "b0c9ef5c-27a2-4eb0-9c93-731dd5123c61=01_02020101-003_02_period30=426802db-bd2d-47fa-ae17-66b453efdfa9\n" +
            "2794f6d4-9d3f-4d12-8782-0b282fa134df=01_01020101-001_10_period30=66feab42-c8c6-44ba-83fc-83d07f88be0b\n" +
            "a253658c-b33a-41a9-b366-2c1284f518c9=01_01020101-001_03_period20=ed908a00-ff3c-4561-b56a-66370d15dfe5\n" +
            "52ec54ee-ec1a-4b96-9743-19d99b2c3bb3=01_01020101-001_09_period10=2c3f19cd-6cec-4ce0-ace0-b045062bc5be\n" +
            "19d7d720-21d5-4a08-856a-9003f8077b8e=01_03020101-003_03_period10=858f24f0-1728-436e-9b13-2e5f42bb61dd\n" +
            "b5812238-d163-4b42-811f-36fef558e324=01_04020101-003_05_period20=01e21d75-2a57-4c6d-a037-3cc698dbcda3\n" +
            "5069bb39-658a-4c77-b9f3-19e9af12d913=01_04020101-003_09_period10=9c197561-5911-4e96-813f-c9bae275fe25\n" +
            "de368f7c-ce2a-4c97-8b50-980ff74450f1=01_01020101-001_06_period50=9d6a03ad-d172-4fb5-9276-685fd5f5e119\n" +
            "eaa30da3-4821-4f56-9882-c98b575f2128=01_01020101-001_02_period10=430fa88c-3e28-433c-8b08-6f460c005a63\n" +
            "b735ba05-ad90-4375-adab-0cabbc55cf93=01_03020101-003_07_002_period10=3fa70353-087e-4076-96bf-d79b763ac375\n" +
            "8edf3915-9585-4039-b436-88d46148894c=01_03020101-003_07_002_period20=17b8f28e-d454-4f31-be17-da2b173a465b\n" +
            "cb954ebe-c421-4126-bb1f-061c66332fc7=01_05020101-003_02_period10=5ab18350-5cc7-4f42-ac65-9f97be366ddb\n" +
            "6b4c9843-85da-4e45-8613-afd3492e73f3=01_02020101-003_05_period40=2221a0a7-b398-4559-9564-69bdce187965\n" +
            "d86e804c-6a8f-4a31-b62b-057ab93db771=01_04020101-003_05_period30=9af6f70c-a4a6-483e-b685-1f1b45b1ee88\n" +
            "54aafdd0-3c8a-44a0-ae15-1bc6f4d7845e=01_01020101-001_10_period10=8f7bb967-00ec-4084-8188-e57e507f5c35\n" +
            "23e116ed-5d71-46c8-b71f-21d32b15afb5=01_04020101-003_10_period10=e62d03bd-b7de-4874-9eb6-7424d55d7798\n" +
            "bf535f04-058b-4ae3-aee9-a6394c804ad8=01_01020101-001_07_period10=5e262065-49dd-479a-b09b-9b44293f7eb1\n" +
            "d2ffb888-9f5a-4d5a-a743-327886dbe06b=01_01020101-001_04_period50=b3f15cec-b8e4-4bbc-8ba0-d2a1ecc42949\n" +
            "89096758-754e-41e9-99c2-2ac31632e0a4=01_02020101-003_04_period80=c3e19842-3823-4fa7-b1c2-2f16e8911b29\n" +
            "efcc67a5-633c-41a8-a741-01e36365a4ea=01_01020101-001_03_period10=30ecd7bf-62fd-4a22-9e1a-c5e6f63422b6\n" +
            "daa35b40-f5ef-468b-bc4b-4c7e7eb1e3e0=01_06020101-003_08_period20=930c6e9e-1256-488c-a2ac-63fb2dc2bf7c\n" +
            "439e6c13-c653-4085-88b7-0167fb3f3ecd=01_06020101-003_05_period20=3146c469-84b7-4a5d-8232-55affbee64c9\n" +
            "8f44f743-f4b3-4dd7-a603-b4991723c1de=01_01020101-001_06_period30=a405db8b-2cf7-4101-b8df-fc8ec2bec5e3\n" +
            "4b861b4f-b826-4a0f-a263-b9916a539068=01_02020101-003_10_period50=36b8e275-35d2-4ec3-8ec0-cf07cbfec6d6\n" +
            "15742bbc-f040-47fa-9742-94878ef59437=01_02020101-003_05_period20=e85c9472-0a02-4eef-b343-5c7b18b709ef\n" +
            "b17e9a04-b075-481d-9309-6b4411a5c021=01_01020101-001_04_period60=5502660a-54c6-43b9-8fb8-1a870b987f77\n" +
            "a81300ee-e3d4-4f80-8fe8-0b606a4dddea=01_01020101-001_04_period70=fa567283-b4c3-4596-a1cf-50ae73eb35c5\n" +
            "fd0072d8-49bb-4c6c-82e8-6e5205b45725=01_02020101-003_06_period60=50a94b60-d17c-46e2-9a6d-9cc24c6c3ed2\n" +
            "820804e4-1346-4bed-b735-eaf5be968720=01_02020101-003_03_period60=bcbecb50-2867-45a2-bde3-a8e904a687b4\n" +
            "57b6dc28-c54d-4182-bcf8-cb095505bfb5=01_01020101-001_04_period10=3e8b7c4f-1c3e-4756-8243-b573259e8eb7\n" +
            "29016cca-09ce-4400-8890-6346da677f75=01_06020101-003_11_period10=b9021cbc-dd98-4f01-8822-e6b1408d2fe0";

    @Test
    public void sync_learn_node_master_fetch() {
//        sync_learn_study_log1();
//        sync_learn_study_log2();
//        sync_learn_study_log3();


        SkylabRequest<MasterFetch4NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo sceneInfo = new AiDiagSceneInfo();
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.PRIMARY_STAGE_TEST);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-18");
        //教材
        sceneInfo.setPressCode("01");

        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"a98e48f5-198f-4fe8-bade-949edbab49bf\":\"01_04020101-003_02_001_period10\",\n" +
                "\t\t\t\"85de0a00-be77-4b02-8dc7-8d566a4772bd\":\"01_04020101-003_02_001_period20\",\n" +
                "\t\t\t\"4457aec0-2097-40ff-b6a2-b4fb33915625\":\"01_06020101-003_02_period10\"\n" +
                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        sceneInfo.setNodeCatalogMap(nodeCatalogMap);

        skylabRequest.setScene(sceneInfo);

        MasterFetch4NodeParam masterFetch4NodeParam = new MasterFetch4NodeParam();
        masterFetch4NodeParam.setNodeIds(Arrays.asList("a98e48f5-198f-4fe8-bade-949edbab49bf", "85de0a00-be77-4b02-8dc7-8d566a4772bd", "4457aec0-2097-40ff-b6a2-b4fb33915625"));
        skylabRequest.setPayload(masterFetch4NodeParam);


        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    @Test
    public void sync_learn_node_master_fetch2() throws ExecutionException, InterruptedException {
//        sync_learn_study_log1();
//        sync_learn_study_log2();
//        sync_learn_study_log3();

        List<String> anchors = new ArrayList<>();
        List<String> split = StrUtil.split(data, "\n");
        for (String s : split) {
            List<String> split1 = StrUtil.split(s, "=");
            anchors.add(split1.get(0));
        }


        SkylabRequest<MasterFetch4NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.AI_DIAG);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-18");
        //教材
        sceneInfo.setPressCode("01");

        skylabRequest.setScene(sceneInfo);

        MasterFetch4NodeParam masterFetch4NodeParam = new MasterFetch4NodeParam();
//        masterFetch4NodeParam.setNodeIds(Arrays.asList("8a536a7a-130f-49f7-860b-4296a11c5432", "d631c763-b6de-478f-b20b-d349fb3df824", "9bbd48b3-366b-4d12-909d-dd19d90616e8", "a81a899b-682f-4c9e-ba44-f0268f02e686", "8fa1bc4b-d631-4ec9-aed1-e82a9a0abb6f", "7ab4d070-8846-4140-b812-4b6d5aeb286a", "d271d4d4-51c3-4c88-86ef-0de4b210af46", "efb6f2b1-9c86-4050-8174-3b1cc16f0109", "6c941566-9e8f-4540-a1b2-35ebb7728dcd", "b0c9ef5c-27a2-4eb0-9c93-731dd5123c61", "2794f6d4-9d3f-4d12-8782-0b282fa134df", "a253658c-b33a-41a9-b366-2c1284f518c9", "52ec54ee-ec1a-4b96-9743-19d99b2c3bb3", "19d7d720-21d5-4a08-856a-9003f8077b8e", "b5812238-d163-4b42-811f-36fef558e324", "5069bb39-658a-4c77-b9f3-19e9af12d913", "de368f7c-ce2a-4c97-8b50-980ff74450f1", "eaa30da3-4821-4f56-9882-c98b575f2128", "b735ba05-ad90-4375-adab-0cabbc55cf93", "8edf3915-9585-4039-b436-88d46148894c", "cb954ebe-c421-4126-bb1f-061c66332fc7", "6b4c9843-85da-4e45-8613-afd3492e73f3", "d86e804c-6a8f-4a31-b62b-057ab93db771", "54aafdd0-3c8a-44a0-ae15-1bc6f4d7845e", "23e116ed-5d71-46c8-b71f-21d32b15afb5", "bf535f04-058b-4ae3-aee9-a6394c804ad8", "d2ffb888-9f5a-4d5a-a743-327886dbe06b", "89096758-754e-41e9-99c2-2ac31632e0a4", "efcc67a5-633c-41a8-a741-01e36365a4ea", "daa35b40-f5ef-468b-bc4b-4c7e7eb1e3e0", "439e6c13-c653-4085-88b7-0167fb3f3ecd", "8f44f743-f4b3-4dd7-a603-b4991723c1de", "4b861b4f-b826-4a0f-a263-b9916a539068", "15742bbc-f040-47fa-9742-94878ef59437", "b17e9a04-b075-481d-9309-6b4411a5c021", "a81300ee-e3d4-4f80-8fe8-0b606a4dddea", "fd0072d8-49bb-4c6c-82e8-6e5205b45725", "820804e4-1346-4bed-b735-eaf5be968720", "57b6dc28-c54d-4182-bcf8-cb095505bfb5", "1a6d4b8e-5543-4a77-90c1-8ea7b95d3ab9"));
        List<List<String>> split1 = CollUtil.split(anchors, 10);
        log.info(split1.get(0).toString());
        masterFetch4NodeParam.setNodeIds(split1.get(0));
        skylabRequest.setPayload(masterFetch4NodeParam);

        List<Future<Boolean>> aa = new ArrayList<>();
        int core = 1;
        ExecutorService executorService = ThreadUtil.newExecutor(core);

        CopyOnWriteArrayList<Long> list = new CopyOnWriteArrayList<>();

        for (int i = 0; i < 1; i++) {
            Future<Boolean> submit = executorService.submit(() -> {
                long currentTimeMillis = System.currentTimeMillis();
                SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
                System.err.println(JSONUtil.toJsonPrettyStr(skylabResponse));
//                log.info("耗时：{}", System.currentTimeMillis() - currentTimeMillis);
                long aaa = System.currentTimeMillis() - currentTimeMillis;
//                System.err.println(aaa);
                list.add(aaa);
                Assert.isTrue(skylabResponse.getCode() == 0);
                return true;
            });
            aa.add(submit);
        }

        for (Future<Boolean> future : aa) {
            Boolean aBoolean = future.get();
            if (aBoolean) {
                double avg = list.stream().reduce((a, b) -> a + b).get() / list.size();
                log.info("线程数：{}，最大值：{}，最小值：{}，平均值：{}", core, CollUtil.max(list), CollUtil.min(list), avg);
            }
        }


//        String toJsonPrettyStr = JSONUtil.toJsonStr(skylabResponse);
//        if (toJsonPrettyStr.contains("error")) {
//            log.error("兜底了");
//        }

//        log.info(toJsonPrettyStr);
    }

    @Test
    public void genLog() {

        List<String> split = StrUtil.split(data, "\n");
        for (String s : split) {
            List<String> split1 = StrUtil.split(s, "=");
            sync_learn_study_log1(split1.get(1), split1.get(0), split1.get(2));
            sync_learn_study_log1(split1.get(1), split1.get(0), split1.get(2));
            sync_learn_study_log1(split1.get(1), split1.get(0), split1.get(2));
            sync_learn_study_log1(split1.get(1), split1.get(0), split1.get(2));
            sync_learn_study_log1(split1.get(1), split1.get(0), split1.get(2));
        }
    }

    public void sync_learn_study_log1(String cata, String anchor, String topic) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-18");
        //教材
        sceneInfo.setPressCode("01");

        String[] s = cata.split("_");
        sceneInfo.setBookCode(s[0] + "_" + s[1]);
        sceneInfo.setCatalogCode(cata);


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
//        studyLogRecord.setNodeId("efcc67a5-633c-41a8-a741-01e36365a4ea");
        studyLogRecord.setNodeId(anchor);
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId(topic);
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId(traceId);
        studyLogRecord.setRefTraceId(UUID.randomUUID().toString());
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);

        studyLogRecord.setBookCode(sceneInfo.getBookCode());
        studyLogRecord.setCatalogCode(sceneInfo.getCatalogCode());


        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

}
