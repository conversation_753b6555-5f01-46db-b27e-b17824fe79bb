package com.iflytek.skylab.service.front.consumer.syncos;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.service.front.consumer.CheckResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * OS备考
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncOsExamApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabStandService skylabStandService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    static SceneInfo sceneInfo = null;

    static HisStudyCodeEnum his = HisStudyCodeEnum.MID_EXAM;

    static {
        sceneInfo = new SceneInfo();

//        sceneInfo.setGraphVersion("20240401_002");
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setHisStudyCode(his);

        sceneInfo.setAreaCode("340100");

        sceneInfo.setGradeCode("08");
        //学科
        sceneInfo.setSubjectCode("05");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        sceneInfo.setCatalogCode("41_08020224-002_02");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        sceneInfo.setLayerType("highScoreAdvanced");
    }

    @Test
    public void master_fetch_node() {

        SkylabRequest<MasterFetch5CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.NONE);
        skylabRequest.setScene(sceneInfo);

        //章节
        MasterFetch5CatalogParam masterFetch5CatalogParam = new MasterFetch5CatalogParam();
        masterFetch5CatalogParam.setCatalogIds(Arrays.asList(sceneInfo.getCatalogCode()));
        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.CHECK_POINT));
        skylabRequest.setPayload(masterFetch5CatalogParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }

    @Test
    public void master_fetch_node1() {

        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.FINAL_EXAM);

        sceneInfo.setAreaCode("340100");

        sceneInfo.setGradeCode("08");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        sceneInfo.setCatalogCode("30_08020213-002_005");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        sceneInfo.setLayerType(null);

        sceneInfo.setBizAction(BizActionEnum.NONE);
        skylabRequest.setScene(sceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();
        List<NodeInfo> nodeInfos = new ArrayList<>();
        nodeInfos.add(new NodeInfo("f2cb2ac2-fe2a-452e-b5f3-9dc7802adb73", NodeTypeEnum.CHECK_POINT, "30_08020213-002_005", null));
        masterFetch5NodeParam.setNodeInfos(nodeInfos);

//        MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();
//        masterDiagnoseParam.setNodeIds(Arrays.asList("f2cb2ac2-fe2a-452e-b5f3-9dc7802adb73"));
//        //精准学os下点和章节都传
//        masterDiagnoseParam.setCatalogIds(Arrays.asList(sceneInfo.getCatalogCode()));
//
//        //精准学os -单元复习-入门测 批量上报日志-做测评终止画像更新  allUpdate=true
//        if (HisStudyCodeEnum.UNIT_REVIEW.equals(sceneInfo.getHisStudyCode()) && BizActionEnum.EXAM_UNIT_BASIS.equals(sceneInfo.getBizAction())) {
//            masterDiagnoseParam.setAllUpdate(true);
//        }


        skylabRequest.setPayload(masterFetch5NodeParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


    @Test
    public void sync_learn_study_log() {
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.FINAL_EXAM);

        sceneInfo.setAreaCode("340100");

        sceneInfo.setGradeCode("08");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        sceneInfo.setCatalogCode("30_08020213-002_005");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        sceneInfo.setLayerType(null);

        sceneInfo.setBizAction(BizActionEnum.NONE);

        sceneInfo.setBizAction(BizActionEnum.KC_SYNC_REC);
        for (int i = 0; i < 1; i++) {
            long start = System.currentTimeMillis();
            sync_learn_study_log(sceneInfo, "f2cb2ac2-fe2a-452e-b5f3-9dc7802adb73", "a29f5efa-c8b8-4920-94f3-61d78a30e39b",
                    1, "12333", "22222");
            System.err.println("耗时：" + (System.currentTimeMillis() - start));
        }
    }

    public void sync_learn_study_log(SceneInfo sceneInfo, String nodeId, String topic, int index, String refTraceId, String roundId) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        StudyLogParam studyLogParam = new StudyLogParam();

        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId(nodeId);
        studyLogRecord.setNodeType(NodeTypeEnum.CHECK_POINT);
        studyLogRecord.setResNodeId(topic);
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);

        studyLogRecord.setRoundId(roundId);

        studyLogRecord.setRefTraceId(refTraceId);
        studyLogRecord.setRoundIndex("" + index);
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode(sceneInfo.getBookCode());
        studyLogRecord.setCatalogCode(sceneInfo.getCatalogCode());
        studyLogRecord.setCorrectTraceId("c4016d98-2496-4808-ae28-4db2765cfa9a");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

}
