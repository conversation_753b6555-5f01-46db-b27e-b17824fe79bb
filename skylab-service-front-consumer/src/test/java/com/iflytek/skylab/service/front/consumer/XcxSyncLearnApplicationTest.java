package com.iflytek.skylab.service.front.consumer;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class XcxSyncLearnApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;


    @Test
    public void sync_learn_rec_eval() {

        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

//        小程序
        sceneInfo.setExt1("MINI_PROGRAM");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("zx182-7");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_07020101-001");
        sceneInfo.setCatalogCode("01_07020101-001_03_002");

        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recEval4InOutParam.setCatalogIds(Arrays.asList("01_07020101-001_03_002"));
        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);

        log.info(JSON.toJSONString(skylabResponse));
        Assert.assertEquals(0, skylabResponse.getCode());

    }

    @Test
    public void sync_learn_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");
        sceneInfo.setExt1("MINI_PROGRAM");
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("zx182-7");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");
        sceneInfo.setLayerType("conventional");

        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("01_08020101-002_06_003"));

        Map<String, Object> recExt = new HashMap<>();
        recExt.put("anchorCntLimit", 1);
        recNodeParam.setRecExt(recExt);
        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
        Assert.assertEquals(0, skylabResponse.getCode());

    }


    //ToDO 重新验证
    @Test
    public void sync_learn_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");
        sceneInfo.setExt1("MINI_PROGRAM");
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xwliu01");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");
        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("ac935ac1-bbdf-4654-bddc-7a0a9e97beb3");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
        Assert.assertEquals(0, skylabResponse.getCode());

    }

//    @Test
//    public void sync_learn_rec_topic_pack(){
//        SkylabRequest<RecTopicPackParam> skylabRequest = new SkylabRequest<>();
//        String traceId = UUID.randomUUID().toString();
//        log.info("traceId: {}", traceId);
//        skylabRequest.setTraceId(traceId);
//
//        SceneInfo sceneInfo = new SceneInfo();
//        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
//        sceneInfo.setAreaCode("000000");
//        sceneInfo.setBizAction(BizActionEnum.SYNC_AIM);
//        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
////        sceneInfo.setGraphVersion("v10");
//        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
//        sceneInfo.setGradeCode("07");
//        //学科
//        sceneInfo.setSubjectCode("05");
//        //学段
//        sceneInfo.setPhaseCode("04");
//        //用户id
//        sceneInfo.setUserId("hhji2_synlearn_evel3_allright");
//        //教材
//        sceneInfo.setPressCode("01");
//        sceneInfo.setBookCode("01_08050101-001");
//        sceneInfo.setCatalogCode("01_08050101-001_04");
//
//        skylabRequest.setScene(sceneInfo);
//
//        RecTopicPackParam recTopicPackParam = new RecTopicPackParam();
//        recTopicPackParam.setNodeIds(Arrays.asList("9a8892cc-6700-42c3-bd7c-553ed3ed4293"));
//        skylabRequest.setPayload(recTopicPackParam);
//        SkylabResponse<RecTopicPackResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicPackResult.class);
//        log.info(JSON.toJSONString(skylabResponse));
//    }

    @Test
    public void sync_learn_master_fetch() {

        SkylabRequest<MasterFetch4CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");
        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        sceneInfo.setExt1("MINI_PROGRAM");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();
        masterFetch4CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_06_003"));
        skylabRequest.setPayload(masterFetch4CatalogParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
        Assert.assertEquals(0, skylabResponse.getCode());

    }

    @Test
    public void sync_learn_study_log() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");
        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        sceneInfo.setExt1("MINI_PROGRAM");
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xwliu01");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("5aaf5a95-bffc-4cc1-a012-451313bcd581");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode("01_08020101-002_06_003");
        studyLogRecord.setCorrectTraceId("c4016d98-2496-4808-ae28-4db2765cfa9a");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
        Assert.assertEquals(0, skylabResponse.getCode());

    }

    @Autowired
    private SkylabStandService skylabStandService;


    @Test
    public void feature() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v20");
        sceneInfo.setExt1("MINI_PROGRAM");
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("test_202303241111");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

//        featureParamItem.setGraphVersion("v20");
        featureParamItem.setFeatureVersion(1);
        featureParamItem.setFeatureName("user_level");
        Map<String, String> params = new HashMap<>();
        params.put("user_id", "test_202303241111");
        params.put("biz_code", "ZSY_XXJ");
        params.put("subject_code", "02");
        params.put("phase_code", "04");

        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));

        skylabRequest.setPayload(featureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
        Assert.assertEquals(0, skylabResponse.getCode());

    }


    @Test
    public void acquire_feature() {

        SkylabRequest<AcquireFeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v20");
        sceneInfo.setExt1("MINI_PROGRAM");
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("zx182-7");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");

        skylabRequest.setScene(sceneInfo);

        AcquireFeatureParam acquireFeatureParam = new AcquireFeatureParam();

        acquireFeatureParam.setFeatureNames(Arrays.asList("catalog_predict_answer_time"));
        acquireFeatureParam.setParams(Arrays.asList("01_08020101-002_06_003"));


        skylabRequest.setPayload(acquireFeatureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
        Assert.assertEquals(0, skylabResponse.getCode());


    }
}
