package com.iflytek.skylab.service.front.consumer.syncos;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.service.front.consumer.CheckResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 精准学os
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncOsLearnApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabStandService skylabStandService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    static SceneInfo sceneInfo = null;

    static {
        sceneInfo = new SceneInfo();

//        sceneInfo.setGraphVersion("20240401_002");
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setAreaCode("350800");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("13");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test1");
        //教材
        sceneInfo.setPressCode("272");
        sceneInfo.setBookCode("278_07130107278-6391");
        sceneInfo.setCatalogCode("278_07130107278-6391_07130107278-6391-189529_07130107278-6391-189537_07130107278-6391-189539");

        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
//        sceneInfo.setLayerType("conventional");
    }

    /**
     * 测评 todo
     */
    @Test
    public void sync_learn_rec_eval() {
        String roundId = UUID.randomUUID().toString();
        for (int i = 0; i < 1; i++) {
//            sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
            sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
            sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = UUID.randomUUID().toString();
            log.info("traceId 1: {}", traceId1);

            skylabRequest.setTraceId(traceId1);
            sceneInfo.setUserId("dsdadwdsada");

            sceneInfo.setCatalogCode("01_08020101-002_02_002");
            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
            recEval4InOutParam.setTopicOrderNumber(1);
            recEval4InOutParam.setRoundId(roundId);

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

            skylabRequest.setPayload(recEval4InOutParam);

            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

            EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);
            if (skylabResponse.getPayload().isTerminationFlag()) {
                break;
            }
            //作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
        }
    }

    /**
     * 测评 todo
     */
    @Test
    public void sync_learn_rec_eva2l() {
        String roundId = UUID.randomUUID().toString();
        for (int i = 0; i < 30; i++) {

            sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
            sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = UUID.randomUUID().toString();
            log.info("traceId 1: {}", traceId1);

            skylabRequest.setTraceId(traceId1);
            sceneInfo.setBookCode("01_08020101-002");
            sceneInfo.setCatalogCode("01_08020101-002_02_002");
            sceneInfo.setLayerVersion("highScoreAdvanced");
            sceneInfo.setUserId("sync_os_use_pre_user_32");
            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
            recEval4InOutParam.setTopicOrderNumber(1);
            recEval4InOutParam.setRoundId("1");

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

            skylabRequest.setPayload(recEval4InOutParam);
            long start = System.currentTimeMillis();
            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            log.info("耗时：{}", System.currentTimeMillis() - start);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
            log.info("skylabResponse.getPayload().isTerminationFlag()={}", skylabResponse.getPayload().isTerminationFlag());
//            EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);
//            if (skylabResponse.getPayload().isTerminationFlag()) {
//                break;
//            }
            //作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
        }


//        String traceId2 = UUID.randomUUID().toString();
//        log.info("traceId 2: {}", traceId2);
//        recEval4InOutParam.setTopicOrderNumber(2);
//        skylabRequest.setTraceId(traceId2);
//        SkylabResponse<RecEval4InOutResult> skylabResponse2 = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
//        CheckResultUtil.check(skylabResponse2, sceneInfo.getUserId());
//
//        EvaluationItem evaluationItem2 = skylabResponse2.getPayload().getEvaluationItems().get(0);
//
//        //作答
//        sync_learn_study_log(sceneInfo, evaluationItem2.getNodeId(), evaluationItem2.getResNodeId(), 2, traceId2, roundId);

    }

    @Test
    public void sync_learn_study_log() {
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        for (int i = 0; i < 2; i++) {
            sceneInfo.setUserId("27779f37-ff18-49d1-9c57-37f8d770aeee");
            long start = System.currentTimeMillis();
            sync_learn_study_log(sceneInfo, "8d4170f4-91f7-483d-b93e-11d237092b4b", "f6185fa0-d838-41d1-9a89-68fac5adf875",
                    1, "12333", "22222");
            System.err.println("耗时：" + (System.currentTimeMillis() - start));
        }

    }

    public void sync_learn_study_log(SceneInfo sceneInfo, String nodeId, String topic, int index, String refTraceId, String roundId) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId(nodeId);
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId(topic);
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);

        studyLogRecord.setRoundId(roundId);

        studyLogRecord.setRefTraceId(refTraceId);
        studyLogRecord.setRoundIndex("" + index);
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("278_07130107278");
        studyLogRecord.setCatalogCode(sceneInfo.getCatalogCode());
        studyLogRecord.setCorrectTraceId("c4016d98-2496-4808-ae28-4db2765cfa9a");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * 薄弱点推荐
     */
    @Test
    public void sync_learn_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();

        recNodeParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

        skylabRequest.setPayload(recNodeParam);
        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }

    /**
     * 点推题
     */
    @Test
    public void sync_learn_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);


        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("c4b2c778-ab97-4d2a-b747-cd6ccf01f7c8");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);
        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * 全书地图锚点推题
     */
    @Test
    public void sync_learn_rec_topic2() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_OS_MAP);
        sceneInfo.setBizAction(BizActionEnum.SYNC_OS_ANCHOR_REC);


        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("c4b2c778-ab97-4d2a-b747-cd6ccf01f7c8");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);
        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * 目录画像 todo
     */
    @Test
    public void master_fetch_catalog() {

        SkylabRequest<MasterFetch5CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

//        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.NONE);

        sceneInfo.setCatalogCode("01_08020101-002_06_002");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setAreaCode("130000");


        skylabRequest.setScene(sceneInfo);

        //章节
        MasterFetch5CatalogParam masterFetch5CatalogParam = new MasterFetch5CatalogParam();
        masterFetch5CatalogParam.setCatalogIds(Arrays.asList(sceneInfo.getCatalogCode()));
        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT, NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
        skylabRequest.setPayload(masterFetch5CatalogParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
//
//        //章
//        masterFetch5CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_02"));
//        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.REVIEW_POINT));
////        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
//        skylabRequest.setPayload(masterFetch5CatalogParam);
//
//        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
//        SkylabResponse<MasterFetchResult> skylabResponse2 = diagnoseService.diagnose(skylabRequest);
//        CheckResultUtil.check(skylabResponse2, sceneInfo.getUserId());
//
//
//        //章
////        masterFetch5CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_06"));
//        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT));
////        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT, NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
////        skylabRequest.setPayload(masterFetch5CatalogParam);
////
////        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
////        SkylabResponse<MasterFetchResult> skylabResponse3 = diagnoseService.diagnose(skylabRequest);
////        CheckResultUtil.check(skylabResponse3, sceneInfo.getUserId());

    }

    /**
     * /**
     * 目录画像 todo
     */
    @Test
    public void master_fetch_catalog3() throws ExecutionException, InterruptedException {

        SkylabRequest<MasterDiagnoseParam> skylabRequest = new SkylabRequest<>();

        String bb = "{\n" +
                "            \"bizAction\": \"SYNC_EVAL\",\n" +
                "            \"bizCode\": \"ZSY_XXJ\",\n" +
                "            \"gradeCode\": \"08\",\n" +
                "            \"graphVersion\": \"20240424_001\",\n" +
                "            \"layerVersion\": \"SYNC_LEARN_UPDATE\",\n" +
                "            \"hisStudyCode\": \"SYNC_LEARN\",\n" +
                "            \"studyCode\": \"SYNC_OS\",\n" +
                "            \"phaseCode\": \"04\",\n" +
                "            \"areaCode\": \"010100\",\n" +
                "            \"functionCode\": \"MASTER_DIAGNOSE\",\n" +
                "            \"catalogCode\": \"01_08020101-002_05_001\",\n" +
                "            \"bookCode\": \"01_08020101-002\",\n" +
                "            \"userId\": \"xiangzhang182\",\n" +
                "            \"subjectCode\": \"02\",\n" +
                "            \"pressCode\": \"01\",\n" +
                "            \"layerType\": \"conventional\",\n" +
                "            \"test\": true\n" +
                "        }";
        SceneInfo sceneInfo1 = JSONUtil.parseObj(bb).toBean(SceneInfo.class);
        MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();
        masterDiagnoseParam.setCatalogIds(Arrays.asList("01_08020101-002_05_001"));
//        masterDiagnoseParam.setNodeIds(Arrays.asList("bce723cf-d4f7-4141-9ef9-a8afb79eeac4"));
        masterDiagnoseParam.setAllUpdate(true);
        skylabRequest.setPayload(masterDiagnoseParam);
//                sceneInfo1.setUserId("xiangzhang182_test");
        sceneInfo1.setUserId(IdUtil.fastSimpleUUID());
        skylabRequest.setScene(sceneInfo1);
        skylabRequest.setTraceId(IdUtil.fastSimpleUUID());
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo1.getUserId());

    }

    /**
     * /**
     * 目录画像 todo
     */
    @Test
    public void master_fetch_catalog2() throws ExecutionException, InterruptedException {
        int numberOfTasks = 4;
        ExecutorService executorService = Executors.newFixedThreadPool(numberOfTasks);

        // 创建Future列表来收集异步执行的结果
        List<Future<Boolean>> futures = new ArrayList<>();

        for (int i = 0; i < 100000; i++) {
            int finalI = i;
            Future<Boolean> submit = executorService.submit(() -> {

                SkylabRequest<MasterDiagnoseParam> skylabRequest = new SkylabRequest<>();
                String a = "{\"allUpdate\":false,\"catalogIds\":[\"01_08020101-002_02_002\"],\"learnPointUpdate\":false,\"nodeIds\":[\"bce723cf-d4f7-4141-9ef9-a8afb79eeac4\"]" +
                        ",\"sceneInfo\":{\"areaCode\":\"010100\",\"bizAction\":\"SYNC_REC\",\"bizCode\":\"ZSY_XXJ\",\"bookCode\":\"01_08020101-002\",\"bookVersion\":\"01\",\"functionCode\":\"MASTER_DIAGNOSE\",\"graphVersion\":\"20240424_001\",\"phaseCode\":\"04\",\"studyCode\":\"SYNC_OS\",\"subjectCode\":\"02\",\"userId\":\"sync_os_use_pre_user_66\"},\"sessionInfo\":{\"strategyId\":\"ff9a3f68efd60b261d280315e5a59347\",\"traceId\":\"4ebaf6be-f21d-450e-b3fa-b93a9ba15e3f\"}}";

                SceneInfo sceneInfo1 = JSONUtil.parseObj(a).getJSONObject("sceneInfo").toBean(SceneInfo.class);
                MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();
                masterDiagnoseParam.setCatalogIds(Arrays.asList("01_08020101-002_02_002"));
//                masterDiagnoseParam.setCatalogIds(Arrays.asList("01_08020101-002_06"));
                masterDiagnoseParam.setNodeIds(Arrays.asList("bce723cf-d4f7-4141-9ef9-a8afb79eeac4"));
//                masterDiagnoseParam.setNodeIds(Arrays.asList("8e4af69d-5be2-48f2-bac6-badd22cf4fab"));

                masterDiagnoseParam.setAllUpdate(true);
                skylabRequest.setPayload(masterDiagnoseParam);
                sceneInfo1.setUserId("xiangzhang182_test");
//                sceneInfo1.setUserId(IdUtil.fastSimpleUUID());
                skylabRequest.setScene(sceneInfo1);
                skylabRequest.setTraceId(IdUtil.fastSimpleUUID());
                long bb = System.currentTimeMillis();
                SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
                CheckResultUtil.check(skylabResponse, sceneInfo1.getUserId());
                System.err.println(finalI + "耗时" + (System.currentTimeMillis() - bb));

                return true;
            });
            futures.add(submit);

        }
        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get(); // 等待任务完成，可以处理异常
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                e.printStackTrace();
            } finally {
                executorService.shutdown(); // 关闭线程池
            }
        }

//
//        //章
//        masterFetch5CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_02"));
//        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.REVIEW_POINT));
////        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
//        skylabRequest.setPayload(masterFetch5CatalogParam);
//
//        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
//        SkylabResponse<MasterFetchResult> skylabResponse2 = diagnoseService.diagnose(skylabRequest);
//        CheckResultUtil.check(skylabResponse2, sceneInfo.getUserId());
//
//
//        //章
////        masterFetch5CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_06"));
//        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT));
////        masterFetch5CatalogParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT, NodeTypeEnum.CHECK_POINT, NodeTypeEnum.REVIEW_POINT));
////        skylabRequest.setPayload(masterFetch5CatalogParam);
////
////        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
////        SkylabResponse<MasterFetchResult> skylabResponse3 = diagnoseService.diagnose(skylabRequest);
////        CheckResultUtil.check(skylabResponse3, sceneInfo.getUserId());

    }

    /**
     * 点画像
     */
    @Test
    public void master_fetch_node() {

        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setUserId("zhangxiang182");
        skylabRequest.setScene(sceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();
//        MASTER_FETCH

        masterFetch5NodeParam.setMasterFuncEnum(MasterFuncEnum.MASTER_FETCH);
        masterFetch5NodeParam.setMasterFuncEnum(MasterFuncEnum.KC_MASTER_FETCH);

        List<NodeInfo> nodeInfos = new ArrayList<>();
        nodeInfos.add(new NodeInfo("53349d80-f419-403a-ba04-1d78256e30f8", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_02_002", null));
        nodeInfos.add(new NodeInfo("7cffb5c8-e7d4-4998-bc4e-4525f44c61f0", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_02_002", null));
        nodeInfos.add(new NodeInfo("090b7cb1-a052-4781-91a4-6a55cba94bba", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_04_002", null));
        nodeInfos.add(new NodeInfo("0a488b60-0db3-4bd5-8ff7-21e40ac627e5", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_02_001", null));
        nodeInfos.add(new NodeInfo("3e927441-1b8e-4e36-bf41-c918a4cf3fd7", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_03_002", null));

        masterFetch5NodeParam.setNodeInfos(nodeInfos);
        skylabRequest.setPayload(masterFetch5NodeParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


//    /**
//     * 点画像
//     */
//    @Test
//    public void master_fetch_node_check() {
//
//        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
//        String traceId = UUID.randomUUID().toString();
//        log.info("traceId: {}", traceId);
//        skylabRequest.setTraceId(traceId);
//        sceneInfo.setGraphVersion("vtest");
//        sceneInfo.setBizAction(BizActionEnum.NONE);
//        sceneInfo.setUserId("tbxt_diagnose_user01");
//        skylabRequest.setScene(sceneInfo);
//
//        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();
//
//        List<NodeInfo> nodeInfos = JSONUtil.toList("[{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_001rev_001ck\",\"nodeType\":\"CHECK_POINT\"},{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_001rev_002ck\",\"nodeType\":\"CHECK_POINT\"},{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_001rev_003ck\",\"nodeType\":\"CHECK_POINT\"},{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_002rev_001ck\",\"nodeType\":\"CHECK_POINT\"},{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_002rev_002ck\",\"nodeType\":\"CHECK_POINT\"},{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_002rev_003ck\",\"nodeType\":\"CHECK_POINT\"},{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_003rev_001ck\",\"nodeType\":\"CHECK_POINT\"},{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_003rev_002ck\",\"nodeType\":\"CHECK_POINT\"},{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_003rev_003ck\",\"nodeType\":\"CHECK_POINT\"},{\"catalogId\":\"01_08020101-002_01uoswl\",\"nodeId\":\"01_08020101-002_02uoswl_004rev_001ck\",\"nodeType\":\"CHECK_POINT\"}]",
//                NodeInfo.class
//        );
//
//        masterFetch5NodeParam.setNodeInfos(nodeInfos);
//        skylabRequest.setPayload(masterFetch5NodeParam);
//
//        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
//        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
//        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
//
//    }

    /**
     * 全书地图
     */
    @Test
    public void master_fetch_book() {

        SkylabRequest<MasterFetch5BookParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
//        sceneInfo.setUserId("bdfb88da-a355-405d-a931-5f4e885a3c24");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setAreaCode("13000");
        skylabRequest.setScene(sceneInfo);

        MasterFetch5BookParam fetch5BookParam = new MasterFetch5BookParam();
        fetch5BookParam.setBookCode("42_08020126-001");
        fetch5BookParam.setCatalogIds(Arrays.asList("01_08020101-002_02"));
//        fetch5BookParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.ANCHOR_POINT));
//        fetch5BookParam.setExpPointTypes(Lists.newArrayList(NodeTypeEnum.REVIEW_POINT));
        skylabRequest.setPayload(fetch5BookParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

    }


    /**
     * 学习轨迹
     */
    @Test
    public void path() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("cph_ck_hx");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();
        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

        featureParamItem.setFeatureName("user_node_mastery_path");
        Map<String, String> params = new HashMap<>();
        params.put("node_id", "01_08020101-002_01uosCKwl_003rev_001ck");

        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));
        featureParam.setSimpleMode(true);
        skylabRequest.setPayload(featureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabRequest, skylabResponse);

    }


    //    ai诊断题包
    @Test
    public void ai_diag_rec_topic_pack() {
        SkylabRequest<RecTopicPackParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        AiDiagSceneInfo aiDiagSceneInfo = new AiDiagSceneInfo();
        aiDiagSceneInfo.setAreaCode("340100");
        aiDiagSceneInfo.setBizAction(BizActionEnum.AI_DIAGNOSIS_REC_PACK);
        aiDiagSceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        aiDiagSceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        aiDiagSceneInfo.setHisStudyCode(HisStudyCodeEnum.AI_DIAG);
        aiDiagSceneInfo.setGradeCode("07");
        //学科
        aiDiagSceneInfo.setSubjectCode("02");
        //学段
        aiDiagSceneInfo.setPhaseCode("04");
        //用户id
        aiDiagSceneInfo.setUserId("hhji2_synlearn_evel3_allright11");
        //教材
        aiDiagSceneInfo.setPressCode("01");
        //特殊字段处理
        String nodeCatalogMapString = "{\n" +
                "\t\t\t\"34e87921-cfcf-44aa-b1f6-49fc703cfcd7\":\"01_08020101-002_06_003\",\n" +
                "\t\t\t\"ac935ac1-bbdf-4654-bddc-7a0a9e97beb3\":\"01_08020101-002_06_003\",\n" +
                "\t\t\t\"f6ecc8a2-00d7-4c5f-be7e-8a38245823ff\":\"01_07020101-001_02_001\"\n" +
                "\t\t}";
        Map<String, String> nodeCatalogMap = JSON.parseObject(nodeCatalogMapString, new TypeReference<Map<String, String>>() {
        });
        aiDiagSceneInfo.setNodeCatalogMap(nodeCatalogMap);
        skylabRequest.setScene(aiDiagSceneInfo);

        RecTopicPackParam recTopicPackParam = new RecTopicPackParam();
        recTopicPackParam.setNodeIds(Arrays.asList("34e87921-cfcf-44aa-b1f6-49fc703cfcd7", "ac935ac1-bbdf-4654-bddc-7a0a9e97beb3", "f6ecc8a2-00d7-4c5f-be7e-8a38245823ff"));
        skylabRequest.setPayload(recTopicPackParam);
        SkylabResponse<RecTopicPackResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicPackResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * 学习轨迹
     */
    @Test
    public void checkpoint_prepare_graph_layer() {

        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("cph_ck_hx");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();
        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

        featureParamItem.setFeatureName("checkpoint_prepare_graph_layer");
        Map<String, String> params = new HashMap<>();
//        {
//            "subject_code":"05",
//                "phase_code":"04",
//                "biz_code":"ZSY_XXJ",
//                "area_code":"340000",
//                "checkpoint_id":"29148e6e-efd7-4eff-847d-a681720aca2f",
//                "item_identify_key":"29148e6e-efd7-4eff-847d-a681720aca2f",
//                "catalog_code":"272_08050201272-8940_08050201272-8940-213737",
//                "study_code":"SYNC_OS"
//        }
//
        params.put("subject_code", "05");
        params.put("phase_code", "04");
        params.put("biz_code", "ZSY_XXJ");
        params.put("area_code", "340000");
        params.put("checkpoint_id", "29148e6e-efd7-4eff-847d-a681720aca2f");
        params.put("item_identify_key", "29148e6e-efd7-4eff-847d-a681720aca2f");
        params.put("catalog_code", "272_08050201272-8940_08050201272-8940-213737");
        params.put("study_code", "SYNC_OS");


        featureParamItem.setParams(Arrays.asList(params));
        featureParam.setItems(Arrays.asList(featureParamItem));
        featureParam.setSimpleMode(true);
        skylabRequest.setPayload(featureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandService.featureFetch(skylabRequest);
        CheckResultUtil.check(skylabRequest, skylabResponse);

    }

}
