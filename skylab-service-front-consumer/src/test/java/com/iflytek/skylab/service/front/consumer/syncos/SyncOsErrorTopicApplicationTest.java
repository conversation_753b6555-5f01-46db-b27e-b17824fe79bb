package com.iflytek.skylab.service.front.consumer.syncos;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.front.consumer.CheckResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 精准学os
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncOsErrorTopicApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabStandService skylabStandService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    static SceneInfo sceneInfo = null;

    static {
        sceneInfo = new SceneInfo();

//        sceneInfo.setGraphVersion("20240401_002");
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setAreaCode("420000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_002");

        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        sceneInfo.setLayerType("conventional");
    }

    /**
     * 错题本变式题推荐
     */
    @Test
    public void macrographErrorBookVary() {

        SkylabRequest<RecErrorTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);


        sceneInfo.setHisStudyCode(HisStudyCodeEnum.MACROGRAPH_ERROR_BOOK);

        sceneInfo.setBizAction(BizActionEnum.MACROGRAPH_ERROR_BOOK_VARY);

        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        skylabRequest.setScene(sceneInfo);

        RecErrorTopicParam recErrorTopicParam = new RecErrorTopicParam();
        recErrorTopicParam.setTopicId("0a860eb5-3324-4ff1-ba7a-7e00cec43bd2");
        recErrorTopicParam.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        recErrorTopicParam.setTopicSection("020401");

        skylabRequest.setPayload(recErrorTopicParam);

        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecErrorTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecErrorTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    /**
     * 测试不重题，换一换
     */
    @Test
    public void macrographErrorBookVary2() {
        List<String> topics = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            SkylabRequest<RecErrorTopicParam> skylabRequest = new SkylabRequest<>();
            String traceId = UUID.randomUUID().toString();
            log.info("traceId: {}", traceId);
            skylabRequest.setTraceId(traceId);

            sceneInfo.setHisStudyCode(HisStudyCodeEnum.MACROGRAPH_ERROR_BOOK);

            sceneInfo.setBizAction(BizActionEnum.MACROGRAPH_ERROR_BOOK_VARY);

            //教材
            sceneInfo.setPressCode("01");
            sceneInfo.setBookCode("01_08020101-002");
            sceneInfo.setCatalogCode("01_08020101-002_06_003");

            skylabRequest.setScene(sceneInfo);

            RecErrorTopicParam recErrorTopicParam = new RecErrorTopicParam();
            recErrorTopicParam.setTopicId("0a860eb5-3324-4ff1-ba7a-7e00cec43bd2");
            recErrorTopicParam.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
            recErrorTopicParam.setTopicSection("020401");
            recErrorTopicParam.setChange(true);

            skylabRequest.setPayload(recErrorTopicParam);

            SkylabResponse<RecErrorTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecErrorTopicResult.class);
            log.info("i={}", i);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());

            for (EvaluationItem item : skylabResponse.getPayload().getEvaluationItems()) {
                if (topics.contains(item.getResNodeId())) {
                    Assert.isTrue(false, "第{}次发生重题", i);
                } else {
                    topics.add(item.getResNodeId());
                }
            }
        }

    }

    /**
     * 错题本溯源点推题
     */
    @Test
    public void macrographErrorBookTopic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setHisStudyCode(HisStudyCodeEnum.MACROGRAPH_ERROR_BOOK);

        sceneInfo.setBizAction(BizActionEnum.MACROGRAPH_ERROR_BOOK_TOPIC);

        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setRecTopicEnum(RecTopicEnum.REC_TOPIC_TRACE_POINT);
        recTopicParam.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);
        log.info(JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }


}
