package com.iflytek.skylab.service.front.consumer.kc;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.service.front.consumer.CheckResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 精准学os-知识簇-寒暑
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncOsKcSyncApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabStandService skylabStandService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    static SceneInfo sceneInfo = null;

    static {
        sceneInfo = new SceneInfo();

        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("420000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("aaaaaxiangzhang182_test1111");
        //教材

        sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period10");

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

    }

    /**
     * 1、同步学-流式测评-所有点找弱项
     */
    @Test
    public void KC_SYNC_EVAL() {
        String roundId = UUID.randomUUID().toString();
        log.info("roundId: {}", roundId);
        for (int i = 0; i < 1; i++) {



//            sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188948_07020101272-6314-188953_period20");

            List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
            sceneInfo.setPressCode(split.get(0));
            sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

            sceneInfo.setBizAction(BizActionEnum.KC_SYNC_EVAL);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = IdUtil.fastSimpleUUID();
            log.info("traceId 1: {}", traceId1);
            skylabRequest.setTraceId(traceId1);

            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.KC_REC_EVAL4IN);
            recEval4InOutParam.setTopicOrderNumber(i + 1);
            recEval4InOutParam.setRoundId(roundId);
            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));
//            recEval4InOutParam.setForbiddenNodes(Arrays.asList("7afa74eb-8859-40dd-b6bf-2a0bfd87edae"));
            skylabRequest.setPayload(recEval4InOutParam);
            log.info("KC_SYNC_EVAL-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));

            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);

//            SkylabResponse<RecEval4InOutResult> skylabResponse =    SDK.getRecommendService.recommend(skylabRequest, RecEval4InOutResult.class);

            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_SYNC_EVAL");

            EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);
            if (skylabResponse.getPayload().isTerminationFlag()) {
                break;
            }
            //作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
        }
    }

    /**
     * 2、同步学-流式测评-仅灰点找弱项
     */
    @Test
    public void KC_SYNC_EVAL_CTN() {
        String roundId = UUID.randomUUID().toString();
        log.info("roundId: {}", roundId);
        for (int i = 0; i < 1; i++) {

            sceneInfo.setBizAction(BizActionEnum.KC_SYNC_EVAL_CTN);

            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId1 = IdUtil.fastSimpleUUID();
            log.info("traceId 1: {}", traceId1);
            skylabRequest.setTraceId(traceId1);

            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.KC_REC_EVAL4CTN);
            recEval4InOutParam.setTopicOrderNumber(i + 1);
            recEval4InOutParam.setRoundId(roundId);

            recEval4InOutParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));

//            recEval4InOutParam.setForbiddenNodes(Arrays.asList("4be29f4a-1f32-4f9f-a764-9b83dcf652d3"));

            skylabRequest.setPayload(recEval4InOutParam);
            log.info("KC_SYNC_EVAL_CTN-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_SYNC_EVAL_CTN");

            EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);
            if (skylabResponse.getPayload().isTerminationFlag()) {
                break;
            }
            //作答
//            sync_learn_study_log(sceneInfo, evaluationItem.getNodeId(), evaluationItem.getResNodeId(), i, traceId1, roundId);
        }

    }


    /**
     * 3、同步学-簇下练习-AI规划学推题
     */
    @Test
    public void KC_SYNC_CLUSTER_REC_PLAN() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.KC_SYNC_CLUSTER_REC_PLAN);

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        //中心点 58d6671e-757a-4a50-83c1-d0beaa1a2e47
//        recTopicParam.setNodeId("58d6671e-757a-4a50-83c1-d0beaa1a2e47");
        //延展点 ae70514d-c78c-4cc2-b80b-aa679f8dac20
        recTopicParam.setNodeId("ae70514d-c78c-4cc2-b80b-aa679f8dac20");
        recTopicParam.setRecTopicEnum(RecTopicEnum.KC_REC_TOPIC_PLAN);
        recTopicParam.setTopicOrderNumber(1);
        recTopicParam.setForbiddenNodes(Arrays.asList("936ab7d7-d60e-49b8-988b-2d6d596993ad", "327db37a-1f95-4d66-8f77-1305e4dcf8a8"));
        skylabRequest.setPayload(recTopicParam);
        log.info("KC_SYNC_CLUSTER_REC_PLAN-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_SYNC_CLUSTER_REC_PLAN");
    }

    /**
     * 4、同步学-AI规划学-薄弱点推荐（需要ai伴学规划列表）
     */
    @Test
    public void KC_SYNC_SEARCH_WEAK_PLAN() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.KC_SYNC_SEARCH_WEAK_PLAN);
        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setRecNodeEnum(RecNodeEnum.KC_REC_NODE_PLAN);
        recNodeParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));
        recNodeParam.setSelectedCenterPoint("58d6671e-757a-4a50-83c1-d0beaa1a2e47");
        skylabRequest.setPayload(recNodeParam);
        log.info("KC_SYNC_SEARCH_WEAK_PLAN-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_SYNC_SEARCH_WEAK_PLAN");

    }


    /**
     * 5、同步学-簇下练习-自主学点推题
     */
    @Test
    public void KC_SYNC_AIM() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        //        test_xxj_pre_user_ysni0#02#04#ZSY_XXJ#SYNC_OS#09708e18-8bbc-4daf-8208-3b1020b34cb4#ANCHOR_POINT

        sceneInfo.setUserId("test_xxj_pre_user_ysni0");
        sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188948_07020101272-6314-188953_period20");
        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        sceneInfo.setBizAction(BizActionEnum.KC_SYNC_AIM);
        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        //中心点 58d6671e-757a-4a50-83c1-d0beaa1a2e47
//        recTopicParam.setNodeId("58d6671e-757a-4a50-83c1-d0beaa1a2e47");
        recTopicParam.setNodeId("09708e18-8bbc-4daf-8208-3b1020b34cb4");
        //延展点 ae70514d-c78c-4cc2-b80b-aa679f8dac20
//        recTopicParam.setNodeId("ae70514d-c78c-4cc2-b80b-aa679f8dac20");
        recTopicParam.setRecTopicEnum(RecTopicEnum.KC_REC_TOPIC);
        recTopicParam.setTopicOrderNumber(1);
//        recTopicParam.setForbiddenNodes(Arrays.asList("936ab7d7-d60e-49b8-988b-2d6d596993ad", "327db37a-1f95-4d66-8f77-1305e4dcf8a8"));
        skylabRequest.setPayload(recTopicParam);
        log.info("KC_SYNC_AIM-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_SYNC_AIM");
    }


    /**
     * 6、同步学-簇下练习-自主学点排序
     */
    @Test
    public void KC_SYNC_SEARCH_WEAK_VARIANT() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.KC_SYNC_SEARCH_WEAK_VARIANT);
        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setRecNodeEnum(RecNodeEnum.KC_REC_NODE_VARIANT);
        recNodeParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));
        skylabRequest.setPayload(recNodeParam);
        log.info("KC_SYNC_SEARCH_WEAK_VARIANT-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_SYNC_SEARCH_WEAK_VARIANT");

    }

    /**
     * 7、同步学-看板点排序
     * 薄弱点高亮
     */
    @Test
    public void KC_SYNC_SEARCH_WEAK() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.KC_SYNC_SEARCH_WEAK);
        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setRecNodeEnum(RecNodeEnum.KC_REC_NODE);
        recNodeParam.setCatalogIds(Lists.newArrayList(sceneInfo.getCatalogCode()));
        skylabRequest.setPayload(recNodeParam);
        log.info("KC_SYNC_SEARCH_WEAK-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_SYNC_SEARCH_WEAK");

    }

    /**
     * 7、再推一题
     */
    @Test
    public void KC_SYNC_REC() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        sceneInfo.setBizAction(BizActionEnum.KC_SYNC_REC);
        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        //中心点 58d6671e-757a-4a50-83c1-d0beaa1a2e47
        recTopicParam.setNodeId("58d6671e-757a-4a50-83c1-d0beaa1a2e47");
        //延展点 ae70514d-c78c-4cc2-b80b-aa679f8dac20
//        recTopicParam.setNodeId("ae70514d-c78c-4cc2-b80b-aa679f8dac20");
        recTopicParam.setRecTopicEnum(RecTopicEnum.KC_REC_TOPIC_ONEMORE);
        recTopicParam.setTopicOrderNumber(1);
//        recTopicParam.setForbiddenNodes(Arrays.asList("936ab7d7-d60e-49b8-988b-2d6d596993ad", "327db37a-1f95-4d66-8f77-1305e4dcf8a8"));
        skylabRequest.setPayload(recTopicParam);
        log.info("KC_SYNC_REC-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "KC_SYNC_REC");

    }


    /**
     * 点画像
     */
    @Test
    public void master_fetch_node() {
        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        skylabRequest.setScene(sceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();
        masterFetch5NodeParam.setMasterFuncEnum(MasterFuncEnum.KC_MASTER_FETCH);

        List<NodeInfo> nodeInfos = new ArrayList<>();

        nodeInfos.add(new NodeInfo("58d6671e-757a-4a50-83c1-d0beaa1a2e47", NodeTypeEnum.ANCHOR_POINT, "272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period10", null));

        masterFetch5NodeParam.setNodeInfos(nodeInfos);
        skylabRequest.setPayload(masterFetch5NodeParam);

        log.info("master_fetch_node-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "master_fetch_node");

    }

    /**
     * 目录画像
     */
    @Test
    public void master_fetch_catalog() {

        SkylabRequest<MasterFetch5CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
        sceneInfo.setBizAction(BizActionEnum.NONE);
        skylabRequest.setScene(sceneInfo);

        //章节
        MasterFetch5CatalogParam masterFetch5CatalogParam = new MasterFetch5CatalogParam();
        masterFetch5CatalogParam.setMasterFuncEnum(MasterFuncEnum.KC_MASTER_FETCH);
        masterFetch5CatalogParam.setCatalogIds(Arrays.asList(sceneInfo.getCatalogCode()));
        skylabRequest.setPayload(masterFetch5CatalogParam);

        log.info("master_fetch_catalog-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "master_fetch_catalog");

    }

    /**
     * 目录画像
     */
    @Test
    public void FAST_MASTER_FETCH1() {

        SkylabRequest<MasterFetch5CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
        sceneInfo.setUserId("9875a722-82c4-446f-a48e-b5ed3865431f");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        skylabRequest.setScene(sceneInfo);

        //章节
        MasterFetch5CatalogParam masterFetch5CatalogParam = new MasterFetch5CatalogParam();
//        masterFetch5CatalogParam.setMasterFuncEnum(MasterFuncEnum.FAST_MASTER_FETCH);
        masterFetch5CatalogParam.setCatalogIds(Arrays.asList("272_08020101272-9237_08020101272-9237-228609_08020101272-9237-228610"));
        skylabRequest.setPayload(masterFetch5CatalogParam);

        log.info("FAST_MASTER_FETCH1-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "FAST_MASTER_FETCH1");

    }

    /**
     * 点画像
     */
    @Test
    public void FAST_MASTER_FETCH2() {
        SkylabRequest<MasterFetch5NodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);
        sceneInfo.setBizAction(BizActionEnum.NONE);

        sceneInfo.setUserId("ytxiong3");

        skylabRequest.setScene(sceneInfo);

        MasterFetch5NodeParam masterFetch5NodeParam = new MasterFetch5NodeParam();
//        masterFetch5NodeParam.setMasterFuncEnum(MasterFuncEnum.FAST_MASTER_FETCH);

        List<NodeInfo> nodeInfos = new ArrayList<>();

//        8a8be044-6743-49fb-858b-4f7de41780d6
//        8b82b984-c461-49af-b9c1-764ffcfa52d7
//        67bec164-6206-47ac-afef-639b7891302d
//        34e87921-cfcf-44aa-b1f6-49fc703cfcd7
//        nodeInfos.add(new NodeInfo("58d6671e-757a-4a50-83c1-d0beaa1a2e47", NodeTypeEnum.ANCHOR_POINT, "272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period10", null));
//        nodeInfos.add(new NodeInfo("8a8be044-6743-49fb-858b-4f7de41780d6", NodeTypeEnum.ANCHOR_POINT, "272_07020101272-6314_07020101272-6314-188912", null));
//        nodeInfos.add(new NodeInfo("8b82b984-c461-49af-b9c1-764ffcfa52d7", NodeTypeEnum.ANCHOR_POINT, "01_08020101-002_06", null));
//        nodeInfos.add(new NodeInfo("67bec164-6206-47ac-afef-639b7891302d", NodeTypeEnum.ANCHOR_POINT, "42_08020126-001_05", null));
        nodeInfos.add(new NodeInfo("b511183c-9406-4f21-ba12-cfe073797cc3", NodeTypeEnum.ANCHOR_POINT, "272_07020101272-6314_07020101272-6314-188948", null));

        masterFetch5NodeParam.setNodeInfos(nodeInfos);
        skylabRequest.setPayload(masterFetch5NodeParam);

        log.info("FAST_MASTER_FETCH2-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "FAST_MASTER_FETCH2");

    }


    @Test
    public void sync_learn_study_log() {
        sceneInfo.setUserId("aaaaaaa");
        sceneInfo.setBizAction(BizActionEnum.KC_SYNC_REC);
        for (int i = 0; i < 1; i++) {
            long start = System.currentTimeMillis();
            sync_learn_study_log(sceneInfo, "58d6671e-757a-4a50-83c1-d0beaa1a2e47", "936ab7d7-d60e-49b8-988b-2d6d596993ad",
                    1, "12333", "22222");
            System.err.println("耗时：" + (System.currentTimeMillis() - start));
        }
    }

    public void sync_learn_study_log(SceneInfo sceneInfo, String nodeId, String topic, int index, String refTraceId, String roundId) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        StudyLogParam studyLogParam = new StudyLogParam();

        studyLogParam.setStudyLogFuncEnum(StudyLogFuncEnum.KC_STUDY_LOG);

        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId(nodeId);
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId(topic);
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);

        studyLogRecord.setRoundId(roundId);

        studyLogRecord.setRefTraceId(refTraceId);
        studyLogRecord.setRoundIndex("" + index);
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(0);
        studyLogRecord.setBookCode(sceneInfo.getBookCode());
        studyLogRecord.setCatalogCode(sceneInfo.getCatalogCode());
        studyLogRecord.setCorrectTraceId("c4016d98-2496-4808-ae28-4db2765cfa9a");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        log.info("sync_learn_study_log-入参:{}", JSONUtil.toJsonPrettyStr(skylabRequest));
        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId(), "sync_learn_study_log");
    }


}
