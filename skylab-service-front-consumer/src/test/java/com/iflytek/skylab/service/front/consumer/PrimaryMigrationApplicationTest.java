package com.iflytek.skylab.service.front.consumer;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.contract.service.SkylabPrimaryMigrationService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.PrimaryMigrationBehavior;
import com.iflytek.skylab.core.domain.PrimaryMigrationMastery;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/26 15:35
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class PrimaryMigrationApplicationTest {

    @Autowired
    private SkylabPrimaryMigrationService skylabPrimaryMigrationService;

    @Test
    public void behavior() {

        SkylabRequest<PrimaryMigrationBehaviorParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v10");
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xwliu16-behavior-test-01");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);
        PrimaryMigrationBehaviorParam primaryMigrationBehaviorParam = new PrimaryMigrationBehaviorParam();
        List<PrimaryMigrationBehavior> items = new ArrayList<>();

        PrimaryMigrationBehavior primaryMigrationBehavior = new PrimaryMigrationBehavior();
        primaryMigrationBehavior.setUserId("xwliu16-behavior-test-01");
        primaryMigrationBehavior.setSubjectCode("02");
        primaryMigrationBehavior.setPhaseCode("03");
        primaryMigrationBehavior.setNodeId("13cc38a2-df06-4a6b-a0b2-af04cb2c32d1");
        primaryMigrationBehavior.setResNodeId("50b98331-edf9-405a-b44e-9acaef9dc305");
        primaryMigrationBehavior.setScore(0D);
        primaryMigrationBehavior.setStandardScore(100D);
        primaryMigrationBehavior.setCreateTime(1682585746459L);

        items.add(primaryMigrationBehavior);
        primaryMigrationBehaviorParam.setItems(items);
        skylabRequest.setPayload(primaryMigrationBehaviorParam);

        log.info("behavior SkylabRequest= {}", skylabRequest);
        SkylabResponse<FuncResult> skylabResponse = skylabPrimaryMigrationService.behavior(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }


    @Test
    public void mastery() {
        SkylabRequest<PrimaryMigrationMasteryParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion("v10");
        sceneInfo.setGradeCode("03");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("xwliu16-behavior-test-01");
        //教材
        sceneInfo.setPressCode("272");
        sceneInfo.setBookCode("272_03020101272-9363");
        sceneInfo.setCatalogCode("272_03020101272-9363_03020101272-9363-234271");

        skylabRequest.setScene(sceneInfo);
        PrimaryMigrationMasteryParam primaryMigrationMasteryParam = new PrimaryMigrationMasteryParam();

        List<PrimaryMigrationMastery> items = new ArrayList<>();

        PrimaryMigrationMastery primaryMigrationMastery = new PrimaryMigrationMastery();
        primaryMigrationMastery.setNodeId("53b8d1f5-d68d-4752-aad7-fc75fe64fc1b");
        primaryMigrationMastery.setReal(-1d);
        primaryMigrationMastery.setPredict(-1d);
        items.add(primaryMigrationMastery);
        primaryMigrationMasteryParam.setItems(items);
        primaryMigrationMasteryParam.setUserId("xwliu16-behavior-test-01");
        primaryMigrationMasteryParam.setSubjectCode("02");
        primaryMigrationMasteryParam.setPhaseCode("03");
        primaryMigrationMasteryParam.setBookCode("272_03020101272-9363");
        primaryMigrationMasteryParam.setCatalogId("272_03020101272-9363_03020101272-9363-234271");
        skylabRequest.setPayload(primaryMigrationMasteryParam);

        log.info("mastery SkylabRequest= {}", skylabRequest);
        SkylabResponse<FuncResult> skylabResponse = skylabPrimaryMigrationService.mastery(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }
}
