package com.iflytek.skylab.service.front.consumer;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.data.MasterFetch4CatalogParam;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/15 15:00
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SentinelTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;

    /**
     * 1、110次错误请求触发熔断
     * 等待5s
     * 2、10次正常请求被熔断
     * 等待61s
     * 3、10次正常请求恢复
     */
    @Test
    public void fusing_sync_learn_master_fetch() {

        long currentTimeMillis = System.currentTimeMillis();
        //1、错误请求触发熔断
        for (int i = 0; i < 110; i++) {
            SkylabResponse<MasterFetchResult> skylabResponse = getMasterFetchResultSkylabResponse("v10-1");
            System.err.println(i + "==" + JSON.toJSONString(skylabResponse));
        }
        System.err.println("耗时：" + (System.currentTimeMillis() - currentTimeMillis));
        System.err.println();
        ThreadUtil.safeSleep(5000);
        //2、正常请求被熔断
        for (int i = 0; i < 10; i++) {
            SkylabResponse<MasterFetchResult> skylabResponse = getMasterFetchResultSkylabResponse(null);
            skylabResponse.setPayload(null);
            System.err.println(i + "==" + JSON.toJSONString(skylabResponse));
        }
        System.err.println();
        ThreadUtil.safeSleep(61000);
        //3、正常请求恢复
        for (int i = 0; i < 10; i++) {
            SkylabResponse<MasterFetchResult> skylabResponse = getMasterFetchResultSkylabResponse(null);
            skylabResponse.setPayload(null);
            System.out.println(i + "==" + JSON.toJSONString(skylabResponse));
        }
    }

    private SkylabResponse<MasterFetchResult> getMasterFetchResultSkylabResponse(String version) {
        SkylabRequest<MasterFetch4CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGraphVersion(version);
        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("test-zx");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();
        masterFetch4CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_06_003"));
        skylabRequest.setPayload(masterFetch4CatalogParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        return skylabResponse;
    }
}
