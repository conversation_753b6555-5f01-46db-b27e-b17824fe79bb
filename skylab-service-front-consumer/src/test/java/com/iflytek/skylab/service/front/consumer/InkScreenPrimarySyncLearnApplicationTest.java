package com.iflytek.skylab.service.front.consumer;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONUtil;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class InkScreenPrimarySyncLearnApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;


    @Test
    public void sync_learn_rec_eval() {
        String roundId = UUID.randomUUID().toString();

        for (int i = 0; i < 1; i++) {
            SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
            String traceId = UUID.randomUUID().toString();
            log.info("traceId: {}", traceId);
            skylabRequest.setTraceId(traceId);

            SceneInfo sceneInfo = new SceneInfo();
            sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
            sceneInfo.setAreaCode("000000");
            sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
            sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

            sceneInfo.setGradeCode("01");
            //学科
            sceneInfo.setSubjectCode("02");
            //学段
            sceneInfo.setPhaseCode("03");
            //用户id
            sceneInfo.setUserId("zx182-7");
            //教材
            sceneInfo.setPressCode("01");
            sceneInfo.setBookCode("01_01020101-001");
            sceneInfo.setCatalogCode("01_01020101-001_03_period10");

            sceneInfo.setExt1("LNK-SCREEN");

            skylabRequest.setScene(sceneInfo);

            RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
            recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
            recEval4InOutParam.setTopicOrderNumber(i + 1);
            recEval4InOutParam.setRoundId(roundId);
            recEval4InOutParam.setCatalogIds(Arrays.asList("01_01020101-001_03_period10"));
            skylabRequest.setPayload(recEval4InOutParam);

            SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
            log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
            RecEval4InOutResult payload = skylabResponse.getPayload();
            List<EvaluationItem> evaluationItems = payload.getEvaluationItems();
            EvaluationItem evaluationItem = evaluationItems.get(0);
            ThreadUtil.safeSleep(2000);
            sync_learn_study_log(recEval4InOutParam.getTopicOrderNumber(), evaluationItem.getNodeId(), evaluationItem.getResNodeId(), traceId, recEval4InOutParam.getRoundId());
            ThreadUtil.safeSleep(2000);

            sync_learn_master_fetch();
        }

    }

    @Test
    public void sync_learn_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-7");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_01020101-001");
        sceneInfo.setCatalogCode("01_01020101-001_03_period10");
        sceneInfo.setExt1("LNK-SCREEN");

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("01_01020101-001_03_period10"));

        Map<String, Object> recExt = new HashMap<>();
        recExt.put("anchorCntLimit", 1);
        recNodeParam.setRecExt(recExt);
        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }


    //ToDO 重新验证
    @Test
    public void sync_learn_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");
        sceneInfo.setExt1("LNK-SCREEN");

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-7");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_01020101-001");
        sceneInfo.setCatalogCode("01_01020101-001_03_period10");

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("efcc67a5-633c-41a8-a741-01e36365a4ea");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    @Test
    public void sync_learn_master_fetch() {

        SkylabRequest<MasterFetch4CatalogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setExt1("LNK-SCREEN");

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-7");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_01020101-001");
        sceneInfo.setCatalogCode("01_01020101-001_03_period10");

        skylabRequest.setScene(sceneInfo);

        MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();
        masterFetch4CatalogParam.setCatalogIds(Arrays.asList("01_01020101-001_03_period10"));
        skylabRequest.setPayload(masterFetch4CatalogParam);
        SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

    //    @Test
    public void sync_learn_study_log(int idnex, String nodeId, String topicId, String refTraceId, String roundId) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("zx182-7");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_01020101-001");
        sceneInfo.setCatalogCode("01_01020101-001_03_period10");

        sceneInfo.setExt1("LNK-SCREEN");

        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
//        studyLogRecord.setNodeId("efcc67a5-633c-41a8-a741-01e36365a4ea");
        studyLogRecord.setNodeId(nodeId);
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId(topicId);
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId(roundId);
        studyLogRecord.setRefTraceId(refTraceId);
        studyLogRecord.setRoundIndex(idnex + "");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_01020101-001");
        studyLogRecord.setCatalogCode("01_01020101-001_03_period10");


        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }

}
