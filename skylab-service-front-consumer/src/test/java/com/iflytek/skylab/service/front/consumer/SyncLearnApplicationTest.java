package com.iflytek.skylab.service.front.consumer;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/26 19:56
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class SyncLearnApplicationTest {
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;


    @Test
    public void sync_learn_rec_eval() throws ExecutionException, InterruptedException {


        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("420000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_002");

//        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        skylabRequest.setScene(sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setRecEvalEnum(RecEvalEnum.REC_EVAL4IN);
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        recEval4InOutParam.setCatalogIds(Arrays.asList("01_08020101-002_06_002"));
        skylabRequest.setPayload(recEval4InOutParam);

        SkylabResponse<RecEval4InOutResult> skylabResponse = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
        EvaluationItem evaluationItem = skylabResponse.getPayload().getEvaluationItems().get(0);

        //作答
        Future<Boolean> async = ThreadUtil.execAsync(() -> {
            ThreadUtil.safeSleep(100);
            sync_learn_study_log(evaluationItem.getNodeId(), evaluationItem.getResNodeId(), 1, traceId, null);
            return true;
        });

        recEval4InOutParam.setTopicOrderNumber(2);
        SkylabResponse<RecEval4InOutResult> skylabResponse2 = recommendService.recommend(skylabRequest, RecEval4InOutResult.class);
        CheckResultUtil.check(skylabResponse2, sceneInfo.getUserId());

        async.get();
    }
    public void sync_learn_study_log(String nodeId, String topic, int index, String refTraceId, String roundId) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");
//        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("8a0cbc3f-0d7a-4009-a8fb-a3817ce15d33");
        //教材
        sceneInfo.setPressCode("282");
        sceneInfo.setBookCode("282_01020108282-6470");
        sceneInfo.setCatalogCode("282_01020108282-6470_01020108282-6470-192440_01020108282-6470-192767_period30");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId(nodeId);
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId(topic);
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);

        studyLogRecord.setRoundId(roundId);

        studyLogRecord.setRefTraceId(refTraceId);
        studyLogRecord.setRoundIndex("" + index);
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("282_01020108282-6470");
        studyLogRecord.setCatalogCode("282_01020108282-6470_01020108282-6470-192440_01020108282-6470-192767_period30");
        studyLogRecord.setCorrectTraceId("2c3a82d545c14810a4d85f08aa99fc84");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }


    @Test
    public void sync_learn_rec_node() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("zxxxx182");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");
        sceneInfo.setLayerType("conventional");

        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("01_08020101-002_06_003"));

        Map<String, Object> recExt = new HashMap<>();
        recExt.put("anchorCntLimit", 1);
        recNodeParam.setRecExt(recExt);
        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }


    @Test
    public void sync_learn_rec_node2() {

        SkylabRequest<RecNodeParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("zxxxx182");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_07020101-001");
        sceneInfo.setCatalogCode("01_07020101-001_02_002");
//        sceneInfo.setLayerType("conventional");

//        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");

        skylabRequest.setScene(sceneInfo);

        RecNodeParam recNodeParam = new RecNodeParam();
        recNodeParam.setCatalogIds(Arrays.asList("01_07020101-001_02_002"));

        Map<String, Object> recExt = new HashMap<>();
        recExt.put("anchorCntLimit", 1);
        recNodeParam.setRecExt(recExt);
        skylabRequest.setPayload(recNodeParam);

        SkylabResponse<RecNodeResult> skylabResponse = recommendService.recommend(skylabRequest, RecNodeResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }


    //ToDO 重新验证
    @Test
    public void sync_learn_rec_topic() {

        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");
        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("ac935ac1-bbdf-4654-bddc-7a0a9e97beb3");
        recTopicParam.setTopicOrderNumber(1);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void sync_learn_rec_topic_pack() {
        SkylabRequest<RecTopicPackParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_AIM);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("05");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("hhji2_synlearn_evel3_allright");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08050101-001");
        sceneInfo.setCatalogCode("01_08050101-001_04");

        skylabRequest.setScene(sceneInfo);

        RecTopicPackParam recTopicPackParam = new RecTopicPackParam();
        recTopicPackParam.setNodeIds(Arrays.asList("9a8892cc-6700-42c3-bd7c-553ed3ed4293"));
        skylabRequest.setPayload(recTopicPackParam);
        SkylabResponse<RecTopicPackResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicPackResult.class);
        log.info(JSON.toJSONString(skylabResponse));
    }

    @Test
    public void sync_learn_master_fetch() {


        for (int i = 0; i < 1; i++) {

            SkylabRequest<MasterFetch4CatalogParam> skylabRequest = new SkylabRequest<>();
            String traceId = UUID.randomUUID().toString();
            log.info("traceId: {}", traceId);
            skylabRequest.setTraceId(traceId);

            SceneInfo sceneInfo = new SceneInfo();
            sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
            sceneInfo.setAreaCode("000000");
            sceneInfo.setBizAction(BizActionEnum.NONE);
            sceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
//        sceneInfo.setGraphVersion("v10");
//        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
            sceneInfo.setGradeCode("07");
            //学科
            sceneInfo.setSubjectCode("02");
            //学段
            sceneInfo.setPhaseCode("04");
            //用户id
            sceneInfo.setUserId("xz182_test77");
            //教材
            sceneInfo.setPressCode("01");
            sceneInfo.setBookCode("01_08020101-002");
            sceneInfo.setCatalogCode("01_08020101-002_06_002");

            skylabRequest.setScene(sceneInfo);

            MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();
            masterFetch4CatalogParam.setCatalogIds(Arrays.asList("01_08020101-002_06_003"));
            skylabRequest.setPayload(masterFetch4CatalogParam);
            SkylabResponse<MasterFetchResult> skylabResponse = diagnoseService.diagnose(skylabRequest);
            log.info(JSONUtil.toJsonPrettyStr(skylabResponse));

        }
    }

    @Test
    public void sync_learn_study_log() {
//        sync_learn_master_fetch();
        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");
//        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        sceneInfo.setGradeCode("02");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("xz182_test777");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("001_02020101-003");
        sceneInfo.setCatalogCode("01_02020101-003_02_period30");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("3b391655-f3ec-40fb-9898-3910b5ab0497");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("c2d162ef-78e1-4938-a5f7-ed653a994dc0");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("18a8638e-290e-4f8c-b460-c783696efd91");
        studyLogRecord.setRefTraceId("fdbf37d957af4e3dac342f271adac799");
        studyLogRecord.setRoundIndex("3");
        studyLogRecord.setScore(0.0);
        studyLogRecord.setStandardScore(2.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_02020101-003");
        studyLogRecord.setCatalogCode("01_02020101-003_02_period30");
//        studyLogRecord.setCorrectTraceId("c4016d98-2496-4808-ae28-4db2765cfa9a");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);

        CheckResultUtil.check(skylabRequest, skylabResponse);
        sync_learn_master_fetch();
    }

    @Test
    public void sync_learn_study_log2() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");
//        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        sceneInfo.setGradeCode("01");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("03");
        //用户id
        sceneInfo.setUserId("8a0cbc3f-0d7a-4009-a8fb-a3817ce15d33");
        //教材
        sceneInfo.setPressCode("282");
        sceneInfo.setBookCode("282_01020108282-6470");
        sceneInfo.setCatalogCode("282_01020108282-6470_01020108282-6470-192440_01020108282-6470-192767_period30");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("de9dcf3a-80d0-41bc-8e1f-9847d5564fcb");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("e7a693d4-ae93-40be-8b5a-d311a2c2deb4");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);

        studyLogRecord.setRoundId("cd67ccd2-3979-4437-8acd-e1274c348d0e");

        studyLogRecord.setRefTraceId("6471f43400ac458aaa7e932e2584b5af");
        studyLogRecord.setRoundIndex("" + 1);
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("282_01020108282-6470");
        studyLogRecord.setCatalogCode("282_01020108282-6470_01020108282-6470-192440_01020108282-6470-192767_period30");
        studyLogRecord.setCorrectTraceId("2c3a82d545c14810a4d85f08aa99fc84");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }

    @Test
    public void sync_learn_study_log_mac() {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_SYNC_TEST);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.NONE);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xz182");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("5aaf5a95-bffc-4cc1-a012-451313bcd581");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode("01_08020101-002_06_003");
        studyLogRecord.setCorrectTraceId("c4016d98-2496-4808-ae28-4db2765cfa9a");

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));


        sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_QUALITY_TEST_PAPER);
        skylabRequest.setTraceId(IdUtil.fastSimpleUUID());
        SkylabResponse<StudyLogResult> skylabResponse2 = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse2));

        sceneInfo.setStudyCode(StudyCodeEnum.MACROGRAPH_ERROR_BOOK);
        sceneInfo.setBizAction(BizActionEnum.MACROGRAPH_ERROR_BOOK_VARY);
        skylabRequest.setTraceId(IdUtil.fastSimpleUUID());
        SkylabResponse<StudyLogResult> skylabResponse3 = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse3));


    }

    @Test
    public void sync_learn_study_correct_log() {

        SkylabRequest<StudyCorrectLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
//        sceneInfo.setGraphVersion("v10");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xwliu01");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");


        StudyCorrectLogParam studyCorrectLogParam = new StudyCorrectLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId("34e87921-cfcf-44aa-b1f6-49fc703cfcd7");
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId("5aaf5a95-bffc-4cc1-a012-451313bcd581");
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecord.setRoundId("RoundId27f0f2478c57428daced462d47f0e50b");
        studyLogRecord.setRefTraceId("e6e35705-a84c-4cb6-bb49-d686d1c3469e");
        studyLogRecord.setRoundIndex("1");
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode("01_08020101-002");
        studyLogRecord.setCatalogCode("01_08020101-002_06_003");

        studyCorrectLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyCorrectLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        log.info(JSON.toJSONString(skylabResponse));
    }


    @Test
    public void study_log_query() {

        SkylabRequest<StudyLogQueryParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xz182");
        //教材
        sceneInfo.setPressCode("01");

        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");

        skylabRequest.setScene(sceneInfo);

        StudyLogQueryParam queryParam = new StudyLogQueryParam();
        queryParam.setCatalogId("01_08020101-002_06_003");

        skylabRequest.setPayload(queryParam);

        SkylabResponse<StudyLogQueryResult> skylabResponse = recommendService.recommend(skylabRequest, StudyLogQueryResult.class);
        log.info(JSONUtil.toJsonPrettyStr(skylabResponse));
    }
}
