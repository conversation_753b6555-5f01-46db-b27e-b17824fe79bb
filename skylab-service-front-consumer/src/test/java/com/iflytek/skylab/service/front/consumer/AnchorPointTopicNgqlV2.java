package com.iflytek.skylab.service.front.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * 锚点补题删题测试用例
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/2 16:57
 */
@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class AnchorPointTopicNgqlV2 {


    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabBehaviorService behaviorService;

    @Test
    public void checkAfter1() {
        checkAfter();
    }
    @Test
    public void test() {
//        String nodeIds = "ec4dd422-9e4e-46d9-a681-0701b02c08d3,53afc663-2337-4af1-a917-c44426ca0d8f,ad05227e-093c-4d3e-a2f9-c4c30823c822,55e32607-ccc2-49bd-8346-336fe38472da,6568e40d-bf67-4538-825c-74791d9cd83f,0bb770aa-c80f-4d6e-bd94-84a361b7205a,1086a1a1-3f83-4988-b982-5024c1b1a2a8,cc6f58e0-bc6b-4384-8bc8-26cdb250fb30,a7aa88c1-c8d4-4127-87b2-4dbb5b41529c,52d7479d-7db9-4f15-92c2-9717dc584ffa,e4a25154-22f9-4131-b16f-b434e3da60e3,2a303008-37b2-4801-bda0-0dcb2dd38c80,ba937c03-3ba9-42de-91af-0b82b4b83e85,51977b30-3bd9-4b2f-8a2e-9ed912351350,389c61df-ce32-425c-b6e1-23004509728a,df479220-1323-445c-b9a1-94c7a8f6dc86,e5c6c530-fdd1-4d75-9cb0-215f41aebdfb,be4e4357-6d2d-42b9-86f2-a2cc98e236f4,b39f7ed0-cc5a-403a-a3d2-a53af333b5a1,fd05b7c1-c11e-4400-8d77-442abadb0af2,edcb58b3-4db3-4fe7-bef9-2f7e96b46df3,f3495c99-9b96-4af2-bd6b-a8ecc8b49977,29927a73-737a-4c76-bd0c-89b6ca491a0a,15a59d7c-c0ce-4f6d-81bb-14f74739725f";

        Map<String, List<String>> cataLogMap = JSON.parseObject(FileUtil.readUtf8String("D:\\11\\cataAnchor.txt"), new TypeReference<Map<String, List<String>>>() {
        });

        for (Map.Entry<String, List<String>> entry : cataLogMap.entrySet()) {
            String cata = entry.getKey();
            String cataLog = cata.split("#")[0];
            String subjectCode = cata.split("#")[1];
            String phaseCode = cata.split("#")[2];
            List<String> anchors = entry.getValue();
            for (String anchor : anchors) {
                for (int i = 1; i < 30; i++) {
                    sync_learn_rec_topic(subjectCode, phaseCode, cataLog, anchor, i);
                }
            }
//            break;
        }

        System.err.println("结束");


    }


    public void sync_learn_rec_topic(String subjectCode,String phaseCode,String cataLog, String nodeId, int i) {
        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode(subjectCode);
        //学段
        sceneInfo.setPhaseCode(phaseCode);
        //用户id
        sceneInfo.setUserId("xiangzhang182_test");
        //教材
        sceneInfo.setCatalogCode(cataLog);

        List<String> split = StrUtil.split(sceneInfo.getCatalogCode(), "_");
        sceneInfo.setPressCode(split.get(0));
        sceneInfo.setBookCode(split.get(0) + "_" + split.get(1));

        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
//        recTopicParam.setNodeId("ac935ac1-bbdf-4654-bddc-7a0a9e97beb3");
        recTopicParam.setNodeId(nodeId);
        recTopicParam.setTopicOrderNumber(i);
        skylabRequest.setPayload(recTopicParam);

        SkylabResponse<RecTopicResult> skylabResponse = recommendService.recommend(skylabRequest, RecTopicResult.class);
        RecTopicResult payload = skylabResponse.getPayload();
        log.info(JSON.toJSONString(skylabResponse));
        FileUtil.appendUtf8Lines(Lists.newArrayList(JSON.toJSONString(skylabResponse)), "pre-byod-result.txt");
        sync_learn_study_log(sceneInfo,payload.getNodeId(), payload.getTopicId(), i, traceId, IdUtil.fastSimpleUUID());
    }

    public void sync_learn_study_log( SceneInfo sceneInfo,String nodeId, String topic, int index, String refTraceId, String roundId) {

        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        log.info("traceId: {}", traceId);
        skylabRequest.setTraceId(traceId);


        StudyLogParam studyLogParam = new StudyLogParam();
        StudyLogRecord studyLogRecord = new StudyLogRecord();
        studyLogRecord.setFeedbackTime(new Date());
        studyLogRecord.setNodeId(nodeId);
        studyLogRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecord.setResNodeId(topic);
        studyLogRecord.setResNodeType(ResourceTypeEnum.TOPIC);

        studyLogRecord.setRoundId(roundId);

        studyLogRecord.setRefTraceId(refTraceId);
        studyLogRecord.setRoundIndex("" + index);
        studyLogRecord.setScore(5.0);
        studyLogRecord.setStandardScore(5.0);
        studyLogRecord.setTimeCost(650);
        studyLogRecord.setBookCode(sceneInfo.getBookCode());
        studyLogRecord.setCatalogCode(sceneInfo.getCatalogCode());
        studyLogRecord.setCorrectTraceId(IdUtil.fastSimpleUUID());

        studyLogParam.setItems(Arrays.asList(studyLogRecord));

        skylabRequest.setScene(sceneInfo);
        skylabRequest.setPayload(studyLogParam);

        SkylabResponse<StudyLogResult> skylabResponse = behaviorService.reportAnswerRecord(skylabRequest);
        CheckResultUtil.check(skylabResponse, sceneInfo.getUserId());
    }


    public static void checkAfter() {
        HashSet<String> containsTopics = new HashSet<>();

        Map<String, List<String>> anchorPointTopicMap = JSON.parseObject(FileUtil.readUtf8String("D:\\11\\anchorPointTopicMap.txt"), new TypeReference<Map<String, List<String>>>() {
        });
        HashSet<String> topics = new HashSet<>( );
        for (Map.Entry<String, List<String>> entry : anchorPointTopicMap.entrySet()) {
            topics.addAll(entry.getValue());
        }
        List<String> fileList = FileUtil.readUtf8Lines("D:\\workspace\\tuijing\\skylab-platform\\skylab-service-front-consumer\\target\\test-classes\\pre-byod-result.txt");

        for (String s : fileList) {
            for (String topic : topics) {
                if (s.contains(topic)) {
                    System.out.println(topic);
                    containsTopics.add(topic);
                }
            }
        }
        System.err.println("命中" + containsTopics.size());

        Collection<String> subtract = CollUtil.subtract(topics, containsTopics);
        System.err.println("未命中" + subtract.size());
        System.err.println(JSONUtil.toJsonStr(subtract));
    }
}
