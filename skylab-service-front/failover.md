# skylab-platform兜底功能
skylab-platform兜底功能主要针对 **推荐 / 诊断相关接口**，由两部分组成：
- skylab-front(接入服务)中的兜底逻辑判断
- skylab-failover(兜底服务)

## 1 skylab-front兜底逻辑判断
接入服务中，主要是对触发兜底的条件或阈值，进行判断或控制。



### 1.1 常规请求调用过程
```mermaid
sequenceDiagram
title: 通常情况下推荐/诊断请求调用过程

请求方 ->> 接入服务: 请求
接入服务 ->> 路由服务: 请求
路由服务 ->> 推荐/诊断服务: 请求
推荐/诊断服务 ->> 推荐/诊断服务: 调用引擎接口，完成推荐/诊断
推荐/诊断服务 -->> 路由服务: 返回数据
路由服务 -->> 接入服务: 返回数据
接入服务 -->> 请求方: 返回数据
```



### 1.2 兜底逻辑判断

*控制逻辑的前提，是需要开启兜底功能，否则无效。*

**一、发送request至路由服务前：**

若当前场景(StudyCode)的功能(FunctionCode)**连续错误**次数达到阈值，则将此次请求直接转发至兜底服务处理。



**二、收到路由服务response后：**

若response中code == 0，则重置当前场景(StudyCode)的功能(FunctionCode)**连续错误**次数。

若response中code != 0，则当前场景(StudyCode)的功能(FunctionCode)**连续错误**次数+1。并根据response的头中ErrorNodes是否包含"failover"字符，判断引擎是否要求此次请求兜底。若是，则将此次请求转发至兜底服务处理；若否，则直接返回。



**三、请求路由服务异常情况：**

将此次请求转发至兜底服务处理。



### 1.3 相关配置项
```properties
# 兜底功能是否开启
skylab.front.failover.enable=false
# 连续错误次数阈值
skylab.front.failover.failed-limit=30
```



### 1.4 兜底相关@Endpoint
为了便于观察、控制兜底功能，接入服务通过 SpringBoot Actuator 提供了两个@Endpoint。

**一、failover-info** 

查看兜底配置、以及各场景(StudyCode)的功能(FunctionCode)**连续错误**次数

**二、failover-reset**

重置各场景(StudyCode)的功能(FunctionCode)**连续错误**次数





## 2 skylab-failover(兜底服务)

兜底服务中，主要是为 **兜底引擎** 提供数据出入参的转换，并有一部分业务逻辑(逻辑同常规推荐 / 诊断)

兜底引擎依赖
```xml
<dependency>
    <groupId>com.iflytek.hy.pl.engine</groupId>
    <artifactId>recommend-engine-simple</artifactId>
    <version>1.0.11-SNAPSHOT</version>
</dependency>
```
