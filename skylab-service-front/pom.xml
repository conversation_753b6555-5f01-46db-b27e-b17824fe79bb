<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skylab</groupId>
        <artifactId>skylab-platform</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>skylab-service-front</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.source.skip>true</maven.source.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-dataapi</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-service-failover</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-base</artifactId>
            <version>${recommend-engine.version}</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.edu</groupId>
            <artifactId>epas-dubbo</artifactId>
            <version>${epas-dubbo.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-expression</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
            <version>0.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.6</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ZooKeeper 3.4.x 已经不维护了，所以不能和 Curator 最新版本兼容，需要使用 Curator 4.2.x 的兼容模式 -->
        <!-- https://curator.apache.org/zk-compatibility-34.html -->
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>4.2.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-x-discovery</artifactId>
            <version>4.2.0</version>
        </dependency>

        <!--   alibaba sentinel (<NAME_EMAIL> 2023/07/11)   -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-core</artifactId>
            <version>1.8.6</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-transport-simple-http</artifactId>
            <version>1.8.6</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}-Build${buildSid}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <echo message="copy *.jar to target ..."/>
                                <copy todir="${basedir}/../target">
                                    <fileset dir="${project.build.directory}">
                                        <include name="*.jar"/>
                                    </fileset>
                                </copy>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>
