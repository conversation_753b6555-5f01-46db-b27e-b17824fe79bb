## skylab-service-front

HTTP 接入层（汇聚外部请求，转发至内部服务；同时暴露 Dubbo/EPAS Provider）

### 1. 项目概述
- 项目名称：skylab-service-front
- 项目描述：统一 HTTP 接入，封装 scene/payload/traceId，并路由推荐、画像、行为、特征、清空画像、小学迁移等能力
- 版本信息：2.0.9-SNAPSHOT（开发中）
- 端口：32181（dev）；Dubbo 端口：32180

### 2. 技术架构
- 框架：Spring Boot、Dubbo、Zookeeper、Knife4j
- 控制器：FrontController（/skylab/api/v2/*）
- Provider：front-spring-dubbo.xml 中注册 Skylab*Service（EPAS 与 ZK 双注册）

#### 架构图（Mermaid）
```mermaid
flowchart TD
  EXT[External Clients] --> FRONT[skylab-service-front]
  FRONT -->|REST| STAND
  FRONT -->|REST| DIAG
  FRONT -->|REST| SORT
  FRONT -->|REST| CLEAR
  FRONT -->|REST| PRIMARY
  FRONT ---|Dubbo/EPAS| DUBBO[(EPAS/ZK)]
```

### 3. 主要功能
- 推荐：POST /skylab/api/v2/recommend
- 画像：POST /skylab/api/v2/diagnose
- 行为：POST /skylab/api/v2/reportAnswerRecord、/queryAnswerRecord
- 特征：POST /skylab/api/v2/featureFetch
- 清空画像：POST /skylab/api/v2/clearMastery
- 小学迁移：POST /skylab/api/v2/primary/behavior、/primary/mastery

### 4. 业务处理逻辑
- 按 header: sceneType/funcParam/funcResult 动态解析 SceneInfo 与 FuncParam，委托至对应 Dubbo Service

### 5. 接口（REST）
均为 POST，Content-Type: application/json；详见 FrontController 源码

### 6. 系统配置
- 端口：`server.port=32181`；`dubbo.port=32180`
- Dubbo XML 路径：`skylab.dubbo.xml.path`（默认 classpath:/front-spring-dubbo.xml）
- Zookeeper：`spring.cloud.zookeeper.connect-string=${IP}:2181`
- Sentinel：application.properties 中 csp.sentinel.* 与本地规则

### 7. 启动与验证
```bash
mvn -pl skylab-service-front spring-boot:run -Dspring-boot.run.profiles=dev
```
访问 http://<host>:32181/doc.html 查看文档

### 8. 开发指南
- 启动类：com.iflytek.skylab.service.front.FrontAppBoot
- Dubbo 服务与契约：skylab-core-contract
- 建议：为 http 接入编写端到端联调用例（Mock Dubbo 层或本地起各服务）
