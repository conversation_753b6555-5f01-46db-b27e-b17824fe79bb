package com.iflytek.skylab.service.front.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/5/16 14:44
 */@Getter
@Setter
@ApiModel(description = "兜底更新请求")
public class FailoverRequest {

    @ApiModelProperty(name = "是否强制兜底")
    private Boolean force;

    @ApiModelProperty(name = "是否开启兜底")
    private Boolean enable;

    @ApiModelProperty(name = "进入兜底模式前，最大连续失败次数")
    private Integer failedLimit;

    @ApiModelProperty(name = "是否重置失败请求计数")
    private Boolean reset;

}
