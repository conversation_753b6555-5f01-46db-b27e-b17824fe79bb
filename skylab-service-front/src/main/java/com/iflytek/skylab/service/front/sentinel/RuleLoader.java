package com.iflytek.skylab.service.front.sentinel;

import cn.hutool.core.io.FileUtil;
import com.alibaba.csp.sentinel.slots.block.AbstractRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.alibaba.csp.sentinel.slots.block.degrade.circuitbreaker.EventObserverRegistry;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/11 17:25
 */
@Slf4j
public class RuleLoader {

    private static final String RULE_PATH_CONFIG_KEY = "app.sentinel.rule-path";

    private final String path;
    private final SentinelDegradeObserver observer;


    public RuleLoader(Environment env, SentinelDegradeObserver sentinelDegradeObserver) {
        this.path = env.getProperty(RULE_PATH_CONFIG_KEY);
        this.observer = sentinelDegradeObserver;
    }


    @PostConstruct
    public void load() {
        if (StringUtils.isBlank(this.path)) {
            log.error("load rule failed, file path is not configured");
            return;
        }

        // 读取sentinel规则文件
        RuleObject ruleObject;
        try {
            String content = FileUtil.readUtf8String(this.path);
            ruleObject = JSONObject.parseObject(content, RuleObject.class);
        } catch (Exception e) {
            log.error("load rule failed, can not read file '{}' ! message:{}", this.path, e.getMessage());
            return;
        }

        // 装载sentinel规则
        try {
            loadFlowRule(ruleObject);
            loadDegradeRule(ruleObject);
        } catch (Exception e) {
            log.error("load rule failed! message:{}", e.getMessage());
        }
    }

    /**
     * 装载sentinel限流规则
     *
     * @param ruleObject
     * @throws Exception
     */
    private void loadFlowRule(RuleObject ruleObject) throws Exception {
        if (ruleObject.flow == null) {
            return;
        }

        FlowRuleManager.loadRules(ruleObject.flow);
    }


    /**
     * 装载sentinelr降级规则
     *
     * @param ruleObject
     * @throws Exception
     */
    private void loadDegradeRule(RuleObject ruleObject) throws Exception {
        if (ruleObject.degrade == null) {
            return;
        }

        List<String> resources = DegradeResourceUtils.getResources();

        Map<String, DegradeRule> ruleMap = Maps.uniqueIndex(ruleObject.degrade, AbstractRule::getResource);
        DegradeRule defaultRule = ruleMap.get("*");

        // 遍历资源，组装rule对象
        List<DegradeRule> degradeRules = new ArrayList<>();
        for (String resource : resources) {
            // 已显式配置的，应用配置的规则
            if (ruleMap.containsKey(resource)) {
                degradeRules.add(ruleMap.get(resource));
                continue;
            }

            // 未显式配置的，尝试应用默认的规则
            if (defaultRule != null) {
                DegradeRule rule = new DegradeRule(resource);
                BeanUtils.copyProperties(defaultRule, rule, "resource");
                degradeRules.add(rule);
            }
        }
        // 规则加载到sentinel
        DegradeRuleManager.loadRules(degradeRules);
        // 注册熔断事件监听
        EventObserverRegistry.getInstance().addStateChangeObserver(observer.getClass().getSimpleName(), observer);
    }

    @Getter
@Setter
    public static class RuleObject {

        // 限流规则
        private List<FlowRule> flow;

        // 降级规则
        private List<DegradeRule> degrade;

    }
}
