package com.iflytek.skylab.service.front.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.SphO;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.Tracer;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.TraceConstant;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.dataapi.exception.SkylabException;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.failover.controller.FailoverController;
import com.iflytek.skylab.service.front.configuration.FrontProperties;
import com.iflytek.skylab.service.front.exception.FailoverFeignException;
import com.iflytek.skylab.service.front.sentinel.DegradeResourceUtils;
import com.iflytek.skylab.service.front.util.FailoverUtils;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.data.TraceHeaderUtils;
import com.iflytek.skyline.common.api.*;
import com.iflytek.skyline.common.feign.SkylineRouterFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.MultiValueMap;

import java.util.Optional;

/**
 * 非推荐相关的功能 服务接口
 *
 * <AUTHOR>
 * @date 2022/3/2 10:48 上午
 */
@Slf4j
public class BaseFrontService {

    @Value("${spring.application.name:skylab-front}")
    private String springApplicationName;

    @Autowired
    private FailoverGuard2 failoverGuard2;

    @Lazy
    @Autowired
    private SkylineRouterFeign skylineRouterFeign;

    @Lazy
    @Autowired
    private FailoverController failoverController;

    // @Value("${skylab.data.api.graph.defaultVersion}")
    // private String graphDefaultVersion;

    @Autowired
    private FrontProperties frontProperties;
    @Autowired
    private TraceUtils traceUtils;


    /**
     * 准备调用路由服务，并处理限流
     *
     * @param skylabRequest 场景、点诊断或推题 交互上下文、推荐信息
     * @param clazz
     * @return 相似题列表
     */
    public <T extends FuncResult> SkylabResponse<T> process(SkylabRequest<? extends FuncParam> skylabRequest, Class<T> clazz, String sentinelFlowResName) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabRequest= {}", skylabRequest);
        }
        SkylabResponse<T> response = new SkylabResponse<>();
        response.setTraceId(skylabRequest.getTraceId());

        ApiResponse apiResponse;
        // Sentinel流量控制
        if (SphO.entry(sentinelFlowResName)) {
            // 未到限流情况，执行正常逻辑
            try {
                apiResponse = this.routeCall(skylabRequest);
            } finally {
                SphO.exit();
            }
        } else {
            // 达到限流情况，执行兜底逻辑
            apiResponse = this.failoverCall(toApiRequest(skylabRequest), "流量控制");
        }
        if (log.isDebugEnabled()) {
            log.debug("ApiResponse= {}", apiResponse);
        }
        assert apiResponse != null;
        response.setCode(apiResponse.getHeader().getCode());
        response.setMessage(apiResponse.getHeader().getMessage());

        if (apiResponse.getHeader().getCode() == 0 && apiResponse.getPayload() != null) {
            T obj = JSON.to(clazz, apiResponse.getPayload());
            response.setPayload(obj);

            //调试状态，回写 相关的调试信息
            if (skylabRequest.getScene().isTest() && apiResponse.getHeader().getErrorNodes() != null && apiResponse.getHeader().getErrorNodes().size() > 0) {
                response.setContext(apiResponse.getHeader().getContext());
                response.setErrorNodes(JSON.parseArray(JSON.toJSONString(apiResponse.getHeader().getErrorNodes())));
            }
        }

        // 写入兜底标记信息
        FailoverUtils.appendSkylabResponseErrorNodeIfFoundFailoverErrorNode(apiResponse, response);
        if (log.isDebugEnabled()) {
            log.debug("SkylabResponse= {}", response);
        }
        return response;
    }


    /**
     * 调用路由服务，并处理熔断降级
     *
     * @param skylabRequest
     * @return
     */
    private ApiResponse routeCall(SkylabRequest<? extends FuncParam> skylabRequest) {
        ApiRequest apiRequest = toApiRequest(skylabRequest);
        ApiResponse apiResponse = null;

        SceneInfo sceneInfo = skylabRequest.getScene();
        // 从sceneInfo中拼接熔断降级资源
        String degradeResource = DegradeResourceUtils.getResource(sceneInfo);

        // Sentinel熔断降级控制
        Entry entry = null;
        try {
            entry = SphU.entry(degradeResource);

            // 路由服务转发
            MultiValueMap<String, String> header = TraceHeaderUtils.buildHeader(skylabRequest.getTraceId(), springApplicationName);
            apiResponse = skylineRouterFeign.routeCall(apiRequest, header);

            // 读取respCode
            Optional<ApiResponseHeader> respHeader = Optional.ofNullable(apiResponse).map(ApiResponseGeneric::getHeader);
            int respCode = respHeader.map(ApiResponseHeader::getCode).orElse(ApiErrorCode.ERROR.getCode());
            // 请求未成功则抛出异常
            if (respCode != ApiErrorCode.SUCCESS.getCode()) {
                String message = respHeader.map(ApiResponseHeader::getMessage).orElse(ApiErrorCode.ERROR.getMessage());
                throw new SkylabException(respCode, message);
            }
        } catch (Exception ex) {
            if (BlockException.isBlockException(ex)) {
                apiResponse = this.failoverCall(apiRequest, "熔断降级");
            } else {
                // 若捕获的是业务异常，非Sentinel的降级异常，则需要记录异常
                Tracer.trace(ex);
                apiResponse = this.failoverCall(apiRequest, "业务异常");
            }
        } finally {
            if (entry != null) {
                entry.exit();
            }
        }
        return apiResponse;
    }

    /**
     * 调用兜底逻辑
     *
     * @param apiRequest
     * @param reason
     * @return
     */
    private ApiResponse failoverCall(ApiRequest apiRequest, String reason) {
        log.error("{} : failoverCall because of 【{}】", apiRequest.getTraceId(), reason);
        String userId = Optional.of(apiRequest)
                .map(ApiRequestBase::getScene)
                .map(scene -> scene.getString("userId"))
                .orElse(null);
        if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
            //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
        }else {
            traceUtils.record("FAIL_OVER", reason);
        }
        try {
            ApiResponse apiResponse = failoverController.process(apiRequest);
            // apiResponse.getHeader().setMessage(String.format("failoverCall because of 【%s】", reason));
            return apiResponse;
        } catch (Exception e) {
            log.error("call failover service failed! msg:{}", e.getMessage());
            throw new FailoverFeignException(-1, "调用兜底服务异常", e);
        }
    }


    /**
     * 入参转换
     *
     * @param skylabRequest
     * @return
     */
    private ApiRequest toApiRequest(SkylabRequest<? extends FuncParam> skylabRequest) {
        Assert.notBlank(skylabRequest.getTraceId(), "skylabRequest.traceId must not empty.");
        Assert.notNull(skylabRequest.getScene(), "skylabRequest.scene must not null.");
        Assert.notNull(skylabRequest.getPayload(), "skylabRequest.payload must not null.");
        Assert.notBlank(skylabRequest.getPayload().getFuncCode(), "skylabRequest.payload.funcCode must not empty.");

        String userId = skylabRequest.getScene().getUserId();
        String funcCode = skylabRequest.getPayload().getFuncCode();
        String graphVersion = skylabRequest.getScene().getGraphVersion();
        if (StrUtil.isBlank(graphVersion)) {
            graphVersion = frontProperties.getDefaultGraphVersion();
        }
        log.info("request userId:{}, traceId:{}, funcCode:{}, graphVersion:{}", userId, skylabRequest.getTraceId(), funcCode, graphVersion);

        skylabRequest.getScene().setFunctionCode(funcCode);
        skylabRequest.getScene().setGraphVersion(graphVersion);

        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setTraceId(skylabRequest.getTraceId());
        apiRequest.setObjectPayload(skylabRequest.getPayload());
        apiRequest.setObjectScene(skylabRequest.getScene());

        return apiRequest;
    }
}
