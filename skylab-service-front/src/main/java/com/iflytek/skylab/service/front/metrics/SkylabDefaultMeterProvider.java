package com.iflytek.skylab.service.front.metrics;

import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.front.configuration.MetricsConfiguration;
import com.iflytek.skylab.service.front.util.FailoverUtils;
import com.iflytek.skyline.brave.metric.SkylineMeterProvider;
import com.iflytek.skyline.brave.metric.TagValueDict;
import com.iflytek.skyline.common.api.ApiResponseHeader.ErrorNode;
import io.micrometer.core.instrument.Tags;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Optional;

import static com.iflytek.skyline.brave.metric.SkylineTagsRegistry.NONE;
import static com.iflytek.skyline.brave.metric.TagNames.*;

/**
 * @author: leitong
 * @date: 2023/1/5 16:02
 * @description:
 **/
public class SkylabDefaultMeterProvider implements SkylineMeterProvider {

    /**
     * ext1="MINI_PROGRAM"
     */
    public static final String MINI_PROGRAM = "MINI_PROGRAM";

    /**
     * 产生标签 ：业务，学科，学段，场景，请求类型
     * @param args
     * @return
     */
    @Override
    public Tags tagsByArgs(Object[] args) {
        if(ArrayUtils.isNotEmpty(args) && args[0] instanceof SkylabRequest){
            SkylabRequest<?> request = (SkylabRequest<?>) args[0];
            SceneInfo scene = request.getScene();
            String bizCode = Optional.ofNullable(scene).map(s -> normalizeBizCode(s.getBizCode())).orElse(NONE);
            String biz = TagValueDict.getValue(BIZ_CODE, bizCode, NONE);
            String subjectCode = Optional.ofNullable(scene).map(s -> s.getSubjectCode()).orElse(NONE);
            String subject = TagValueDict.getValue(SUBJECT_CODE, subjectCode, NONE);
            String phaseCode = Optional.ofNullable(scene).map(s -> s.getPhaseCode()).orElse(NONE);
            String phase = TagValueDict.getValue(PHASE_CODE, phaseCode, NONE);
            String scenario = Optional.ofNullable(scene).map(s -> s.getStudyCode().getDesc()).orElse(NONE);
            String test = Optional.ofNullable(scene).map(s -> new Boolean(s.isTest()).toString()).orElse(NONE);
//            String isMobileApp = Optional.ofNullable(scene).map(s->new Boolean(MINI_PROGRAM.equals(s.getExt1())).toString()).orElse(NONE);
            String isMobileApp = Optional.ofNullable(scene).map(s -> s.getExt1()).orElse(NONE);
            return Tags.of(BIZ_CODE, bizCode, BIZ, biz, SUBJECT_CODE, subjectCode,
                    SUBJECT, subject, PHASE_CODE, phaseCode, PHASE, phase, SCENARIO, scenario, TEST, test, MetricsConfiguration.IS_MOBILE_APP, isMobileApp);
        }
        return Tags.empty();
    }

    @Override
    public Tags tagsByReturn(Object retObj) {
        if(retObj !=null && retObj instanceof SkylabResponse){
            SkylabResponse<?> response = (SkylabResponse<?>)retObj;
            String code = String.valueOf(response.getCode());
            // 判断是否兜底
            ErrorNode errorNode = FailoverUtils.findFailoverErrorNode(response);
            String guard = errorNode == null ? "false" : "true";

            return Tags.of(CODE, code, GUARD, guard);
        }
        return Tags.empty();
    }

    /**
     * 业务编码规范化，保持全局统一
     * @param bizCodeEnum
     * @return
     */
    private static String normalizeBizCode(BizCodeEnum bizCodeEnum){
        String bizCode = bizCodeEnum.toString().toLowerCase();
        return bizCode.replace("zsy_","");
    }
}
