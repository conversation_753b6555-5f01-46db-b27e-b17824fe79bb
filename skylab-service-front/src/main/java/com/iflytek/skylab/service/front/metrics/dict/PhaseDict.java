package com.iflytek.skylab.service.front.metrics.dict;

import java.util.HashMap;
import java.util.Map;

/**
 * 学段
 * <AUTHOR>
 * @date 2022/7/8 11:55
 */
public final class PhaseDict {

    public static String getName(String code) {
        return dictMap.getOrDefault(code, "unknown");
    }

    public static Map<String, String> dictMap = new HashMap<>();
    static {
        dictMap.put("01","幼儿园");
        dictMap.put("02","学前班");
        dictMap.put("03","小学");
        dictMap.put("04","初中");
        dictMap.put("05","高中");
        dictMap.put("06","大学");
        dictMap.put("10","中职");
        dictMap.put("07","演示");
        dictMap.put("08","职教");
        dictMap.put("09","特殊教育");
        dictMap.put("11","高职");
        dictMap.put("16","研究生");
    }
}
