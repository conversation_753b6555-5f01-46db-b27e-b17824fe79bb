package com.iflytek.skylab.service.front.service;

import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.RecFuncEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.constant.StudyFuncEnum;
import com.iflytek.skylab.core.data.AcquireFeatureParam;
import com.iflytek.skylab.core.data.FeatureParam;
import com.iflytek.skylab.core.data.MasterFetch4CatalogParam;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.front.configuration.FailoverProperties;
import com.iflytek.skyline.common.api.ApiResponse;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.AppContext;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/10 11:03
 */
@Slf4j
public class FailoverGuard2 {

    @Getter
    private final Map<String, AtomicInteger> countMap = new ConcurrentHashMap<>();
    private static final String GAUGE_NAME = "skylab_platform_failover_count_value";

    private final List<String> recFunctions;
    private final List<String> diagFunctions;

    private final List<String> studentLogFunctions;

    private final List<String> featureFunctions;

    private final MeterRegistry meterRegistry;
    private final FailoverProperties properties;
    private final String ip;

    public FailoverGuard2(MeterRegistry meterRegistry, FailoverProperties properties, AppContext appContext) {
        this.meterRegistry = meterRegistry;
        this.properties = properties;
        this.ip = StringUtils.isBlank(appContext.getIpEndpoint()) ? "unknown" : appContext.getIpEndpoint();
        this.recFunctions = Arrays.stream(RecFuncEnum.values()).map(Enum::name).collect(Collectors.toList());
        this.diagFunctions = Lists.newArrayList(MasterFetch4CatalogParam.FUNC_CODE);
        this.studentLogFunctions = Arrays.stream(StudyFuncEnum.values()).map(Enum::name).collect(Collectors.toList());
        this.featureFunctions = Lists.newArrayList(FeatureParam.FUNC_CODE, AcquireFeatureParam.FUNC_CODE);
    }

    @PostConstruct
    private void init() {
        for (StudyCodeEnum studyCode : StudyCodeEnum.values()) {
            // 初始化推荐计数
            for (String funcCode : recFunctions) {
                AtomicInteger count = this.countMap.computeIfAbsent(mapKey(studyCode, funcCode), val -> new AtomicInteger());
                this.meterRegistry.gauge(GAUGE_NAME, tags(studyCode, funcCode), count, AtomicInteger::get);
            }
            // 初始化诊断计数
            for (String funcCode : diagFunctions) {
                AtomicInteger count = this.countMap.computeIfAbsent(mapKey(studyCode, funcCode), val -> new AtomicInteger());
                this.meterRegistry.gauge(GAUGE_NAME, tags(studyCode, funcCode), count, AtomicInteger::get);
            }
            // 初始化作答日志计数
            for (String funcCode : studentLogFunctions) {
                AtomicInteger count = this.countMap.computeIfAbsent(mapKey(studyCode, funcCode), val -> new AtomicInteger());
                this.meterRegistry.gauge(GAUGE_NAME, tags(studyCode, funcCode), count, AtomicInteger::get);
            }
            // 初始化特征
            for (String funcCode : featureFunctions) {
                AtomicInteger count = this.countMap.computeIfAbsent(mapKey(studyCode, funcCode), val -> new AtomicInteger());
                this.meterRegistry.gauge(GAUGE_NAME, tags(studyCode, funcCode), count, AtomicInteger::get);
            }
        }
    }

    /**
     * 调用路由服务前，判断是否达到阈值，进行兜底
     *
     * @param traceId
     * @param sceneInfo
     * @return
     */
    public boolean beforeRoute(String traceId, SceneInfo sceneInfo) {
        StudyCodeEnum studyCode = sceneInfo.getStudyCode();
        String funcCode = sceneInfo.getFunctionCode();
        // 判断功能是否支持，开关是否开启
        if (properties.isEnable()) {
            AtomicInteger count = this.countMap.get(mapKey(studyCode, funcCode));
            // 判断计数是否达到阈值
            if (count != null && count.get() >= properties.getFailedLimit()) {
                log.info("traceId:{},studyCode:{},funcCode:{}，call skylab-failover because of: exceed the limit", traceId, studyCode, funcCode);
                return true;
            }
        }
        return false;
    }

    public boolean afterRoute(String traceId, SceneInfo sceneInfo, ApiResponse apiResponse) {
        StudyCodeEnum studyCode = sceneInfo.getStudyCode();
        String funcCode = sceneInfo.getFunctionCode();
        AtomicInteger count = this.countMap.get(mapKey(studyCode, funcCode));
        // 判断功能是否支持，开关是否开启
        if (properties.isEnable() && count != null) {
            int respCode = apiResponse.getHeader().getCode();
            // 异常请求，增加技术
            if (respCode != 0) {
                int newCount = count.incrementAndGet();
                log.info("traceId:{},studyCode:{},funcCode:{}，call skylab-failover because of: respCode != 0. newCount={}", traceId, studyCode, funcCode, newCount);
                return true;
            }
            // 正常请求，重置计数
            count.set(0);
            log.debug("traceId:{},studyCode:{},funcCode:{} reset count", traceId, studyCode, funcCode);
        }
        return false;
    }

    public boolean failedRoute(String traceId, SceneInfo sceneInfo, Exception ex) {
        StudyCodeEnum studyCode = sceneInfo.getStudyCode();
        String funcCode = sceneInfo.getFunctionCode();
        AtomicInteger count = this.countMap.get(mapKey(studyCode, funcCode));
        // 如果调用异常，直接兜底。 并计数
        if (properties.isEnable() && count != null) {
            int newCount = count.incrementAndGet();
            log.info("traceId:{},studyCode:{},funcCode:{}，call skylab-failover because of: call exception. newCount={}", traceId, studyCode, funcCode, newCount);
            return true;
        }
        return false;
    }


    public void resetAll() {
        this.countMap.forEach((key, val) -> {
            val.set(0);
            log.debug("{} 计数置为 0", key);
        });
    }


    private Tags tags(StudyCodeEnum studyCode, String funcCode) {
        if (diagFunctions.contains(funcCode)) {
            return Tags.of("ip", ip, "studyCode", studyCode.name(), "func", "diagnose");
        }
        if (recFunctions.contains(funcCode)) {
            return Tags.of("ip", ip, "studyCode", studyCode.name(), "func", "recommend");
        }
        if (studentLogFunctions.contains(funcCode)) {
            return Tags.of("ip", ip, "studyCode", studyCode.name(), "func", "studentLog");
        }
        if (featureFunctions.contains(funcCode)) {
            return Tags.of("ip", ip, "studyCode", studyCode.name(), "func", "feature");
        }

        return Tags.of("ip", ip, "studyCode", studyCode.name(), "func", "notSupported");
    }


    private String mapKey(StudyCodeEnum studyCode, String funcCode) {
        if (diagFunctions.contains(funcCode)) {
            return String.format("%s >> %s >> %s", ip, studyCode.name(), "diagnose");
        }
        if (recFunctions.contains(funcCode)) {
            return String.format("%s >> %s >> %s", ip, studyCode.name(), "recommend");
        }
        if (studentLogFunctions.contains(funcCode)) {
            return String.format("%s >> %s >> %s", ip, studyCode.name(), "studentLog");
        }
        if (featureFunctions.contains(funcCode)) {
            return String.format("%s >> %s >> %s", ip, studyCode.name(), "feature");
        }

        return String.format("%s >> %s >> %s", ip, studyCode.name(), "notSupported");
    }
}
