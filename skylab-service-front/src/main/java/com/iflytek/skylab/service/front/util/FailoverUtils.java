package com.iflytek.skylab.service.front.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.api.ApiResponseHeader.ErrorNode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/18 17:14
 */
public class FailoverUtils {

    private static final String FAILOVER_FLAG_KEY = "failover";
    private static final String FAILOVER_FLAG_VAL = "true";

    public static ErrorNode findFailoverErrorNode(ApiResponse response) {
        List<ApiResponseHeader.ErrorNode> errorNodes = response.getHeader().getErrorNodes();
        if (errorNodes != null && !errorNodes.isEmpty()) {
            for (ApiResponseHeader.ErrorNode node : errorNodes) {
                if (failoverErrorNode(node)) {
                    return node;
                }
            }
        }
        return null;
    }

    public static ErrorNode findFailoverErrorNode(SkylabResponse<?> response) {
        JSONArray errorNodes = response.getErrorNodes();
        if (errorNodes != null && !errorNodes.isEmpty()) {
            for (int i = 0; i < errorNodes.size(); i++) {
                ErrorNode node = errorNodes.getJSONObject(i).to(ErrorNode.class);
                if (failoverErrorNode(node)) {
                    return node;
                }
            }
        }
        return null;
    }

    public static void appendSkylabResponseErrorNodeIfFoundFailoverErrorNode(ApiResponse apiResponse, SkylabResponse<?> response) {
        ErrorNode errorNode = findFailoverErrorNode(apiResponse);
        if (errorNode != null) {
            JSONArray errorNodes = response.getErrorNodes();
            errorNodes = errorNodes == null ? new JSONArray() : errorNodes;
            errorNodes.add(JSON.parseObject(JSON.toJSONString(errorNode)));
            response.setErrorNodes(errorNodes);
        }
    }


    private static boolean failoverErrorNode(ErrorNode node) {
        return FAILOVER_FLAG_KEY.equals(node.getCode()) && FAILOVER_FLAG_VAL.equals(node.getName());
    }




}
