package com.iflytek.skylab.service.front.endpoint;

import com.iflytek.skylab.service.front.configuration.FailoverProperties;
import com.iflytek.skylab.service.front.service.FailoverGuard2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2022/5/16 11:22
 */
@Component
@Endpoint(id = "failover-info")
public class FailoverEndpoint {

    @Autowired
    private FailoverProperties failoverProperties;
    @Autowired
    private FailoverGuard2 guard;

    @ReadOperation
    public Object info() {
        Map<String, String> propMap = new HashMap<>();
        propMap.put("enable", String.valueOf(failoverProperties.isEnable()));
        propMap.put("failed-limit", String.valueOf(failoverProperties.getFailedLimit()));

        List<String> infos = new ArrayList<>();
        guard.getCountMap().forEach((k,v)-> infos.add(String.format("%s: %d", k, v.get())));
        Collections.sort(infos);

        Map<String, Object> statusMap = new HashMap<>();
        statusMap.put("prop", propMap);
        statusMap.put("counts", infos);
        return statusMap;
    }
}
