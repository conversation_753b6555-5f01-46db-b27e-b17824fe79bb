package com.iflytek.skylab.service.front.configuration;

import com.iflytek.cog2.feaflow.sdk.annotation.EnableSkylabZion;
import com.iflytek.hy.rec.feaacquire.annotation.EnableFeaAcquire;
import com.iflytek.hy.rec.feaprocess.annotation.EnableFeaProcess;
import com.iflytek.skylab.core.contract.service.*;
import com.iflytek.skylab.core.dataapi.annotation.EnableBizDataAPI;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.service.StudyTraceService;
import com.iflytek.skylab.service.failover.controller.FailoverController;
import com.iflytek.skylab.service.front.metrics.RequestMetricsAspect;
import com.iflytek.skylab.service.front.service.FailoverGuard2;
import com.iflytek.skylab.service.front.service.LogStudyTraceAspect;
import com.iflytek.skylab.service.front.service.impl.*;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import com.iflytek.skyline.common.EnableSkylineFeign;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.AppContext;
import skynet.boot.annotation.EnableSkynetLogging;


/**
 * 接入服务  相关Bean 配置
 *
 * <AUTHOR>
 */
@EnableSkynetLogging
@EnableSkylineBrave
@EnableSkylineFeign
@EnableBizDataAPI
@Configuration(proxyBeanMethods = false)
@EnableDataHub
@EnableFeignClients("com.iflytek.skylab.service.front.feign")
@EnableFeaProcess
@EnableFeaAcquire
@EnableSkylabZion
public class FrontAutoConfiguration {

    @Bean
    @ConfigurationProperties("skylab.front")
    @RefreshScope
    public FrontProperties frontProperties() {
        return new FrontProperties();
    }

    @Bean
    @ConfigurationProperties("skylab.front.failover")
    public FailoverProperties failoverProperties() {
        return new FailoverProperties();
    }

    @Bean
    public FailoverGuard2 failoverGuard2(MeterRegistry meterRegistry, FailoverProperties failoverProperties, AppContext appContext) {
        return new FailoverGuard2(meterRegistry, failoverProperties, appContext);
    }

    @Bean
    public LogStudyTraceAspect logStudyTraceAspect(StudyTraceService studyTraceService, FrontProperties frontProperties, TraceUtils traceUtils) {
        return new LogStudyTraceAspect(studyTraceService, frontProperties, traceUtils);
    }

    @Bean("SkylabStandServiceProvider")
    public SkylabStandService skylabStandServiceProvider() {
        return new SkylabStandServiceImpl();
    }

    @Bean("SkylabDiagnoseServiceProvider")
    public SkylabDiagnoseService skylabDiagnoseServiceProvider() {
        return new SkylabDiagnoseServiceImpl();
    }

    @Bean("SkylabRecommendServiceProvider")
    public SkylabRecommendService skylabRecommendServiceProvider() {
        return new SkylabRecommendServiceImpl();
    }

    @Bean("SkylabBehaviorServiceProvider")
    public SkylabBehaviorService skylabBehaviorServiceProvider() {
        return new SkylabBehaviorServiceImpl();
    }

    @Bean("SkylabExtServiceProvider")
    public SkylabExtService skylabExtServiceProvider() {
        return new SkylabExtServiceImpl();
    }

    @Bean("SkylabClearMasteryServiceProvider")
    public SkylabClearMasteryService skylabClearMasteryServiceProvider() {
        return new SkylabClearMasteryServiceImpl();
    }

    @Bean("SkylabPrimaryMigrationServiceProvider")
    public SkylabPrimaryMigrationService skylabPrimaryMigrationServiceProvider() {
        return new SkylabPrimaryMigrationServiceImpl();
    }

   // @Bean
   // public RequestMetricsAspect requestMetricsAspect(MeterRegistry meterRegistry, AppContext appContext) {
   //     return new RequestMetricsAspect(meterRegistry, appContext);
   // }

    @Bean
    public FailoverController failoverController() {
        return new FailoverController();
    }
}
