package com.iflytek.skylab.service.front.service.impl;

import com.iflytek.skylab.core.contract.service.SkylabExtService;
import com.iflytek.skylab.core.data.ExtFuncParam;
import com.iflytek.skylab.core.data.ExtFuncResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.service.front.metrics.ServiceRequestMetrics;
import com.iflytek.skylab.service.front.metrics.SkylabDefaultMeterProvider;
import com.iflytek.skylab.service.front.service.BaseFrontService;
import com.iflytek.skylab.service.front.service.LogStudyTrace;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 通用扩展功能 服务接口
 *
 * <AUTHOR>
 * @date 2022/3/2 10:48 上午
 */
@Slf4j
@Validated
public class SkylabExtServiceImpl extends BaseFrontService implements SkylabExtService {

    /**
     * 业务端获取通用特征数据
     *
     * @param skylabRequest
     */
    @Override
    @SkylineTraceStarter(isSync4Request = false)
    @SkylineMetric(value = "通用扩展接口", meterProviders = SkylabDefaultMeterProvider.class)
    @LogStudyTrace("call")
    @ServiceRequestMetrics(service = "SkylabExtService")
//    @LoggingCost
    public SkylabResponse<ExtFuncResult> call(@Valid @NotNull SkylabRequest<ExtFuncParam> skylabRequest) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabRequest<ExtFuncParam>= {}", skylabRequest);
        }
        return super.process(skylabRequest, ExtFuncResult.class, "com.iflytek.skylab.service.front.service.impl.SkylabExtServiceImpl#call");
    }
}
