package com.iflytek.skylab.service.front.service.impl;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.contract.service.SkylabPrimaryMigrationService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.service.front.configuration.FrontProperties;
import com.iflytek.skylab.service.front.feign.PrimaryMigrationFeign;
import com.iflytek.skylab.service.front.metrics.SkylabDefaultMeterProvider;
import com.iflytek.skylab.service.front.service.BaseFrontService;
import com.iflytek.skylab.service.front.service.LogStudyTrace;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/12 10:44
 */
@Slf4j
@Validated
public class SkylabPrimaryMigrationServiceImpl extends BaseFrontService implements SkylabPrimaryMigrationService {

    @Lazy
    @Autowired
    private PrimaryMigrationFeign primaryMigrationFeign;


    @Autowired
    private FrontProperties frontProperties;


    private SkylabResponse<FuncResult> call(SkylabRequest<? extends FuncParam> skylabRequest) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabPrimaryMigrationServiceImpl SkylabRequest= {}", skylabRequest);
        }
        String funcCode = skylabRequest.getPayload().getFuncCode();
        skylabRequest.getScene().setFunctionCode(funcCode);
        // 使用默认图谱版本
        skylabRequest.getScene().setGraphVersion(frontProperties.getDefaultGraphVersion());

        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setTraceId(skylabRequest.getTraceId());
        apiRequest.setObjectPayload(skylabRequest.getPayload());
        apiRequest.setObjectScene(skylabRequest.getScene());

        SkylabResponse<FuncResult> response = new SkylabResponse<>();
        response.setTraceId(skylabRequest.getTraceId());

        //小学精准学数据同步
        ApiResponse apiResponse = primaryMigrationFeign.process(apiRequest);
        if (log.isDebugEnabled()) {
            log.debug("SkylabPrimaryMigrationServiceImpl ApiResponse= {}", apiResponse);
        }
        assert apiResponse != null;
        response.setCode(apiResponse.getHeader().getCode());
        response.setMessage(apiResponse.getHeader().getMessage());

        if (apiResponse.getHeader().getCode() == 0 && apiResponse.getPayload() != null) {
            //调试状态，回写 相关的调试信息
            if (skylabRequest.getScene().isTest() && apiResponse.getHeader().getErrorNodes() != null && !apiResponse.getHeader().getErrorNodes().isEmpty()) {
                response.setContext(apiResponse.getHeader().getContext());
                response.setErrorNodes(JSON.parseArray(JSON.toJSONString(apiResponse.getHeader().getErrorNodes())));
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("SkylabPrimaryMigrationServiceImpl SkylabResponse= {}", response);
        }
        return response;
    }


    @SkylineTraceStarter(isSync4Request = false)
    @LogStudyTrace("primaryMigrationbehavior")
    @SkylineMetric(value = "小学精准学答题记录上报", meterProviders = SkylabDefaultMeterProvider.class)
    @Override
    public SkylabResponse<FuncResult> behavior(@Valid @NotNull SkylabRequest<PrimaryMigrationBehaviorParam> skylabRequest) {
        return call(skylabRequest);
    }


    @SkylineTraceStarter(isSync4Request = false)
    @LogStudyTrace("primaryMigrationmastery")
    @SkylineMetric(value = "小学精准学章节掌握度上报", meterProviders = SkylabDefaultMeterProvider.class)
    @Override
    public SkylabResponse<FuncResult> mastery(@Valid @NotNull SkylabRequest<PrimaryMigrationMasteryParam> skylabRequest) {
        return call(skylabRequest);
    }
}
