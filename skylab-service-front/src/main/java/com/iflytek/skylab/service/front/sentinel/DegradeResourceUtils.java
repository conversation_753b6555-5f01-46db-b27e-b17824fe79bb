package com.iflytek.skylab.service.front.sentinel;

import com.iflytek.skylab.core.constant.RecFuncEnum;
import com.iflytek.skylab.core.constant.StudyFuncEnum;
import com.iflytek.skylab.core.data.AcquireFeatureParam;
import com.iflytek.skylab.core.data.FeatureParam;
import com.iflytek.skylab.core.data.MasterFetch4CatalogParam;
import com.iflytek.skylab.core.domain.SceneInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * Sentinel熔断降级资源 工具类
 *
 * <AUTHOR>
 * @date 2023/10/17 16:48
 */
public class DegradeResourceUtils {

    // 小学、初中、高中
    private static final String[] PHASES = {"03", "04", "05"};
    // 数学、物理、化学、生物、科学
    private static final String[] SUBJECTS = {"02", "05", "06", "13", "19"};

    // 所有Sentinel熔断资源
    private static final List<String> RESOURCES = new ArrayList<>();

    static {
        // 组装所有资源
        for (String phase : PHASES) {
            for (String subject : SUBJECTS) {
                // 推荐功能
                for (RecFuncEnum func : RecFuncEnum.values()) {
                    RESOURCES.add(resource(phase, subject, func.name()));
                }
                // 诊断功能
                RESOURCES.add(resource(phase, subject, MasterFetch4CatalogParam.FUNC_CODE));
                // 答题功能
                for (StudyFuncEnum func : StudyFuncEnum.values()) {
                    RESOURCES.add(resource(phase, subject, func.name()));
                }
                // 特征功能
                RESOURCES.add(resource(phase, subject, FeatureParam.FUNC_CODE));
                RESOURCES.add(resource(phase, subject, AcquireFeatureParam.FUNC_CODE));
            }
        }
        // 小学只有数学
        RESOURCES.removeIf(res -> res.startsWith("03#") && !res.startsWith("03#02#"));
    }

    /**
     * 所有的资源集合
     *
     * @return
     */
    public static List<String> getResources() {
        return RESOURCES;
    }


    /**
     * 从场景信息中中提取降级资源定义
     *
     * @param sceneInfo
     * @return
     */
    public static String getResource(SceneInfo sceneInfo) {
        return resource(sceneInfo.getPhaseCode(), sceneInfo.getSubjectCode(), sceneInfo.getFunctionCode());
    }

    /**
     * 由此方法统一规定resource组成的字段
     *
     * @param phase
     * @param subject
     * @param funcCode
     * @return
     */
    private static String resource(String phase, String subject, String funcCode) {
        return phase + "#" + subject + "#" + funcCode;
    }
}
