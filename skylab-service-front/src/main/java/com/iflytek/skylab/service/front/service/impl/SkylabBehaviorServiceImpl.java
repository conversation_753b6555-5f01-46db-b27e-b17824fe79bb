package com.iflytek.skylab.service.front.service.impl;

import com.iflytek.skylab.core.contract.service.SkylabBehaviorService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.service.front.metrics.ServiceRequestMetrics;
import com.iflytek.skylab.service.front.metrics.SkylabDefaultMeterProvider;
import com.iflytek.skylab.service.front.service.BaseFrontService;
import com.iflytek.skylab.service.front.service.LogStudyTrace;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 行为收集服务
 *
 * <AUTHOR>
 * @date 2022/3/3 8:57 下午
 */
@Slf4j
@Validated
public class SkylabBehaviorServiceImpl extends BaseFrontService implements SkylabBehaviorService {

    /**
     * 汇报 答题记录
     * <p>
     *
     * @param skylabRequest
     */
    @Override
    @SkylineTraceStarter(isSync4Request = false)
    @LogStudyTrace("reportAnswerRecord")
    @ServiceRequestMetrics(service = "SkylabBehaviorService")
//    @LoggingCost
    @SkylineMetric(value = "汇报答题记录", meterProviders = SkylabDefaultMeterProvider.class)
    public SkylabResponse<StudyLogResult> reportAnswerRecord(@Valid @NotNull SkylabRequest<? extends StudyLogParam> skylabRequest) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabRequest<StudyLogParam>= {}", skylabRequest);
        }
        return super.process(skylabRequest, StudyLogResult.class, "com.iflytek.skylab.service.front.service.impl.SkylabBehaviorServiceImpl#reportAnswerRecord");
    }

    @Override
    @SkylineTraceStarter(isSync4Request = false)
    @LogStudyTrace("queryAnswerRecord")
    @ServiceRequestMetrics(service = "SkylabBehaviorService")
//    @LoggingCost
    @SkylineMetric(value = "查询用户答题记录", meterProviders = SkylabDefaultMeterProvider.class)
    public SkylabResponse<BatchStudyLogQueryResult> queryAnswerRecord(SkylabRequest<? extends BehaviorQueryParam> skylabRequest) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabRequest<StudyLogParam>= {}", skylabRequest);
        }

        return super.process(skylabRequest, BatchStudyLogQueryResult.class, "com.iflytek.skylab.service.front.service.impl.SkylabBehaviorServiceImpl#queryAnswerRecord");
    }
}
