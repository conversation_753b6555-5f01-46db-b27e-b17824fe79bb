package com.iflytek.skylab.service.front.configuration;

import com.iflytek.skylab.service.front.sentinel.RuleLoader;
import com.iflytek.skylab.service.front.sentinel.SentinelDegradeObserver;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import skynet.boot.AppContext;

/**
 * @author: leitong
 * @date: 2023/6/20 17:13
 * @description:
 **/
@Configuration
@ConditionalOnProperty(value = "app.sentinel.enabled", havingValue = "true")
public class SentinelAutoConfiguration {


    @Bean
    public SentinelDegradeObserver sentinelDegradeObserver(MeterRegistry meterRegistry, AppContext appContext) {
        return new SentinelDegradeObserver(meterRegistry, appContext);
    }

    @Bean
    public RuleLoader ruleLoader(Environment env, SentinelDegradeObserver sentinelDegradeObserver) {
        return new RuleLoader(env, sentinelDegradeObserver);
    }


    // public static class RuleLoader{
    //
    //     private final String path;
    //     private final SentinelDegradeObserver observer;
    //
    //
    //     public RuleLoader(String path, SentinelDegradeObserver sentinelDegradeObserver) {
    //         this.path = path;
    //         this.observer = sentinelDegradeObserver;
    //     }
    //
    //     @PostConstruct
    //     public void load(){
    //         if(StringUtils.isBlank(this.path)){
    //             log.error("'{}' is not configured, no rules to load.", RULE_PATH_CONFIG_KEY);
    //             return;
    //         }
    //         try{
    //             String content = this.loadContent();
    //             RuleObject ruleObject = JSONObject.parseObject(content, RuleObject.class);
    //             if(ruleObject.flow != null){
    //                 FlowRuleManager.loadRules(ruleObject.flow);
    //             }
    //             if(ruleObject.degrade != null){
    //                 DegradeRuleManager.loadRules(ruleObject.degrade);
    //                 // 注册熔断事件监听
    //                 EventObserverRegistry.getInstance().addStateChangeObserver(observer.getClass().getSimpleName(), observer);
    //             }
    //         }catch (Exception e) {
    //             log.error("Fail to load sentinel rule '{}'", this.path, e);
    //         }
    //     }
    //
    //     private String loadContent() throws IOException {
    //         if(!this.path.startsWith("/") || this.path.startsWith("classpath")) {
    //             return this.loadContentFromClasspath();
    //         }
    //         return this.loadContentByAbsPath();
    //     }
    //
    //     private String loadContentFromClasspath() throws IOException {
    //         String ret = null;
    //         try(InputStream in = RuleLoader.class.getClassLoader().getResourceAsStream(this.path)){
    //             if(in != null){
    //                 ret = IOUtils.toString(in, Charset.forName("utf-8"));
    //             }
    //         }
    //         return ret;
    //     }
    //
    //     private String loadContentByAbsPath() throws IOException {
    //         String ret = null;
    //         try(InputStream in = Files.newInputStream(new File(this.path).toPath())){
    //             if(in != null){
    //                 ret = IOUtils.toString(in, Charset.forName("utf-8"));
    //             }
    //         }
    //         return ret;
    //     }
    // }
    //
    // @Getter
//@Setter
    // public static class RuleObject {
    //
    //     // 限流规则
    //     private List<FlowRule> flow;
    //
    //     // 降级规则
    //     private List<DegradeRule> degrade;
    //
    // }

}