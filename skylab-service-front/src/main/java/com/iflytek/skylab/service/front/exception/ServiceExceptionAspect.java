package com.iflytek.skylab.service.front.exception;

import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.dataapi.exception.SkylabException;
import com.iflytek.skyline.brave.exception.BraveException;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import javax.validation.ValidationException;

/**
 * service异常处理切面
 *
 * <AUTHOR>
 * @date 2022/5/5 09:52
 */
@Slf4j
@Aspect
public class ServiceExceptionAspect {


    /**
     * 异常直接不抛出，封装成response
     *
     * @param joinPoint
     */
    @Around("execution(public * com.iflytek.skylab..front.service..*.*(..))")
    public Object handleThrowable(ProceedingJoinPoint joinPoint) {

        try {
            return joinPoint.proceed();
        } catch (ValidationException e) {
            SkylabResponse<FuncResult> result = wrapException(joinPoint, e);
            log.error("TraceId={}; 参数异常(ValidationException)= {}", result.getTraceId(), e.getMessage());
            return result;
        } catch (ClientAbortException e) {
            SkylabResponse<FuncResult> result = wrapException(joinPoint, e);
            log.error("TraceId={}; 客户端请求中断异常(ClientAbortException)= {}", result.getTraceId(), e.getMessage());
            return result;
        } catch (feign.RetryableException e) {
            SkylabResponse<FuncResult> result = wrapException(joinPoint, e);
            log.error("TraceId={}; Feign请求中断异常(feign.RetryableException)= {}", result.getTraceId(), e.getMessage());
            return result;
        } catch (SkylabException e) {
            SkylabResponse<FuncResult> result = wrapException(joinPoint, e);
            log.error("TraceId={}; 业务异常(SkylabException)= {}", result.getTraceId(), e.getMessage());
            return result;
        } catch (BraveException e) {
            SkylabResponse<FuncResult> result = wrapException(joinPoint, e);
            log.error("TraceId={}; Trace异常(BraveException)= {}", result.getTraceId(), e.getMessage());
            return result;
        } catch (Throwable e) {
            SkylabResponse<FuncResult> result = wrapException(joinPoint, e);
            log.error(String.format("TraceId=%s; 系统未知异常=%s", result.getTraceId(), e.getMessage()), e);
            return result;
        }
    }

    private SkylabResponse<FuncResult> wrapException(ProceedingJoinPoint joinPoint, Throwable e) {
        // 获取SkylabRequest
        SkylabRequest<FuncParam> skylabRequest = new SkylabRequest<>();
        for (Object arg : joinPoint.getArgs()) {
            if (arg instanceof SkylabRequest) {
                skylabRequest = (SkylabRequest<FuncParam>) arg;
            }
        }

        // 组装SkylabResponse
        SkylabResponse<FuncResult> skylabResponse = new SkylabResponse<>();
        skylabResponse.setTraceId(skylabRequest != null ? skylabRequest.getTraceId() : "UnSET");
        skylabResponse.setCode(-1);
        skylabResponse.setMessage(e.getMessage());
        return skylabResponse;
    }
}