package com.iflytek.skylab.service.front.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetException;

/**
 * 接入异常 服务  相关Bean 配置
 *
 * <AUTHOR>
 */
@Slf4j
@EnableSkynetException
@Configuration(proxyBeanMethods = false)
public class FrontExceptionAutoConfiguration {

    @Bean
    public FrontExceptionMessageFormatter frontExceptionMessageFormatter() {
        log.info("Build FrontExceptionMessageFormatter.");
        return new FrontExceptionMessageFormatter();
    }

    @Bean
    public ServiceExceptionAspect serviceExceptionAspect() {
        return new ServiceExceptionAspect();
    }
}
