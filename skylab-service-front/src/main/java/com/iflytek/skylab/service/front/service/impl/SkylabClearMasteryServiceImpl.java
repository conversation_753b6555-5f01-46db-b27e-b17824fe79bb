package com.iflytek.skylab.service.front.service.impl;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.contract.service.SkylabClearMasteryService;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.service.front.feign.ClearMasteryFeign;
import com.iflytek.skylab.service.front.metrics.SkylabDefaultMeterProvider;
import com.iflytek.skylab.service.front.service.BaseFrontService;
import com.iflytek.skylab.service.front.service.LogStudyTrace;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/20 10:44
 */
@Slf4j
@Validated
public class SkylabClearMasteryServiceImpl extends BaseFrontService implements SkylabClearMasteryService {

    @Lazy
    @Autowired
    private ClearMasteryFeign clearMasteryFeign;

    /**
     * 清空画像业务请求
     *
     * @param skylabRequest 场景信息等
     * @return SkylabResponse<FuncResult>
     */
    @SkylineTraceStarter(isSync4Request = false)
    @LogStudyTrace("clearMastery")
    @SkylineMetric(value = "清空画像接口", meterProviders = SkylabDefaultMeterProvider.class)
//    @LoggingCost
    @Override
    public SkylabResponse<ClearMasteryResult> clearMastery(SkylabRequest<ClearMasteryParam> skylabRequest) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabRequest<ClearMasteryParam>= {}", skylabRequest);
        }
        return call(skylabRequest);
    }

    private <T extends FuncResult> SkylabResponse<T> call(SkylabRequest<? extends FuncParam> skylabRequest) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabRequest= {}", skylabRequest);
        }
        String funcCode = skylabRequest.getPayload().getFuncCode();
        skylabRequest.getScene().setFunctionCode(funcCode);

        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setTraceId(skylabRequest.getTraceId());
        apiRequest.setObjectPayload(skylabRequest.getPayload());
        apiRequest.setObjectScene(skylabRequest.getScene());

        SkylabResponse<T> response = new SkylabResponse<>();
        response.setTraceId(skylabRequest.getTraceId());

        //画像清空
        ApiResponse apiResponse = clearMasteryFeign.process(apiRequest);
        if (log.isDebugEnabled()) {
            log.debug("ApiResponse= {}", apiResponse);
        }
        assert apiResponse != null;
        response.setCode(apiResponse.getHeader().getCode());
        response.setMessage(apiResponse.getHeader().getMessage());

        if (apiResponse.getHeader().getCode() == 0 && apiResponse.getPayload() != null) {
            T obj = JSON.to((Class<T>) ClearMasteryResult.class, apiResponse.getPayload());
            response.setPayload(obj);

            //调试状态，回写 相关的调试信息
            if (skylabRequest.getScene().isTest() && apiResponse.getHeader().getErrorNodes() != null && !apiResponse.getHeader().getErrorNodes().isEmpty()) {
                response.setContext(apiResponse.getHeader().getContext());
                response.setErrorNodes(JSON.parseArray(JSON.toJSONString(apiResponse.getHeader().getErrorNodes())));
            }
        }


        if (log.isDebugEnabled()) {
            log.debug("SkylabResponse= {}", response);
        }
        return response;
    }
}
