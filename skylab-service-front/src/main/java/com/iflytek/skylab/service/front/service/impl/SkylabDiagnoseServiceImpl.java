package com.iflytek.skylab.service.front.service.impl;

import com.iflytek.skylab.core.contract.service.SkylabDiagnoseService;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.service.front.metrics.ServiceRequestMetrics;
import com.iflytek.skylab.service.front.metrics.SkylabDefaultMeterProvider;
import com.iflytek.skylab.service.front.service.BaseFrontService;
import com.iflytek.skylab.service.front.service.LogStudyTrace;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 诊断服务
 *
 * <AUTHOR>
 * @date 2022/3/10 10:24 上午
 */
@Slf4j
@Validated
public class SkylabDiagnoseServiceImpl extends BaseFrontService implements SkylabDiagnoseService {

    /**
     * 诊断接口
     *
     * @param skylabRequest 场景、点诊断或推题 交互上下文、推荐信息
     * @return 相似题列表
     */
    @Override
    @SkylineMetric(value = "诊断接口", meterProviders = SkylabDefaultMeterProvider.class)
    @SkylineTraceStarter(isSync4Request = false)
    @LogStudyTrace("diagnose")
    @ServiceRequestMetrics(service = "SkylabDiagnoseService")
//    @LoggingCost
    public SkylabResponse<MasterFetchResult> diagnose(@Valid @NotNull SkylabRequest<? extends FuncParam> skylabRequest) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabDiagnoseService,SkylabRequest= {}", skylabRequest);
        }
        return super.process(skylabRequest, MasterFetchResult.class, "com.iflytek.skylab.service.front.service.impl.SkylabDiagnoseServiceImpl#diagnose");
    }
}
