package com.iflytek.skylab.service.front;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.annotation.ImportResource;
import skynet.boot.AppUtils;

/**
 * 接入服务 入口
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class
})
//@ImportResource({"classpath:/front-spring-dubbo.xml"})
@ImportResource("${skylab.dubbo.xml.path:classpath:/front-spring-dubbo.xml}")
public class FrontAppBoot {

    public static void main(String[] args) {
        AppUtils.run(FrontAppBoot.class, args);
    }
}
