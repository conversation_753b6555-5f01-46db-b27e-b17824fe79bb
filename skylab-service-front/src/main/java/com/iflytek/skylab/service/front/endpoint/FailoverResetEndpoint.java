package com.iflytek.skylab.service.front.endpoint;

import com.iflytek.skylab.service.front.service.FailoverGuard2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/10 14:23
 */
@Component
@Endpoint(id = "failover-reset")
public class FailoverResetEndpoint {

    @Autowired
    private FailoverGuard2 guard;

    @ReadOperation
    public Object reset() {
        guard.resetAll();
        return "reset ok";
    }
}
