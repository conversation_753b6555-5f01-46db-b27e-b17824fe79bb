package com.iflytek.skylab.service.front.configuration;

import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.service.front.metrics.SkylabDefaultMeterProvider;
import com.iflytek.skyline.brave.metric.SkylineTagsRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.iflytek.skyline.brave.metric.TagNames.*;

/**
 * @author: leitong
 * @date: 2023/1/5 15:57
 * @description: 监控相关配置
 **/
@Configuration
public class MetricsConfiguration {
    /**
     * 新增小程序监控指标
     */
    public static final String IS_MOBILE_APP = "isMobileApp";

    @Bean
    SkylineTagsRegistry skylabTagsRegistry(){
        // 统一业务视角的指标标签
        SkylineTagsRegistry.Builder builderForDefault = SkylineTagsRegistry.Builder.defaults()
                .register(SkylineTagsRegistry.Type.BEFORE,
                        BIZ, BIZ_CODE, SCENARIO, SUBJECT, SUBJECT_CODE, PHASE, PHASE_CODE, IS_MOBILE_APP)
                .register(SkylineTagsRegistry.Type.AFTER, CODE, GUARD);
        // data api调用监控指标
        SkylineTagsRegistry.Builder builder4DataApi = SkylineTagsRegistry.Builder.defaults(SkylabDataApiMeterProvider.METRIC_NAME);
        return SkylineTagsRegistry.build(builderForDefault, builder4DataApi);
    }

    @Bean
    SkylabDefaultMeterProvider skylabDefaultMeterProvider(){
        return new SkylabDefaultMeterProvider();
    }

}
