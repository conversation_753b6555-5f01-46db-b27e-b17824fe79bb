package com.iflytek.skylab.service.front.metrics;

import cn.hutool.core.lang.Assert;
import com.google.common.base.Stopwatch;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.front.util.FailoverUtils;
import com.iflytek.skyline.common.api.ApiResponseHeader.ErrorNode;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import skynet.boot.AppContext;

import java.lang.reflect.Method;
import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2022/7/12 15:02
 */
@Slf4j
@Aspect
@Deprecated
public class RequestMetricsAspect {

    private static final String DEFAULT_TAG_VALUE = "unknown";

    public final String ip;
    public final MeterRegistry meterRegistry;


    public RequestMetricsAspect(MeterRegistry meterRegistry, AppContext appContext) {
        this.meterRegistry = meterRegistry;
        this.ip = StringUtils.isBlank(appContext.getIpEndpoint()) ? DEFAULT_TAG_VALUE : appContext.getIpEndpoint();
    }


    @Around("@annotation(serviceRequestMetrics)")
    public Object around(ProceedingJoinPoint joinPoint, ServiceRequestMetrics serviceRequestMetrics) throws Throwable {
        Stopwatch stopWatch = Stopwatch.createStarted();
        Object result = null;
        try {
            result = joinPoint.proceed();
        } finally {
            Tags tags = parseTags(joinPoint)
                    .and("ip", ip)
                    .and("service", serviceRequestMetrics.service())
                    .and("respCode", parseCode(result));
            Duration duration = stopWatch.stop().elapsed();

            this.meterRegistry.timer("skylab.front.requests.seconds", tags).record(duration);
            if (log.isDebugEnabled()) {
                log.debug("skylab.front.requests.seconds, tags:{}, duration:{}", tags, duration.toMillis());
            }
        }
        return result;
    }

    private String parseCode(Object result) {
        String code = DEFAULT_TAG_VALUE;
        if (result == null) {
            return code;
        }

        try {
            SkylabResponse<?> resp = ((SkylabResponse<?>) result);
            code = String.valueOf(resp.getCode());
            // 检查是否有兜底参与，若有兜底参与，替换错误码
            ErrorNode errorNode = FailoverUtils.findFailoverErrorNode(resp);
            if (errorNode != null) {
                code = "55555";
            }
        } catch (Throwable t) {
            log.info("parse response error");
        }
        return code;
    }

    private Tags parseTags(ProceedingJoinPoint joinPoint) {
        try {
            SkylabRequest<?> skylabRequest = (SkylabRequest<?>) joinPoint.getArgs()[0];
            SceneInfo sceneInfo = skylabRequest.getScene();

            String studyCode = sceneInfo.getStudyCode().name();
            String subject = sceneInfo.getSubjectCode();
            String phase = sceneInfo.getPhaseCode();
            String test = String.valueOf(sceneInfo.isTest());

            Assert.notBlank(studyCode);
            Assert.notBlank(subject);
            Assert.notBlank(phase);
            Assert.notBlank(test);

            return Tags.of("studyCode", studyCode, "subject", subject, "phase", phase, "test", test);
        } catch (Throwable t) {
            Method targetMethod = ((MethodSignature) joinPoint.getSignature()).getMethod();
            log.info("illegal request, method:{}", targetMethod.getName());
        }

        return Tags.of("studyCode", DEFAULT_TAG_VALUE, "subject", DEFAULT_TAG_VALUE, "phase", DEFAULT_TAG_VALUE, "test", DEFAULT_TAG_VALUE);
    }
}
