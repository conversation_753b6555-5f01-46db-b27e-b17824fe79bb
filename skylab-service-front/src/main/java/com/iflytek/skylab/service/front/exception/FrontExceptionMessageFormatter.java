package com.iflytek.skylab.service.front.exception;

import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.SkylabResponse;
import skynet.boot.exception.SkynetException;
import skynet.boot.exception.message.DefaultExceptionMessageFormatter;
import skynet.boot.exception.message.ErrorMessage;

/**
 * 异常格式化
 *
 * <AUTHOR>
 * @date 2022/6/14 14:31
 */
public class FrontExceptionMessageFormatter extends DefaultExceptionMessageFormatter {

    @Override
    public ErrorMessage format(Throwable e, int code) {
        ErrorMessage errorMessage = super.format(e, code);
        SkylabErrorResponse skylabErrorResponse = new SkylabErrorResponse();
        skylabErrorResponse.setMessage(errorMessage.getMessage());
        skylabErrorResponse.setCode(errorMessage.getCode());
        skylabErrorResponse.setTraceId(errorMessage.getTraceId());
        return skylabErrorResponse;
    }

    /**
     * 转换 Skynet 异常
     */
    @Override
    public ErrorMessage format(SkynetException e) {
        return this.format(e, e.getCode());
    }

    public static class SkylabErrorResponse extends SkylabResponse<FuncResult> implements ErrorMessage {
    }

}
