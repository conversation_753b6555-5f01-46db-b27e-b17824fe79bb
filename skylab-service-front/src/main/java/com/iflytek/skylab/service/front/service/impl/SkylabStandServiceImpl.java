package com.iflytek.skylab.service.front.service.impl;

import com.iflytek.skylab.core.contract.service.SkylabStandService;
import com.iflytek.skylab.core.data.FeatureResult;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.service.front.metrics.ServiceRequestMetrics;
import com.iflytek.skylab.service.front.metrics.SkylabDefaultMeterProvider;
import com.iflytek.skylab.service.front.service.BaseFrontService;
import com.iflytek.skylab.service.front.service.LogStudyTrace;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 非推荐相关的功能 服务接口
 *
 * <AUTHOR>
 * @date 2022/3/2 10:48 上午
 */
@Slf4j
@Validated
public class SkylabStandServiceImpl extends BaseFrontService implements SkylabStandService {

    /**
     * 业务端获取通用特征数据
     *
     * @param skylabRequest
     */
    @Override
    @SkylineTraceStarter(isSync4Request = false)
    @SkylineMetric(value = "通用特征查询接口", meterProviders = SkylabDefaultMeterProvider.class)
    @LogStudyTrace("featureFetch")
    @ServiceRequestMetrics(service = "SkylabStandService")
//    @LoggingCost
    public SkylabResponse<FeatureResult> featureFetch(@Valid @NotNull SkylabRequest<? extends FuncParam> skylabRequest) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabStandService,SkylabRequest= {}", skylabRequest);
        }
        return super.process(skylabRequest, FeatureResult.class, "com.iflytek.skylab.service.front.service.impl.SkylabStandServiceImpl#featureFetch");
    }
}
