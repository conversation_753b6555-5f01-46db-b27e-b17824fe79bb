package com.iflytek.skylab.service.front.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.iflytek.skylab.core.constant.TraceConstant;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyTraceRecord;
import com.iflytek.skylab.core.dataapi.service.StudyTraceService;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.front.configuration.FrontProperties;
import com.iflytek.skyline.brave.TraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.time.Instant;
import java.util.Optional;

/**
 * <pre>
 * 记录学习轨迹
 * </pre>
 *
 * <AUTHOR>
 * @date 2022/3/21 3:11 下午
 */
@Slf4j
@Aspect
public class LogStudyTraceAspect {

    private final StudyTraceService studyTraceService;
    private final FrontProperties frontProperties;
    private final TraceUtils traceUtils;
    private final CopyOptions copyOptions;


    public LogStudyTraceAspect(StudyTraceService studyTraceService, FrontProperties frontProperties, TraceUtils traceUtils) {
        this.studyTraceService = studyTraceService;
        this.frontProperties = frontProperties;
        this.traceUtils = traceUtils;
        this.copyOptions = CopyOptions.create();
    }

    @Around("@annotation(logStudyTrace)")
    public Object traceApiRequest(ProceedingJoinPoint joinPoint, LogStudyTrace logStudyTrace) throws Throwable {
        if (frontProperties.isLogStudyTrace()) {
            log.debug("LogStudyTrace begin ...");
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0 && args[0] instanceof SkylabRequest) {
                log.debug("LogStudyTrace doing ...");
                SkylabRequest skylabRequest = (SkylabRequest) args[0];
                StudyTraceRecord studyTraceRecord = new StudyTraceRecord();
                studyTraceRecord.setCreateTime(Instant.now());
                studyTraceRecord.setTraceId(skylabRequest.getTraceId());
                BeanUtil.copyProperties(skylabRequest.getScene(), studyTraceRecord, copyOptions);
                if (StringUtils.hasText(logStudyTrace.value())) {
                    studyTraceRecord.setActionCode(logStudyTrace.value());
                } else {
                    Method targetMethod = ((MethodSignature) joinPoint.getSignature()).getMethod();
                    studyTraceRecord.setActionCode(targetMethod.getName());
                }

                studyTraceRecord.setTraceContext(convert2JSONObject(skylabRequest.getPayload()));
                if (log.isDebugEnabled()) {
                    log.debug("StudyTraceRecord= {}", studyTraceRecord);
                }   //记录到日志中（file and kafka）


                String userId = Optional.of(skylabRequest)
                        .map(SkylabRequest::getScene)
                        .map(SceneInfo::getUserId)
                        .orElse(null);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
                    //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
                }else {
                    traceUtils.record("STUDY_TRACE", studyTraceRecord);
                }
                studyTraceService.saveStudyTraceRecordAsync(studyTraceRecord);
            }
            log.debug("LogStudyTrace end.[cost={}]", stopWatch);
        }
        return joinPoint.proceed();
    }

    /**
     * 将payload 转换成JSONObject
     * 保持Double字段类型不变
     *
     * @param payload
     * @return
     */
    private JSONObject convert2JSONObject(Object payload) {
        if (payload == null) {
            return null;
        }

        JSON.config(JSONReader.Feature.UseBigDecimalForDoubles, true);

        String payloadStr = JSON.toJSONString(payload);
        JSONObject payloadObj = JSON.parseObject(payloadStr, JSONObject.class);

        return payloadObj;
    }
}
