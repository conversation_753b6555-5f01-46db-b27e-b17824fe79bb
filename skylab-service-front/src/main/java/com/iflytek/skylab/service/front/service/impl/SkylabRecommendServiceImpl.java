package com.iflytek.skylab.service.front.service.impl;

import com.iflytek.skylab.core.contract.service.SkylabRecommendService;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.service.front.metrics.ServiceRequestMetrics;
import com.iflytek.skylab.service.front.metrics.SkylabDefaultMeterProvider;
import com.iflytek.skylab.service.front.service.BaseFrontService;
import com.iflytek.skylab.service.front.service.LogStudyTrace;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 推题
 *
 * <AUTHOR>
 * @date 2022/3/10 10:24 上午
 */
@Slf4j
@Validated
public class SkylabRecommendServiceImpl extends BaseFrontService implements SkylabRecommendService {

    /**
     * 推题接口
     *
     * @param skylabRequest 场景、点诊断或推题 交互上下文、推荐信息
     * @param clazz
     * @return 相似题列表
     */
    @Override
    @SkylineTraceStarter(isSync4Request = false)
    @SkylineMetric(value = "推题接口", meterProviders = SkylabDefaultMeterProvider.class)
    @LogStudyTrace("recommend")
    @ServiceRequestMetrics(service = "SkylabRecommendService")
//    @LoggingCost
    public <T extends FuncResult> SkylabResponse<T> recommend(@Valid @NotNull SkylabRequest<? extends FuncParam> skylabRequest, @Valid @NotNull Class<T> clazz) {
        if (log.isDebugEnabled()) {
            log.debug("SkylabRequest= {}", skylabRequest);
        }
        return super.process(skylabRequest, clazz, "com.iflytek.skylab.service.front.service.impl.SkylabRecommendServiceImpl#recommend");
    }
}
