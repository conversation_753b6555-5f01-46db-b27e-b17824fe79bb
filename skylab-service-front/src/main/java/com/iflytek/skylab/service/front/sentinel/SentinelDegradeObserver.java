package com.iflytek.skylab.service.front.sentinel;

import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.circuitbreaker.CircuitBreaker.State;
import com.alibaba.csp.sentinel.slots.block.degrade.circuitbreaker.CircuitBreakerStateChangeObserver;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.AppContext;


/**
 * <AUTHOR>
 * @date 2023/10/11 14:22
 */
@Slf4j
public class SentinelDegradeObserver implements CircuitBreakerStateChangeObserver {

    private static final String GAUGE_NAME = "skylab_platform_sentinel_degrade_state";


    private final MeterRegistry meterRegistry;
    private final String ip;

    public SentinelDegradeObserver(MeterRegistry meterRegistry, AppContext appContext) {
        this.meterRegistry = meterRegistry;
        this.ip = appContext.getIpEndpoint();
    }


    @Override
    public void onStateChange(State prevState, State newState, DegradeRule rule, Double snapshotValue) {
        Tags tags = Tags.of("ip", ip, "resource", rule.getResource());
        log.warn("【{}-{}】 state change from 【{}】 to 【{}】 ", ip, rule.getResource(), prevState, newState);

        if (newState == State.OPEN) {
            this.meterRegistry.gauge(GAUGE_NAME, tags, 1);
        }

        if (newState == State.HALF_OPEN) {
            this.meterRegistry.gauge(GAUGE_NAME, tags, 0.5);
        }

        if (newState == State.CLOSED) {
            this.meterRegistry.gauge(GAUGE_NAME, tags, 0);
        }
    }

}
