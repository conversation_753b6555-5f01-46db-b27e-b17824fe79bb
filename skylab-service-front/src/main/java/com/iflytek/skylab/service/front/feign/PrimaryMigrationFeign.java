package com.iflytek.skylab.service.front.feign;

import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/5/10 17:03
 */
@FeignClient("skylab-primary-migration")
public interface PrimaryMigrationFeign {

    @PostMapping("/skylab/api/v1/primaryMigration/process")
    ApiResponse process(@RequestBody ApiRequest apiRequest);
}
