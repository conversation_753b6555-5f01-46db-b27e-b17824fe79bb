package com.iflytek.skylab.service.front.metrics.dict;

import java.util.HashMap;
import java.util.Map;

/**
 * 学科
 * <AUTHOR>
 * @date 2022/7/8 11:55
 */
public final class SubjectDict {

    public static String getName(String code) {
        return dictMap.getOrDefault(code, "unknown");
    }

    public static Map<String, String> dictMap = new HashMap<>();
    
    static {
        dictMap.put("01","语文");
        dictMap.put("02","数学");
        dictMap.put("03","英语");
        dictMap.put("05","物理");
        dictMap.put("06","化学");
        dictMap.put("13","生物");
        dictMap.put("12","历史");
        dictMap.put("27","政治");
        dictMap.put("14","地理");
        dictMap.put("103","思想政治");
        dictMap.put("04","音乐");
        dictMap.put("11","思想品德");
        dictMap.put("07","品德与生活");
        dictMap.put("10","品德与社会");
        dictMap.put("08","历史与社会");
        dictMap.put("16","汉语");
        dictMap.put("09","美术");
        dictMap.put("19","科学");
        dictMap.put("20","语言");
        dictMap.put("21","艺术");
        dictMap.put("22","健康");
        dictMap.put("23","社会");
        dictMap.put("109","写字");
        dictMap.put("110","阅读");
        dictMap.put("24","多元智能");
        dictMap.put("25","体育与健康");
        dictMap.put("26","信息技术");
        dictMap.put("102","通用技术");
        dictMap.put("105","安全教育");
        dictMap.put("106","幼儿教育");
        dictMap.put("101","综合实践活动");
        dictMap.put("107","教育科学规划");
        dictMap.put("108","高中研究性学习");
        dictMap.put("111","心理健康教育");
        dictMap.put("112","特殊教育");
        dictMap.put("17","演示");
        dictMap.put("00","其他");
        dictMap.put("113","体育");
        dictMap.put("114","文综");
        dictMap.put("115","理综");
        dictMap.put("116","社思品");
        dictMap.put("117","哲学与人生");
        dictMap.put("118","职业道德与法律");
        dictMap.put("119","教育活动设计");
        dictMap.put("120","环境创设");
        dictMap.put("121","技术");
        dictMap.put("122","经验分享");
        dictMap.put("123","生活活动");
        dictMap.put("124","游戏活动");
        dictMap.put("125","综合实践");
        dictMap.put("126","经济政治与社会");
        dictMap.put("256","西班牙语");
        dictMap.put("127","职业生涯规划");
        dictMap.put("128","藏语");
        dictMap.put("129","电子技术应用");
        dictMap.put("130","电气运行与控制");
        dictMap.put("131","机电技术应用");
        dictMap.put("132","数控技术应用");
        dictMap.put("133","电子商务");
        dictMap.put("134","工艺美术");
        dictMap.put("135","计算机平面设计");
        dictMap.put("136","计算机网络技术");
        dictMap.put("137","计算机应用");
        dictMap.put("138","计算机与数码产品维修");
        dictMap.put("139","数字媒体技术应用");
        dictMap.put("140","网站建设与管理");
        dictMap.put("141","会计电算化");
        dictMap.put("142","会计");
        dictMap.put("143","市场营销");
        dictMap.put("144","汽车车身修复");
        dictMap.put("145","汽车美容与装潢");
        dictMap.put("146","汽车运用与维修");
        dictMap.put("147","美学与美育");
        dictMap.put("148","计算机应用基础");
        dictMap.put("149","公共基础课");
        dictMap.put("150","职业生涯规划与就业创业");
        dictMap.put("151","汽车整车与配件营销");
        dictMap.put("152","食品生物工艺");
        dictMap.put("153","酒店管理");
        dictMap.put("154","学前教育");
        dictMap.put("155","矿山机电");
        dictMap.put("156","农村电气技术(电工方向)");
        dictMap.put("157","农业机械使用与维护");
        dictMap.put("158","旅游服务与管理");
        dictMap.put("159","选矿技术");
        dictMap.put("160","畜牧兽医");
        dictMap.put("161","室内设计技术");
        dictMap.put("162","农业机械");
        dictMap.put("163","生物技术制药");
        dictMap.put("164","中药制药");
        dictMap.put("165","护理");
        dictMap.put("166","农畜特产品加工");
        dictMap.put("167","电气自动化技术");
        dictMap.put("168","农产品保鲜与加工(涉农）");
        dictMap.put("169","建筑装饰");
        dictMap.put("170","民间传统工艺（铜制品设计与加工方向）");
        dictMap.put("171","化学工艺");
        dictMap.put("172","电子电器应用与维修");
        dictMap.put("173","建筑工程施工");
        dictMap.put("174","高星级饭店运营与管理");
        dictMap.put("175","语言与文学");
        dictMap.put("176","人文与社会科学");
        dictMap.put("177","自然科学");
        dictMap.put("178","自然");
        dictMap.put("179","专题教育");
        dictMap.put("180","生物化学");
        dictMap.put("181","药物学");
        dictMap.put("182","病理学");
        dictMap.put("183","毛泽东思想和中国特色社会主义理论体系概论");
        dictMap.put("184","解剖学");
        dictMap.put("185","生理学");
        dictMap.put("186","免病");
        dictMap.put("187","医学心理学");
        dictMap.put("188","护理伦理学");
        dictMap.put("189","护理学基础");
        dictMap.put("190","营养学");
        dictMap.put("191","儿科护理学");
        dictMap.put("192","健康评估");
        dictMap.put("193","内科护理学");
        dictMap.put("194","外科护理学");
        dictMap.put("195","妇产科护理学");
        dictMap.put("196","急救护理");
        dictMap.put("197","重症监护技术");
        dictMap.put("198","社区护理");
        dictMap.put("199","社区健康教育");
        dictMap.put("200","老年护理");
        dictMap.put("201","老年保健");
        dictMap.put("202","五官护理学");
        dictMap.put("203","传染病护理");
        dictMap.put("204","针灸推拿");
        dictMap.put("205","人际沟通");
        dictMap.put("206","幼师英语");
        dictMap.put("207","汽车英语");
        dictMap.put("208","酒店英语视听说");
        dictMap.put("209","小学生廉洁教育读本");
        dictMap.put("210","中学生廉洁教育读本");
        dictMap.put("211","日语");
        dictMap.put("212","德语");
        dictMap.put("213","法语");
        dictMap.put("214","劳动技术");
        dictMap.put("215","创新");
        dictMap.put("216","综合（地生）");
        dictMap.put("217","社会科学基础知识");
        dictMap.put("218","职场礼仪与沟通");
        dictMap.put("219","综合（物史生）");
        dictMap.put("220","综合（化政地）");
        dictMap.put("221","劳动与技术");
        dictMap.put("222","高中国际课程");
        dictMap.put("223","高中校本课");
        dictMap.put("224","高中学科拓展");
        dictMap.put("225","高中传媒");
        dictMap.put("226","体育与健身");
        dictMap.put("227","生命科学");
        dictMap.put("228","初中信息科技");
        dictMap.put("229","上海市乡土地理");
        dictMap.put("230","初中领导力");
        dictMap.put("90","物理选修");
        dictMap.put("91","化学选修");
        dictMap.put("92","生物选修");
        dictMap.put("93","政治选修");
        dictMap.put("94","历史选修");
        dictMap.put("95","地理选修");
        dictMap.put("97","通用技术选修");
        dictMap.put("96","信息技术选修");
        dictMap.put("239","道德与法治");
        dictMap.put("240","外语");
        dictMap.put("241","俄语");
        dictMap.put("242","高中信息科技");
        dictMap.put("243","生命与健康常识");
        dictMap.put("244","健康教育");
        dictMap.put("245","生涯规划");
        dictMap.put("246","综合学习与实践");
        dictMap.put("247","土木水利类专业");
        dictMap.put("248","信息技术类专业");
        dictMap.put("249","农林牧渔类");
        dictMap.put("250","旅游服务类专业");
        dictMap.put("251","服装类专业");
        dictMap.put("252","电子专业");
        dictMap.put("253","财经商贸类专业");
        dictMap.put("254","加工制造类");
        dictMap.put("255","公共管理与服务类专业");
        dictMap.put("257","农林牧渔类（种植）");
        dictMap.put("258","农林牧渔类（养殖）");
        dictMap.put("259","旅游服务类（烹饪）");
        dictMap.put("260","经济学");
        dictMap.put("261","文秘");
        dictMap.put("262","国学");
        dictMap.put("263","珠算");
        dictMap.put("267","健康成长");
        dictMap.put("264","书法");
        dictMap.put("265","书法练习指导");
        dictMap.put("266","卫生与保健");
        dictMap.put("268","心理");
        dictMap.put("269","校本");
        dictMap.put("271","韩语");
        dictMap.put("272","科技活动");
        dictMap.put("277","武术");
        dictMap.put("280","地方");
        dictMap.put("281","三生教育");
        dictMap.put("282","足球");
        dictMap.put("286","民族常识");
        dictMap.put("287","office");
        dictMap.put("288","PS");
        dictMap.put("289","VB");
        dictMap.put("290","汉录");
        dictMap.put("291","网络");
        dictMap.put("292","教师指导用书");
        dictMap.put("293","AP人文地理");
        dictMap.put("294","AP世界历史");
        dictMap.put("295","AP统计");
        dictMap.put("296","AP物理C");
        dictMap.put("297","AP心理");
        dictMap.put("298","AP化学");
        dictMap.put("299","AP计算机");
        dictMap.put("300","AP美国历史");
        dictMap.put("301","传统文化");
        dictMap.put("302","主题游戏活动");
        dictMap.put("303","语文选修");
        dictMap.put("304","数学选修");
        dictMap.put("305","英语选修");
        dictMap.put("306","民语文");
        dictMap.put("307","英语写作");
        dictMap.put("308","AP环境科学");
        dictMap.put("309","特色课程");
        dictMap.put("310","英语口语考试");
        dictMap.put("311","法制");
        dictMap.put("312","公共安全与生命教育");
        dictMap.put("313","环境教育");
        dictMap.put("314","研究性学习");
        dictMap.put("315","法制教育");
        dictMap.put("316","数学竞赛");
        dictMap.put("317","化学竞赛");
        dictMap.put("318","生物竞赛");
        dictMap.put("319","信息竞赛");
        dictMap.put("320","物理竞赛");
        dictMap.put("321","日语选修");
        dictMap.put("322","特教综合");
        dictMap.put("323","社会与法制");
        dictMap.put("324","知识竞赛");
        dictMap.put("325","行政");
        dictMap.put("326","医学类专业");
        dictMap.put("327","校本选修");
        dictMap.put("328","竞赛");
        dictMap.put("329","品德与科学");
        dictMap.put("331","制冷与空调设备运行与维修");
        dictMap.put("332","物流服务与管理");
        dictMap.put("333","综合");
        dictMap.put("334","综合（初中）");
        dictMap.put("335","综合（高中）");
        dictMap.put("337","戏剧");
        dictMap.put("338","习惯");
        dictMap.put("339","环境");
        dictMap.put("340","义工");
        dictMap.put("341","名著导读");
        dictMap.put("342","研学");
        dictMap.put("343","商贸");
        dictMap.put("344","餐旅");
        dictMap.put("345","ESL");
        dictMap.put("346","IG ESL");
        dictMap.put("347","IG 生物");
        dictMap.put("348","IG 经济学");
        dictMap.put("349","IG 数学");
        dictMap.put("350","IG 化学");
        dictMap.put("351","IG 物理");
        dictMap.put("352","College prep");
        dictMap.put("353","Science");
        dictMap.put("354","AP微积分");
        dictMap.put("355","AP经济学");
        dictMap.put("356","民族");
        dictMap.put("357","化学（选修）");
        dictMap.put("358","写字与书法");
        dictMap.put("359","民语文1");
        dictMap.put("360","高数");
        dictMap.put("361","计算机科学");
        dictMap.put("362","经济");
        dictMap.put("363","AP 微积分 AB");
        dictMap.put("364","AP 微积分 BC");
        dictMap.put("365","AP 物理电磁");
        dictMap.put("366","AP 物理力学");
        dictMap.put("367","科学研究");
        dictMap.put("368","留学安全");
        dictMap.put("369","人文探索");
        dictMap.put("370","商科");
        dictMap.put("371","社会学");
        dictMap.put("372","升学指导");
        dictMap.put("373","双语科学");
        dictMap.put("374","双语生物");
        dictMap.put("375","双语数学");
        dictMap.put("376","外教科学");
        dictMap.put("377","外教英语");
        dictMap.put("378","文学赏析");
        dictMap.put("379","英文阅读");
        dictMap.put("380","有机化学");
        dictMap.put("381","哲学");
        dictMap.put("382","典籍");
        dictMap.put("383","民族语文");
        dictMap.put("384","物理合格考");
        dictMap.put("385","化学合格考");
        dictMap.put("386","生物合格考");
        dictMap.put("387","政治合格考");
        dictMap.put("388","历史合格考");
        dictMap.put("389","地理合格考");
        dictMap.put("390","AP经济地理");
        dictMap.put("391","有机化学A");
        dictMap.put("392","有机化学B");
        dictMap.put("393","天文地质选修");
        dictMap.put("394","艺术史");
        dictMap.put("395","纯数学");
        dictMap.put("396","文明礼仪教育");
        dictMap.put("397","海洋教育");
        dictMap.put("398","职教");
        dictMap.put("399","卫生防疫");
        dictMap.put("400","劳动");
        dictMap.put("401","劳技");
        dictMap.put("402","唱游");
        dictMap.put("403","探究");
        dictMap.put("404","体育活动");
        dictMap.put("405","拓展课");
        dictMap.put("406","德育");
        dictMap.put("407","意大利语");
        dictMap.put("408","职业素养");
        dictMap.put("409","活动类课程");
        dictMap.put("410","家长教育类课程");
        dictMap.put("411","俄语选修");
        dictMap.put("412","健康社会与艺术");
        dictMap.put("413","科学与数学");
        dictMap.put("414","快乐生活");
        dictMap.put("415","服务礼仪");
        dictMap.put("416","旅游文化");
        dictMap.put("417","饭店服务");
        dictMap.put("418","旅游理论");
        dictMap.put("419","网店开设");
        dictMap.put("420","网页设计");
        dictMap.put("421","客户服务");
        dictMap.put("422","网络营造");
        dictMap.put("423","商品拍摄");
        dictMap.put("424","仓储物流");
        dictMap.put("425","电商技能");
        dictMap.put("426","旅游技能");
        dictMap.put("427","零基础日语");
        dictMap.put("428","综合（英语）");
        dictMap.put("429","托福听力");
        dictMap.put("430","托福口语");
        dictMap.put("431","托福阅读");
        dictMap.put("432","托福写作");
        dictMap.put("433","托福词汇");
        dictMap.put("434","高级阅读");
        dictMap.put("435","高级语法");
        dictMap.put("436","高级写作");
        dictMap.put("437","AP 历史");
        dictMap.put("438","AP 文学");
        dictMap.put("439","文学写作");
        dictMap.put("440","生活语文");
        dictMap.put("441","生活数学");
        dictMap.put("442","生活适应");
        dictMap.put("443","语文识字写字与词语");
        dictMap.put("444","语文句子标点与修辞");
        dictMap.put("445","语文口语交际");
        dictMap.put("446","语文综合性学习");
        dictMap.put("447","语文写作");
        dictMap.put("448","语文阅读");
        dictMap.put("449","技术选修");
        dictMap.put("450","英语听力与写作");
        dictMap.put("451","AP微观经济学");
        dictMap.put("452","美国历史与文化");
        dictMap.put("453","AP宏观经济学");
        dictMap.put("454","综合（数英）");
        dictMap.put("455","AP经济微观学");
        dictMap.put("456","强化物理学");
        dictMap.put("457","AP研讨");
        dictMap.put("458","英美文学");
        dictMap.put("459","学术写作");
        dictMap.put("460","学术英语");
        dictMap.put("461","工艺");
        dictMap.put("462","色彩");
        dictMap.put("463","素描");
        dictMap.put("464","零基础法语");
        dictMap.put("465","活动");
        dictMap.put("466","化学晚课");
        dictMap.put("467","标准中文");
        dictMap.put("468","人文");
        dictMap.put("469","专业课3");
        dictMap.put("470","专业课4");
        dictMap.put("471","专业课1");
        dictMap.put("472","专业课2");
        dictMap.put("473","机械加工");
        dictMap.put("474","人生规划");
        dictMap.put("475","维护");
        dictMap.put("476","基础专业");
        dictMap.put("477","数控专业");
        dictMap.put("478","模具专业");
        dictMap.put("479","平面设计");
        dictMap.put("480","地方课程");
        dictMap.put("481","校本课程");
        dictMap.put("482","学生发展指导");
        dictMap.put("483","会计信息化");
        dictMap.put("484","农林牧渔类（果树）");
        dictMap.put("485","园林绿化");
        dictMap.put("486","C语言程序设计");
        dictMap.put("487","综合（小学素养）");
        dictMap.put("488","人工智能");
        dictMap.put("489","形体");
        dictMap.put("490","普通心理学");
        dictMap.put("491","幼儿教育概论");
        dictMap.put("492","幼儿游戏");
        dictMap.put("493","语教");
        dictMap.put("494","幼儿卫生保健");
        dictMap.put("495","幼儿学习与发展");
        dictMap.put("496","幼儿文学");
        dictMap.put("497","幼儿园环境创设");
        dictMap.put("498","美教");
        dictMap.put("499","体教");
        dictMap.put("500","音教");
        dictMap.put("501","科学与技术");
        dictMap.put("502","思品与科学");
        dictMap.put("503","限定选修生涯");
        dictMap.put("504","舞蹈");
        dictMap.put("505","口语");
        dictMap.put("506","专业戏曲");
        dictMap.put("507","零件加工工艺分析与编制");
        dictMap.put("508","机械基础");
        dictMap.put("509","商务英语听说");
        dictMap.put("510","液气压系统安装与调试");
        dictMap.put("511","机械制图");
        dictMap.put("512","化工仪表及自动化");
        dictMap.put("513","机电专业英语");
        dictMap.put("514","化工安全与清洁生产");
        dictMap.put("515","中国特色社会主义");
        dictMap.put("516","会计操作技能");
        dictMap.put("517","化学基础与分析检测");
        dictMap.put("518","心理健康与职业生涯");
        dictMap.put("519","化工产品仓储作业实务");
        dictMap.put("520","导游讲解");
        dictMap.put("521","社会常识");
        dictMap.put("522","自然常识");
        dictMap.put("523","总复习专项");
        dictMap.put("524","经济法");
        dictMap.put("525","唱游与律动");
        dictMap.put("526","晨圈活动");
        dictMap.put("527","户外活动");
        dictMap.put("528","康复训练");
        dictMap.put("529","劳动与技能");
        dictMap.put("530","午点活动");
        dictMap.put("531","运动与保健");
        dictMap.put("532","知动");
        dictMap.put("533","主题活动");
        dictMap.put("534","升旗");
        dictMap.put("535","社团活动");
        dictMap.put("536","主题班会");
        dictMap.put("537","绘画与手工");
        dictMap.put("538","劳动教育");
        dictMap.put("539","教育科研");
        dictMap.put("540","数学附加");
        dictMap.put("541","英语附加");
        dictMap.put("542","调查问卷");
        dictMap.put("543","综合（政史）");
        dictMap.put("544","机电专业理论");
        dictMap.put("545","医药护理");
        dictMap.put("546","药理学基础");
        dictMap.put("547","计算机综合");
        dictMap.put("548","幼儿园课程");
        dictMap.put("549","土建类专业知识");
        dictMap.put("550","商贸类专业综合");
        dictMap.put("551","餐饮服务与管理");
        dictMap.put("552","化工过程控制");
        dictMap.put("553","公共安全");
        dictMap.put("554","化工装置操作");
        dictMap.put("555","精馏");
        dictMap.put("556","无机化学");
        dictMap.put("557","互换性与测量技术");
        dictMap.put("558","工业机器人技术应用");
        dictMap.put("559","智慧物流技术应用");
        dictMap.put("560","现代物流基础");
        dictMap.put("561","基础会计");
        dictMap.put("562","职业通识教育");
        dictMap.put("563","英语双");
        dictMap.put("564","数学双");
        dictMap.put("565","课后服务");
        dictMap.put("566","计算机技能");
        dictMap.put("567","汽车机械识图");
        dictMap.put("568","计算机理论");
        dictMap.put("569","计算机网络");
        dictMap.put("570","重视烹调技艺");
        dictMap.put("571","中式面点基础");
        dictMap.put("572","中式热菜制作");
        dictMap.put("573","烹饪原料基础知识");
        dictMap.put("574","烹饪营养与卫生");
        dictMap.put("575","西点制作");
        dictMap.put("576","创新菜肴制作");
        dictMap.put("577","烹饪技能");
        dictMap.put("578","京饪基本功");
        dictMap.put("579","中式面点技艺");
        dictMap.put("580","电工技术基础");
        dictMap.put("581","电工基础");
        dictMap.put("582","烹饪基本功");
        dictMap.put("583","电子线路");
        dictMap.put("584","电子技术墓础");
        dictMap.put("585","计算机原理");
        dictMap.put("586","物理历史");
        dictMap.put("587","生物物理");
        dictMap.put("588","化学政治");
        dictMap.put("589","现代农艺");
        dictMap.put("590","食品加工");
        dictMap.put("591","畜牧养殖");
        dictMap.put("592","水产养殖");
        dictMap.put("593","建筑施工");
        dictMap.put("594","建筑设计与管理");
        dictMap.put("595","机械制造");
        dictMap.put("596","设备维修");
        dictMap.put("597","机电技术");
        dictMap.put("598","自动控制");
        dictMap.put("599","电气技术");
        dictMap.put("600","电子技术");
        dictMap.put("601","化工技术");
        dictMap.put("602","环境保护");
        dictMap.put("603","服装工程");
        dictMap.put("604","纺织工程");
        dictMap.put("605","服装展演");
        dictMap.put("606","车辆维修");
        dictMap.put("607","水上运输");
        dictMap.put("608","运输管理");
        dictMap.put("609","数字媒体");
        dictMap.put("610","网络技术");
        dictMap.put("611","软件与应用技术");
        dictMap.put("612","医学技术");
        dictMap.put("613","财税");
        dictMap.put("614","金融");
        dictMap.put("615","国际商务");
        dictMap.put("616","物流管理");
        dictMap.put("617","烹饪与营养");
        dictMap.put("618","旅游管理");
        dictMap.put("619","公共服务与管理");
        dictMap.put("620","艺术设计");
        dictMap.put("621","物理会考");
        dictMap.put("622","政治会考");
        dictMap.put("623","综合素质");
        dictMap.put("624","物流基础");
        dictMap.put("625","物流客户服务");
        dictMap.put("626","仓储与配送作业实务");
        dictMap.put("627","运输作业实务");
        dictMap.put("628","快递实务");
        dictMap.put("629","货运代理");
        dictMap.put("630","电子商务基础");
        dictMap.put("631","网络营销实务");
        dictMap.put("632","电子商务客户服务与管理");
        dictMap.put("633","电子商务物流");
        dictMap.put("634","移动商务基础");
        dictMap.put("635","网店运营");
        dictMap.put("636","网络安全技术");
        dictMap.put("637","网络设备安装与调试");
        dictMap.put("638","图形图像处理");
        dictMap.put("639","网页制作");
        dictMap.put("640","计算机组装与维护");
        dictMap.put("641","实用美术基础");
        dictMap.put("642","数字摄影摄像");
        dictMap.put("643","常用工具软件");
        dictMap.put("644","汽车机械基础及维修基础知识");
        dictMap.put("645","汽车电工电子基础知识");
        dictMap.put("646","汽车发动机知识");
        dictMap.put("647","汽车底盘知识");
        dictMap.put("648","汽车电气设备知识");
        dictMap.put("649","新能源汽车基础知识");
        dictMap.put("650","电工技术基础及应用");
        dictMap.put("651","模拟电子技术基础及应用");
        dictMap.put("652","数字电子技术基础及应用");
        dictMap.put("653","电子测量技术");
        dictMap.put("654","变压器与电动机");
        dictMap.put("655","传感器应用技术");
        dictMap.put("656","单片机控制技术");
        dictMap.put("657","幼儿教育学");
        dictMap.put("658","幼儿园活动设计与指导");
        dictMap.put("659","幼儿心理学");
        dictMap.put("660","幼儿卫生学");
        dictMap.put("661","网络营销策划");
        dictMap.put("662","网店美工");
        dictMap.put("663","餐饮客房");
        dictMap.put("664","旅游心理学");
        dictMap.put("665","网络技术专业综合");
        dictMap.put("666","学前教育专业综合");
        dictMap.put("667","基础会计与快递实务");
        dictMap.put("668","轨道交通机电设备");
        dictMap.put("669","城市轨道交通车辆构造");
        dictMap.put("670","生命安全教育");
        dictMap.put("671","传媒教学");
        dictMap.put("672","英语e听说");
        dictMap.put("673","语文阅读（评价）");
        dictMap.put("674","语文写作（评价）");
        dictMap.put("675","数学附加（评价）");
        dictMap.put("676","数学一试（评价）");
        dictMap.put("677","数学二试（评价）");
        dictMap.put("678","数学Ⅰ一试（评价）");
        dictMap.put("679","地理E（评价）");
        dictMap.put("680","数学Ⅰ二试（评价）");
        dictMap.put("681","数学C一试（评价）");
        dictMap.put("682","数学E一试（评价）");
        dictMap.put("683","数学E二试（评价）");
        dictMap.put("684","直升班数学（评价）");
        dictMap.put("685","地理C（评价）");
        dictMap.put("686","外招班数学（评价）");
        dictMap.put("687","直升班数学一试（评价）");
        dictMap.put("688","直升班数学二试（评价）");
        dictMap.put("689","物理E实验（评价）");
        dictMap.put("690","直升Ⅰ数学一试（评价）");
        dictMap.put("691","直升Ⅰ数学二试（评价）");
        dictMap.put("692","物理E笔试（评价）");
        dictMap.put("693","直升Ⅱ数学一试（评价）");
        dictMap.put("694","物理C实验（评价）");
        dictMap.put("695","直升Ⅱ数学二试（评价）");
        dictMap.put("696","外招班数学一试（评价）");
        dictMap.put("697","物理C笔试（评价）");
        dictMap.put("698","外招班数学二试（评价）");
        dictMap.put("699","物理实验（评价）");
        dictMap.put("700","物理笔试（评价）");
        dictMap.put("701","培优班数学一试（评价）");
        dictMap.put("702","化学E实验（评价）");
        dictMap.put("703","培优班数学二试（评价）");
        dictMap.put("704","化学E笔试（评价）");
        dictMap.put("705","超前班数学一试（评价）");
        dictMap.put("706","化学C实验（评价）");
        dictMap.put("707","超前班数学二试（评价）");
        dictMap.put("708","英语Ⅰ笔试（评价）");
        dictMap.put("709","化学C笔试（评价）");
        dictMap.put("710","英语Ⅱ笔试（评价）");
        dictMap.put("711","化学实验（评价）");
        dictMap.put("712","英语Ⅰ口语（评价）");
        dictMap.put("713","化学笔试（评价）");
        dictMap.put("714","英语Ⅱ口语（评价）");
        dictMap.put("715","生物E实验（评价）");
        dictMap.put("716","英语原著阅读（评价）");
        dictMap.put("717","生物E笔试（评价）");
        dictMap.put("718","英语e听说（评价）");
        dictMap.put("719","科学笔试（评价）");
        dictMap.put("720","生物C实验（评价）");
        dictMap.put("721","直升班科学（评价）");
        dictMap.put("722","雅思听口（评价）");
        dictMap.put("723","直升班科学笔试（评价）");
        dictMap.put("724","生物C笔试（评价）");
        dictMap.put("725","生物实验（评价）");
        dictMap.put("726","直升Ⅰ科学实验（评价）");
        dictMap.put("727","生物笔试（评价）");
        dictMap.put("728","外招班科学实验（评价）");
        dictMap.put("729","外招班科学（评价）");
        dictMap.put("730","外招班科学笔试（评价）");
        dictMap.put("731","直升班科学实验（评价）");
        dictMap.put("732","直升Ⅱ科学实验（评价）");
        dictMap.put("733","科学实验（评价）");
        dictMap.put("734","直升Ⅱ科学笔试（评价）");
        dictMap.put("735","直升Ⅰ科学笔试（评价）");
        dictMap.put("736","数学C二试（评价）");
        dictMap.put("737","阅览");
        dictMap.put("738","主探");
        dictMap.put("739","特色教育");
        dictMap.put("740","拓展活动");
        dictMap.put("741","版拓");
        dictMap.put("742","五育");
        dictMap.put("743","心理健康发展");
        dictMap.put("744","化工职业通识认知");
        dictMap.put("745","化学工艺概论");
        dictMap.put("746","化工质量检测");
        dictMap.put("747","工艺参数测量");
        dictMap.put("748","智能制造概论");
        dictMap.put("749","计算机专业英语");
        dictMap.put("750","企业成本核算");
        dictMap.put("751","物流实用英语");
        dictMap.put("752","班会");
        dictMap.put("753","理论");
        dictMap.put("754","实作");
        dictMap.put("755","政治素养");
        dictMap.put("756","教育学");
        dictMap.put("757","心理学");
        dictMap.put("758","人文素养");
        dictMap.put("759","科学素养");
        dictMap.put("760","礼仪");
        dictMap.put("761","电子商务法律基础知识");
        dictMap.put("762","非遗");
        dictMap.put("763","健康领域");
        dictMap.put("764","语言领域");
        dictMap.put("765","社会领域");
        dictMap.put("766","科学领域");
        dictMap.put("767","艺术领域");
        dictMap.put("768","园本");
        dictMap.put("769","烹饪");
    }
    
}
