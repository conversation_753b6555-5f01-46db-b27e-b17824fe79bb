package com.iflytek.skylab.service.front.controller;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.contract.service.*;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import io.swagger.annotations.Api;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.boot.annotation.EnableSkynetSwagger2;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Api(tags = "http接入服务")
@Slf4j
@RestController
@EnableSkynetSwagger2
@RequestMapping(
        value = "/skylab/api/v2",
        consumes = {MediaType.APPLICATION_JSON_VALUE},
        produces = {MediaType.APPLICATION_JSON_VALUE}
)
public class FrontController {

    @Autowired
    private SkylabRecommendService recommendService;
    @Autowired
    private SkylabDiagnoseService diagnoseService;
    @Autowired
    private SkylabBehaviorService behaviorService;
    @Autowired
    private SkylabStandService standService;
    @Autowired
    private SkylabClearMasteryService clearMasteryService;
    @Autowired
    private SkylabPrimaryMigrationService primaryMigrationService;

    @PostMapping("/recommend")
    public Object recommend(@RequestHeader("sceneType") String sceneType,
                            @RequestHeader("funcParam") String funcParam,
                            @RequestHeader("funcResult") String funcResult,
                            @RequestBody SkylabJsonRequest request) throws ClassNotFoundException {

        log.info("http recommend receive");
        SkylabRequest<FuncParam> skylabRequest = new SkylabRequest<>();
        skylabRequest.setTraceId(request.traceId);
        skylabRequest.setScene(parseSceneInfo(sceneType, request));
        skylabRequest.setPayload(parseFuncParam(funcParam, request));

        funcResult = FuncResult.class.getPackage().getName() + "." + funcResult;
        return recommendService.recommend(skylabRequest, (Class<? extends FuncResult>) Class.forName(funcResult));
    }

    @PostMapping("/diagnose")
    public Object diagnose(@RequestHeader("sceneType") String sceneType,
                           @RequestHeader("funcParam") String funcParam,
                           @RequestBody SkylabJsonRequest request) throws ClassNotFoundException {
        log.info("http diagnose receive");
        SkylabRequest<FuncParam> skylabRequest = new SkylabRequest<>();
        skylabRequest.setTraceId(request.traceId);
        skylabRequest.setScene(parseSceneInfo(sceneType, request));
        skylabRequest.setPayload(parseFuncParam(funcParam, request));

        return diagnoseService.diagnose(skylabRequest);
    }

    @PostMapping("/reportAnswerRecord")
    public Object reportAnswerRecord(@RequestHeader("sceneType") String sceneType,
                                     @RequestHeader(name = "funcParam", required = false) String funcParam,
                                     @RequestBody SkylabJsonRequest request) throws ClassNotFoundException {
        log.info("http reportAnswerRecord receive");
        SkylabRequest<StudyLogParam> skylabRequest = new SkylabRequest<>();
        skylabRequest.setTraceId(request.traceId);
        skylabRequest.setScene(parseSceneInfo(sceneType, request));
        if (StudyCorrectLogParam.class.getSimpleName().equals(funcParam)) {
            skylabRequest.setPayload(JSON.to(StudyCorrectLogParam.class, request.getPayload()));
        } else {
            skylabRequest.setPayload(JSON.to(StudyLogParam.class, request.getPayload()));
        }

        return behaviorService.reportAnswerRecord(skylabRequest);
    }

    @PostMapping("/queryAnswerRecord")
    public Object queryAnswerRecord(@RequestHeader("sceneType") String sceneType,
                                     @RequestBody SkylabJsonRequest request) throws ClassNotFoundException {
        log.info("http queryAnswerRecord receive");
        SkylabRequest<BehaviorQueryParam> skylabRequest = new SkylabRequest<>();
        skylabRequest.setTraceId(request.traceId);
        skylabRequest.setScene(parseSceneInfo(sceneType, request));
        skylabRequest.setPayload(JSON.to(BehaviorQueryParam.class, request.getPayload()));

        return behaviorService.queryAnswerRecord(skylabRequest);
    }

    @PostMapping("/featureFetch")
    public Object featureFetch(@RequestHeader("sceneType") String sceneType,
                               @RequestHeader(name = "funcParam", required = false) String funcParam,
                               @RequestBody SkylabJsonRequest request) throws ClassNotFoundException {
        log.info("http featureFetch receive");
        SkylabRequest<FuncParam> skylabRequest = new SkylabRequest<>();
        skylabRequest.setTraceId(request.traceId);
        skylabRequest.setScene(parseSceneInfo(sceneType, request));
        if (AcquireFeatureParam.class.getSimpleName().equals(funcParam)) {
            skylabRequest.setPayload(parseFuncParam(funcParam, request));
        } else {
            skylabRequest.setPayload(JSON.to(FeatureParam.class, request.getPayload()));
        }
        return standService.featureFetch(skylabRequest);
    }

    @PostMapping("/clearMastery")
    public Object clearMastery(@RequestHeader("sceneType") String sceneType,
                               @RequestBody SkylabJsonRequest request) throws ClassNotFoundException{
        log.info("http clearMastery receive");
        SkylabRequest<ClearMasteryParam> skylabRequest = new SkylabRequest<>();
        skylabRequest.setTraceId(request.traceId);
        skylabRequest.setScene(parseSceneInfo(sceneType, request));
        skylabRequest.setPayload(JSON.to(ClearMasteryParam.class, request.getPayload()));

        return clearMasteryService.clearMastery(skylabRequest);
    }

    /**
     * 小学精准学答题记录上报
     * @param request
     * @return
     */
    @PostMapping("/primary/behavior")
    public SkylabResponse<FuncResult> behavior(@RequestHeader("sceneType") String sceneType,
                                               @RequestBody SkylabJsonRequest request) throws ClassNotFoundException{
        log.info("http primary/behavior receive");
        SkylabRequest<PrimaryMigrationBehaviorParam> skylabRequest = new SkylabRequest<>();
        skylabRequest.setTraceId(request.traceId);
        skylabRequest.setScene(parseSceneInfo(sceneType, request));
        skylabRequest.setPayload(JSON.to(PrimaryMigrationBehaviorParam.class, request.getPayload()));

        return primaryMigrationService.behavior(skylabRequest);
    }


    /**
     * 小学精准学章节掌握度上报
     * @param request
     * @return
     */
    @PostMapping("/primary/mastery")
    public SkylabResponse<FuncResult> mastery(@RequestHeader("sceneType") String sceneType,
                                              @RequestBody SkylabJsonRequest request) throws ClassNotFoundException{
        SkylabRequest<PrimaryMigrationMasteryParam> skylabRequest = new SkylabRequest<>();
        log.info("http primary/mastery receive");
        skylabRequest.setTraceId(request.traceId);
        skylabRequest.setScene(parseSceneInfo(sceneType, request));
        skylabRequest.setPayload(JSON.to(PrimaryMigrationMasteryParam.class, request.getPayload()));
        return primaryMigrationService.mastery(skylabRequest);
    }

    private SceneInfo parseSceneInfo(String sceneType, SkylabJsonRequest request) throws ClassNotFoundException {
        // SceneInfo类本身
        if (SceneInfo.class.getSimpleName().equals(sceneType)) {
            return JSON.to(SceneInfo.class, request.getScene());
        }

        // SceneInfo的子类
        sceneType = AiDiagSceneInfo.class.getPackage().getName() + "." + sceneType;
        return (SceneInfo) JSON.to(Class.forName(sceneType), request.getScene());
    }

    private FuncParam parseFuncParam(String funcParam, SkylabJsonRequest request) throws ClassNotFoundException {
        funcParam = FuncParam.class.getPackage().getName() + "." + funcParam;
        return (FuncParam) JSON.to(Class.forName(funcParam), request.getPayload());
    }


    @Getter
@Setter
    private static class SkylabJsonRequest implements Serializable {
        @NotBlank
        private String traceId;

        @NotNull
        private JSONObject scene;

        @NotNull
        private JSONObject payload;

    }

}
