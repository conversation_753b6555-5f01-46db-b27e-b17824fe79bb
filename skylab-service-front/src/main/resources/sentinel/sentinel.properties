# ------sentinel-transport-common \u7684\u914D\u7F6E\u9879------#
# \u63A7\u5236\u53F0\u7684\u5730\u5740\uFF0C\u6307\u5B9A\u63A7\u5236\u53F0\u540E\u5BA2\u6237\u7AEF\u4F1A\u81EA\u52A8\u5411\u8BE5\u5730\u5740\u53D1\u9001\u5FC3\u8DF3\u5305\u3002\u5730\u5740\u683C\u5F0F\u4E3A\uFF1AhostIp:port
csp.sentinel.dashboard.server=${csp.sentinel.dashboard.server}
# \u672C\u5730\u542F\u52A8 HTTP API Server \u7684\u7AEF\u53E3\u53F7
csp.sentinel.api.port=${csp.sentinel.api.port}
# \u5FC3\u8DF3\u5305\u53D1\u9001\u5468\u671F\uFF0C\u5355\u4F4D\u6BEB\u79D2
#csp.sentinel.heartbeat.interval.ms=
# \u6307\u5B9A\u5FC3\u8DF3\u5305\u4E2D\u672C\u673A\u7684 IP,\u82E5\u4E0D\u6307\u5B9A\u5219\u901A\u8FC7 HostNameUtil \u89E3\u6790\uFF1B\u8BE5\u914D\u7F6E\u9879\u591A\u7528\u4E8E\u591A\u7F51\u5361\u73AF\u5883
csp.sentinel.heartbeat.client.ip=${IP}
#------sentinel-core \u7684\u914D\u7F6E\u9879--------#
# \u5E94\u7528\u7684\u540D\u79F0
project.name=${SKYNET_PLUGIN_CODE}
# \u5355\u4E2A\u76D1\u63A7\u65E5\u5FD7\u6587\u4EF6\u7684\u5927\u5C0F
csp.sentinel.metric.file.single.size=52428800
# \u76D1\u63A7\u65E5\u5FD7\u6587\u4EF6\u7684\u603B\u6570\u4E0A\u9650
csp.sentinel.metric.file.total.count=6
# \u6700\u5927\u7684\u6709\u6548\u54CD\u5E94\u65F6\u957F\uFF08ms\uFF09\uFF0C\u8D85\u51FA\u6B64\u503C\u5219\u6309\u7167\u6B64\u503C\u8BB0\u5F55
csp.sentinel.statistic.max.rt=3000
#------\u65E5\u5FD7\u76F8\u5173\u914D\u7F6E\u9879--------#
# Sentinel \u65E5\u5FD7\u6587\u4EF6\u76EE\u5F55
csp.sentinel.log.dir=${SKYNET_HOME}/logs/sentinel/${SKYNET_ACTION_CODE}
# \u65E5\u5FD7\u6587\u4EF6\u540D\u4E2D\u662F\u5426\u52A0\u5165\u8FDB\u7A0B\u53F7\uFF0C\u7528\u4E8E\u5355\u673A\u90E8\u7F72\u591A\u4E2A\u5E94\u7528\u7684\u60C5\u51B5
csp.sentinel.log.use.pid=true
# Record \u65E5\u5FD7\u8F93\u51FA\u7684\u7C7B\u578B\uFF0Cfile \u4EE3\u8868\u8F93\u51FA\u81F3\u6587\u4EF6\uFF0Cconsole \u4EE3\u8868\u8F93\u51FA\u81F3\u7EC8\u7AEF
csp.sentinel.log.output.type=file
