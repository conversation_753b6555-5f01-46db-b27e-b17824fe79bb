{"degrade": [{"resource": "*", "grade": 2, "count": 100, "timeWindow": 60, "statIntervalMs": 60000}, {"resource": "04#02#MASTER_FETCH", "grade": 2, "count": 100, "timeWindow": 60, "statIntervalMs": 60000}], "flow": [{"_desc": "汇报答题记录方法基于qps限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabBehaviorServiceImpl#reportAnswerRecord", "grade": 1, "count": 200}, {"_desc": "汇报答题记录方法基于线程数限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabBehaviorServiceImpl#reportAnswerRecord", "grade": 0, "count": 30}, {"_desc": "诊断接口方法基于qps限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabDiagnoseServiceImpl#diagnose", "grade": 1, "count": 200}, {"_desc": "诊断接口方法基于线程数限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabDiagnoseServiceImpl#diagnose", "grade": 0, "count": 30}, {"_desc": "通用扩展接口方法基于qps限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabExtServiceImpl#call", "grade": 1, "count": 200}, {"_desc": "通用扩展接口方法基于线程数限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabExtServiceImpl#call", "grade": 0, "count": 30}, {"_desc": "推题接口方法基于qps限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabRecommendServiceImpl#recommend", "grade": 1, "count": 200}, {"_desc": "推题接口方法基于线程数限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabRecommendServiceImpl#recommend", "grade": 0, "count": 30}, {"_desc": "通用特征查询接口方法基于qps限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabStandServiceImpl#featureFetch", "grade": 1, "count": 500}, {"_desc": "通用特征查询接口方法基于线程数限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabStandServiceImpl#featureFetch", "grade": 0, "count": 30}, {"_desc": "查询答题记录方法基于qps限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabBehaviorServiceImpl#queryAnswerRecord", "grade": 1, "count": 500}, {"_desc": "查询答题记录方法基于线程数限流", "resource": "com.iflytek.skylab.service.front.service.impl.SkylabBehaviorServiceImpl#queryAnswerRecord", "grade": 0, "count": 30}]}