IP=*************
SKYNET_CLUSTER=skynet
SKYNET_PLUGIN_CODE=skylab-platform
server.port=32181
#------------------------------------------------------------
spring.profiles.active=dev
#------------------------------------------------------------
dubbo.port=32180
dubbo.timeout=6000
skylab.epas.zookeeper.address=${IP}:2181
skylab.dubbo.xml.path=classpath:/front-spring-dubbo.xml
#skylab.dubbo.xml.path=file:D:\\front-spring-dubbo.xml
#------------------------------------------------------------
skyline.brave.enabled=true
skyline.brave.aop-enabled=true
#------------------------------------------------------------
spring.application.name=skylab-front
skynet.action-point=${spring.application.name}@${SKYNET_PLUGIN_CODE}
skynet.zookeeper.enabled=true
spring.cloud.zookeeper.enabled=true
spring.cloud.zookeeper.discovery.root=/${SKYNET_CLUSTER}/discovery/${SKYNET_PLUGIN_CODE}
spring.cloud.zookeeper.connect-string=${IP}:2181
#------------------------------------------------------------
logging.level.ROOT=INFO
logging.level.com.iflytek.skyline=INFO
logging.level.com.iflytek.skylab=INFO
#------------------------------------------------------------
spring.groovy.template.check-template-location=false
#------------------------------------------------------------
# \u8BCA\u65AD\u670D\u52A1\u515C\u5E95\u53C2\u6570
skylab.front.failover.diag.force=false
skylab.front.failover.diag.enable=false
skylab.front.failover.diag.failed-limit=30
# \u6392\u5E8F\u670D\u52A1\u515C\u5E95\u53C2\u6570
skylab.front.failover.sort.force=false
skylab.front.failover.sort.enable=false
skylab.front.failover.sort.failed-limit=30
# \u66B4\u9732\u515C\u5E95\u4FE1\u606Fendpoint
management.endpoints.web.exposure.include=failover-guard
# \u670D\u52A1\u8C03\u7528\u8D85\u65F6\u8BBE\u7F6E
feign.client.config.skyline-router.read-timeout=5000

skylab.front.log-study-trace=false

#-----------------------------------------------------------------
#debuging
skynet.logging.enabled=true
skynet.logging.debug.dubbo.enabled=true
skynet.logging.debug.springmvc.enabled=true
skynet.logging.debug.enable.com.iflytek=true

# sentinel
app.sentinel.enabled=true
app.sentinel.rule-path=classpath:sentinel/sentinel-rule-default.json
csp.sentinel.config.file=classpath:sentinel/sentinel.properties
csp.sentinel.api.port=8719
csp.sentinel.dashboard.server=127.0.0.1:58080

skylab.front.default-graph-version=20250217_001
skylab.primary.migration.properties.graphVersion=${skylab.front.default-graph-version}
skylab.data.api.graph.hosts=***********:9669
skylab.data.api.graph.maxConnSize=1000

skyline.data.api.sdk.app-key=app-69v3u6vj
skyline.data.api.sdk.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
skyline.data.api.sdk.url=http://**************:30890/api/v1/execute
skyline.data.api.sdk.keepAliveDurationSecond=5
skylab.zion.dict-table-name=dim_xxj_dic_model
skylab.zion.dict-family=u
skylab.zion.dict-qualifier=dicModel
skylab.zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
skylab.zion.dict-refresh-period-seconds=3600
skylab.zion.dict-data-api-item.dataApiId=api-vimqibeu
#????data-api-item.version
skylab.zion.dict-data-api-item.version=
skylab.zion.cache-max-size=1000000
skylab.zion.feature-data-api-item.dataApiId=api-x4znzm0l
#????data-api-item.version
skylab.zion.feature-data-api-item.version=
skylab.zion.post-process-fea-names=user_level,user_node_mastery_path
skylab.zion.cache-exclude-fea-names=user_level,anchor_similaryuser_mastery,check_similaryuser_mastery
skylab.data.api.study-log.featureVersion=1
skylab.data.api.study-log.graphVersion=v2022-03

zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.es-host=***********:9200,***********:9200,***********:9200
#zion.query-data-base=es
zion.dict-refresh-period-seconds=10
zion.query-data-base=hbase_data_api
zion.dict-table-name=dim_xxj_dic_model
zion.dict-data-api-item.dataApiId=api-vimqibeu
zion.feature-data-api-item.dataApiId=api-x4znzm0l
zion.app-key=app-69v3u6vj
zion.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
zion.url=http://**************:30890/api/v1/execute