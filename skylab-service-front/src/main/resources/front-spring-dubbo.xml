<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans.xsd
     http://code.alibabatech.com/schema/dubbo
     http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:application name="skylab-front"/>
    <dubbo:protocol name="dubbo" port="${dubbo.port}"/>

    <dubbo:registry id="app" protocol="zookeeper" address="${skylab.epas.zookeeper.address}" file="skylab-front-dubbo.cache"/>
    <dubbo:registry id="epas" protocol="epas" address="epasConfig"/>

    <!-- epas配置 服务需要使用EpasProviderConfig配置 -->
    <bean id="epasConfig" class="com.iflytek.edu.epas.dubbo.config.EpasProviderConfig">
        <!-- 在平台申请的appKey -->
        <property name="appKey" value="${skylab.epas.appKey}" />
        <property name="appSecret" value="${skylab.epas.appSecret}" />
        <!-- 地址服务url -->
        <property name="addrServerUrl" value="${skylab.address.server.url}" />
    </bean>

    <!-- zhixue default provider config-->
    <dubbo:provider id="zhixueProvider" protocol="dubbo" filter="-epasauth,skynetLoggingDebugDubboFilter" default="false" timeout="${dubbo.timeout}" retries="0" payload="11557050"/>
    <!-- EPAS default provider config -->
    <dubbo:provider id="epasProvider" protocol="dubbo" filter="skynetLoggingDebugDubboFilter" default="false" timeout="${dubbo.timeout}" retries="0" payload="11557050"/>

    <!--epas注册-->
    <!-- 推荐服务提供者 -->
    <dubbo:service registry="epas" provider="epasProvider" ref="SkylabRecommendServiceProvider"
                   interface="com.iflytek.skylab.core.contract.service.SkylabRecommendService" protocol="dubbo"/>
    <dubbo:service registry="epas" provider="epasProvider" ref="SkylabDiagnoseServiceProvider"
                   interface="com.iflytek.skylab.core.contract.service.SkylabDiagnoseService" protocol="dubbo"/>
    <dubbo:service registry="epas" provider="epasProvider" ref="SkylabBehaviorServiceProvider"
                   interface="com.iflytek.skylab.core.contract.service.SkylabBehaviorService" protocol="dubbo"/>
    <dubbo:service registry="epas" provider="epasProvider" ref="SkylabStandServiceProvider"
                   interface="com.iflytek.skylab.core.contract.service.SkylabStandService" protocol="dubbo"/>
    <dubbo:service registry="epas" provider="epasProvider" ref="SkylabExtServiceProvider"
                   interface="com.iflytek.skylab.core.contract.service.SkylabExtService" protocol="dubbo"/>
    <dubbo:service registry="epas" provider="epasProvider" ref="SkylabClearMasteryServiceProvider"
                   interface="com.iflytek.skylab.core.contract.service.SkylabClearMasteryService" protocol="dubbo"/>
    <dubbo:service registry="epas" provider="epasProvider" ref="SkylabPrimaryMigrationServiceProvider"
                   interface="com.iflytek.skylab.core.contract.service.SkylabPrimaryMigrationService" protocol="dubbo"/>


    <!--zk注册-->
    <!-- 推荐服务提供者 -->
    <dubbo:service registry="app" provider="zhixueProvider" ref="SkylabRecommendServiceProvider" group="${dubbo.group:production}"
                   interface="com.iflytek.skylab.core.contract.service.SkylabRecommendService"/>
    <dubbo:service registry="app" provider="zhixueProvider" ref="SkylabDiagnoseServiceProvider" group="${dubbo.group:production}"
                   interface="com.iflytek.skylab.core.contract.service.SkylabDiagnoseService"/>
    <dubbo:service registry="app" provider="zhixueProvider" ref="SkylabBehaviorServiceProvider" group="${dubbo.group:production}"
                   interface="com.iflytek.skylab.core.contract.service.SkylabBehaviorService"/>
    <dubbo:service registry="app" provider="zhixueProvider" ref="SkylabStandServiceProvider" group="${dubbo.group:production}"
                   interface="com.iflytek.skylab.core.contract.service.SkylabStandService"/>
    <dubbo:service registry="app" provider="zhixueProvider" ref="SkylabExtServiceProvider" group="${dubbo.group:production}"
                   interface="com.iflytek.skylab.core.contract.service.SkylabExtService"/>
    <dubbo:service registry="app" provider="zhixueProvider" ref="SkylabClearMasteryServiceProvider" group="${dubbo.group:production}"
                   interface="com.iflytek.skylab.core.contract.service.SkylabClearMasteryService"/>
    <dubbo:service registry="app" provider="zhixueProvider" ref="SkylabPrimaryMigrationServiceProvider" group="${dubbo.group:production}"
                   interface="com.iflytek.skylab.core.contract.service.SkylabPrimaryMigrationService"/>
</beans>
