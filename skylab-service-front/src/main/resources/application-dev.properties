IP=***********

dubbo.port=32180
dubbo.timeout=6000
#------------------------------------------------------------
#skyline.epas.zookeeper.address=**************:2181,*************:2181,**************:2181
skylab.epas.zookeeper.address=${IP}:2181
#------------------------------------------------------------
logging.level.ROOT=ERROR
logging.level.com.iflytek.skyline=DEBUG
logging.level.com.iflytek.skylab=DEBUG
#-----------------------------------------------------------------
skynet.api.swagger2.enabled=false
#-----------------------------------------------------------------
skyline.brave.allowSubjectCodes=02
# -------------------------------------------


skynet.logging.enabled=true
skynet.logging.debug.enabled=false
skynet.logging.debug.expression=$.scene.userId=="zhangsan"

skynet.logging.springmvc.enabled=true
skynet.logging.dubbo.enabled=true

skynet.logging.debug.enable.root=false
skynet.logging.debug.enable.com.iflytek.skylab.service=true
skynet.logging.debug.enable.com.iflytek.skylab.core=true
skynet.logging.debug.enable.com.iflytek.hy.rec=true

#EPAS
skylab.epas.appKey=skylab-front
skylab.epas.appSecret=ab0d0dae218f74a9
skylab.address.server.url=http://pre.epas.changyan.com/address

#EPAS TEST
#skylab.epas.appKey=skylab-front-test
#skylab.epas.appSecret=96c2ed8a07ebdaa1
#skylab.address.server.url=http://pre.epas.changyan.com/address

skylab.front.failover.enable=true
skylab.front.failover.failedLimit=50