POST http://172.31.98.68:32182/skylab/api/v1/feature/query
Content-Type: application/json
SKYLINE-SPAN-ID: test

{
    "traceId": "{{$uuid}}",
    "scene": {
        "subjectCode": "02",
        "classId": "string",
        "deviceId": "string",
        "test": true,
        "userId": "string",
        "userLevel": "string"
    },
    "payload": {
        "items": [
            {
                "featureName": "catalog_learn_path",
                "params": [
                    {
                        "_INDEX_":1,
                        "catalog_code": "01_0702010101-1372_0702010101-1372-51031_0702010101-1372-51035",
                        "biz_code": "zsy_xxj",
                        "study_code": "sync_learn"
                    }
                ],
                "featureVersion": 1,
                "graphVersion": "v2022-01",
                "tags": {
                    "scene": "xxj"
                }
            } , {
                "featureName": "anchorpoint_difficulty",
                "params": [
                    {
                        "_INDEX_":2,
                        "anchorpoint_code": "0de31d53-f9a8-4c46-9be7-a7c3d9378e12"
                    }
                ],
                "featureVersion": 1,
                "graphVersion": "v2022-01",
                "tags": {
                    "scene": "xxj"
                }
            },{
                "featureName": "anchorpoint_depth",
                "params": [
                    {
                        "_INDEX_":3,
                        "anchorpoint_code": "0de31d53-f9a8-4c46-9be7-a7c3d9378e12"
                    }
                ],
                "featureVersion": 1,
                "graphVersion": "v2022-01",
                "tags": {
                    "scene": "xxj"
                }
            }
        ]
    }
}