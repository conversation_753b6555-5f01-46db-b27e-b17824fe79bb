### user_level (simpleMode=true)
#POST http://172.31.98.68:32182/skylab/api/v1/feature/query
POST http://172.31.98.11:32182/skylab/api/v1/feature/query
Content-Type: application/json
SKYLINE-SPAN-ID: test

{
  "traceId": "{{$uuid}}",
  "scene": {
    "bizCode": "ZSY_XXJ",
    "areaCode": "000000",
    "phaseCode": "04",
    "studyCode": "SYNC_LEARN",
    "bizAction": "NONE",
    "subjectCode": "02",
    "test": false,
    "userId": "yclv_synLearn_NodeDiagnose_User01",
    "catalogCode": "01_07020101-001_02_001"
  },
  "payload": {
    "simpleMode": true,
    "items": [{
      "featureName": "user_level",
      "params": []
    }]
  }
}

### user_level (simpleMode=false)
POST http://172.31.98.68:32182/skylab/api/v1/feature/query
Content-Type: application/json
SKYLINE-SPAN-ID: test

{
  "traceId": "{{$uuid}}",
  "scene": {
    "bizCode": "ZSY_XXJ",
    "areaCode": "000000",
    "phaseCode": "04",
    "studyCode": "SYNC_LEARN",
    "bizAction": "NONE",
    "subjectCode": "02",
    "test": false,
    "userId": "yclv_synLearn_NodeDiagnose_User01"
  },
  "payload": {
    "simpleMode": false,
    "items": [{
      "featureName": "user_level",
      "params": [{
        "user_id": "yclv_synLearn_NodeDiagnose_User01",
        "biz_code": "ZSY_XXJ",
        "subject_code": "02",
        "phase_code": "04"
      }]
    }]
  }
}


### anchor_similaryuser_mastery (simpleMode=true)
POST http://172.31.98.68:32182/skylab/api/v1/feature/query
Content-Type: application/json
SKYLINE-SPAN-ID: test

{
  "traceId": "{{$uuid}}",
  "scene": {
    "bizCode": "ZSY_XXJ",
    "areaCode": "000000",
    "phaseCode": "04",
    "studyCode": "SYNC_LEARN",
    "bizAction": "NONE",
    "subjectCode": "02",
    "test": false,
    "userId": "grayAfterEvalUser03",
    "catalogCode": "23_0802020923-351_0802020923-351-11392_0802020923-351-11398"
  },
  "payload": {
    "simpleMode": true,
    "items": [{
      "featureName": "anchor_similaryuser_mastery",
      "params": [
        {"anchorpoint_code": "a3722c21-e85f-42c9-9c8e-5d8b4c7ebda1"},
        {"anchorpoint_code": "3572f232-84d9-4ff5-89f8-a04098470f8e"}
      ]
    }]
  }
}


### anchor_similaryuser_mastery (simpleMode=false)
POST http://172.31.98.68:32182/skylab/api/v1/feature/query
Content-Type: application/json
SKYLINE-SPAN-ID: test

{
  "traceId": "{{$uuid}}",
  "scene": {
    "bizCode": "ZSY_XXJ",
    "areaCode": "000000",
    "phaseCode": "04",
    "studyCode": "SYNC_LEARN",
    "bizAction": "NONE",
    "subjectCode": "02",
    "test": false,
    "userId": "grayAfterEvalUser03"
  },
  "payload": {
    "simpleMode": false,
    "items": [{
      "featureName": "anchor_similaryuser_mastery",
      "params": [{
        "user_id": "grayAfterEvalUser03",
        "biz_code": "ZSY_XXJ",
        "study_code": "SYNC_LEARN",
        "subject_code": "02",
        "phase_code": "04",
        "catalog_code": "23_0802020923-351_0802020923-351-11392_0802020923-351-11398",
        "anchorpoint_code": "a3722c21-e85f-42c9-9c8e-5d8b4c7ebda1"
      },{
        "user_id": "grayAfterEvalUser03",
        "biz_code": "ZSY_XXJ",
        "study_code": "SYNC_LEARN",
        "subject_code": "02",
        "phase_code": "04",
        "catalog_code": "23_0802020923-351_0802020923-351-11392_0802020923-351-11398",
        "anchorpoint_code": "3572f232-84d9-4ff5-89f8-a04098470f8e"
      }]
    }]
  }
}