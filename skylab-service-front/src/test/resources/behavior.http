#学习行为收集
POST http://localhost:32182/skylab/api/v1/behavior/reportAnswerRecord?debbo=true
#POST http://************:32182/skylab/api/v1/behavior/reportAnswerRecord?debbo=true
#POST http://*************:32182/skylab/api/v1/behavior/reportAnswerRecord?debbo=true
Content-Type: application/json
SKYLINE-SPAN-ID: {{$timestamp}}

{
  "traceId": "{{$uuid}}",
  "scene": {
    "areaCode": "010100",
    "graphVersion": "000",
    "gradeCode": "07",
    "studyCode": "SYNC_LEARN",
    "bizCode": "ZSY_XXJ",
    "bizAction": "SYNC_EVAL",
    "subjectCode": "02",
    "phaseCode": "04",
    "userId": "zhangsan",
    "bookVersionCode": "01",
    "bookVolumeCode": "07020101-001",
    "functionCode": "STUDY_LOG",
    "test": false
  },
  "payload": {
    "items": [
      {
        "refTraceId": 1234567890,
        "nodeId": "1111111111",
        "nodeType": "ANCHOR_POINT",
        "resNodeId": "222222222",
        "resNodeType": "TOPIC",
        "roundId": "mission-id",
        "roundIndex": "1",
        "timeCost": 10,
        "score":3,
        "standardScore":3,
        "feedbackTime": "2022-03-31T04:00:18.121Z"
      }
    ]
  }
}