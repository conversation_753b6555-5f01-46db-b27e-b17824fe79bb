### recommend
#POST http://localhost:32181/skylab/api/v1/dialing-test/recommend
POST http://*************:32182/skylab/api/v1/dialing-test/recommend
Content-Type: application/json
Timeout: 50000
sceneType: AiDiagSceneInfo
funcParam: RecEval4InOutParam
funcResult: RecEval4InOutResult

{
  "traceId": "{{$random.uuid}}",
  "scene": {
        "studyCode": "SYNC_LEARN",
        "bizAction": "SYNC_EVAL",
        "subjectCode": "02",
        "phaseCode": "04",
        "catalogCode": "01_09020101-002_001_001",
        "pressCode": "01",
        "test": false,
        "userId": "zhangsan",
        "bizCode": "ZSY_XXJ",
        "gradeCode": "07",
        "areaCode": "000000",
        "graphVersion": "v16",
        "bookCode": "01_09020101-002",
        "nodeCatalogMap": {
        "key": "val"
        }
  },
  "payload": {
        "catalogIds": [
            "01_09020101-002_001_001"
        ],
        "roundId": "1",
        "topicOrderNumber": 1,
        "funcCode": "REC_EVAL4IN",
        "recEvalEnum": "REC_EVAL4IN"
  }
}

### diagnose
POST http://*************:32182/skylab/api/v1/dialing-test/diagnose
Content-Type: application/json
Timeout: 50000
sceneType: SceneInfo
funcParam: MasterFetch4CatalogParam

{
  "traceId": "{{$random.uuid}}",
  "scene": {
        "areaCode": "010100",
        "graphVersion": "v16",
        "studyCode": "SYNC_LEARN",
        "bizAction": "NONE",
        "bizCode": "ZSY_XXJ",
        "subjectCode": "02",
        "phaseCode": "04",
        "userId": "6eefd5a8-bd02-4899-bc5a-598795aec7b8",
        "bookVersionCode": "01",
        "bookVolumeCode": "01_07020101-001",
        "test": false
  },
  "payload": {
         "catalogIds": [
            "01_07020101-001_02_001"
          ]
  }
}

### reportAnswerRecord
POST http://172.29.67.254:32182/skylab/api/v1/dialing-test/clearMastery
Content-Type: application/json
Timeout: 50000
sceneType: SceneInfo

{
  "traceId": "{{$random.uuid}}",
  "scene": {
        "studyCode": "SYNC_LEARN",
        "bizAction": "SYNC_EVAL",
        "subjectCode": "02",
        "phaseCode": "04",
        "catalogCode": "01_09020101-002_001_001",
        "pressCode": "01",
        "test": false,
        "userId": "yfding10",
        "bizCode": "ZSY_XXJ",
        "gradeCode": "07",
        "areaCode": "000000",
        "graphVersion": "v22",
        "bookCode": "01_09020101-002"
  },
  "payload": {
        "items": [
            {
                "resNodeType": "TOPIC",
                "roundIndex": "1",
                "score": 0,
                "standardScore": 1,
                "feedbackTime": "2022-05-04T07:09:28.193Z",
                "nodeType": "CHECK_POINT",
                "refTraceId": "8565d8c0-88a6-4111-a77a-9776b7c38984",
                "timeCost": 65,
                "nodeId": "diagnose-adjust-checkpoint-001",
                "resNodeId": "diagnose-adjust-exam-topic10",
                "roundId": "1"
            }
        ]
  }
}

### featureFetch
POST http://*************:32182/skylab/api/v1/dialing-test/featureFetch
Content-Type: application/json
Timeout: 50000
sceneType: SceneInfo

{
  "traceId": "{{$random.uuid}}",
  "scene": {
    "bizCode": "ZSY_XXJ",
    "areaCode": "000000",
    "phaseCode": "04",
    "studyCode": "SYNC_LEARN",
    "bizAction": "NONE",
    "subjectCode": "02",
    "test": false,
    "userId": "yclv_synLearn_NodeDiagnose_User01",
    "catalogCode": "01_07020101-001_02_001"
  },
  "payload": {
    "simpleMode": true,
    "items": [{
      "featureName": "user_level",
      "params": []
    }]
  }
}