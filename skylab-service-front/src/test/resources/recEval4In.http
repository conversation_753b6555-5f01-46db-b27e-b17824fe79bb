#测评推题(进门测)测试
#POST http://localhost:32182/skylab/api/v1/recEval
#POST http://*************:32182/skylab/api/v1/recEval
POST http://*************:32182/skylab/api/v1/recEval
Content-Type: application/json
SKYLINE-SPAN-ID: {{$timestamp}}


{
    "payload": {
        "catalogIds": [
            "01_09020101-002_001_001"
        ],
        "roundId": "1",
        "topicOrderNumber": 1,
        "funcCode": "REC_EVAL4IN",
        "recEvalEnum": "REC_EVAL4IN"
    },
    "traceId": "{{$uuid}}",
    "scene": {
        "studyCode": "EXT2ND",
        "bizAction": "SYNC_EVAL",
        "subjectCode": "02",
        "phaseCode": "04",
        "catalogCode": "01_09020101-002_001_001",
        "pressCode": "01",
        "test": false,
        "userId": "zhangsan",
        "bizCode": "ZSY_XXJ",
        "gradeCode": "07",
        "areaCode": "000000",
        "graphVersion": "v6",
        "bookCode": "09020101-002"
    }
}