package com.iflytek.skylab.service.front;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.concurrent.TimeUnit;

import static com.iflytek.skylab.service.front.TestUtil.AREA_CODE;
import static com.iflytek.skylab.service.front.TestUtil.GRADE_CODE;

/**
 * <AUTHOR>
 * @date 2023/10/17 17:50
 */
@Slf4j
public class SentinelDegradeTest {


    private static final String ADDRESS = "localhost:32181";


    public static void main(String[] args) throws InterruptedException {
        // recTopic("04", "02");

        new Thread(() -> {
            for (;;) {
                recTopic("03", "02", 1000);
            }
        }, "t-0302").start();

        new Thread(() -> {
            for (;;) {
                recTopic("04", "02", 1000);
            }
        },"t-0402").start();

        TimeUnit.MINUTES.sleep(5);
    }


    @SneakyThrows
    private static void recTopic(String phase, String subject, long sleep) {
        RecTopicParam payload = new RecTopicParam()
                .setNodeId("nodeId" + RandomStringUtils.randomAlphanumeric(8))
                .setRoundId("roundId" + RandomStringUtils.randomAlphanumeric(8))
                .setTopicOrderNumber(1);

        SkylabRequest<RecTopicParam> request = new SkylabRequest<>();
        request.setTraceId(phase + "#" + subject + "#" + RandomStringUtils.randomAlphanumeric(8));
        request.setScene(sceneInfo(phase, subject));
        request.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/recTopic";
        post(url, JSON.toJSONString(request));

        Thread.sleep(sleep);
    }



    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .callTimeout(1, TimeUnit.MINUTES)
            .connectTimeout(1, TimeUnit.MINUTES)
            .readTimeout(1, TimeUnit.MINUTES)
            .writeTimeout(1, TimeUnit.MINUTES)
            .build();

    @SneakyThrows
    private static void post(String url, String json) {
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), json);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        Response response = httpClient.newCall(request).execute();
        String responseJson = response.body().string();

        JSONObject jsonObject = JSONObject.parseObject(responseJson);
        String traceId = jsonObject.getString("traceId");
        String code = jsonObject.getString("code");
        String message = jsonObject.getString("message");

        System.out.printf("##### traceId: %s, code: %s, message: %s \n", traceId, code, message);
    }

    private static SceneInfo sceneInfo(String phase, String subject) {
        SceneInfo sceneInfo = new SceneInfo()
                .setAreaCode(AREA_CODE)
                .setGraphVersion("20231011_001")
                .setStudyCode(StudyCodeEnum.SYNC_LEARN)
                .setBizCode(BizCodeEnum.ZSY_XXJ)
                .setSubjectCode(subject)
                .setPhaseCode(phase)
                .setUserId(phase + "_" + subject)
                .setPressCode("01")
                // .setBookCode("01_07020201-002")
                .setGradeCode(GRADE_CODE)
                .setBizAction(BizActionEnum.SYNC_EVAL);

        if ("03".equals(phase) && "02".equals(subject)) {
            sceneInfo.setBookCode("01_01020101-001");
        }
        if ("04".equals(phase) && "02".equals(subject)) {
            sceneInfo.setBookCode("01_07020201-002");
        }
        return sceneInfo;
    }


}
