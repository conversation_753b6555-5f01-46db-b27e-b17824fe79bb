package com.iflytek.skylab.service.front;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;

import java.time.Instant;
import java.util.Date;
import java.util.Set;

import static com.iflytek.skylab.service.front.TestUtil.*;

/**
 * 点推题 然后 答题
 *
 * <AUTHOR>
 * @date 2022/4/5 16:17
 */
@Slf4j
public class RecTopic_ReportAnswerRecord {

    private static final Boolean USE_THINKING_EXPANSION = true;

    // private static final String NODE_ID = "4d02c6fe-0f11-416a-9c5f-cc7d063e4a62";
    // private static final SceneInfo sceneInfo = new SceneInfo()
    //         .setAreaCode(AREA_CODE)
    //         .setGraphVersion("v2")
    //         .setStudyCode(StudyCodeEnum.UNIT_REVIEW)
    //         .setBizCode(BizCodeEnum.ZSY_XXJ)
    //         .setSubjectCode("02")
    //         .setPhaseCode("04")
    //         .setUserId("桃花依旧笑春风")
    //         .setBookVersionCode("19")
    //         .setBookVolumeCode("19_09020107")
    //         .setGradeCode(GRADE_CODE)
    //         .setBizAction(BizActionEnum.EXAM_UNIT_REC);

    // private static final String NODE_ID = "55d7ed8f-390f-4f7c-a1ae-d484e8c264ea";
    // private static final SceneInfo sceneInfo = new SceneInfo()
    //         .setAreaCode(AREA_CODE)
    //         .setGraphVersion("v2")
    //         .setStudyCode(StudyCodeEnum.MID_EXAM)
    //         .setBizCode(BizCodeEnum.ZSY_XXJ)
    //         .setSubjectCode("02")
    //         .setPhaseCode("04")
    //         .setUserId("桃花依旧笑春风")
    //         .setBookVersionCode("19")
    //         .setBookVolumeCode("19_09020107")
    //         .setGradeCode(GRADE_CODE)
    //         .setBizAction(BizActionEnum.EXAM_STAGE_BREAK_THROUGH);

    private static final String NODE_ID = "55d7ed8f-390f-4f7c-a1ae-d484e8c264ea";
    private static final SceneInfo sceneInfo = new SceneInfo()
            .setAreaCode(AREA_CODE)
            .setGraphVersion("v2")
            .setStudyCode(StudyCodeEnum.FINAL_EXAM)
            .setBizCode(BizCodeEnum.ZSY_XXJ)
            .setSubjectCode("02")
            .setPhaseCode("04")
            .setUserId("桃花依旧笑春风")
            .setPressCode("19")
            .setBookCode("19_09020107")
            .setGradeCode(GRADE_CODE)
            .setBizAction(BizActionEnum.EXAM_STAGE_BREAK_THROUGH);

    private static final boolean[] allTrue = {true, true, true, true, true, true, true, true, true, true, true, true};
    private static final boolean[] allFalse = {false, false, false, false, false, false, false, false, false, false, false, false};
    private static final boolean[] random = {true, false, true, true, false, true, false, true, true, false, true, true, false};

    private static final boolean[] order = allFalse;

    /**
     * 点推题 然后 答题
     *
     * @param args
     */
    public static void main(String[] args) {
        recTopicAndReportAnswerRecord(NODE_ID, 12);
    }

    @SneakyThrows
    public static void recTopicAndReportAnswerRecord(String nodeId, int roundMaxIndex) {
        String roundId = IdUtil.fastSimpleUUID();
        Set<String> roundTopics = Sets.newHashSet();

        for (int i = 1; i <= roundMaxIndex; i++) {
            String traceId = IdUtil.fastUUID();
            log.info("\n");
            log.info(i + "========================================================================================================================================================================================================================================================================");

            // 模拟推题
            RecTopicResult recTopicResult = recTopic(traceId, sceneInfo, nodeId, roundId, i);
            if (StrUtil.isBlank(recTopicResult.getTopicId())) {
                log.info("topicId is empty");
                return;
            }

            // 如果引擎返回终止标识，不再推题
            if (recTopicResult.isTerminationFlag()) {
                log.info("terminalFlag:{}", recTopicResult.isTerminationFlag());
                break;
            }

            if (roundTopics.contains(recTopicResult.getTopicId())) {
                log.error("本轮有重复的推荐题！{}", recTopicResult.getTopicId());
            }
            roundTopics.add(recTopicResult.getTopicId());


            // 模拟答题
            Thread.sleep(RandomUtil.randomLong(100, 1000));
            // 答题记录
            StudyLogRecord record = new StudyLogRecord();
            record.setRefTraceId(traceId);
            record.setNodeId(recTopicResult.getNodeId());
            record.setNodeType(NodeTypeEnum.ANCHOR_POINT);
            record.setResNodeId(recTopicResult.getTopicId());
            record.setResNodeType(ResourceTypeEnum.TOPIC);
            record.setRoundId(roundId);
            record.setRoundIndex(String.valueOf(i));
            record.setTimeCost(RandomUtil.randomInt(100));
            if (order[i]) {
                record.setStandardScore(RandomUtil.randomDouble(10, 100));
                record.setScore(record.getStandardScore());
            } else {
                record.setStandardScore(RandomUtil.randomDouble(10, 100));
                record.setScore(RandomUtil.randomDouble(record.getStandardScore()));
            }
            record.setFeedbackExt(new JSONObject(MapUtil.of("extKey", "extVal-" + record.getScore())));
            record.setFeedbackTime(new Date());
            log.info("------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------");
            reportAnswerRecord(IdUtil.fastUUID(), sceneInfo, Lists.newArrayList(record));
        }

        log.info("本轮推荐题量：{}", roundTopics.size());

    }
}
