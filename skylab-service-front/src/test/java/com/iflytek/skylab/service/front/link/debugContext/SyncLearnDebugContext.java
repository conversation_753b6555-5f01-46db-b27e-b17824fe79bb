package com.iflytek.skylab.service.front.link.debugContext;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.MasterInfo;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.service.front.link.debugContext.core.AbstractDebugContext;
import com.iflytek.skylab.service.front.link.util.DebugUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 同步学 全流程测试
 */
@Slf4j
public class SyncLearnDebugContext extends AbstractDebugContext {
    @Override
    public void go() {
        String traceId;
        String roundId = IdUtil.fastSimpleUUID();
        Set<String> roundTopics = Sets.newHashSet();
        // 入门测
        for (int i = 0; i < 20; i++) {
            traceId = IdUtil.fastUUID();
            int roundIndex = i + 1;


            RecEval4InOutResult recEval4InOutResult = recEval4In(traceId, roundId, roundIndex);
            if (recEval4InOutResult.isTerminationFlag()) {
                log.info("入门测推题终止, roundIndex:{}", roundIndex);
                break;
            }

            // 答题
            List<StudyLogRecord> records = Lists.newArrayList();
            List<EvaluationItem> evaluationItems = recEval4InOutResult.getEvaluationItems();
            for (EvaluationItem item : evaluationItems) {
                Assert.isFalse(roundTopics.contains(item.getResNodeId()), "点推题题目有重复");
                roundTopics.add(item.getResNodeId());

                StudyLogRecord record = DebugUtil.recEval4InStudyLogRecord(traceId, roundId, roundIndex, item, RandomUtil.randomBoolean());
                records.add(record);
            }
            StudyLogResult studyLogResult = reportAnswerRecord(records);
            log.info("入库的答题记录ID：{}", CollectionUtil.join(studyLogResult.getIdList(), ","));
        }

        // 查画像
        MasterFetchResult masterFetchResult = masterFetch4Catalog();
        Set<Double> masteryScores = masterFetchResult.getMasterInfos().stream().map(MasterInfo::getMasteryScore).collect(Collectors.toSet());
        log.info("masterFetch4Catalog masteryScores: {}", CollectionUtil.join(masteryScores, ","));

        // 点搜索
        RecNodeResult recNodeResult = recNode();
        log.info("recNodeResult.getNodeIds:{}", CollectionUtil.join(recNodeResult.getNodeIds(), ","));
        String recNodeId = recNodeResult.getNodeIds().get(0);

        // 点推题
        roundId = IdUtil.fastSimpleUUID();
        roundTopics = Sets.newHashSet();
        for (int i = 0; i < 20; i++) {
            traceId = IdUtil.fastUUID();
            int roundIndex = i + 1;

            RecTopicResult recTopicResult = recTopic(traceId, recNodeId, roundId, roundIndex);
            if (recTopicResult.isTerminationFlag()) {
                log.info("点推题终止, roundIndex:{}", roundIndex);
                break;
            }

            Assert.isFalse(roundTopics.contains(recTopicResult.getTopicId()), "点推题题目有重复");
            roundTopics.add(recTopicResult.getTopicId());

            // 答题
            StudyLogRecord record = DebugUtil.recTopicStudyLogRecord(traceId, roundId, roundIndex, recTopicResult, true);
            reportAnswerRecord(Lists.newArrayList(record));
        }

        // 查画像
        masterFetchResult = masterFetch4Catalog();
        masteryScores = masterFetchResult.getMasterInfos().stream().map(MasterInfo::getMasteryScore).collect(Collectors.toSet());
        log.info("masterFetch4Catalog masteryScores: {}", CollectionUtil.join(masteryScores, ","));

        log.info("Run Done!");
    }

    @Override
    protected SceneInfo sceneInfo() {
        return new SceneInfo()
                .setUserId("38cda48e-d143-4e7c-abce-1a3bce22d455")
                .setStudyCode(StudyCodeEnum.SYNC_LEARN)
                .setBizAction(BizActionEnum.SYNC_EVAL)
                .setBizCode(BizCodeEnum.ZSY_XXJ)
                .setSubjectCode("02")
                .setPhaseCode("04")
                .setGradeCode("07")
                .setAreaCode("010100")
                // .setFunctionCode()
                .setGraphVersion("v2")
                .setPressCode("01")
                .setBookCode("01_07020101-001");
    }

    @Override
    protected List<String> nodeIds() {
        return Lists.newArrayList("3ac8ff83-40ed-4f73-929d-b35049412e8c");
    }

    @Override
    protected List<String> catalogIds() {
        return Lists.newArrayList("01_07020101-001_02_002");
    }
}
