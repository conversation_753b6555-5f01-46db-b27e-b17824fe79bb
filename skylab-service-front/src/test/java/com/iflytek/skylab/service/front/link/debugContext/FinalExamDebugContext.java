package com.iflytek.skylab.service.front.link.debugContext;

import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.domain.SceneInfo;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/19 16:25
 */
public class FinalExamDebugContext extends MidExamDebugContext {

    @Override
    public SceneInfo sceneInfo() {
        return new SceneInfo()
                .setUserId("却是旧时相识")
                .setStudyCode(StudyCodeEnum.FINAL_EXAM)
                .setBizAction(BizActionEnum.NONE)
                .setBizCode(BizCodeEnum.ZSY_XXJ)
                .setSubjectCode("02")
                .setPhaseCode("04")
                .setGradeCode("07")
                .setAreaCode("010100")
                // .setFunctionCode()
                .setGraphVersion("v2")
                .setPressCode("01")
                .setBookCode("01_07020101-001");
    }

    @Override
    public List<String> nodeIds() {
        return Lists.newArrayList("7d373d7a-e13f-4741-a83f-c639dd74a61f");
    }

    @Override
    public List<String> catalogIds() {
        return Lists.newArrayList("72_09020154-002_001");
    }
}
