package com.iflytek.skylab.service.front.link.debugContext;

import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.domain.SceneInfo;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/20 16:33
 */
public class SeniorExamTwoDebugContext extends SeniorExamOneDebugContext {

    @Override
    protected SceneInfo sceneInfo() {
        return super.sceneInfo()
                .setStudyCode(StudyCodeEnum.SENIOR_EXAM_2);
    }

    @Override
    protected List<String> nodeIds() {
        return Lists.newArrayList("3984b253-b8dc-4fe9-9bea-6f978217af27");
    }

    @Override
    protected List<String> catalogIds() {
        return Lists.newArrayList("083242f1-251d-4fae-8e15-3642126e83f4");
    }
}
