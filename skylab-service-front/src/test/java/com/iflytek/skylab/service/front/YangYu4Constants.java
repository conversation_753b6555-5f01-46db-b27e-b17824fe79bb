package com.iflytek.skylab.service.front;

import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.RecEvalEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;

/**
 * <AUTHOR>
 * @date 2022/4/11 11:47
 */
public interface YangYu4Constants {

    String userId = "68eeb310-2475-4724-97db-ce278cbabe45";
    BizCodeEnum bizCode = BizCodeEnum.ZSY_XXJ;
    String graphVersion = "v2";
    String bookVersionCode = "01";
    String bookVolumeCode = "01_07020101-001";
    String subjectCode = "05";
    String phaseCode = "04";
    String areaCode = "010100";
    String gradeCode = "07";
    String catalogId = "72_09020254-002_004";

    interface 入门测_单元复习 {
        StudyCodeEnum studyCode = StudyCodeEnum.UNIT_REVIEW;
    }

    interface 入门测_期中备考 {
        StudyCodeEnum studyCode = StudyCodeEnum.MID_EXAM;

    }

    interface 入门测_期末备考 {
        StudyCodeEnum studyCode = StudyCodeEnum.FINAL_EXAM;

    }

    interface 出门测_单元复习 {
        StudyCodeEnum studyCode = StudyCodeEnum.UNIT_REVIEW;

    }

    interface 出门测_期中备考 {
        StudyCodeEnum studyCode = StudyCodeEnum.MID_EXAM;

    }

    interface 出门测_期末备考 {
        StudyCodeEnum studyCode = StudyCodeEnum.FINAL_EXAM;

    }


}
