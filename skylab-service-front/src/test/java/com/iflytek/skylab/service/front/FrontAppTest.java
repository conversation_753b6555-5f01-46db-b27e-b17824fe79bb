package com.iflytek.skylab.service.front;

import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.MasterFetch4CatalogParam;
import com.iflytek.skylab.core.data.RecEval4InOutParam;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.UUID;

@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
// @RunWith(SpringRunner.class)
public class FrontAppTest {


    @Test
    public void test() {
        MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();
        masterFetch4CatalogParam.setCatalogIds(Arrays.asList(UUID.randomUUID().toString(),
                UUID.randomUUID().toString(), UUID.randomUUID().toString()));

        System.out.println(masterFetch4CatalogParam);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
        sceneInfo.setSubjectCode("02");
        sceneInfo.setPhaseCode("04");
        sceneInfo.setBookCode("01");
        sceneInfo.setPressCode("11111");
        sceneInfo.setAreaCode("010100");
        sceneInfo.setUserId(UUID.randomUUID().toString());
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setGraphVersion("");
        log.debug("SceneInfo= {}", sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setCatalogIds(Arrays.asList(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        recEval4InOutParam.setRoundId(UUID.randomUUID().toString());
//        recEval4InOutParam.setTopicPackageType(TopicPackageType.CONCEPT_CARDING);
        recEval4InOutParam.setTopicOrderNumber(1);

        SkylabRequest<RecEval4InOutParam> skylabRequest = new SkylabRequest<>();

        skylabRequest.setTraceId(UUID.randomUUID().toString());
        skylabRequest.setPayload(recEval4InOutParam);
        skylabRequest.setScene(sceneInfo);

        log.info("SkylabRequest= {}", skylabRequest);


    }

}
