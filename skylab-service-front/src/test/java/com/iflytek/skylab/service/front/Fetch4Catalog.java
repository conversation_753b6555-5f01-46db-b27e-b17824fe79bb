package com.iflytek.skylab.service.front;

import cn.hutool.core.util.IdUtil;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.domain.SceneInfo;
import org.assertj.core.util.Lists;

import static com.iflytek.skylab.service.front.TestUtil.*;

/**
 * <AUTHOR>
 * @date 2022/4/7 11:50
 */
public class Fetch4Catalog {

    private static final String CATALOG_ID = "01_07020101-001_02_002";

    private static final SceneInfo sceneInfo = new SceneInfo()
            .setAreaCode(AREA_CODE)
            .setGraphVersion(GRAPH_VERSION)
            .setStudyCode(STUDY_CODE)
            .setBizCode(BIZ_CODE)
            .setSubjectCode(SUBJECT_CODE)
            .setPhaseCode(PHASE_CODE)
            .setUserId(USER_ID)
            .setPressCode(BOOK_VERSION_CODE)
            .setBookCode(BOOK_VOLUME_CODE)
            .setGradeCode(GRADE_CODE);
            // .setBizAction(BizActionEnum.SYNC_EVAL);// todo ?

    public static void main(String[] args) {
        MasterFetchResult result = fetch4Catalog(IdUtil.fastUUID(), sceneInfo, Lists.newArrayList(CATALOG_ID));
        System.out.println(result);
    }


}
