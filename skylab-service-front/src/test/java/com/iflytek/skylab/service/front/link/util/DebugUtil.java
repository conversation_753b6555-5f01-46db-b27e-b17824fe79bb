package com.iflytek.skylab.service.front.link.util;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.ResourceTypeEnum;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.RecTopicResult;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.time.Instant;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/4/19 12:49
 */
@Slf4j
public class DebugUtil {

    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .callTimeout(1, TimeUnit.MINUTES)
            .connectTimeout(1, TimeUnit.MINUTES)
            .readTimeout(1, TimeUnit.MINUTES)
            .writeTimeout(1, TimeUnit.MINUTES)
            .build();

    @SneakyThrows
    public static <T extends FuncResult> T post(String url, String json, Class<?> clazz) {
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), json);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        String requestMapping = url.substring(url.lastIndexOf("/"));
        log.info("{}, http request= {}", requestMapping, json);
        Response response = httpClient.newCall(request).execute();

        String responseJson = response.body().string();
        log.info("{}, http response= {}", requestMapping, responseJson);

        JSONObject jsonObject = JSONObject.parseObject(responseJson);
        return (T) jsonObject.getObject("payload", clazz);
    }


    public static StudyLogRecord recTopicStudyLogRecord(String refTraceId, String roundId, int roundIndex, RecTopicResult rtr, boolean rightAnswer) {
        StudyLogRecord record = new StudyLogRecord();
        record.setRefTraceId(refTraceId);
        record.setNodeId(rtr.getNodeId());
        record.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        record.setResNodeId(rtr.getTopicId());
        record.setResNodeType(ResourceTypeEnum.TOPIC);
        record.setRoundId(roundId);
        record.setRoundIndex(String.valueOf(roundIndex));
        record.setTimeCost(RandomUtil.randomInt(100));
        if (rightAnswer) {
            record.setStandardScore(RandomUtil.randomDouble(10, 100));
            record.setScore(record.getStandardScore());
        } else {
            record.setStandardScore(RandomUtil.randomDouble(10, 100));
            record.setScore(RandomUtil.randomDouble(record.getStandardScore()));
        }
        record.setFeedbackExt(new JSONObject(MapUtil.of("extKey", "extVal-" + record.getScore())));
        record.setFeedbackTime(new Date());
        return record;
    }

    /**
     * 入门测推荐试题对应的答题记录
     *
     * @param item
     * @return
     */
    public static StudyLogRecord recEval4InStudyLogRecord(String refTraceId, String roundId, int roundIndex, EvaluationItem item, boolean rightAnswer) {
        StudyLogRecord record = new StudyLogRecord();
        record.setRefTraceId(refTraceId);
        record.setNodeId(item.getNodeId());
        record.setNodeType(item.getNodeType());
        record.setResNodeId(item.getResNodeId());
        record.setResNodeType(item.getResNodeType());
        record.setRoundId(roundId);
        record.setRoundIndex(String.valueOf(roundIndex));
        record.setTimeCost(RandomUtil.randomInt(100));
        if (rightAnswer) {
            record.setStandardScore(RandomUtil.randomDouble(10, 100));
            record.setScore(record.getStandardScore());
        } else {
            record.setStandardScore(RandomUtil.randomDouble(10, 100));
            record.setScore(RandomUtil.randomDouble(record.getStandardScore()));
        }
        record.setFeedbackExt(new JSONObject(MapUtil.of("extKey", "extVal-" + record.getScore())));
        record.setFeedbackTime(new Date());
        return record;
    }

}
