package com.iflytek.skylab.service.front.link.debugContext;

import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.front.link.debugContext.core.AbstractDebugContext;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/20 16:33
 */
public class SeniorExamThreeDebugContext extends SeniorExamOneDebugContext {
    @Override
    protected SceneInfo sceneInfo() {
        return null;
    }

    @Override
    protected List<String> nodeIds() {
        return null;
    }

    @Override
    protected List<String> catalogIds() {
        return null;
    }
}
