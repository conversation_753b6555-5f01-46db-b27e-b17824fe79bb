package com.iflytek.skylab.service.front.link.debugContext;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.MasterInfo;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.service.front.link.debugContext.core.AbstractDebugContext;
import com.iflytek.skylab.service.front.link.util.DebugUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/19 11:43
 */
@Slf4j
public class UnitReviewDebugContext extends AbstractDebugContext {

    @Override
    public void go() {
        // 入门测 - 推题
        String traceId = IdUtil.fastUUID();
        String roundId = IdUtil.fastUUID();
        int roundIndex = 1;
        RecEval4InOutResult recEval4InOutResult = recEval4In(traceId, roundId, roundIndex);
        Assert.isTrue(recEval4InOutResult.getEvaluationItems().size() > 1);
        log.info("√ 推荐试题数量>1");
        Assert.isTrue(recEval4InOutResult.isTerminationFlag());
        log.info("√ 直接返回终止=true");

        Set<String> topics = recEval4InOutResult.getEvaluationItems().stream().map(EvaluationItem::getResNodeId).collect(Collectors.toSet());
        log.info("第一次入门测返回的试题:{}", CollectionUtil.join(topics, ","));

        // 根据入门测推题记录，进行答题
        List<StudyLogRecord> records = Lists.newArrayList();
        for (int i = 0; i < recEval4InOutResult.getEvaluationItems().size(); i++) {
            EvaluationItem item = recEval4InOutResult.getEvaluationItems().get(0);
            StudyLogRecord record = DebugUtil.recEval4InStudyLogRecord(traceId, roundId, roundIndex, item, RandomUtil.randomBoolean());
            records.add(record);
        }
        StudyLogResult studyLogResult = reportAnswerRecord(records);
        log.info("入库的答题记录ID：{}", CollectionUtil.join(studyLogResult.getIdList(), ","));
        log.info("√ 答题记录入库");


        // 再次入门测
        traceId = IdUtil.fastUUID();
        roundId = IdUtil.fastUUID();
        roundIndex = 1;
        recEval4InOutResult = recEval4In(traceId, roundId, roundIndex);
        Assert.isTrue(recEval4InOutResult.getEvaluationItems().size() > 1);
        log.info("√ 推荐试题数量>1");
        Assert.isTrue(recEval4InOutResult.isTerminationFlag());
        log.info("√ 直接返回终止=true");

        Set<String> topics2 = recEval4InOutResult.getEvaluationItems().stream().map(EvaluationItem::getResNodeId).collect(Collectors.toSet());
        log.info("第二次入门测返回的试题:{}", CollectionUtil.join(topics2, ","));

        // 判断两次入门测结果
        Assert.isTrue(topics.equals(topics2), "第一次入门测 与 第二次入门测 推题结果不同");
        log.info("√ 返回试题同首次请求");

        // 目录获取用户画像
        MasterFetchResult masterFetchResult = masterFetch4Catalog();
        Set<Double> masteryScores = masterFetchResult.getMasterInfos().stream().map(MasterInfo::getMasteryScore).collect(Collectors.toSet());
        log.info("masterFetch4Catalog masteryScores: {}", CollectionUtil.join(masteryScores, ","));
        Set<Double> not0dot2 = masterFetchResult.getMasterInfos().stream().map(MasterInfo::getMasteryScore).filter(val -> val != null && val != 0.2).collect(Collectors.toSet());
        Assert.notEmpty(not0dot2);
        log.info("√ 目录获取用户画像，不全为0.2");


        // 点排序 - 点推题刷绿
        for (int k = 0; k < 20; k++) {
            log.info("点搜索、点推题循环刷绿 k={}", k);
            RecNodeResult recNodeResultN = recNode();
            log.info("recNodeResultN.getNodeIds:{}", CollectionUtil.join(recNodeResultN.getNodeIds(), ","));

            if (recNodeResultN.isPointRecommendEnd()) {
                masterFetchResult = masterFetch4Catalog();
                log.info("PointRecommendEnd MasterInfos:{}", CollectionUtil.join(masterFetchResult.getMasterInfos().stream().map(MasterInfo::getMasteryScore).collect(Collectors.toList()), ","));
                log.info("√ 点排序终止");
                break;
            }

            roundId = IdUtil.fastUUID();
            Set<String> recTopics = Sets.newHashSet();
            for (int i = 1; i < 20; i++) {
                traceId = IdUtil.fastUUID();
                RecTopicResult recTopicResult = recTopic(traceId, recNodeResultN.getNodeIds().get(0), roundId, roundIndex);
                if (recTopicResult.isTerminationFlag()) {
                    log.info("点推题结束，i={}", i);
                    break;
                }

                Assert.isFalse(recTopics.contains(recTopicResult.getTopicId()), "点推题题目有重复");
                recTopics.add(recTopicResult.getTopicId());

                StudyLogRecord record = DebugUtil.recTopicStudyLogRecord(traceId, roundId, roundIndex, recTopicResult, true);
                reportAnswerRecord(Lists.newArrayList(record));
            }
        }
        log.info("√ 推题不重复");

        // 出门测
        traceId = IdUtil.fastUUID();
        roundId = IdUtil.fastUUID();
        roundIndex = 1;
        RecEval4InOutResult recEval4InOutResult333 = recEval4Out(traceId, roundId, roundIndex);
        Assert.isTrue(recEval4InOutResult333.getEvaluationItems().size() > 1);
        log.info("√ 推荐试题数量>1");
        Assert.isTrue(recEval4InOutResult333.isTerminationFlag());
        log.info("√ 直接返回终止=true");

        Set<String> topics333 = recEval4InOutResult333.getEvaluationItems().stream().map(EvaluationItem::getResNodeId).collect(Collectors.toSet());
        log.info("出门测返回的试题:{}", CollectionUtil.join(topics333, ","));


        traceId = IdUtil.fastUUID();
        roundId = IdUtil.fastUUID();
        roundIndex = 1;
        RecEval4InOutResult recEval4InOutResult444 = recEval4Out(traceId, roundId, roundIndex);
        Assert.isTrue(recEval4InOutResult444.getEvaluationItems().size() > 1);
        log.info("√ 推荐试题数量>1");
        Assert.isTrue(recEval4InOutResult444.isTerminationFlag());
        log.info("√ 直接返回终止=true");

        Set<String> topics444 = recEval4InOutResult444.getEvaluationItems().stream().map(EvaluationItem::getResNodeId).collect(Collectors.toSet());
        log.info("再次出门测返回的试题:{}", CollectionUtil.join(topics444, ","));


        Assert.isTrue(topics333.equals(topics444));
        log.info("√ 重复请求，推荐提包相同");
    }


    @Override
    public SceneInfo sceneInfo() {
        return new SceneInfo()
                .setUserId("雁过也")
                .setStudyCode(StudyCodeEnum.UNIT_REVIEW)
                .setBizAction(BizActionEnum.NONE)
                .setBizCode(BizCodeEnum.ZSY_XXJ)
                .setSubjectCode("02")
                .setPhaseCode("04")
                .setGradeCode("07")
                .setAreaCode("010100")
                // .setFunctionCode()
                .setGraphVersion("v2")
                .setPressCode("01")
                .setBookCode("01_07020101-001");
    }

    @Override
    public List<String> nodeIds() {
        return Lists.newArrayList("82b53f77-061e-4555-821f-d31f06553c62");
    }

    @Override
    public List<String> catalogIds() {
        return Lists.newArrayList("72_07020154-001_04");
    }

}
