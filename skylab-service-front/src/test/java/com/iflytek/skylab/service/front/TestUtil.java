package com.iflytek.skylab.service.front;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.gson.reflect.TypeToken;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.RecEvalEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.lang.reflect.Type;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/4/5 16:19
 */
@Slf4j
public class TestUtil {
    public static final String ADDRESS_TEST = "*************:32182";
    public static final String ADDRESS_DEV = "************:32182";
    public static final String ADDRESS = ADDRESS_DEV;
    public static final String AREA_CODE = "010100";
    public static final String GRAPH_VERSION = "v2";
    public static final StudyCodeEnum STUDY_CODE = StudyCodeEnum.SYNC_LEARN;
    public static final BizCodeEnum BIZ_CODE = BizCodeEnum.ZSY_XXJ;
    public static final String SUBJECT_CODE = "02";// 数学
    public static final String PHASE_CODE = "04";// 初中
    public static final String USER_ID = "38cda48e-d143-4e7c-abce-1a3bce22d455";
    public static final String BOOK_VERSION_CODE = "01";
    public static final String BOOK_VOLUME_CODE = "01_07020101-001";
    public static final String GRADE_CODE = "07";

    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .callTimeout(1, TimeUnit.MINUTES)
            .connectTimeout(1, TimeUnit.MINUTES)
            .readTimeout(1, TimeUnit.MINUTES)
            .writeTimeout(1, TimeUnit.MINUTES)
            .build();

    @SneakyThrows
    private static <T extends FuncResult> T post(String url, String json, Class<?> clazz) {
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), json);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        log.info("http request= {}", json);
        Response response = httpClient.newCall(request).execute();

        String responseJson = response.body().string();
        log.info("http response= {}", responseJson);

        JSONObject jsonObject = JSONObject.parseObject(responseJson);
        return (T) jsonObject.getObject("payload", clazz);
    }


    /**
     * 入门测/出门测
     *
     * @param traceId
     * @param sceneInfo
     * @param inOut
     * @param catalogIds
     * @param roundId
     * @param roundIndex
     * @return
     */
    public static RecEval4InOutResult recEval(String traceId, SceneInfo sceneInfo, RecEvalEnum inOut, List<String> catalogIds, String roundId, int roundIndex) {
        RecEval4InOutParam payload = new RecEval4InOutParam()
                .setRecEvalEnum(inOut)
                .setCatalogIds(catalogIds)
                .setRoundId(roundId)
                .setTopicOrderNumber(roundIndex);

        SkylabRequest<RecEval4InOutParam> request = new SkylabRequest<>();
        request.setTraceId(traceId);
        request.setScene(sceneInfo);
        request.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/recEval";
        return post(url, JSON.toJSONString(request), RecEval4InOutResult.class);
    }


    /**
     * 答题记录上报
     *
     * @param traceId
     * @param sceneInfo
     * @param records
     * @return
     */
    public static StudyLogResult reportAnswerRecord(String traceId, SceneInfo sceneInfo, List<StudyLogRecord> records) {
        StudyLogParam payload = new StudyLogParam().setItems(records);

        SkylabRequest<StudyLogParam> request = new SkylabRequest<>();
        request.setTraceId(traceId);
        request.setScene(sceneInfo);
        request.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/behavior/reportAnswerRecord";
        return post(url, JSON.toJSONString(request), StudyLogResult.class);
    }

    /**
     * 点搜索
     *
     * @param traceId
     * @param sceneInfo
     * @param catalogIds
     * @return
     */
    public static RecNodeResult recNode(String traceId, SceneInfo sceneInfo, List<String> catalogIds, boolean useThinkingExpansion) {
        RecNodeParam payload = new RecNodeParam()
                .setCatalogIds(catalogIds)
                .setTargetMastery(0.8F)
                .setUseThinkingExpansion(useThinkingExpansion);

        SkylabRequest<RecNodeParam> request = new SkylabRequest<>();
        request.setTraceId(traceId);
        request.setScene(sceneInfo);
        request.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/recNode";
        return post(url, JSON.toJSONString(request), RecNodeResult.class);
    }


    public static RecTopicPackResult recTopicPack(String traceId, SceneInfo sceneInfo, List<String> nodeIds) {
        SkylabRequest<RecTopicPackParam> request = new SkylabRequest<>();
        request.setTraceId(traceId);
        request.setScene(sceneInfo);
        request.setPayload(new RecTopicPackParam().setNodeIds(nodeIds));

        String url = "http://" + ADDRESS + "/skylab/api/v1/recTopicPack";
        return post(url, JSON.toJSONString(request), RecTopicPackResult.class);
    }


    public static RecNodePropResult recNodeProp(String traceId, SceneInfo sceneInfo, List<String> nodeIds) {
        SkylabRequest<RecNodePropParam> request = new SkylabRequest<>();
        request.setTraceId(traceId);
        request.setScene(sceneInfo);
        request.setPayload(new RecNodePropParam().setNodeIds(nodeIds));

        String url = "http://" + ADDRESS + "/skylab/api/v1/recNodeProp";
        return post(url, JSON.toJSONString(request), RecNodePropResult.class);
    }


    /**
     * 点推题
     *
     * @param traceId
     * @param sceneInfo
     * @param nodeId
     * @param roundId
     * @param roundIndex
     * @return
     */
    public static RecTopicResult recTopic(String traceId, SceneInfo sceneInfo, String nodeId, String roundId, int roundIndex) {
        RecTopicParam payload = new RecTopicParam()
                .setNodeId(nodeId)
                .setRoundId(roundId)
                .setTopicOrderNumber(roundIndex);

        SkylabRequest<RecTopicParam> request = new SkylabRequest<>();
        request.setTraceId(traceId);
        request.setScene(sceneInfo);
        request.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/recTopic";
        return post(url, JSON.toJSONString(request), RecTopicResult.class);

    }


    public static MasterFetchResult fetch4Catalog(String traceId, SceneInfo sceneInfo, List<String> catalogIds) {
        MasterFetch4CatalogParam payload = new MasterFetch4CatalogParam()
                .setCatalogIds(catalogIds);

        SkylabRequest<MasterFetch4CatalogParam> request = new SkylabRequest<>();
        request.setTraceId(traceId);
        request.setScene(sceneInfo);
        request.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/master/fetch4Catalog";
        return post(url, JSON.toJSONString(request), MasterFetchResult.class);
    }
}
