package com.iflytek.skylab.service.front.link.debugContext.core;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.constant.RecEvalEnum;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.service.front.link.util.DebugUtil;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/4/19 11:42
 */
public abstract class AbstractDebugContext implements DebugContext {

    /**
     * 子类实现，逻辑流程
     */
    public abstract void go();


    protected RecEval4InOutResult recEval4In(String traceId, String roundId, int roundIndex) {
        RecEval4InOutResult result = recEval(traceId, RecEvalEnum.REC_EVAL4IN, roundId, roundIndex);
        recEval4InAssert(result);
        return result;
    }

    protected void recEval4InAssert(RecEval4InOutResult result) {
        Assert.notNull(result);
        Assert.notEmpty(result.getEvaluationItems());
        Assert.isTrue(result.getEvaluationItems().size() > 0);
    }


    protected RecEval4InOutResult recEval4Out(String traceId, String roundId, int roundIndex) {
        RecEval4InOutResult result = recEval(traceId, RecEvalEnum.REC_EVAL4OUT, roundId, roundIndex);
        recEval4OutAssert(result);
        return result;
    }

    protected void recEval4OutAssert(RecEval4InOutResult result) {
        Assert.notNull(result);
        Assert.notEmpty(result.getEvaluationItems());
        Assert.isTrue(result.getEvaluationItems().size() > 0);
    }

    protected StudyLogResult reportAnswerRecord(List<StudyLogRecord> records) {
        StudyLogParam payload = new StudyLogParam().setItems(records);

        SkylabRequest<StudyLogParam> request = new SkylabRequest<>();
        request.setTraceId(IdUtil.fastUUID());
        request.setScene(sceneInfo());
        request.setPayload(payload);

        String url = "http://" + address() + "/skylab/api/v1/behavior/reportAnswerRecord";
        StudyLogResult result = DebugUtil.post(url, JSON.toJSONString(request), StudyLogResult.class);
        reportAnswerRecordAssert(result);
        return result;
    }

    protected void reportAnswerRecordAssert(StudyLogResult result) {
        Assert.notNull(result);
        Assert.notEmpty(result.getIdList());
    }

    protected MasterFetchResult masterFetch4Catalog() {
        SkylabRequest<MasterFetch4CatalogParam> httpRequest = new SkylabRequest<>();
        httpRequest.setTraceId(IdUtil.fastUUID());
        httpRequest.setScene(sceneInfo());
        httpRequest.setPayload(new MasterFetch4CatalogParam().setCatalogIds(catalogIds()));

        String url = "http://" + address() + "/skylab/api/v1/master/fetch4Catalog";
        MasterFetchResult result = DebugUtil.post(url, JSON.toJSONString(httpRequest), MasterFetchResult.class);
        masterFetch4CatalogAssert(result);
        return result;
    }

    protected void masterFetch4CatalogAssert(MasterFetchResult result) {
        Assert.notNull(result);
        Assert.notEmpty(result.getMasterInfos());
    }


    protected MasterFetchResult masterFetch4Node() {
        SkylabRequest<MasterFetch4NodeParam> httpRequest = new SkylabRequest<>();
        httpRequest.setTraceId(IdUtil.fastUUID());
        httpRequest.setScene(sceneInfo());
        httpRequest.setPayload(new MasterFetch4NodeParam().setNodeIds(nodeIds()));

        String url = "http://" + address() + "/skylab/api/v1/master/fetch4Node";
        MasterFetchResult result = DebugUtil.post(url, JSON.toJSONString(httpRequest), MasterFetchResult.class);
        masterFetch4NodeAssert(result);
        return result;
    }

    protected void masterFetch4NodeAssert(MasterFetchResult result) {
        Assert.notNull(result);
        Assert.notEmpty(result.getMasterInfos());
    }


    protected RecNodeResult recNode() {
        RecNodeParam payload = new RecNodeParam()
                .setCatalogIds(catalogIds())
                .setTargetMastery(0.8F);
        // .setUseThinkingExpansion(useThinkingExpansion);

        SkylabRequest<RecNodeParam> request = new SkylabRequest<>();
        request.setTraceId(IdUtil.fastUUID());
        request.setScene(sceneInfo());
        request.setPayload(payload);

        String url = "http://" + address() + "/skylab/api/v1/recNode";
        RecNodeResult result = DebugUtil.post(url, JSON.toJSONString(request), RecNodeResult.class);
        recNodeAssert(result);
        return result;
    }

    protected void recNodeAssert(RecNodeResult result) {
        Assert.notNull(result);
        Assert.notEmpty(result.getNodeIds());
    }


    protected RecTopicResult recTopic(String traceId, String nodeId, String roundId, int roundIndex) {
        RecTopicParam payload = new RecTopicParam()
                .setNodeId(nodeId)
                .setRoundId(roundId)
                .setTopicOrderNumber(roundIndex);

        SkylabRequest<RecTopicParam> request = new SkylabRequest<>();
        request.setTraceId(traceId);
        request.setScene(sceneInfo());
        request.setPayload(payload);

        String url = "http://" + address() + "/skylab/api/v1/recTopic";
        RecTopicResult result = DebugUtil.post(url, JSON.toJSONString(request), RecTopicResult.class);
        recTopicAssert(result);
        return result;
    }

    protected void recTopicAssert(RecTopicResult result) {
        Assert.notNull(result);
    }

    private RecEval4InOutResult recEval(String traceId, RecEvalEnum inOut, String roundId, int roundIndex) {
        RecEval4InOutParam payload = new RecEval4InOutParam()
                .setRecEvalEnum(inOut)
                .setCatalogIds(catalogIds())
                .setRoundId(roundId)
                .setTopicOrderNumber(roundIndex);

        SkylabRequest<RecEval4InOutParam> request = new SkylabRequest<>();
        request.setTraceId(traceId);
        request.setScene(sceneInfo());
        request.setPayload(payload);

        String url = "http://" + address() + "/skylab/api/v1/recEval";
        return DebugUtil.post(url, JSON.toJSONString(request), RecEval4InOutResult.class);
    }

    /**
     * 子类实现，提供场景信息
     *
     * @return
     */
    protected abstract SceneInfo sceneInfo();

    /**
     * 子类实现，提供点
     *
     * @return
     */
    protected abstract List<String> nodeIds();

    /**
     * 子类实现，提供目录
     *
     * @return
     */
    protected abstract List<String> catalogIds();
}
