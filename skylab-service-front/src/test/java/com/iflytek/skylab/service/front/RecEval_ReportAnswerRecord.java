package com.iflytek.skylab.service.front;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.RecEvalEnum;
import com.iflytek.skylab.core.data.RecEval4InOutResult;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static com.iflytek.skylab.service.front.TestUtil.*;

/**
 * 入门测推题 然后 答题
 *
 * <AUTHOR>
 * @date 2022/4/5 13:57
 */
@Slf4j
public class RecEval_ReportAnswerRecord {

    // private static final String CATALOG_ID = "01_07020101-001_02_001";
    private static final String CATALOG_ID = "19_09020107-002_04_001";

    private static final SceneInfo sceneInfo = new SceneInfo()
            .setAreaCode(AREA_CODE)
            .setGraphVersion("v2")
            .setStudyCode(STUDY_CODE)
            .setBizCode(BIZ_CODE)
            .setSubjectCode(SUBJECT_CODE)
            .setPhaseCode(PHASE_CODE)
            .setUserId("6eefd5a8-bd02-4899-bc5a-598795aec777")
            .setPressCode("19")
            .setBookCode("19_09020107-002")
            .setGradeCode(GRADE_CODE)
            .setBizAction(BizActionEnum.SYNC_EVAL);

    private static final boolean[] allTrue = {true, true, true, true, true, true, true, true, true, true, true, true};
    private static final boolean[] allFalse = {false, false, false, false, false, false, false, false, false, false, false, false};
    private static final boolean[] random = {true, false, true, true, false, true, false, true, true, false, true, true, false};

    private static final boolean[] order = allTrue;

    /**
     * 入门测 然后 答题
     *
     * @param args
     */
    @SneakyThrows
    public static void main(String[] args) {
        String roundId = IdUtil.fastSimpleUUID();
        Set<String> roundTopics = Sets.newHashSet();

        int roundMaxIndex = 11;

        for (int i = 1; i <= roundMaxIndex; i++) {
            String traceId = IdUtil.fastUUID();
            List<String> catalogIds = Lists.newArrayList(CATALOG_ID);
            log.info("\n");
            log.info(i + "========================================================================================================================================================================================================================================================================");

            // 模拟推题
            RecEval4InOutResult recEvalResult = recEval(traceId, sceneInfo, RecEvalEnum.REC_EVAL4IN, catalogIds, roundId, i);
            // RecEval4InOutResult recEvalResult = recEval(traceId, sceneInfo, RecEvalEnum.REC_EVAL4OUT, catalogIds, roundId, i);
            if (CollectionUtil.isEmpty(recEvalResult.getEvaluationItems())) {
                log.info("EvaluationItems is empty");
                return;
            }

            // 如果引擎返回终止标识，不再答题、推题
            if (recEvalResult.isTerminationFlag()) {
                log.info("terminalFlag:{}", recEvalResult.isTerminationFlag());
                break;
            }

            // 模拟答题
            Thread.sleep(RandomUtil.randomLong(1000, 2000));
            // 答题记录
            List<StudyLogRecord> records = Lists.newArrayList();
            for (EvaluationItem item : recEvalResult.getEvaluationItems()) {
                if (roundTopics.contains(item.getResNodeId())) {
                    log.error("本轮有重复的推荐题！{}", item.getResNodeId());
                }
                roundTopics.add(item.getResNodeId());

                StudyLogRecord record = new StudyLogRecord();
                record.setRefTraceId(traceId);
                record.setNodeId(item.getNodeId());
                record.setNodeType(item.getNodeType());
                record.setResNodeId(item.getResNodeId());
                record.setResNodeType(item.getResNodeType());
                record.setRoundId(roundId);
                record.setRoundIndex(String.valueOf(i));
                record.setTimeCost(RandomUtil.randomInt(100));
                if (order[i]) {
                    record.setStandardScore(RandomUtil.randomDouble(10, 100));
                    record.setScore(record.getStandardScore());
                } else {
                    record.setStandardScore(RandomUtil.randomDouble(10, 100));
                    record.setScore(RandomUtil.randomDouble(record.getStandardScore()));
                }
                record.setFeedbackExt(new JSONObject(MapUtil.of("extKey", "extVal-" + record.getScore())));
                record.setFeedbackTime(new Date());

                records.add(record);
            }
            log.info("------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------");
            reportAnswerRecord(IdUtil.fastUUID(), sceneInfo, records);
            Thread.sleep(RandomUtil.randomLong(1000, 2000));
        }
        log.info("本轮推荐题量：{}", roundTopics.size());
    }

}
