package com.iflytek.skylab.service.front;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;

import static com.iflytek.skylab.service.front.TestUtil.*;
import static com.iflytek.skylab.service.front.TestUtil.GRADE_CODE;

/**
 * 点搜索  再 （第一个）点推题  再 答题
 *
 * <AUTHOR>
 * @date 2022/4/5 17:02
 */
@Slf4j
public class RecNode_RecTopic_ReportAnswerRecord {

    private static final String CATALOG_ID = "01_07020101-001_02_001";
    private static final SceneInfo sceneInfo = new SceneInfo()
            .setAreaCode(AREA_CODE)
            .setGraphVersion(GRAPH_VERSION)
            .setStudyCode(STUDY_CODE)
            .setBizCode(BIZ_CODE)
            .setSubjectCode(SUBJECT_CODE)
            .setPhaseCode(PHASE_CODE)
            .setUserId(USER_ID)
            .setPressCode(BOOK_VERSION_CODE)
            .setBookCode(BOOK_VOLUME_CODE)
            .setGradeCode(GRADE_CODE)
            .setBizAction(BizActionEnum.SYNC_SEARCH_WEAK);


    public static void main(String[] args) {
        String traceId = IdUtil.fastUUID();
        // 点搜索
        RecNodeResult recNodeResult = recNode(traceId, sceneInfo, Lists.newArrayList(CATALOG_ID), true);
        if (CollectionUtil.isEmpty(recNodeResult.getNodeIds())) {
            log.info("nodeIds is empty");
            return;
        }
        String nodeId = recNodeResult.getNodeIds().get(0);
        log.info("nodeIda={}", String.join(",", recNodeResult.getNodeIds()));
        log.info("nodeId={}", nodeId);
        log.info("------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------");

        // 点推题 并 答题
        // RecTopic_ReportAnswerRecord.recTopicAndReportAnswerRecord(nodeId, 10);
    }

}
