package com.iflytek.skylab.service.front;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.data.RecNodePropResult;
import com.iflytek.skylab.core.data.RecTopicPackResult;
import com.iflytek.skylab.core.data.StudyLogResult;
import com.iflytek.skylab.core.data.TopicInfo;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import org.assertj.core.util.Lists;

import java.time.Instant;
import java.util.Arrays;
import java.util.Date;

import static com.iflytek.skylab.core.constant.StudyCodeEnum.AI_DIAG;
import static com.iflytek.skylab.service.front.TestUtil.reportAnswerRecord;

/**
 * <AUTHOR>
 * @date 2022/6/9 17:20
 */
public class REcNodeProp_RecTopicPack_ReportAnswerRecord {


    private static final SceneInfo sceneInfo = new SceneInfo()
            .setAreaCode("000000")
            .setGraphVersion("v2")
            .setStudyCode(AI_DIAG)
            .setBizCode(BizCodeEnum.ZSY_XXJ)
            .setSubjectCode("02")
            .setPhaseCode("04")
            // .setUserId("6eefd5a8-bd02-4899-bc5a-598795aec7b8")
            .setUserId("qqqqqqqqqqqqqqqqqqqqqqqqq")
            .setPressCode("01")
            .setBookCode("01_07020101-001")
            .setGradeCode("07")
            .setBizAction(BizActionEnum.AI_REVIEW_EVAL);

    private static final String[] nodeIds = {
            "337c890a-8a38-4454-9f59-8d3f481f53df", "5384a4ce-d2d3-40ac-a146-faf0741a8483", "b17c91ed-2626-40d4-80a3-14442cba7c5e",
            "ee815a3a-0894-40d3-a08e-cd3681fa53da", "ca253ca7-d095-4585-bb71-6a5dbce292df", "938e3965-2a50-44e6-9e73-da88a47aff43",
            "699de538-4e35-437d-97de-cad63d2ed16f", "7b886133-594b-480b-be83-0e372b1487f9", "14bc7ccd-6527-40e4-a034-b0e3d6054d70",
            "d5a36410-d6ed-497d-b3ba-ce45f19b82a7", "668bcfb5-bbb0-4172-976e-9e3a1c999a76", "9fd01e63-7185-41b7-9edd-ec0afb50f315"
    };

    public static void main(String[] args) {
        String traceId = IdUtil.fastSimpleUUID();
        RecNodePropResult recNodePropResult = TestUtil.recNodeProp(traceId, sceneInfo, Arrays.asList(nodeIds));
        System.out.println(recNodePropResult);

        traceId = IdUtil.fastSimpleUUID();
        RecTopicPackResult recTopicPackResult = TestUtil.recTopicPack(traceId, sceneInfo, Arrays.asList(nodeIds));
        System.out.println(recTopicPackResult);


        TopicInfo info = recTopicPackResult.getTopicInfos().get(0);
        StudyLogRecord record = new StudyLogRecord();
        record.setRefTraceId(traceId);
        record.setNodeId(info.getNodeId());
        record.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        record.setResNodeId(info.getTopicId());
        record.setResNodeType(ResourceTypeEnum.TOPIC);
        record.setRoundId(IdUtil.fastSimpleUUID());
        record.setRoundIndex("1");
        record.setTimeCost(RandomUtil.randomInt(100));
        record.setStandardScore(RandomUtil.randomDouble(10, 100));
        record.setScore(RandomUtil.randomDouble(record.getStandardScore()));
        record.setFeedbackExt(new JSONObject(MapUtil.of("extKey", "extVal-" + record.getScore())));
        record.setFeedbackTime(new Date());
        record.setFrom("iflytek-666");

        StudyLogResult studyLogResult = reportAnswerRecord(IdUtil.fastUUID(), sceneInfo, Lists.newArrayList(record));
        System.out.println(studyLogResult);
    }

}
