# http拨测接口



## 1 诊断功能对外统一接口
### 1.1 url
http://ip:port/skylab/api/v1/dialing-test/diagnose
### 1.2 请求头

| 请求头       | 必填 | 说明                                                |
| ------------ | ---- | --------------------------------------------------- |
| Content-Type | Y    | application/json                                    |
| sceneType    | Y    | 场景类型简单名称。如 SceneInfo                      |
| funcParam    | Y    | 请求的参数类型简单名称。如 MasterFetch4CatalogParam |

### 1.3 请求Body

参考[新平台接口协议](https://skyline-doc.iflyresearch.com/skylab/)。

### 1.4 请求示例

```http
### diagnose
POST http://*************:32182/skylab/api/v1/dialing-test/diagnose
Content-Type: application/json
Timeout: 50000
sceneType: SceneInfo
funcParam: MasterFetch4CatalogParam

{
  "traceId": "{{$random.uuid}}",
  "scene": {
        "areaCode": "010100",
        "graphVersion": "v16",
        "studyCode": "SYNC_LEARN",
        "bizAction": "NONE",
        "bizCode": "ZSY_XXJ",
        "subjectCode": "02",
        "phaseCode": "04",
        "userId": "6eefd5a8-bd02-4899-bc5a-598795aec7b8",
        "bookVersionCode": "01",
        "bookVolumeCode": "01_07020101-001",
        "test": false
  },
  "payload": {
         "catalogIds": [
            "01_07020101-001_02_001"
          ]
  }
}
```



## 2 推荐功能对外统一接口

### 2.1 url

http://ip:port/skylab/api/v1/dialing-test/recommend

### 2.2 请求头

| 请求头       | 必填 | 说明                                           |
| ------------ | ---- | ---------------------------------------------- |
| Content-Type | Y    | application/json                               |
| sceneType    | Y    | 场景类型简单名称。如 AiDiagSceneInfo           |
| funcParam    | Y    | 请求的参数类型简单名称。如 RecEval4InOutParam  |
| funcResult   | Y    | 返回的参数类型简单名称。如 RecEval4InOutResult |

### 2.3 请求Body

参考[新平台接口协议](https://skyline-doc.iflyresearch.com/skylab/)。

### 2.4 请求示例

```http
### recommend
POST http://*************:32182/skylab/api/v1/dialing-test/recommend
Content-Type: application/json
Timeout: 50000
sceneType: AiDiagSceneInfo
funcParam: RecEval4InOutParam
funcResult: RecEval4InOutResult

{
  "traceId": "{{$random.uuid}}",
  "scene": {
        "studyCode": "SYNC_LEARN",
        "bizAction": "SYNC_EVAL",
        "subjectCode": "02",
        "phaseCode": "04",
        "catalogCode": "01_09020101-002_001_001",
        "pressCode": "01",
        "test": false,
        "userId": "zhangsan",
        "bizCode": "ZSY_XXJ",
        "gradeCode": "07",
        "areaCode": "000000",
        "graphVersion": "v16",
        "bookCode": "01_09020101-002",
        "nodeCatalogMap": {
        "key": "val"
        }
  },
  "payload": {
        "catalogIds": [
            "01_09020101-002_001_001"
        ],
        "roundId": "1",
        "topicOrderNumber": 1,
        "funcCode": "REC_EVAL4IN",
        "recEvalEnum": "REC_EVAL4IN"
  }
}
```



## 3 答题记录上报接口

### 3.1 url

http://ip:port/skylab/api/v1/dialing-test/reportAnswerRecord

### 3.2 请求头

| 请求头       | 必填 | 说明                           |
| ------------ | ---- | ------------------------------ |
| Content-Type | Y    | application/json               |
| sceneType    | Y    | 场景类型简单名称。如 SceneInfo |

### 3.3 请求Body

参考[新平台接口协议](https://skyline-doc.iflyresearch.com/skylab/)。

### 3.4 请求示例

```http
### reportAnswerRecord
POST http://*************:32182/skylab/api/v1/dialing-test/reportAnswerRecord
Content-Type: application/json
Timeout: 50000
sceneType: SceneInfo

{
  "traceId": "{{$random.uuid}}",
  "scene": {
        "studyCode": "SYNC_LEARN",
        "bizAction": "SYNC_EVAL",
        "subjectCode": "02",
        "phaseCode": "04",
        "catalogCode": "01_09020101-002_001_001",
        "pressCode": "01",
        "test": false,
        "userId": "zhangsan",
        "bizCode": "ZSY_XXJ",
        "gradeCode": "07",
        "areaCode": "000000",
        "graphVersion": "v16",
        "bookCode": "01_09020101-002"
  },
  "payload": {
        "items": [
            {
                "resNodeType": "TOPIC",
                "roundIndex": "1",
                "score": 0,
                "standardScore": 1,
                "feedbackTime": "2022-05-04T07:09:28.193Z",
                "nodeType": "CHECK_POINT",
                "refTraceId": "8565d8c0-88a6-4111-a77a-9776b7c38984",
                "timeCost": 65,
                "nodeId": "diagnose-adjust-checkpoint-001",
                "resNodeId": "diagnose-adjust-exam-topic10",
                "roundId": "1"
            }
        ]
  }
}
```



## 4 通用特征查询接口

### 4.1 url

http://ip:port/skylab/api/v1/dialing-test/featureFetch

### 4.2 请求头

| 请求头       | 必填 | 说明                           |
| ------------ | ---- | ------------------------------ |
| Content-Type | Y    | application/json               |
| sceneType    | Y    | 场景类型简单名称。如 SceneInfo |

### 4.3 请求Body

参考[新平台接口协议](https://skyline-doc.iflyresearch.com/skylab/)。

### 4.4 请求示例

```http
### featureFetch
POST http://*************:32182/skylab/api/v1/dialing-test/featureFetch
Content-Type: application/json
Timeout: 50000
sceneType: SceneInfo

{
  "traceId": "{{$random.uuid}}",
  "scene": {
    "bizCode": "ZSY_XXJ",
    "areaCode": "000000",
    "phaseCode": "04",
    "studyCode": "SYNC_LEARN",
    "bizAction": "NONE",
    "subjectCode": "02",
    "test": false,
    "userId": "yclv_synLearn_NodeDiagnose_User01",
    "catalogCode": "01_07020101-001_02_001"
  },
  "payload": {
    "simpleMode": true,
    "items": [{
      "featureName": "user_level",
      "params": []
    }]
  }
}
```

