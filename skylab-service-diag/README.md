## skylab-service-diag

画像诊断服务（含 v1/v2 精准学 OS、画像合并）

### 1. 项目概述
- 项目名称：skylab-service-diag
- 项目描述：提供用户画像诊断、点画像查询、精准学 OS 画像与画像合并能力
- 版本信息：2.0.9-SNAPSHOT（开发中）
- 端口：23336（dev）

### 2. 技术架构
- 框架：Spring Boot、Knife4j、Skyline Dispatcher
- 控制器：
  - DiagnoseController（/skylab/api/v1/master/diagnose）
  - DiagnoseQueryController（/skylab/api/v1/master/query）
  - DiagnoseSyncOsController（/skylab/api/v2/master/diagnose）
  - DiagnoseMergeController（/skylab/api/v1/merge/diagnose，本地调度）
- 关键依赖：skylab-core-data-adapter（DiagParamAdapter、DiagSyncOsParamAdapter）、skylab-core-data（SceneInfo、MasterFetchResult）

#### 架构图（Mermaid）
```mermaid
flowchart TD
  C[Client] --> DIAG[skylab-service-diag]
  DIAG -->|适配| ADPT[Diag Param Adapter]
  DIAG -->|调用| ENGINE[Diagnose Engine]
  DIAG -->|存储/埋点| DATA[Mongo/Kafka]
```

### 3. 主要功能
- 画像诊断：/v1/master/diagnose
- 点画像查询：/v1/master/query
- 精准学 OS 画像：/v2/master/diagnose（FAST_MASTER_FETCH、MASTER_SNAPSHOOT_FETCH 等）
- 画像合并：/v1/merge/diagnose（SpringBeanDispatcher 本地调度）

### 4. 业务处理逻辑
- 场景解析 -> 参数适配 -> 引擎诊断 -> 画像日志存储与埋点 -> 结果适配返回
- 异常处理：EngineException 携带 errorCode/redirect，封装至 ApiResponseHeader

### 5. 接口（REST）
- POST `/skylab/api/v1/master/diagnose`
- POST `/skylab/api/v1/master/query`
- POST `/skylab/api/v2/master/diagnose`
- POST `/skylab/api/v1/merge/diagnose`

### 6. 系统配置
- 端口：`server.port=23336`
- Zookeeper 注册：spring.cloud.zookeeper.*
- Graph 配置：见 application.properties 中 graph 缓存开关与版本

### 7. 启动与验证
```bash
mvn -pl skylab-service-diag spring-boot:run -Dspring-boot.run.profiles=dev
```

### 8. 开发指南
- 启动类：com.iflytek.skylab.service.diag.DiagAppBoot
- 控制器见 controller 包；适配器见 skylab-core-data-adapter
- 建议使用 MockMvc/RestAssured 进行接口回归测试

