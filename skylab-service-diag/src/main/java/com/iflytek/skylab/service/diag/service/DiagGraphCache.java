package com.iflytek.skylab.service.diag.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.GraphVertexType;
import com.iflytek.skylab.core.constant.StudyingScienceType;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.GraphLabelQuery;
import com.iflytek.skylab.core.dataapi.data.GraphVertexesQuery;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * todo 1、图谱多版本需要改造  2、内存缓存过大后需要降级磁盘缓存
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/22 11:07
 */
@Slf4j
public class DiagGraphCache {
    @Autowired
    private GraphService graphService;

    /**
     * 初中数学
     */
    private static final List<String[]> allowSubjectPhase = Lists.newArrayList();

    /**
     * 多版本
     */
    @Value("${skylab.front.multi-graph-version}")
    private String multiVersion;

    /**
     * 章 -> 点关系
     */
    protected static final ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashSet<String>>> MULTI_GRAPH_CATA_TO_POINT_CACHE = new ConcurrentHashMap<>();
    /**
     * 复习点 ->章
     */
    protected static final ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashSet<String>>> MULTI_REVIEW_POINT_TO_UNIT_CACHE = new ConcurrentHashMap<>();

    /**
     * 区域->复习点
     */
    protected static final ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashSet<String>>> MULTI_AREA_REVIEW_POINT = new ConcurrentHashMap<>();


    static {
        //初中数学
        allowSubjectPhase.add(new String[]{StudyingScienceType.JUNIOR, StudyingScienceType.MATH});
        allowSubjectPhase.add(new String[]{StudyingScienceType.PRIMARY, StudyingScienceType.MATH});
    }

    @PostConstruct
    public void initGraph() {
        bookToUnitToPointCache();
        //日志输出
        List<String> versions = StrUtil.split(multiVersion, ",");
        for (String version : versions) {
            Set<String> allBooksCache = getAllBooksCache(version);
            log.info("图谱：{},学科学段{}，关联书本详情：{}", version, JSON.toJSONString(allowSubjectPhase), allBooksCache);
        }
    }

    /**
     * 获取 书本
     *
     * @param graphVersion
     * @return
     */
    public static Set<String> getAllBooksCache(String graphVersion) {
        HashSet<String> books = new HashSet<>();
        ConcurrentHashMap<String, ConcurrentHashSet<String>> hashMap = MULTI_GRAPH_CATA_TO_POINT_CACHE.get(graphVersion);
        if (hashMap == null) {
            return Collections.emptySet();
        }
        for (Map.Entry<String, ConcurrentHashSet<String>> entry : hashMap.entrySet()) {
            String key = entry.getKey();
            List<String> split = StrUtil.split(key, "_");
            if (split.size() > 2) {
                String book = split.get(0) + "_" + split.get(1);
                books.add(book);
            }
        }

        log.info("图谱：{},学科学段{}，全书地图关联书本：{}本", graphVersion, JSON.toJSONString(allowSubjectPhase), books.size());
        return books;
    }


    /**
     * 获取 复习的章 关系
     *
     * @param graphVersion
     * @param reviewPoint
     * @return
     */
    public static Set<String> getReviewPointToUnitCache(String graphVersion, String reviewPoint) {
        ConcurrentHashMap<String, ConcurrentHashSet<String>> hashMap = MULTI_REVIEW_POINT_TO_UNIT_CACHE.get(graphVersion);
        if (hashMap == null) {
            return Collections.emptySet();
        }
        ConcurrentHashSet<String> hashSet = hashMap.get(reviewPoint);
        return hashSet == null ? new ConcurrentHashSet<>() : hashSet;
    }


    /**
     * 获取 区域 复习点
     *
     * @param graphVersion
     * @param area
     * @return
     */
    public static Set<String> getAreaToReviewPointToCache(String graphVersion, String area) {
        ConcurrentHashMap<String, ConcurrentHashSet<String>> hashMap = MULTI_AREA_REVIEW_POINT.get(graphVersion);
        if (hashMap == null) {
            return Collections.emptySet();
        }
        ConcurrentHashSet<String> hashSet = hashMap.get(area);
        return hashSet == null ? new ConcurrentHashSet<>() : hashSet;
    }

    /**
     * 获取 书本 点 关系
     *
     * @param graphVersion
     * @param unit
     * @return
     */
    public static Set<String> getUnitToPointCache(String graphVersion, String unit) {
        ConcurrentHashMap<String, ConcurrentHashSet<String>> hashMap = MULTI_GRAPH_CATA_TO_POINT_CACHE.get(graphVersion);
        if (hashMap == null) {
            return Collections.emptySet();
        }
        ConcurrentHashSet<String> hashSet = hashMap.get(unit);
        return hashSet == null ? new ConcurrentHashSet<>() : hashSet;
    }

    /**
     * 获取 章 点 关系
     *
     * @param graphVersion
     * @param book
     * @return
     */
    public static List<String> getAllUnit(String graphVersion, String book) {
        List<String> catas = new ArrayList<>();
        ConcurrentHashMap<String, ConcurrentHashSet<String>> hashMap = MULTI_GRAPH_CATA_TO_POINT_CACHE.get(graphVersion);
        if (hashMap == null) {
            return catas;
        }
        for (Map.Entry<String, ConcurrentHashSet<String>> entry : hashMap.entrySet()) {
            if (StrUtil.startWith(entry.getKey(), book)) {
                catas.add(entry.getKey());
            }

        }
        return catas;
    }


    /**
     * 兼容多版本
     * 书本到点（锚点、考点、复习点）缓存加载
     */
    private void bookToUnitToPointCache() {

        List<String> versions = StrUtil.split(multiVersion, ",");
        long l = System.currentTimeMillis();
        for (String version : versions) {

            //     * 缓存 复习点-章 关系
            ConcurrentHashMap<String, ConcurrentHashSet<String>> reviewPointToUnitCache = new ConcurrentHashMap<>();

            //   * 缓存 BooK-UNIT-Point 关系
            ConcurrentHashMap<String, ConcurrentHashSet<String>> unitToPointCache = new ConcurrentHashMap<>();
            //   * 缓存 区域-复习点 关系
            ConcurrentHashMap<String, ConcurrentHashSet<String>> areaToReviewPoint = new ConcurrentHashMap<>();

            log.info("书本到点（锚点、考点、复习点）缓存加载开始===============,graph-version={}", version);
            String sourceLabel = GraphVertexType.UNIT;
            List<String> targetLabel = Lists.newArrayList(GraphVertexType.ANCHOR_POINT, GraphVertexType.CHECK_POINT, GraphVertexType.REVIEW_POINT);
            String traceId = IdUtil.fastSimpleUUID();
            List<String> points = new ArrayList<>();
            //查询所有UNIT
            for (String[] allow : allowSubjectPhase) {
                //学科学段过滤
                JSONObject props = new JSONObject().fluentPut("phase", allow[0]).fluentPut("subject", allow[1]);
                GraphData graphData = graphService.lookup(new GraphLabelQuery().setGraphVersion(version).setTraceId(traceId).setLabel(GraphVertexType.UNIT), props);
                points.addAll(graphData.getVertices().stream().map(GraphData.GraphVertex::getId).collect(Collectors.toList()));
            }
            int total = points.size();
            List<List<String>> split = CollUtil.split(points, 50);
            log.info(String.format("Loading %s 0/%s", GraphVertexType.UNIT, total));
            int i = 0;
            for (List<String> list : split) {
                i = i + list.size();
                log.info(String.format("Loading %s %s/%s", sourceLabel + "_" + targetLabel, i, total));

                SubGraphQuery subGraphQuery = new SubGraphQuery();
                subGraphQuery.setTraceId(UUID.randomUUID().toString());
                subGraphQuery.setGraphVersion(version);
                subGraphQuery.setRootVertexLabel(sourceLabel);
                subGraphQuery.setRootVertexIdList(list);
                List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
                for (String anchorType : targetLabel) {
                    edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(sourceLabel).target(anchorType).build());
                }
                subGraphQuery.setEdgeLabels(edgeLabels);
                subGraphQuery.setIncludeFields(Lists.newArrayList("EDGES"));
                GraphData edge = graphService.querySubGraph(subGraphQuery);

                if (CollUtil.isNotEmpty(edge.getEdges())) {

                    Map<String, ConcurrentHashSet<String>> unitPoint = new ConcurrentHashMap<>();
                    for (GraphData.GraphEdge graphEdge : edge.getEdges()) {
                        String target = graphEdge.getTarget();
                        unitPoint.computeIfAbsent(graphEdge.getSource(), k -> new ConcurrentHashSet<>()).add(target);
                    }

                    unitToPointCache.putAll(unitPoint);

                    //章到复习点
                    Map<String, Set<String>> groupMapping = edge.getEdges().stream().filter(item -> "UNIT_REVIEW_POINT".equals(item.getLabel())).collect(Collectors.groupingBy(GraphData.GraphEdge::getTarget, Collectors.mapping(GraphData.GraphEdge::getSource, Collectors.toSet())));
                    for (Map.Entry<String, Set<String>> entry : groupMapping.entrySet()) {
                        if (reviewPointToUnitCache.containsKey(entry.getKey())) {
                            reviewPointToUnitCache.get(entry.getKey()).addAll(entry.getValue());
                        } else {
                            ConcurrentHashSet<String> concurrentHashSet = new ConcurrentHashSet<>();
                            concurrentHashSet.addAll(entry.getValue());
                            reviewPointToUnitCache.put(entry.getKey(), concurrentHashSet);
                        }
                    }

                }
            }

            //区域到复习点
            List<String> reviews = reviewPointToUnitCache.entrySet().stream().map(Map.Entry::getKey).collect(Collectors.toList());
            List<List<String>> splitReviews = CollUtil.split(reviews, 200);
            int j = 0;
            for (List<String> splitReview : splitReviews) {
                j = j + splitReview.size();
                log.info(String.format("Loading %s %s/%s", sourceLabel + "_" + targetLabel, j, reviews.size()));
                GraphVertexesQuery query = new GraphVertexesQuery();
                query.setTraceId(traceId);
                query.setIds(splitReview);
                query.setGraphVersion(version);
                GraphData graphData = graphService.queryVertexByIds(query);
                List<GraphData.GraphVertex> vertices = graphData.getVertices();
                for (GraphData.GraphVertex vertex : vertices) {
                    String reviewPoint = vertex.getId();
                    JSONObject properties = vertex.getProperties();
                    String areaCode = "";
                    if (properties != null) {
                        JSONArray areas = properties.getJSONArray("areas");
                        if (CollUtil.isNotEmpty(areas)) {
                            areaCode = areas.getJSONObject(0).getString("code");
                        }
                    }
                    if (StrUtil.isEmpty(areaCode)) {
                        throw new RuntimeException("复习点：" + reviewPoint + "无区域属性,请质检图谱");
                    }

                    //区域+复习点
                    areaToReviewPoint.computeIfAbsent(areaCode, k -> new ConcurrentHashSet<>()).add(reviewPoint);
                }
            }

            MULTI_GRAPH_CATA_TO_POINT_CACHE.put(version, unitToPointCache);
            MULTI_REVIEW_POINT_TO_UNIT_CACHE.put(version, reviewPointToUnitCache);
            MULTI_AREA_REVIEW_POINT.put(version, areaToReviewPoint);

        }
        for (Map.Entry<String, ConcurrentHashMap<String, ConcurrentHashSet<String>>> entry : MULTI_GRAPH_CATA_TO_POINT_CACHE.entrySet()) {
            log.info("图谱：{}，缓存书本到点关系，章size()={},耗时：{}ms", entry.getKey(), entry.getValue().size(), System.currentTimeMillis() - l);
        }
        for (Map.Entry<String, ConcurrentHashMap<String, ConcurrentHashSet<String>>> entry : MULTI_REVIEW_POINT_TO_UNIT_CACHE.entrySet()) {
            log.info("图谱：{}，书本到点（锚点、考点、复习点）缓存加载完成={},耗时：{}ms", entry.getKey(), entry.getValue().size(), System.currentTimeMillis() - l);
        }
        for (Map.Entry<String, ConcurrentHashMap<String, ConcurrentHashSet<String>>> entry : MULTI_AREA_REVIEW_POINT.entrySet()) {
            log.info("图谱：{}，区域复习点缓存加载完成={},耗时：{}ms", entry.getKey(), entry.getValue().size(), System.currentTimeMillis() - l);
        }
    }

}
