package com.iflytek.skylab.service.diag.controller;

import com.google.common.collect.Lists;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseRequest;
import com.iflytek.hy.rec.framework.exception.EngineException;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.data.adapter.diag.DiagParamAdapter;
import com.iflytek.skylab.core.data.adapter.scene.SceneInfoSelector;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.diag.service.DiagnoseService;
import com.iflytek.skylab.service.diag.service.MasteryBusinessService;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.logging.LoggingCost;

/**
 * 诊断服务-v1接口
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "点画像查询服务")
@RestController
@RequestMapping("/skylab/api/v1/master")
@EnableSkynetSwagger2
public class DiagnoseQueryController {

    /**
     * 诊断Service
     */
    private final DiagnoseService diagnoseService;

    /**
     * 用户画像 业务逻辑实现
     */
    private final MasteryBusinessService userMasteryService;
    private final DiagParamAdapter diagParamAdapter;

    public DiagnoseQueryController(DiagnoseService diagnoseService, MasteryBusinessService userMasteryService) {
        this.diagnoseService = diagnoseService;
        this.userMasteryService = userMasteryService;
        this.diagParamAdapter = new DiagParamAdapter();
    }

    /**
     * 画像诊断与获取
     * <p>
     * header:
     * parameter:
     * payload:
     *
     * @param apiRequest 请求参数
     * @return DispatchApiResponse 请求响应
     */
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "画像诊断与获取")
    @PostMapping("/query")
    // @SkylineMetric(value = "30.画像诊断与获取", provider = SceneInfoMetricProvider.class)
    @LoggingCost
    public DispatchApiResponse nodeQuery(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("DiagnoseQueryController#query,DispatchApiRequest= {}", apiRequest);
        }
        String traceId = apiRequest.getTraceId();

        //获取场景信息 对象
        SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);
        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }

        //参数适配
        GraphDiagnoseRequest graphDiagnoseRequest = diagParamAdapter.adapt(traceId, sceneInfo, apiRequest.getPayload());

        DispatchApiResponse apiResponse = new DispatchApiResponse();
        try {
            //调用诊断引擎
            MasterFetchResult masterFetchResult = diagnoseService.nodeQuery(traceId, sceneInfo, graphDiagnoseRequest);

            apiResponse.setPayload(masterFetchResult);
        } catch (EngineException ee) {
            log.error("Call DiagnoseEngine error code= {}; message= {}; failover={}", ee.getErrorCode(), ee.getMessage(), ee.getRedirect());

            ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
            if (ee.getRedirect()) {
                header.setErrorNodes(Lists.newArrayList(new ApiResponseHeader.ErrorNode("failover", "true")));
            }
            apiResponse.setHeader(header);
        }
        apiResponse.setTraceId(traceId);
        if (log.isDebugEnabled()) {
            log.debug("DispatchApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }
}
