package com.iflytek.skylab.service.diag.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Stopwatch;
import com.iflytek.hy.rec.domain.model.valueobj.EngineConfigItem;
import com.iflytek.hy.rec.dtpmapdiag.GraphDtpMapDiagnose;
import com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseRequest;
import com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.TraceConstant;
import com.iflytek.skylab.core.data.MasterFetch5NodeParam;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.domain.MasterInfo;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.common.exception.ParamInvalidException;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import com.iflytek.skyline.resource.domain.ConfigItem;
import com.iflytek.skyline.resource.domain.ResourcePackage;
import com.iflytek.skyline.resource.manager.ParameterConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import skynet.boot.logging.LoggingCost;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 诊断Service
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
public class DiagnoseSyncOsService {

    /**
     * 诊断引擎入口
     */
    private final GraphDtpMapDiagnose graphDtpMapDiagnose;

    /**
     * 资源配置转换器
     */
    private final ParameterConverter parameterConverter;

    private final TraceUtils traceUtils;

    public DiagnoseSyncOsService(GraphDtpMapDiagnose graphDiagnose, ParameterConverter parameterConverter, TraceUtils traceUtils) {
        this.graphDtpMapDiagnose = graphDiagnose;
        this.parameterConverter = parameterConverter;
        this.traceUtils = traceUtils;
    }

    /**
     * 调用诊断引擎 进行 画像计算
     *
     * @param traceId          跟踪Id
     * @param diagRequest      引擎入参
     * @param configJsonObject 引擎配置
     * @return GraphDiagnoseResponse 诊断返回
     */
    @LoggingCost
    public GraphDiagnoseResponse diagnose(@Valid @NotBlank String traceId, @Valid @NotNull GraphDiagnoseRequest diagRequest, @Valid @NotNull JSONObject configJsonObject) throws Exception {
        if (log.isTraceEnabled()) {
            log.trace("configJsonObject= {};", configJsonObject);
        }
        Assert.hasText(traceId, "The traceId must not blank.");
        Assert.notNull(diagRequest, "The GraphDiagnoseRequest must not null.");
        Assert.notNull(configJsonObject, "The configJsonObject must not null.");

        //参数配置管理
        ResourcePackage resourcePackage = this.parameterConverter.convertPackage(configJsonObject);
        if (log.isTraceEnabled()) {
            log.trace("realConfig= {}", resourcePackage);
        }
        log.debug("StrategyId= {}", resourcePackage.getMd5());
        List<EngineConfigItem> confItems = new ArrayList<>();
        for (ConfigItem item : resourcePackage.getItems()) {
            confItems.add(new EngineConfigItem().setKey(item.getKey()).setConfig(item.getContent()).setMd5(item.getMd5()));
        }

        //stopwatch
        Stopwatch stopwatch = Stopwatch.createStarted();
        //加载配置
        this.graphDtpMapDiagnose.loadConfig(resourcePackage.getMd5(), confItems);
        log.debug("the graphDiagnose loadConfig cost= {}", stopwatch);

        //设置StrategyId
        diagRequest.getSessionInfo().setStrategyId(resourcePackage.getMd5());

        stopwatch.reset().start();
        GraphDiagnoseResponse response = graphDtpMapDiagnose.diagnose(diagRequest);

        String userId = Optional.of(diagRequest)
                .map(GraphDiagnoseRequest::getSceneInfo)
                .map(com.iflytek.hy.rec.domain.model.valueobj.SceneInfo::getUserId)
                .orElse(null);
        if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
            //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
        }else {
            traceUtils.record("graphDtpMapDiagnose#diagnose", Collections.EMPTY_MAP, Collections.singletonMap("cost", String.valueOf(stopwatch.elapsed(TimeUnit.MILLISECONDS))));
        }
        log.debug("the graphDiagnose diagnose cost= {}", stopwatch);

        if (log.isDebugEnabled()) {
            log.debug("GraphDiagnoseResponse:Json= {}", JSON.toJSONString(response));
        }
        return response;
    }


    /**
     * 全书地图
     * 画像快照查询
     *
     * @param traceId   跟踪Id
     * @param sceneInfo
     * @return masterFetch5BookParam 诊断返回
     */
    @LoggingCost
    public DispatchApiResponse masterSnapshootFetch(@Valid @NotBlank String traceId, @Valid @NotNull SceneInfo sceneInfo, @Valid @NotNull JSONObject masterFetch5BookParam) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        stopwatch.reset().start();

        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId(sceneInfo.getUserId());
        String bookCode = masterFetch5BookParam.getString("bookCode");


        //待查询章节
        Map<String, Set<String>> cataPoints = new HashMap<>();
        List<String> catalogIds = masterFetch5BookParam.getList("catalogIds", String.class);
        if (CollUtil.isEmpty(catalogIds)) {
            //查询书本多有章
            catalogIds = DiagGraphCache.getAllUnit(sceneInfo.getGraphVersion(), bookCode);
        }

        //通过章获取所有点
        Set<String> pointSet = new HashSet<>();
        for (String catalogId : catalogIds) {
            Set<String> points = DiagGraphCache.getUnitToPointCache(sceneInfo.getGraphVersion(), catalogId);
            cataPoints.put(catalogId, points);
            pointSet.addAll(points);
        }
        masterQuery.setNodeIdList(new ArrayList<>(pointSet));
        //限制点类型
        List<String> expPointTypes = masterFetch5BookParam.getList("expPointTypes", String.class);
        if (CollUtil.isNotEmpty(expPointTypes)) {
            List<NodeTypeEnum> nodeTypeEnums = expPointTypes.stream().map(NodeTypeEnum::parse).collect(Collectors.toList());
            masterQuery.setNodeTypes(nodeTypeEnums);
        }

        //画像查询
        List<UserMasteryRecord> userMasteryRecords = DataHub.getMasterService().queryMasterDataBatch(traceId, masterQuery);
        MasterFetchResult masterFetchResult = new MasterFetchResult();
        List<MasterInfo> masterInfos = new ArrayList<>();

        //点画像
        Map<String, UserMasteryRecord> nodeUserMasteryRecord = new HashMap<>();
        for (UserMasteryRecord userMasteryRecord : userMasteryRecords) {
            nodeUserMasteryRecord.put(userMasteryRecord.getNodeId(), userMasteryRecord);
        }

        //区域复习点
        Set<String> areaToReviewPointToCache = DiagGraphCache.getAreaToReviewPointToCache(sceneInfo.getGraphVersion(), sceneInfo.getAreaCode());

        for (Map.Entry<String, Set<String>> entry : cataPoints.entrySet()) {

            String catalogId = entry.getKey();
            Set<String> nodeIds = entry.getValue();

            for (String nodeId : nodeIds) {
                //获取点画像
                UserMasteryRecord userMasteryRecord = nodeUserMasteryRecord.get(nodeId);
                if (userMasteryRecord == null) {
                    continue;
                }
                //复习点 区域过滤
                if (NodeTypeEnum.REVIEW_POINT.equals(userMasteryRecord.getNodeType()) && !areaToReviewPointToCache.contains(userMasteryRecord.getNodeId())) {
                    continue;
                }
                MasterInfo info = new MasterInfo();
                BeanUtil.copyProperties(userMasteryRecord, info);
                info.setCatalogId(catalogId);
                info.setComputeType(userMasteryRecord.getMasteryType());
                masterInfos.add(info);
            }
        }

        masterFetchResult.setMasterInfos(masterInfos);
        DispatchApiResponse apiResponse = new DispatchApiResponse();
        apiResponse.setPayload(masterFetchResult);
        apiResponse.setTraceId(traceId);
        return apiResponse;
    }

    /**
     * 快速画像查询
     *
     * @param traceId              跟踪ID
     * @param graphDiagnoseRequest 查询参数
     * @return 画像查询结果
     */
    public GraphDiagnoseResponse fastMasterFetch(@Valid @NotNull String traceId, GraphDiagnoseRequest graphDiagnoseRequest) {
        log.debug("开始快速画像查询, traceId={}, graphDiagnoseRequest={}", traceId, graphDiagnoseRequest);
        // 参数校验
        if (graphDiagnoseRequest == null) {
            log.error("画像查询参数为空, traceId={}", traceId);
            throw new ParamInvalidException("画像查询参数不能为空");
        }
        // 获取查询参数
        GraphDiagnoseResponse graphDiagnoseResponse = graphDtpMapDiagnose.fastMasterFetch(graphDiagnoseRequest);
        log.debug("快速画像查询结果, traceId={}, graphDiagnoseResponse={}", traceId, graphDiagnoseResponse);
        return graphDiagnoseResponse;
    }


}
