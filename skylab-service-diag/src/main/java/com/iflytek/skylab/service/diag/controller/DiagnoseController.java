package com.iflytek.skylab.service.diag.controller;

import com.google.common.collect.Lists;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseRequest;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.hy.rec.framework.exception.EngineException;
import com.iflytek.skylab.core.constant.MasterFuncEnum;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.data.adapter.diag.DiagParamAdapter;
import com.iflytek.skylab.core.data.adapter.scene.SceneInfoSelector;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.diag.service.DiagnoseService;
import com.iflytek.skylab.service.diag.service.MasteryBusinessService;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.logging.LoggingCost;

import java.util.List;

/**
 * 诊断服务-v1接口
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "画像服务")
@RestController
@RequestMapping("/skylab/api/v1/master")
@EnableSkynetSwagger2
public class DiagnoseController {

    /**
     * 诊断Service
     */
    private final DiagnoseService diagnoseService;

    /**
     * 用户画像 业务逻辑实现
     */
    private final MasteryBusinessService userMasteryService;
    private final DiagParamAdapter diagParamAdapter;

    public DiagnoseController(DiagnoseService diagnoseService, MasteryBusinessService userMasteryService) {
        this.diagnoseService = diagnoseService;
        this.userMasteryService = userMasteryService;
        this.diagParamAdapter = new DiagParamAdapter();
    }

    /**
     * 画像诊断与获取
     * <p>
     * header:
     * parameter:
     * payload:
     *
     * @param apiRequest 请求参数
     * @return DispatchApiResponse 请求响应
     */
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "画像诊断与获取")
    @PostMapping("/diagnose")
    // @SkylineMetric(value = "30.画像诊断与获取", provider = SceneInfoMetricProvider.class)
    @LoggingCost
    public DispatchApiResponse diagnose(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("DiagnoseController#diagnose,DispatchApiRequest= {}", apiRequest);
        }
        String traceId = apiRequest.getTraceId();

        //获取场景信息 对象
        // SceneInfo sceneInfo = apiRequest.getScene(SceneInfo.class);
        SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);
        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }

        if (CollectionUtils.isEmpty(apiRequest.getPayload().getData())){
            throw new ParamNotExistException("Payload.data");
        }

        DispatchApiResponse apiResponse = new DispatchApiResponse();
        try {

            //如果全书地图查询
            if (MasterFuncEnum.MASTER_SNAPSHOOT_FETCH.name().equals(sceneInfo.getFunctionCode())) {
                return diagnoseService.masterSnapshotFetch(traceId, sceneInfo, apiRequest.getPayload().getData().get(0).getData());
            }

            //参数适配
            GraphDiagnoseRequest graphDiagnoseRequest = diagParamAdapter.adapt(traceId, sceneInfo, apiRequest.getPayload());

            //移除 场景信息
            if (apiRequest.getParameter().containsKey(DispatchApiRequest.SCENE_INFO_KEY)) {
                apiRequest.getParameter().remove(DispatchApiRequest.SCENE_INFO_KEY);
            }

            //调用诊断引擎
            GraphDiagnoseResponse diagnoseResponse = diagnoseService.diagnose(traceId, graphDiagnoseRequest, apiRequest.getParameter());

            if (diagnoseResponse == null) {
                log.error("GraphDiagnoseResponse=null, traceId={}", traceId);
                throw new ParamNotExistException("GraphDiagnoseResponse");
            }

            //更新用户画像数据
            List<UserMasteryRecord> records = userMasteryService.storeMasterLogs(traceId, sceneInfo, diagnoseResponse);
            // 数据埋点
            userMasteryService.traceLog(records, diagnoseResponse.getMasterInfoList(), graphDiagnoseRequest.getSessionInfo(), graphDiagnoseRequest.getSceneInfo());

            //转换引擎结果 到画像计算结果
            MasterFetchResult masterFetchResult = diagParamAdapter.adapt(graphDiagnoseRequest.getNodeIds(), diagnoseResponse);
            apiResponse.setPayload(masterFetchResult);
        } catch (EngineException ee) {
            log.error("Call DiagnoseEngine error code= {}; message= {}; failover={}", ee.getErrorCode(), ee.getMessage(), ee.getRedirect());

            ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
            if (ee.getRedirect()) {
                header.setErrorNodes(Lists.newArrayList(new ApiResponseHeader.ErrorNode("failover", "true")));
            }
            apiResponse.setHeader(header);
        }
        apiResponse.setTraceId(traceId);
        if (log.isDebugEnabled()) {
            log.debug("DispatchApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }
}
