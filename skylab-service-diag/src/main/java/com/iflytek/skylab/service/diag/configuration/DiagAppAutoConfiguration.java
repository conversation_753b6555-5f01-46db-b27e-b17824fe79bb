package com.iflytek.skylab.service.diag.configuration;

import com.iflytek.cog2.feaflow.sdk.annotation.EnableSkylabZion;
import com.iflytek.hy.rec.diag.GraphDiagnose;
import com.iflytek.hy.rec.diag.annotation.EnableGraphDiagnose;
import com.iflytek.hy.rec.dtpmapdiag.GraphDtpMapDiagnose;
import com.iflytek.hy.rec.dtpmapdiag.annotation.EnableGraphDtpMapDiagnose;
import com.iflytek.hy.rec.merdiag.annotation.EnableGraphMergeDiagnose;
import com.iflytek.skylab.core.dataapi.annotation.EnableApiException;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataApiRedis;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.service.diag.service.*;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import com.iflytek.skyline.common.EnableSkylineFeign;
import com.iflytek.skyline.resource.annotation.EnableSkylineResource;
import com.iflytek.skyline.resource.manager.ParameterConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetLogging;

/**
 * 诊断服务 自动配置
 *
 * <AUTHOR>
 */
@EnableSkynetLogging
@Configuration(proxyBeanMethods = false)
@EnableApiException
@EnableDataHub
@EnableSkylineBrave
@EnableSkylineFeign
@EnableSkylineResource
@EnableGraphDiagnose
@EnableGraphDtpMapDiagnose
@EnableGraphMergeDiagnose
@EnableSkylabZion
@EnableDataApiRedis
@ComponentScan(basePackages = {
        "com.iflytek.skyline.dispatcher"
})
public class DiagAppAutoConfiguration {

//    @Value("${skylab.front.bookCode-version}")
//    private String bookCodeVersion;

    @Bean
    public DiagnoseService diagnoseService(GraphDiagnose graphDiagnose, ParameterConverter parameterConverter) {
        return new DiagnoseService(graphDiagnose, parameterConverter);
    }

    @Bean
    public DiagnoseSyncOsService diagnoseSyncOsService(GraphDtpMapDiagnose graphDtpMapDiagnose, ParameterConverter parameterConverter, TraceUtils traceUtils) {
        return new DiagnoseSyncOsService(graphDtpMapDiagnose, parameterConverter, traceUtils);
    }

    @Bean
    public MasteryBusinessService masteryBusinessService(TraceUtils traceUtils) {
        return new MasteryBusinessService(traceUtils);
    }

    @Bean
    public PortraitTransferService portraitTransferService() {
        return new PortraitTransferService();
    }

    @Bean
    public DiagGraphCache diagGraphCache() {
        return new DiagGraphCache();
    }

}
