package com.iflytek.skylab.service.diag.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.hy.rec.diag.interfaces.param.MasterInfo;
import com.iflytek.hy.rec.diag.interfaces.param.NodeDetail;
import com.iflytek.hy.rec.diag.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.constant.TraceConstant;
import com.iflytek.skylab.core.data.adapter.mapper.MasterItemMapper;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.MasterData;
import com.iflytek.skylab.core.dataapi.data.MasterItem;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.dataapi.util.MasteryUtil;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.diag.data.MasterUpdateTraceLog;
import com.iflytek.skylab.service.diag.data.UserMasteryRecordExtMapper;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.data.TraceRecordGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.CollectionUtils;
import skynet.boot.logging.LoggingCost;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户画像 相关业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
public class MasteryBusinessService {

    private final CopyOptions copyOptions = CopyOptions.create().ignoreNullValue().ignoreError();

    private final TraceUtils traceUtils;

    public MasteryBusinessService(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }

    /**
     * 更新 用户画像数据
     *
     * @param sceneInfo        场景信息
     * @param diagnoseResponse 引擎返回
     */
    @LoggingCost
    public List<UserMasteryRecord> storeMasterLogs(String traceId, SceneInfo sceneInfo, GraphDiagnoseResponse diagnoseResponse) {
        List<MasterInfo> masterInfoList = diagnoseResponse.getMasterInfoList();
        if (CollectionUtils.isEmpty(masterInfoList)) {
            log.warn("traceId={}; masterInfoList is empty.", traceId);
            return new ArrayList<>();
        }

        //设值 画像更新数据
        List<MasterItem> items = new ArrayList<>();
        for (MasterInfo info : masterInfoList) {
            //判断是否需要更新
            if (info.getNeedUpdate()) {
                //用户画像掌握度Item
                MasterItem masterItem = MasterItemMapper.INSTANCE.toMasterItem(traceId, sceneInfo, info);
                if(!MasteryUtil.validMastery(masterItem)){
//                    log.debug("1.validMastery traceId: {}, sceneInfo: {}, masterItem: {}", traceId, JSON.toJSONString(sceneInfo), JSON.toJSONString(masterItem));
                    continue;
                }

                if (StringUtils.isBlank(masterItem.getBookCode()) || StringUtils.isNotBlank(masterItem.getCatalogId())) {
                    List<String> split = StrUtil.split(masterItem.getCatalogId(), "_");
                    if (split.size() >= 2) {
                        masterItem.setBookCode(split.get(0) + "_" + split.get(1));
                    }
                }
                items.add(masterItem);
            }
        }

        //没有需要更新的用户画像数据
        if (items.isEmpty()) {
            log.debug("本次处理暂无需要更新的用户画像数据，userId={}，traceId={}，", sceneInfo.getUserId(), traceId);
            return new ArrayList<>();
        }

        return getUserMasteryRecords(traceId, sceneInfo, items);
    }

    @Nullable
    private static List<UserMasteryRecord> getUserMasteryRecords(String traceId, SceneInfo sceneInfo, List<MasterItem> items) {
        MasterData masterData = new MasterData().setUserId(sceneInfo.getUserId()).setItems(items);
        if (log.isDebugEnabled()) {
            log.debug("traceId={}; updateMasterData= {}", traceId, masterData);
        }
        List<UserMasteryRecord> updated = DataHub.getMasterService().updateMasterDataAndGet(traceId, masterData);
        if (log.isDebugEnabled()) {
            List<String> ids = Optional.ofNullable(updated).orElse(new ArrayList<>()).stream().map(UserMasteryRecord::getId).collect(Collectors.toList());
            log.debug("traceId={}; updateMasterData.ids= {}", traceId, StrUtil.join(",", ids));
        }
        return updated;
    }

    /**
     * 精准学os
     * <p>
     * 更新 用户画像数据
     *
     * @param sceneInfo        场景信息
     * @param diagnoseResponse 引擎返回
     */
    @LoggingCost
    public void storeMasterLogs(String traceId, SceneInfo sceneInfo, com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseResponse diagnoseResponse) {
        List<com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo> masterInfoList = diagnoseResponse.getMasterInfoList();
        if (CollectionUtils.isEmpty(masterInfoList)) {
            log.warn("traceId={}; masterInfoList is empty.", traceId);
        }

        //设值 画像更新数据
        List<MasterItem> items = new ArrayList<>();
        for (com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo info : masterInfoList) {
            //判断是否需要更新 或者 应学点true
            if (info.getNeedUpdate() || (info.getShouldFlag() != null && info.getShouldFlag())) {
                //用户画像掌握度Item
                MasterItem masterItem = MasterItemMapper.INSTANCE.toMasterItem(traceId, sceneInfo, info);
                if(!MasteryUtil.validMastery(masterItem)){
//                    log.debug("2.validMastery traceId: {}, sceneInfo: {}, masterItem: {}", traceId, JSON.toJSONString(sceneInfo), JSON.toJSONString(masterItem));
                    continue;
                }

                List<String> split = StrUtil.split(masterItem.getCatalogId(), "_");
                if (split.size() >= 2) {
                    masterItem.setBookCode(split.get(0) + "_" + split.get(1));
                }
                items.add(masterItem);
            }
        }

        //没有需要更新的用户画像数据
        if (items.isEmpty()) {
            log.debug("本次处理暂无需要更新的用户画像数据，userId={}，traceId={}，", sceneInfo.getUserId(), traceId);
        }
        MasterData masterData = new MasterData().setUserId(sceneInfo.getUserId()).setItems(items);
        if (log.isDebugEnabled()) {
            log.debug("traceId={}; updateMasterData= {}", traceId, masterData);
        }
        if (!items.isEmpty()) {
            //批量更新
            DataHub.getMasterService().updateMasterData(traceId, masterData);
        }
    }

    public void traceLog(List<UserMasteryRecord> records, List<MasterInfo> masterInfoList, SessionInfo sessionInfo, com.iflytek.hy.rec.domain.model.valueobj.SceneInfo sceneInfo) {
        if (log.isDebugEnabled()) {
            log.debug("traceLogParam records:{}", JSON.toJSONString(records));
            log.debug("traceLogParam masterInfoList:{}", JSON.toJSONString(masterInfoList));
            log.debug("traceLogParam sessionInfo:{}", JSON.toJSONString(sessionInfo));
            log.debug("traceLogParam sceneInfo:{}", JSON.toJSONString(sceneInfo));
        }

        if (CollUtil.isEmpty(records) || CollUtil.isEmpty(masterInfoList)) {
            return;
        }

        MasterUpdateTraceLog traceLog = new MasterUpdateTraceLog();
        // 组装数据
        try {
            String userId = sceneInfo.getUserId();
            String studyCode = sceneInfo.getStudyCode();

            // 筛选数据
            Map<String, Boolean> map = new HashMap<>();
            for (MasterInfo masterInfo : masterInfoList) {
                String uniqueKey = String.join("_", userId, studyCode, masterInfo.getNodeId(), masterInfo.getCatalogId());
                Optional.ofNullable(masterInfo.getNodeDetail()).map(NodeDetail::getIsGreenByDynamicPortrait).ifPresent(val -> map.put(uniqueKey, val));
            }

            List<MasterUpdateTraceLog.UserMasteryRecordExt> exts = BeanUtil.copyToList(records, MasterUpdateTraceLog.UserMasteryRecordExt.class, copyOptions);
            exts.forEach(ext -> {
                String uniqueKey = String.join("_", userId, studyCode, ext.getNodeId(), ext.getCatalogId());
                ext.setIsGreenByDynamicPortrait(map.getOrDefault(uniqueKey, null));
            });
            traceLog.setRecords(exts).setSessionInfo(sessionInfo).setSceneInfo(sceneInfo);
        } catch (Exception e) {
            StringBuffer stack = new StringBuffer();
            for (int i = 0; i < Math.min(e.getStackTrace().length - 1, 10); i++) {
                stack.append(e.getStackTrace()[i].toString()).append(";");
            }

            log.error("组装埋点数据 {} 异常!, message:{}, stack:{}", MasterUpdateTraceLog.TYPE, e.getMessage(), stack);
            return;
        }

        // 发送数据
        try {
            String userId = sceneInfo.getUserId();
            if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
                //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
            }else {
                traceUtils.record(TraceRecordGroup.FLOW, MasterUpdateTraceLog.TYPE, traceLog);
            }
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("MasterUpdateTraceLog:{}", traceLog);
            }
            log.error("MasterUpdateTraceLog埋点日志记录异常，message={}", e.getMessage());
        }
    }


    public void traceLog(List<com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo> masterInfoList, com.iflytek.hy.rec.dtpmapdiag.interfaces.param.SessionInfo sessionInfo, com.iflytek.hy.rec.domain.model.valueobj.SceneInfo sceneInfo) {
        if (log.isDebugEnabled()) {
//            log.debug("traceLogParam records:{}", JSON.toJSONString(masterData));
            log.debug("traceLogParam masterInfoList:{}", JSON.toJSONString(masterInfoList));
            log.debug("traceLogParam sessionInfo:{}", JSON.toJSONString(sessionInfo));
            log.debug("traceLogParam sceneInfo:{}", JSON.toJSONString(sceneInfo));
        }

        if (CollUtil.isEmpty(masterInfoList)) {
            return;
        }

        MasterUpdateTraceLog traceLog = new MasterUpdateTraceLog();
        // 组装数据
        try {
            List<MasterUpdateTraceLog.UserMasteryRecordExt> exts = new ArrayList<>();

            for (com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo info : masterInfoList) {
                //判断是否需要更新
                if (info.getNeedUpdate()) {
                    //用户画像掌握度Item
                    MasterUpdateTraceLog.UserMasteryRecordExt recordExt = UserMasteryRecordExtMapper.INSTANCE.toUserMasteryRecordExt(sessionInfo.getTraceId(), sceneInfo, info);
                    List<String> split = StrUtil.split(recordExt.getCatalogId(), "_");
                    if (split.size() >= 2) {
                        recordExt.setBookCode(split.get(0) + "_" + split.get(1));
                    }
                    exts.add(recordExt);
                }
            }
            if (CollUtil.isEmpty(exts)) {
                return;
            }
            traceLog.setRecords(exts).setSessionInfo(sessionInfo).setSceneInfo(sceneInfo);
        } catch (Exception e) {
            StringBuffer stack = new StringBuffer();
            for (int i = 0; i < Math.min(e.getStackTrace().length - 1, 10); i++) {
                stack.append(e.getStackTrace()[i].toString()).append(";");
            }
            log.error("组装埋点数据 {} 异常!, message:{}, stack:{}", MasterUpdateTraceLog.TYPE, e.getMessage(), stack);
            return;
        }
        // 发送数据
        try {
            String userId = sceneInfo.getUserId();
            if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
                //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
            }else {
                traceUtils.record(TraceRecordGroup.FLOW, MasterUpdateTraceLog.TYPE, traceLog);
            }
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("MasterUpdateTraceLog:{}", traceLog);
            }
            log.error("MasterUpdateTraceLog埋点日志记录异常，message={}", e.getMessage());
        }
    }
}
