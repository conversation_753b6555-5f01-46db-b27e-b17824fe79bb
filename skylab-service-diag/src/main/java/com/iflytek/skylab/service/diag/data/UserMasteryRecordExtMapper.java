package com.iflytek.skylab.service.diag.data;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 日志埋点
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/15 14:49
 */
@Mapper
public interface UserMasteryRecordExtMapper {

    UserMasteryRecordExtMapper INSTANCE = Mappers.getMapper(UserMasteryRecordExtMapper.class);


    /**
     * 转换引擎返回画像数据
     *
     * @param traceId   跟踪ID
     * @param sceneInfo 场景信息
     * @param info      引擎返回用户画像计算
     * @return 用户画像掌握度Item
     */
    @Mapping(source = "sceneInfo.userId", target = "userId")
    @Mapping(source = "sceneInfo.bizCode", target = "bizCode")
    @Mapping(source = "sceneInfo.studyCode", target = "studyCode")
    @Mapping(source = "sceneInfo.subjectCode", target = "subjectCode")
    @Mapping(source = "sceneInfo.phaseCode", target = "phaseCode")
    @Mapping(source = "sceneInfo.bookCode", target = "bookCode")
    @Mapping(source = "info.catalogId", target = "catalogId")
    @Mapping(source = "info.catalogType", target = "catalogType")
    @Mapping(source = "info.nodeId", target = "nodeId")
    @Mapping(source = "info.nodeType", target = "nodeType")
    @Mapping(source = "info.masterScore", target = "masteryScore")
    @Mapping(source = "info.masterType.code", target = "masteryType")
    @Mapping(source = "info.shouldFlag", target = "shouldFlag")
    @Mapping(source = "info.masterDetailInfo.fusionMasterScore", target = "fusion")
    @Mapping(source = "info.masterDetailInfo.realMasterScore", target = "real")
    @Mapping(source = "info.masterDetailInfo.predictMasterScore", target = "predict")
    @Mapping(source = "info.masterDetailInfo.algorithmFusionMasterScore", target = "algoFusion")
    @Mapping(source = "info.masterDetailInfo.algorithmRealMasterScore", target = "algoReal")
    @Mapping(source = "info.masterDetailInfo.algorithmPredictMasterScore", target = "algoPredict")

    @Mapping(source = "info.nodeDetail.isGreenByDynamicPortrait", target = "isGreenByDynamicPortrait")
    @Mapping(source = "info.nodeDetail.beforeAlgorithmFusionMasterScore", target = "beforeAlgorithmFusionMasterScore")
    @Mapping(source = "info.nodeDetail.associativePoint", target = "associativePoint")
    MasterUpdateTraceLog.UserMasteryRecordExt toUserMasteryRecordExt(String traceId, com.iflytek.hy.rec.domain.model.valueobj.SceneInfo sceneInfo, com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo info);
}
