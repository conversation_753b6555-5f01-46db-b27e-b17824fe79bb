package com.iflytek.skylab.service.diag.modules;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.base.Stopwatch;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.hy.rec.domain.model.valueobj.EngineConfigItem;
import com.iflytek.hy.rec.framework.exception.ErrorEnum;
import com.iflytek.hy.rec.merdiag.GraphMergeDiagnose;
import com.iflytek.hy.rec.merdiag.interfaces.param.GraphSingleDiganoseRequest;
import com.iflytek.skylab.core.data.adapter.diag.DiagSingleParamAdapter;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import com.iflytek.skyline.dispatcher.core.DispatchSession;
import com.iflytek.skyline.dispatcher.core.Executable;
import com.iflytek.skyline.resource.domain.ConfigItem;
import com.iflytek.skyline.resource.domain.ResourceItem;
import com.iflytek.skyline.resource.domain.ResourcePackage;
import com.iflytek.skyline.resource.manager.ParameterConverter;
import com.iflytek.skyline.resource.manager.ResourceManager;
import com.iflytek.skyline.resource.service.ResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class DiagnoseSingle implements Executable {

    private final DiagSingleParamAdapter diagSingleParamAdapter;
    private final ResourceManager<List<String>> resourceManager;
    /**
     * 诊断引擎入口
     */
    private final GraphMergeDiagnose graphMergeDiagnose;

    /**
     * 资源配置转换器
     */
    private final ParameterConverter parameterConverter;

    private final static String BOOK_CONFIG_KEY = "BookIds";

    private final static String NODE_IDS = "nodeIds";

    public DiagnoseSingle(ResourceService resourceService, GraphMergeDiagnose graphMergeDiagnose, ParameterConverter parameterConverter) {
        this.diagSingleParamAdapter = new DiagSingleParamAdapter();
        this.parameterConverter = parameterConverter;
        this.graphMergeDiagnose = graphMergeDiagnose;
        this.resourceManager = new ResourceManager<List<String>>(resourceService) {
            @Override
            protected List<String> convert(String configContent) {
                return JSON.parseObject(configContent, new TypeReference<List<String>>() {
                });
            }
        };
    }


    @Override
    public DispatchApiResponse execute(String traceId, DispatchApiRequest apiRequest, DispatchSession session) {
        if (log.isDebugEnabled()) {
            log.debug("DispatchApiRequest= {}", apiRequest);
        }


        //获取场景信息 对象
        AiDiagSceneInfo aiDiagSceneInfo = apiRequest.getScene(AiDiagSceneInfo.class);

        GraphSingleDiganoseRequest diganoseSingleRequest = diagSingleParamAdapter.adapt(traceId, aiDiagSceneInfo, apiRequest.getPayload());
        DispatchApiResponse apiResponse = new DispatchApiResponse();
        try {
            // region  参数校验
            JSONObject bookIdsConfig = apiRequest.getParameter().getJSONObject(BOOK_CONFIG_KEY);
            if (bookIdsConfig == null) {
                log.error("Call DiagnoseSingle error code= {}; message= {}", ErrorEnum.REQUEST_VALUE_INVALIDE.getCode(), "parameter." + BOOK_CONFIG_KEY + " of ApiRequest");
                throw new Exception();
            }
            //获取node
            List<String> nodeIds = apiRequest.getParameter().getList(NODE_IDS, String.class);
            //获取bookIds
            ResourceItem resourceItem = bookIdsConfig.toJavaObject(ResourceItem.class);
            if (log.isDebugEnabled()) {
                log.debug("ResourceItem= {}", resourceItem);
            }
            List<String> bookIds = resourceManager.getParam(resourceItem.getId(), resourceItem.getVersion());
            if (log.isDebugEnabled()) {
                log.debug("bookIds= {}", bookIds);
            }
            //移除 场景信息
            if (apiRequest.getParameter().containsKey(DispatchApiRequest.SCENE_INFO_KEY)) {
                apiRequest.getParameter().remove(DispatchApiRequest.SCENE_INFO_KEY);
            }
            //移除 bookIds
            if (apiRequest.getParameter().containsKey(BOOK_CONFIG_KEY)) {
                apiRequest.getParameter().remove(BOOK_CONFIG_KEY);
            }
            //移除 nodeIds
            if (apiRequest.getParameter().containsKey(NODE_IDS)) {
                apiRequest.getParameter().remove(NODE_IDS);
            }
            diganoseSingleRequest.setBookIds(bookIds);
            diganoseSingleRequest.setNodeIds(nodeIds);
            //加载配置
            //参数配置管理
            ResourcePackage resourcePackage = this.parameterConverter.convertPackage(apiRequest.getParameter());
            if (log.isTraceEnabled()) {
                log.trace("realConfig= {}", resourcePackage);
            }
            if (log.isDebugEnabled()) {
                log.debug("StrategyId= {}", resourcePackage.getMd5());
            }
            List<EngineConfigItem> confItems = new ArrayList<>();
            for (ConfigItem item : resourcePackage.getItems()) {
                confItems.add(new EngineConfigItem().setKey(item.getKey()).setConfig(item.getContent()).setMd5(item.getMd5()));
            }

            //stopwatch
            Stopwatch stopwatch = Stopwatch.createStarted();
            //加载配置
            this.graphMergeDiagnose.loadConfig(resourcePackage.getMd5(), confItems);
            log.debug("the DiagnoseSingle loadConfig cost= {}", stopwatch);

            //设置StrategyId
            diganoseSingleRequest.getSessionInfo().setStrategyId(resourcePackage.getMd5());

            stopwatch.reset().start();
            if (log.isDebugEnabled()) {
                log.debug("GraphDiagnoseRequest={}", diganoseSingleRequest);
            }
            GraphDiagnoseResponse response = graphMergeDiagnose.singleDiagnose(diganoseSingleRequest);
            if (log.isDebugEnabled()) {
                log.debug("GraphDiagnoseResponse={}", response);
            }
            ExecuteChain executeChain = new ExecuteChain();
            executeChain.setGraphDiagnoseResponse(response);
            executeChain.setSceneInfo(aiDiagSceneInfo);
            executeChain.setNodeIds(nodeIds);
            apiResponse.setPayload(executeChain);
        } catch (Exception e) {
            log.error("Call DiagnoseSingle error code= {}; message= {}", ErrorEnum.EXCEPTION.getCode(), ErrorEnum.EXCEPTION.getMessage());
        }
        apiRequest.setTraceId(traceId);
        if (log.isDebugEnabled()) {
            log.debug("DispatchApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }

}
