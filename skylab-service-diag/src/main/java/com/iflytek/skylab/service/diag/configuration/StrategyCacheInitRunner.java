package com.iflytek.skylab.service.diag.configuration;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.hy.rec.diag.GraphDiagnose;
import com.iflytek.hy.rec.domain.model.valueobj.EngineConfigItem;
import com.iflytek.hy.rec.dtpmapdiag.GraphDtpMapDiagnose;
import com.iflytek.hy.rec.framework.EngineConfigLoader;
import com.iflytek.hy.rec.merdiag.GraphMergeDiagnose;
import com.iflytek.skylab.core.dataapi.service.SkylineConfigService;
import com.iflytek.skyline.resource.domain.ConfigItem;
import com.iflytek.skyline.resource.domain.ResourcePackage;
import com.iflytek.skyline.resource.manager.ParameterConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.server.WebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 应用启动后 自动加载引擎的内部配置
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StrategyCacheInitRunner implements WebServerFactoryCustomizer {
    @Value("${spring.application.name}")
    private String springApplicationName;

    private final ParameterConverter parameterConverter;
    private final GraphDiagnose graphDiagnose;
    private final GraphMergeDiagnose graphMergeDiagnose;

    private final GraphDtpMapDiagnose graphDtpMapDiagnose;
    private final SkylineConfigService skylineConfigService;

    public StrategyCacheInitRunner(SkylineConfigService skylineConfigService, ParameterConverter parameterConverter, GraphDiagnose graphDiagnose, GraphMergeDiagnose graphMergeDiagnose, GraphDtpMapDiagnose graphDtpMapDiagnose) {
        this.skylineConfigService = skylineConfigService;
        this.parameterConverter = parameterConverter;
        this.graphDiagnose = graphDiagnose;
        this.graphMergeDiagnose = graphMergeDiagnose;
        this.graphDtpMapDiagnose = graphDtpMapDiagnose;
    }

    @Override
    public void customize(WebServerFactory factory) {
        //启动后，主动加载引擎相关的策略配置，预热引擎缓存
        //数据句柄 获取服务对应 策略id&配置
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        List<JSONObject> strategyParameters = skylineConfigService.queryAllConfigs(springApplicationName);
        int size = strategyParameters.size();
        if (size == 0) {
            log.info("未获取[{}]服务的资源配置.", springApplicationName);
            return;
        }
        //异步预热策略配置数据
        log.info("异步预加载策略配置数据 size={} ...", size);

        AtomicInteger index = new AtomicInteger();

        List<EngineConfigLoader> engineConfigLoaders = Lists.newArrayList(graphDiagnose);
        strategyParameters.forEach((jsonObject -> {
            List<EngineConfigItem> confItems = new ArrayList<>();
            try {
                //参数配置管理
                ResourcePackage resourcePackage = this.parameterConverter.convertPackage(jsonObject);
                for (ConfigItem item : resourcePackage.getItems()) {
                    confItems.add(new EngineConfigItem().setKey(item.getKey()).setConfig(item.getContent()).setMd5(item.getMd5()));
                }
                log.info("正在预加载 StrategyId= {} 资源配置...", resourcePackage.getMd5());

                StringBuilder builder = new StringBuilder();
                builder.append(StrUtil.format("预加载 ResourcePackage md5={}, List<ConfigItem> size={}", resourcePackage.getMd5(), resourcePackage.getItems().size()));
                for (int i = 0; i < resourcePackage.getItems().size(); i++) {
                    ConfigItem item = resourcePackage.getItems().get(i);
                    builder.append("|").append(StrUtil.format("get({}): key={}, md5={}", i, item.getKey(), item.getMd5()));
                }
                log.info(builder.toString());

                loadConfig(engineConfigLoaders, resourcePackage.getMd5(), confItems);

                log.info("完成预加载 StrategyId= {} 资源配置[{}/{}].", resourcePackage.getMd5(), index.incrementAndGet(), size);
            } catch (Exception e) {
                log.error(String.format("预加载 失败 error. config=%s", jsonObject), e);
            }
        }));
        log.info("完成 {} /{} 个 策略配置预加载.[cost= {}] -----------------------。", index.get(), size, stopWatch);
    }

    void loadConfig(List<EngineConfigLoader> engineConfigLoaders, String md5, List<EngineConfigItem> confItems) {
        engineConfigLoaders.forEach(engineConfigLoader -> engineConfigLoader.loadConfig(md5, confItems));
    }

}

