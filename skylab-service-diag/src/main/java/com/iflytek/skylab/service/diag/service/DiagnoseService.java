package com.iflytek.skylab.service.diag.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Stopwatch;
import com.iflytek.hy.rec.diag.GraphDiagnose;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseRequest;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.hy.rec.domain.model.valueobj.EngineConfigItem;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.MasterData;
import com.iflytek.skylab.core.dataapi.data.MasterItem;
import com.iflytek.skylab.core.dataapi.data.MasterQuery;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.domain.MasterInfo;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import com.iflytek.skyline.resource.domain.ConfigItem;
import com.iflytek.skyline.resource.domain.ResourcePackage;
import com.iflytek.skyline.resource.manager.ParameterConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import skynet.boot.logging.LoggingCost;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 诊断Service
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
public class DiagnoseService {

    /**
     * 诊断引擎入口
     */
    private final GraphDiagnose graphDiagnose;

    /**
     * 资源配置转换器
     */
    private final ParameterConverter parameterConverter;

    public DiagnoseService(GraphDiagnose graphDiagnose, ParameterConverter parameterConverter) {
        this.graphDiagnose = graphDiagnose;
        this.parameterConverter = parameterConverter;
    }

    /**
     * 调用诊断引擎 进行 画像计算
     *
     * @param traceId          跟踪Id
     * @param diagRequest      引擎入参
     * @param configJsonObject 引擎配置
     * @return GraphDiagnoseResponse 诊断返回
     */
    @LoggingCost
    public GraphDiagnoseResponse diagnose(@Valid @NotBlank String traceId, @Valid @NotNull GraphDiagnoseRequest diagRequest, @Valid @NotNull JSONObject configJsonObject) throws Exception {
        if (log.isTraceEnabled()) {
            log.trace("configJsonObject= {};", configJsonObject);
        }

        Assert.hasText(traceId, "The traceId must not blank.");
        Assert.notNull(diagRequest, "The GraphDiagnoseRequest must not null.");
        Assert.notNull(configJsonObject, "The configJsonObject must not null.");

        //参数配置管理
        ResourcePackage resourcePackage = this.parameterConverter.convertPackage(configJsonObject);
        if (log.isTraceEnabled()) {
            log.trace("realConfig= {}", resourcePackage);
        }
        log.debug("StrategyId= {}", resourcePackage.getMd5());
        List<EngineConfigItem> confItems = new ArrayList<>();
        for (ConfigItem item : resourcePackage.getItems()) {
            confItems.add(new EngineConfigItem().setKey(item.getKey()).setConfig(item.getContent()).setMd5(item.getMd5()));
        }

        //stopwatch
        Stopwatch stopwatch = Stopwatch.createStarted();
        //加载配置
        this.graphDiagnose.loadConfig(resourcePackage.getMd5(), confItems);
        log.debug("the graphDiagnose loadConfig cost= {}", stopwatch);

        //设置StrategyId
        diagRequest.getSessionInfo().setStrategyId(resourcePackage.getMd5());

        stopwatch.reset().start();
        GraphDiagnoseResponse response = graphDiagnose.diagnose(diagRequest);
        log.debug("the graphDiagnose diagnose cost= {}", stopwatch);

        if (log.isDebugEnabled()) {
            log.debug("GraphDiagnoseResponse:Json= {}", JSON.toJSONString(response));
        }
        return response;
    }

    /**
     * 点画像查询
     *
     * @param traceId     跟踪Id
     * @param diagRequest 引擎入参
     * @return GraphDiagnoseResponse 诊断返回
     */
    @LoggingCost
    public MasterFetchResult nodeQuery(@Valid @NotBlank String traceId, @Valid @NotNull SceneInfo sceneInfo, @Valid @NotNull GraphDiagnoseRequest diagRequest) throws Exception {

        Assert.hasText(traceId, "The traceId must not blank.");
        Assert.notNull(diagRequest, "The GraphDiagnoseRequest must not null.");
        Assert.isTrue(sceneInfo instanceof AiDiagSceneInfo, "the SceneInfo must be instanceof AiDiagSceneInfo.");
        Stopwatch stopwatch = Stopwatch.createStarted();
        stopwatch.reset().start();
        AiDiagSceneInfo aiDiagSceneInfo = (AiDiagSceneInfo) sceneInfo;
        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId(diagRequest.getSceneInfo().getUserId());
        masterQuery.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        masterQuery.setNodeIdList(diagRequest.getNodeIds());
        MasterData masterData = DataHub.getMasterService().queryMasterData(traceId, masterQuery);
        List<MasterItem> items = masterData.getItems();
        Map<String, String> nodeCatalogMap = aiDiagSceneInfo.getNodeCatalogMap();

        //画像去重,优先去更新时间最新
        Map<String, MasterItem> map = new HashMap<>();
        for (MasterItem item : items) {
            if (!CharSequenceUtil.equals(nodeCatalogMap.get(item.getNodeId()), item.getCatalogId())) {
                continue;
            }
            if (map.containsKey(item.getNodeId())) {
                MasterItem last = map.get(item.getNodeId());
                Instant lastUpdateTime = last.getUpdateTime();
                Instant itemUpdateTime = item.getUpdateTime();
                if (lastUpdateTime != null && itemUpdateTime != null && lastUpdateTime.isBefore(itemUpdateTime)) {
                    map.put(item.getNodeId(), item);
                }
            } else {
                map.put(item.getNodeId(), item);
            }
        }

        List<MasterInfo> masterInfos = BeanUtil.copyToList(map.values(), MasterInfo.class);
        for (MasterInfo info : masterInfos) {
            MasterItem masterItem = map.get(info.getNodeId());
            if (masterItem != null) {
                info.setComputeType(masterItem.getMasteryType());
            }
        }
        MasterFetchResult masterFetchResult = new MasterFetchResult();
        masterFetchResult.setMasterInfos(masterInfos);
        log.debug("the graphDiagnose nodeQuery cost= {}", stopwatch);

        if (log.isDebugEnabled()) {
            log.debug("masterFetchResult:Json= {}", JSON.toJSONString(masterFetchResult));
        }
        return masterFetchResult;
    }

    /**
     * 画像快照查询
     *
     * @param traceId   跟踪Id
     * @param sceneInfo
     * @return masterFetch5BookParam 诊断返回
     */
    @LoggingCost
    public DispatchApiResponse masterSnapshotFetch(@Valid @NotBlank String traceId, @Valid @NotNull SceneInfo sceneInfo, @Valid @NotNull JSONObject masterFetch5BookParam) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        stopwatch.reset().start();

        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId(sceneInfo.getUserId());
        String bookCode = masterFetch5BookParam.getString("bookCode");


        //待查询章节
        Map<String, Set<String>> cataPoints = new HashMap<>();
        List<String> catalogIds = masterFetch5BookParam.getList("catalogIds", String.class);
        if (CollUtil.isEmpty(catalogIds)) {
            //查询书本多有章
            catalogIds = DiagGraphCache.getAllUnit(sceneInfo.getGraphVersion(), bookCode);
        }

        //通过章获取所有点
        Set<String> pointSet = new HashSet<>();
        for (String catalogId : catalogIds) {
            Set<String> points = DiagGraphCache.getUnitToPointCache(sceneInfo.getGraphVersion(), catalogId);
            cataPoints.put(catalogId, points);
            pointSet.addAll(points);
        }
        masterQuery.setNodeIdList(new ArrayList<>(pointSet));
        //限制点类型
        List<String> expPointTypes = masterFetch5BookParam.getList("expPointTypes", String.class);
        if (CollUtil.isNotEmpty(expPointTypes)) {
            List<NodeTypeEnum> nodeTypeEnums = expPointTypes.stream().map(NodeTypeEnum::parse).collect(Collectors.toList());
            masterQuery.setNodeTypes(nodeTypeEnums);
        }

        if(log.isDebugEnabled()){
            log.debug("画像快照查询 - 快照查询条件， {}", masterQuery.toString());
        }

        //画像查询
        List<UserMasteryRecord> userMasteryRecords = DataHub.getMasterService().queryMasterDataBatch(traceId, masterQuery);
        MasterFetchResult masterFetchResult = new MasterFetchResult();
        List<MasterInfo> masterInfos = new ArrayList<>();

        if(log.isDebugEnabled()){
            log.debug("画像快照查询 - 快照查询条数， {}", userMasteryRecords.size());
        }

        //点画像
        Map<String, UserMasteryRecord> nodeUserMasteryRecord = new HashMap<>();
        for (UserMasteryRecord userMasteryRecord : userMasteryRecords) {
            nodeUserMasteryRecord.put(userMasteryRecord.getNodeId(), userMasteryRecord);
        }

        //区域复习点
        Set<String> areaToReviewPointToCache = DiagGraphCache.getAreaToReviewPointToCache(sceneInfo.getGraphVersion(), sceneInfo.getAreaCode());

        for (Map.Entry<String, Set<String>> entry : cataPoints.entrySet()) {

            String catalogId = entry.getKey();
            Set<String> nodeIds = entry.getValue();

            for (String nodeId : nodeIds) {
                //获取点画像
                UserMasteryRecord userMasteryRecord = nodeUserMasteryRecord.get(nodeId);
                if (userMasteryRecord == null) {
                    continue;
                }
                //复习点 区域过滤
                if (NodeTypeEnum.REVIEW_POINT.equals(userMasteryRecord.getNodeType()) && !areaToReviewPointToCache.contains(userMasteryRecord.getNodeId())) {
                    continue;
                }
                MasterInfo info = new MasterInfo();
                BeanUtil.copyProperties(userMasteryRecord, info);
                info.setCatalogId(catalogId);
                info.setComputeType(userMasteryRecord.getMasteryType());
                masterInfos.add(info);
            }
        }

        if(log.isDebugEnabled()){
            log.debug("画像快照查询 - 过滤后快照查询条数， {}", masterInfos.size());
        }

        masterFetchResult.setMasterInfos(masterInfos);
        DispatchApiResponse apiResponse = new DispatchApiResponse();
        apiResponse.setPayload(masterFetchResult);
        apiResponse.setTraceId(traceId);
        return apiResponse;
    }

}
