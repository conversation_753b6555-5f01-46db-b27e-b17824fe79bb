package com.iflytek.skylab.service.diag.controller;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseRequest;
import com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.hy.rec.framework.exception.EngineException;
import com.iflytek.skylab.core.constant.MasterFuncEnum;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.data.adapter.diag.DiagSyncOsParamAdapter;
import com.iflytek.skylab.core.data.adapter.scene.SceneInfoSelector;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.diag.service.DiagnoseSyncOsService;
import com.iflytek.skylab.service.diag.service.MasteryBusinessService;
import com.iflytek.skylab.service.diag.service.PortraitTransferService;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamInvalidException;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.logging.LoggingCost;

/**
 * 诊断服务-v2接口
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "画像服务-精准学os")
@RestController
@RequestMapping("/skylab/api/v2/master")
@EnableSkynetSwagger2
public class DiagnoseSyncOsController {

    /**
     * 诊断Service
     */
    private final DiagnoseSyncOsService diagnoseSyncOsService;

    /**
     * 用户画像 业务逻辑实现
     */
    private final MasteryBusinessService userMasteryService;
    private final DiagSyncOsParamAdapter diagSyncOsParamAdapter;

    /**
     * 数据迁移Service
     */
    private final PortraitTransferService portraitTransferService;

    public DiagnoseSyncOsController(DiagnoseSyncOsService diagnoseService, MasteryBusinessService userMasteryService, PortraitTransferService portraitTransferService) {
        this.diagnoseSyncOsService = diagnoseService;
        this.portraitTransferService = portraitTransferService;
        this.userMasteryService = userMasteryService;
        this.diagSyncOsParamAdapter = new DiagSyncOsParamAdapter();
    }

    /**
     * 精准学OS
     * 画像诊断与获取
     * <p>
     * header:
     * parameter:
     * payload:
     *
     * @param apiRequest 请求参数
     * @return DispatchApiResponse 请求响应
     */
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "画像诊断与获取")
    @PostMapping("/diagnose")
    @LoggingCost
    public DispatchApiResponse diagnose(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("DiagnoseSyncOsController#diagnose,DispatchApiRequest= {}", apiRequest);
        }
        String traceId = apiRequest.getTraceId();

        //获取场景信息 对象
        SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);
        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }

        DispatchApiResponse apiResponse = new DispatchApiResponse();

        JSONObject params = diagSyncOsParamAdapter.adaptParamsObj(apiRequest.getPayload());
        if (params == null) {
            throw new ParamInvalidException("payload.MasterFetchParam");
        }
        try {
            // 用户画像迁移
//            portraitTransferService.portraitTrans(sceneInfo, traceId);
            //如果全书地图查询
            if (MasterFuncEnum.MASTER_SNAPSHOOT_FETCH.name().equals(sceneInfo.getFunctionCode())) {
                return diagnoseSyncOsService.masterSnapshootFetch(traceId, sceneInfo, params);
            }


            //参数适配
//            GraphDiagnoseRequest graphDiagnoseRequest = diagSyncOsParamAdapter.adapt(traceId, sceneInfo, apiRequest.getPayload());
            GraphDiagnoseRequest graphDiagnoseRequest = diagSyncOsParamAdapter.adapt(traceId, sceneInfo, params);

            //移除 场景信息
            if (apiRequest.getParameter().containsKey(DispatchApiRequest.SCENE_INFO_KEY)) {
                apiRequest.getParameter().remove(DispatchApiRequest.SCENE_INFO_KEY);
            }

            //快速画像查询（点画像或者目录画像）
            if (MasterFuncEnum.FAST_MASTER_FETCH.name().equals(sceneInfo.getFunctionCode())) {
                GraphDiagnoseResponse graphDiagnoseResponse = diagnoseSyncOsService.fastMasterFetch(traceId, graphDiagnoseRequest);
                MasterFetchResult masterFetchResult = diagSyncOsParamAdapter.adapt(graphDiagnoseRequest.getNodeIds(), params, graphDiagnoseResponse);
                apiResponse.setPayload(masterFetchResult);
                return apiResponse;

            }

            //调用诊断引擎
            GraphDiagnoseResponse diagnoseResponse = diagnoseSyncOsService.diagnose(traceId, graphDiagnoseRequest, apiRequest.getParameter());

            if (diagnoseResponse == null) {
                log.error("GraphDiagnoseResponse=null, traceId={}", traceId);
                throw new ParamNotExistException("GraphDiagnoseResponse");
            }

            //更新用户画像数据
            userMasteryService.storeMasterLogs(traceId, sceneInfo, diagnoseResponse);
            // 数据埋点
            userMasteryService.traceLog(diagnoseResponse.getMasterInfoList(), graphDiagnoseRequest.getSessionInfo(), graphDiagnoseRequest.getSceneInfo());

            //转换引擎结果 到画像计算结果
            MasterFetchResult masterFetchResult = diagSyncOsParamAdapter.adapt(graphDiagnoseRequest.getNodeIds(), params, diagnoseResponse);
            apiResponse.setPayload(masterFetchResult);
        } catch (EngineException ee) {
            log.error("Call DiagnoseEngine error code= {}; message= {}; failover={}", ee.getErrorCode(), ee.getMessage(), ee.getRedirect());

            ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
            if (ee.getRedirect()) {
                header.setErrorNodes(Lists.newArrayList(new ApiResponseHeader.ErrorNode("failover", "true")));
            }
            apiResponse.setHeader(header);
        }
        apiResponse.setTraceId(traceId);
        if (log.isDebugEnabled()) {
            log.debug("DispatchApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }
}
