package com.iflytek.skylab.service.diag.controller;

import com.iflytek.hy.rec.framework.exception.ErrorEnum;
import com.iflytek.skylab.core.data.adapter.diag.DiagMergeParamAdapter;
import com.iflytek.skylab.core.data.adapter.scene.SceneInfoSelector;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import com.iflytek.skyline.dispatcher.dispatch.SpringBeanDispatcher;
import com.iflytek.skyline.dispatcher.domain.ServiceProcessBean;
import com.iflytek.skyline.resource.domain.ResourceItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/20 14:52
 */
@Slf4j
@Api(tags = "画像融合服务")
@RestController
@RequestMapping("/skylab/api/v1/merge")
@EnableSkynetSwagger2
public class DiagnoseMergeController {


    @Autowired
    private SpringBeanDispatcher springBeanDispatcher;


    private final DiagMergeParamAdapter diagMergeParamAdapter;

    public DiagnoseMergeController() {
        this.diagMergeParamAdapter = new DiagMergeParamAdapter();
    }

    /**
     * 画像诊断与获取
     * <p>
     * header:
     * parameter:
     * payload:
     *
     * @param dispatchApiRequest 请求参数
     * @return DispatchApiResponse 请求响应
     */
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "画像合并诊断")
    @PostMapping("/diagnose")
    // @SkylineMetric(value = "30.画像诊断与获取", provider = SceneInfoMetricProvider.class)
    public DispatchApiResponse diagnose(@RequestBody DispatchApiRequest dispatchApiRequest) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("DiagnoseMergeController#diagnose,DispatchApiRequest= {}", dispatchApiRequest);
        }
        String traceId = dispatchApiRequest.getTraceId();
        //获取场景信息 对象
        SceneInfo sceneInfo = SceneInfoSelector.select(dispatchApiRequest);
        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }
        DispatchApiResponse dispatchApiResponse = new DispatchApiResponse();
        try {
            //本地调度
            ServiceProcessBean process = dispatchApiRequest.getParameter().getJSONObject("process").toJavaObject(ServiceProcessBean.class);
            ResourceItem resourceItem = new ResourceItem();
            resourceItem.setId(process.getResourceId());
            resourceItem.setVersion(process.getResourceVersion());
            boolean debug = dispatchApiRequest.getParameter().getBooleanValue("debug", false);
            //移除复杂嵌套
            ApiRequest apiRequest = new ApiRequest();
            apiRequest.setHeader(dispatchApiRequest.getHeader());
            List<String> nodeIds = diagMergeParamAdapter.adapt(dispatchApiRequest.getPayload());
            //设置入参
            apiRequest.setObjectPayload(diagMergeParamAdapter.adaptParamObj(dispatchApiRequest.getPayload()));
            apiRequest.setParameter(dispatchApiRequest.getParameter().fluentPut("nodeIds", nodeIds));

            //本地调度
            ApiResponse apiResponse = springBeanDispatcher.dispatch(resourceItem, apiRequest, debug);
            if (log.isDebugEnabled()) {
                log.debug("DiagnoseMergeController#diagnose,ApiResponse= {}", apiResponse);
            }
            dispatchApiResponse.setHeader(apiResponse.getHeader());
            dispatchApiResponse.setPayload(apiResponse.getPayload());
        } catch (Exception ee) {
            log.error("Call DiagnoseEngine error message= {}", ee.getMessage());

            ApiResponseHeader header = new ApiResponseHeader(ErrorEnum.EXCEPTION.getCode(), ErrorEnum.EXCEPTION.getMessage());
            dispatchApiResponse.setHeader(header);
        }
        dispatchApiResponse.setTraceId(traceId);
        if (log.isDebugEnabled()) {
            log.debug("DispatchApiResponse= {}", dispatchApiResponse);
        }
        return dispatchApiResponse;
    }
}
