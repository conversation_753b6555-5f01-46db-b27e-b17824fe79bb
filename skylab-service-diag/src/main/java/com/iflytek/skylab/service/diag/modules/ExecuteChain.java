package com.iflytek.skylab.service.diag.modules;

import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ExecuteChain {
    
    private GraphDiagnoseResponse graphDiagnoseResponse;

    private AiDiagSceneInfo sceneInfo;

    private List<String> nodeIds = new ArrayList<>();
}
