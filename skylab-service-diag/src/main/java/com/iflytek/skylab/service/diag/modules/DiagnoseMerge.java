package com.iflytek.skylab.service.diag.modules;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.hy.rec.domain.model.valueobj.EngineConfigItem;
import com.iflytek.hy.rec.framework.exception.EngineException;
import com.iflytek.hy.rec.framework.exception.ErrorEnum;
import com.iflytek.hy.rec.merdiag.GraphMergeDiagnose;
import com.iflytek.hy.rec.merdiag.interfaces.param.GraphMergeDiagnoseRequest;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.data.adapter.diag.DiagMergeParamAdapter;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.service.diag.service.MasteryBusinessService;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import com.iflytek.skyline.dispatcher.core.DispatchSession;
import com.iflytek.skyline.dispatcher.core.Executable;
import com.iflytek.skyline.resource.domain.ConfigItem;
import com.iflytek.skyline.resource.domain.ResourcePackage;
import com.iflytek.skyline.resource.manager.ParameterConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class DiagnoseMerge implements Executable {

    private final DiagMergeParamAdapter diagMergeParamAdapter;
    /**
     * 诊断引擎入口
     */
    private final GraphMergeDiagnose graphMergeDiagnose;


    /**
     * 用户画像 业务逻辑实现
     */
    private final MasteryBusinessService userMasteryService;

    /**
     * 资源配置转换器
     */
    private final ParameterConverter parameterConverter;


    public DiagnoseMerge(GraphMergeDiagnose graphMergeDiagnose, ParameterConverter parameterConverter, MasteryBusinessService userMasteryService) {
        this.diagMergeParamAdapter = new DiagMergeParamAdapter();
        this.parameterConverter = parameterConverter;
        this.graphMergeDiagnose = graphMergeDiagnose;
        this.userMasteryService = userMasteryService;
    }


    @Override
    public DispatchApiResponse execute(String traceId, DispatchApiRequest apiRequest, DispatchSession session) {
        if (log.isDebugEnabled()) {
            log.debug("DispatchApiRequest= {}", apiRequest);
        }
        DispatchApiResponse apiResponse = new DispatchApiResponse();
        try {
            List<ExecuteChain> executeChains = new ArrayList<>();
            apiRequest.getPayload().getData().forEach(a -> executeChains.add(a.getData().to(ExecuteChain.class)));
            if (CollectionUtils.isEmpty(executeChains)) {
                throw new Exception();
            }
            AiDiagSceneInfo aiDiagSceneInfo = executeChains.get(0).getSceneInfo();
            GraphMergeDiagnoseRequest diganoseMergeRequest = diagMergeParamAdapter.adapt(traceId, aiDiagSceneInfo);
            //加载配置
            //参数配置管理
            ResourcePackage resourcePackage = this.parameterConverter.convertPackage(apiRequest.getParameter());
            if (log.isTraceEnabled()) {
                log.trace("realConfig= {}", resourcePackage);
            }
            log.debug("StrategyId= {}", resourcePackage.getMd5());
            List<EngineConfigItem> confItems = new ArrayList<>();
            for (ConfigItem item : resourcePackage.getItems()) {
                confItems.add(new EngineConfigItem().setKey(item.getKey()).setConfig(item.getContent()).setMd5(item.getMd5()));
            }

            //stopwatch
            Stopwatch stopwatch = Stopwatch.createStarted();
            //加载配置
            this.graphMergeDiagnose.loadConfig(resourcePackage.getMd5(), confItems);
            log.debug("the DiagnoseMerge loadConfig cost= {}", stopwatch);

            //设置StrategyId
            diganoseMergeRequest.getSessionInfo().setStrategyId(resourcePackage.getMd5());

            stopwatch.reset().start();
            //提取GraphDiagnoseResponse
            List<GraphDiagnoseResponse> graphDiagnoseResponseList = new ArrayList<>();
            executeChains.forEach(e -> graphDiagnoseResponseList.add(e.getGraphDiagnoseResponse()));
            diganoseMergeRequest.setGraphDiagnoseResponseList(graphDiagnoseResponseList);
            List<String> nodeIds = executeChains.get(0).getNodeIds();
            diganoseMergeRequest.setNodeIds(executeChains.get(0).getNodeIds());
            if (log.isDebugEnabled()) {
                log.debug("GraphMergeDiagnoseRequest={}", diganoseMergeRequest);
            }

            GraphDiagnoseResponse diagnoseResponse = graphMergeDiagnose.merge(diganoseMergeRequest);

            if (diagnoseResponse == null) {
                log.error("GraphDiagnoseResponse=null, traceId={}", traceId);
                throw new Exception();
            }
            if (log.isDebugEnabled()) {
                log.debug("GraphDiagnoseResponse={}", diagnoseResponse);
            }

            //更新用户画像数据
            List<UserMasteryRecord> records = userMasteryService.storeMasterLogs(traceId, aiDiagSceneInfo, diagnoseResponse);
            // 数据埋点
            userMasteryService.traceLog(records, diagnoseResponse.getMasterInfoList(), diganoseMergeRequest.getSessionInfo(), diganoseMergeRequest.getSceneInfo());


            //转换引擎结果 到画像计算结果
            MasterFetchResult masterFetchResult = diagMergeParamAdapter.adapt(nodeIds, diagnoseResponse);
            apiResponse.setPayload(masterFetchResult);

        } catch (Exception e) {
            if (e instanceof EngineException) {
                EngineException ee = (EngineException) e;
                log.error("Call DiagnoseMerge error code= {}; message= {}", ee.getErrorCode(), ee.getMessage());

                ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
                if (ee.getRedirect()) {
                    header.setErrorNodes(Lists.newArrayList(new ApiResponseHeader.ErrorNode("failover", "true")));
                }
                apiResponse.setHeader(header);
            } else {
                ApiResponseHeader header = new ApiResponseHeader(ErrorEnum.EXCEPTION.getCode(), ErrorEnum.EXCEPTION.getMessage());
                apiResponse.setHeader(header);
            }
        }
        apiRequest.setTraceId(traceId);
        if (log.isDebugEnabled()) {
            log.debug("DispatchApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }

}
