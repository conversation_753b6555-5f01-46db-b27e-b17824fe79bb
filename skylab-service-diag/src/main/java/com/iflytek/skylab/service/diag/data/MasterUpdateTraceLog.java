package com.iflytek.skylab.service.diag.data;

import com.iflytek.hy.rec.domain.model.valueobj.SceneInfo;
import com.iflytek.skylab.core.data.Jsonable;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/8 16:53
 */
@Getter
@Setter
@Accessors(chain = true)
public class MasterUpdateTraceLog extends Jsonable {

    public static final String TYPE = "MASTER_UPDATE_TRACE_LOG";

    private SceneInfo sceneInfo;

    private Object sessionInfo;

    private List<UserMasteryRecordExt> records;

    private Date sendTime = new Date();

    @Getter
@Setter
    @EqualsAndHashCode(callSuper = true)
    public static class UserMasteryRecordExt extends UserMasteryRecord {

        private Boolean isGreenByDynamicPortrait;

        /**
         * 历史画像值
         */
        private Double beforeAlgorithmFusionMasterScore;

        /**
         * 关联点 key点，v点类型
         */
        private Map<String, String> associativePoint;
    }
}
