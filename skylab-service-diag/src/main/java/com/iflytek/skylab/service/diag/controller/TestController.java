package com.iflytek.skylab.service.diag.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.mongo.dao.PortraitMigrationRecordRepository;
import com.iflytek.skylab.core.dataapi.util.SubCollectionUtils;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.diag.service.PortraitTransferService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.*;
import skynet.boot.logging.LoggingCost;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TestController
 * @description TODO
 * @date 2024/4/17 16:00
 */
@Slf4j
@RestController
@RequestMapping("/skylab/api/v1/test")
public class TestController {

    @Autowired
    private PortraitTransferService portraitTransferService;

    @Autowired
    private MongoTemplate masterService;

    @Autowired
    private PortraitMigrationRecordRepository portraitMigrationRecordRepository;

    @LoggingCost
    @GetMapping
    public void diagnose(String userID) {
        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setUserId(userID);
        sceneInfo.setPhaseCode("04");
        sceneInfo.setSubjectCode("02");
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setFunctionCode("MASTER_FETCH");
        long l = System.currentTimeMillis();
        portraitTransferService.portraitTrans(sceneInfo, IdUtil.fastUUID());
        log.info("耗时：{}", System.currentTimeMillis() - l);
        // 删除增加的记录
        Criteria criteria = new Criteria();
        criteria.and("user_id").is(userID)
                .and("study_code").is("SYNC_OS");
        masterService.remove(new Query(criteria), SubCollectionUtils.getSubUserMasteryRecordCollectionName(userID));
        List<String> ids = new ArrayList<>();
        String aId = sceneInfo.getUserId() + sceneInfo.getStudyCode() + sceneInfo.getPhaseCode() + sceneInfo.getSubjectCode() + NodeTypeEnum.ANCHOR_POINT.name();
        String cId = sceneInfo.getUserId() + sceneInfo.getStudyCode() + sceneInfo.getPhaseCode() + sceneInfo.getSubjectCode() + NodeTypeEnum.CHECK_POINT.name();
        ids.add(aId);
        ids.add(cId);
        ids.forEach(id ->portraitMigrationRecordRepository.deleteById(DigestUtil.md5Hex(id)));
    }

    @LoggingCost
    @PostMapping
    public void diagnose1(@RequestBody SceneInfo sceneInfo) {
        long l = System.currentTimeMillis();
        portraitTransferService.portraitTrans(sceneInfo, IdUtil.fastUUID());
        log.info("耗时：{}", System.currentTimeMillis() - l);
    }

}
