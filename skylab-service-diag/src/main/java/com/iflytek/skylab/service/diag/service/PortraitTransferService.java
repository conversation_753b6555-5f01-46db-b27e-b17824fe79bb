package com.iflytek.skylab.service.diag.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.data.GlobalMasteryItem;
import com.iflytek.skylab.core.dataapi.data.MasterQuery;
import com.iflytek.skylab.core.dataapi.mongo.dao.PortraitMigrationRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.PortraitMigrationRecord;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.dataapi.service.MasterService;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PortraitTransferService
 * @description 精准性OS 服务端画像迁移Service
 * @date 2024/4/12 9:46
 */
@Slf4j
public class PortraitTransferService {

    @Autowired
    private MasterService masterService;

    @Autowired
    private PortraitMigrationRecordRepository portraitMigrationRecordRepository;

    @Value("${skylab.front.default-graph-version}")
    private String version;

    private Set<String> bookCodeList;

    public PortraitTransferService() {
    }

    public void portraitTrans(SceneInfo sceneInfo, String traceId) {
        List<NodeTypeEnum> nodeTypeEnums = userMigrationJudgment(sceneInfo);
        if (!nodeTypeEnums.contains(NodeTypeEnum.ANCHOR_POINT)) {
            userAnchorPointMigration(sceneInfo, traceId);
        }
        if (!nodeTypeEnums.contains(NodeTypeEnum.CHECK_POINT)) {
            userCheckPointMigration(sceneInfo, traceId);
        }
    }

    public void savePortraitMigrationRecord(SceneInfo sceneInfo, NodeTypeEnum nodeTypeEnum) {
        // 插入迁移记录
        String id = sceneInfo.getUserId() + sceneInfo.getStudyCode() + sceneInfo.getPhaseCode() + sceneInfo.getSubjectCode() + nodeTypeEnum.name();
        PortraitMigrationRecord portraitMigrationRecord = new PortraitMigrationRecord().setId(DigestUtil.md5Hex(id)).setUserId(sceneInfo.getUserId()).setPhaseCode(sceneInfo.getPhaseCode())
                .setSubjectCode(sceneInfo.getSubjectCode()).setStudyCode(sceneInfo.getStudyCode()).setCreateTime(Instant.now()).setNodeType(nodeTypeEnum);
        portraitMigrationRecordRepository.save(portraitMigrationRecord);
    }

    /**
     * 判断用户是否需要进行画像迁移
     *
     * @param sceneInfo 场景信息
     */
    public List<NodeTypeEnum> userMigrationJudgment(SceneInfo sceneInfo) {
        List<NodeTypeEnum> res = new ArrayList<>();
        if (StudyCodeEnum.SYNC_OS == (sceneInfo.getStudyCode())
                && StrUtil.startWith(sceneInfo.getFunctionCode(), "MASTER_")) {
            MasterQuery masterQuery = new MasterQuery();
            masterQuery.setUserId(sceneInfo.getUserId());
            List<PortraitMigrationRecord> search = portraitMigrationRecordRepository.search(masterQuery.buildCriteria());
            res = search.stream().map(PortraitMigrationRecord::getNodeType).collect(Collectors.toList());
        }
        return res;
    }

    /**
     * 用户锚点画像迁移
     *
     * @param sceneInfo
     */
    public void userAnchorPointMigration(SceneInfo sceneInfo, String traceId) {
        log.debug("{}:锚点迁移处理开始===============", traceId);

        //初中数学-所有书-修改成图谱中获取
        if (bookCodeList == null) {
            bookCodeList = DiagGraphCache.getAllBooksCache(sceneInfo.getGraphVersion());
        }

        long l = System.currentTimeMillis();
        // 用户锚点数据查询
        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId(sceneInfo.getUserId());
        masterQuery.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        List<UserMasteryRecord> search = masterService.queryMasterDataBatch(traceId, masterQuery);

        if (!CollectionUtils.isEmpty(search)) {
            // 数据处理防止没有cookCode
            log.info("TraceId:{},用户查询迁移锚点数量：{}个", traceId, search.size());
            List<UserMasteryRecord> res = search.stream().map(this::userMasteryRecordHandle)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            Map<String, List<UserMasteryRecord>> bookCodeNodeIdMap = getBookCodeNodeIdMap(res);
            List<UserMasteryRecord> lastUserMasteryRecord = getLastUserMasteryRecord(bookCodeNodeIdMap);
            //将唯一画像赋值给全局画像
            lastUserMasteryRecord.forEach(item -> {
                GlobalMasteryItem globalMasteryItem = new GlobalMasteryItem();
                globalMasteryItem.setMasterScore(item.getMasteryScore());
                globalMasteryItem.setFusion(item.getFusion());
                globalMasteryItem.setReal(item.getReal());
                globalMasteryItem.setPredict(item.getPredict());
                item.setGlobalMastery(globalMasteryItem);
            });
            // 数据入精准性OS
            if (!CollectionUtils.isEmpty(lastUserMasteryRecord)) {
                log.info("TraceId:{},用户已锚点迁移========,迁移数量：{}", traceId, lastUserMasteryRecord.size());
                masterService.batchSaveUserMasteryRecord(lastUserMasteryRecord, sceneInfo.getUserId());
            }
        }
        savePortraitMigrationRecord(sceneInfo, NodeTypeEnum.ANCHOR_POINT);
        log.info("{}:锚点迁移处理结束===============耗时：{}", traceId, System.currentTimeMillis() - l);
    }

    /**
     * 用户考点画像迁移
     *
     * @param sceneInfo
     */
    public void userCheckPointMigration(SceneInfo sceneInfo, String traceId) {
        // 用户锚点数据查询
        log.debug("{}:考点迁移处理开始===============", traceId);
        long l = System.currentTimeMillis();
        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId(sceneInfo.getUserId());
        masterQuery.setNodeType(NodeTypeEnum.CHECK_POINT);
        List<UserMasteryRecord> search = masterService.queryMasterDataBatch(traceId, masterQuery);
        // 数据治理
        List<UserMasteryRecord> filterList = search.stream().filter(record -> (StudyCodeEnum.UNIT_REVIEW == record.getStudyCode() || StudyCodeEnum.MID_EXAM == record.getStudyCode()
                || StudyCodeEnum.FINAL_EXAM == record.getStudyCode()) && Objects.nonNull(record.getCatalogId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterList)) {
            log.info("TraceId:{},用户查询迁移考点数量：{}个", traceId, filterList.size());
            // 数据处理防止没有cookCode
            List<UserMasteryRecord> res = filterList.stream().map(record -> userMasteryRecordHandle(record)).collect(Collectors.toList());
            Map<String, List<UserMasteryRecord>> bookCodeNodeIdMap = getBookCodeNodeIdMap(res);
            List<UserMasteryRecord> lastUserMasteryRecord = getLastUserMasteryRecord(bookCodeNodeIdMap);
            // 数据入精准性OS
            if (!CollectionUtils.isEmpty(lastUserMasteryRecord)) {
                log.info("TraceId:{},用户已考点迁移========,迁移数量：{}", traceId, lastUserMasteryRecord.size());

                //将唯一画像赋值给全局画像
                lastUserMasteryRecord.forEach(item -> {
                    GlobalMasteryItem globalMasteryItem = new GlobalMasteryItem();
                    globalMasteryItem.setMasterScore(item.getMasteryScore());
                    globalMasteryItem.setFusion(item.getFusion());
                    globalMasteryItem.setReal(item.getReal());
                    globalMasteryItem.setPredict(item.getPredict());
                    item.setGlobalMastery(globalMasteryItem);
                });
                masterService.batchSaveUserMasteryRecord(lastUserMasteryRecord, sceneInfo.getUserId());
            }
        }
        savePortraitMigrationRecord(sceneInfo, NodeTypeEnum.CHECK_POINT);
        log.info("{}:考点迁移处理结束===============耗时：{}", traceId, System.currentTimeMillis() - l);
    }

    /**
     * 用户复习点画像迁移
     *
     * @param sceneInfo
     */
    public void userReviewPointMigration(SceneInfo sceneInfo, String traceId) {
        // 用户复习点数据查询
        log.debug("{}:复习点迁移处理开始===============", traceId);
        long l = System.currentTimeMillis();
        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId(sceneInfo.getUserId());
        masterQuery.setNodeType(NodeTypeEnum.REVIEW_POINT);
        List<UserMasteryRecord> search = masterService.queryMasterDataBatch(traceId, masterQuery);
        // 数据过滤
        Map<String, List<UserMasteryRecord>> reviewRecords = search.stream().filter(record -> Objects.nonNull(record.getCatalogId())).collect(Collectors.groupingBy(
                record -> record.getUserId() + "_" + record.getCatalogId(),
                Collectors.toList()
        ));
        List<UserMasteryRecord> resList = new ArrayList<>();
        for (Map.Entry<String, List<UserMasteryRecord>> userMasteryRecordListEntry : reviewRecords.entrySet()) {
            int b2 = 0;
            List<UserMasteryRecord> value = userMasteryRecordListEntry.getValue();
            int b1 = value.size();
            for (UserMasteryRecord userMasteryRecord : value) {
                if (Objects.isNull(userMasteryRecord.getReal()) || Objects.isNull(userMasteryRecord.getPredict()) || Objects.isNull(userMasteryRecord.getFusion())) {
                    continue;
                } else if (userMasteryRecord.getReal() == -1 && userMasteryRecord.getPredict() == 0.2 && userMasteryRecord.getFusion() == 0.2) {
                    b2++;
                }
            }
            if (b2 / b1 < 0.2) {
                resList.addAll(value);
            }
        }
        // 数据治理
        if (!CollectionUtils.isEmpty(resList)) {
            log.info("TraceId:{},用户查询迁移复习点数量：{}个", traceId, resList.size());
            List<UserMasteryRecord> res = resList.stream().flatMap(record -> reviewPointHandle(record).stream()).collect(Collectors.toList());
            List<UserMasteryRecord> collect = res.stream().map(record -> userMasteryRecordHandle(record)).filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                // 复习点 去重 一本书只保留一条
                Map<String, List<UserMasteryRecord>> resMap = collect.stream().collect(Collectors.groupingBy(
                        record -> record.getId(),
                        Collectors.toList()
                ));
                List<UserMasteryRecord> lastUserMasteryRecord = getLastUserMasteryRecord(resMap);
                log.info("TraceId:{},用户迁移复习点数量：{}个", traceId, lastUserMasteryRecord.size());
                masterService.batchSaveUserMasteryRecord(lastUserMasteryRecord, sceneInfo.getUserId());
            }
        }
        // 数据入精准性OS
        log.info("{}:复习点迁移处理结束===============耗时：{}", traceId, System.currentTimeMillis() - l);
    }

    /**
     * 复习点处理
     *
     * @param userMasteryRecord
     * @return
     */
    List<UserMasteryRecord> reviewPointHandle(UserMasteryRecord userMasteryRecord) {
        List<UserMasteryRecord> userMasteryRecords = new ArrayList<>();
        String nodeId = userMasteryRecord.getNodeId();
        Set<String> catalogIds = DiagGraphCache.getReviewPointToUnitCache(version, nodeId);
        if (CollectionUtils.isEmpty(catalogIds)) {
            return userMasteryRecords;
        }
        for (String catalogId : catalogIds) {
            UserMasteryRecord newUserMasteryRecord = new UserMasteryRecord();
            BeanUtils.copyProperties(userMasteryRecord, newUserMasteryRecord);
            newUserMasteryRecord.setCatalogId(catalogId);
            userMasteryRecords.add(newUserMasteryRecord);
        }
        return userMasteryRecords;
    }

    /**
     * 数据治理
     *
     * @param userMasteryRecord
     * @return
     */
    public UserMasteryRecord userMasteryRecordHandle(UserMasteryRecord userMasteryRecord) {
        try {
            String catalogId = userMasteryRecord.getCatalogId();
            String[] s = StringUtils.split(catalogId, "_");
            if (Objects.isNull(s) || s.length < 3) {
                return null;
            }
            String bookCode = s[0] + "_" + s[1];
            String catalog = s[0] + "_" + s[1] + "_" + s[2];
            userMasteryRecord.setBookCode(bookCode);
            userMasteryRecord.setStudyCode(StudyCodeEnum.SYNC_OS);
            userMasteryRecord.setCatalogId(catalog);

            //数据治理 充值画像
            userMasteryRecord.setAlgoFusion(userMasteryRecord.getFusion());
            userMasteryRecord.setAlgoReal(userMasteryRecord.getReal());
            userMasteryRecord.setAlgoPredict(userMasteryRecord.getPredict());

            String id = userMasteryRecord.getUserId() + userMasteryRecord.getBookCode() + userMasteryRecord.getNodeId() + userMasteryRecord.getStudyCode();
            userMasteryRecord.setId(DigestUtil.md5Hex(id));
            return userMasteryRecord;
        } catch (Exception e) {
            log.error("画像同步精准学os异常", e);
        }
        return null;
    }

    /**
     * 获取用户最新数据
     *
     * @param userMasteryRecordMap
     * @return
     */
    public List<UserMasteryRecord> getLastUserMasteryRecord(Map<String, List<UserMasteryRecord>> userMasteryRecordMap) {
        List<UserMasteryRecord> list = new ArrayList<>();
        for (List<UserMasteryRecord> value : userMasteryRecordMap.values()) {
            List<UserMasteryRecord> sortedList = value.stream().sorted(Comparator.comparing(record -> {
                if (Objects.nonNull(record.getLastAnswerTime())) {
                    return record.getLastAnswerTime();
                } else if (Objects.nonNull(record.getUpdateTime())) {
                    return record.getUpdateTime();
                } else {
                    return record.getCreateTime() == null ? Instant.MIN : record.getCreateTime();
                }
            })).collect(Collectors.toList());
            UserMasteryRecord resUserMasteryRecord = sortedList.get(sortedList.size() - 1);
            list.add(resUserMasteryRecord);
        }
        return list;
    }

    /**
     * 过滤点画像
     */
    public Map<String, List<UserMasteryRecord>> getBookCodeNodeIdMap(List<UserMasteryRecord> userMasteryRecordList) {
        Map<String, List<UserMasteryRecord>> res = userMasteryRecordList.stream().filter(x -> Objects.nonNull(x.getBookCode()) && Objects.nonNull(x.getNodeId()))
                .collect(Collectors.groupingBy(
                        record -> record.getBookCode() + "_" + record.getNodeId(),
                        Collectors.toList()
                ));
        return res;
    }
}
