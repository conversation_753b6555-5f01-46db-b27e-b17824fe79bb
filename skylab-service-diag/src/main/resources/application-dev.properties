IP=***********
#-----------------------------------------------------------------
skyline.data.api.sdk.app-key=app-69v3u6vj
skyline.data.api.sdk.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
skyline.data.api.sdk.url=http://**************:30890/api/v1/execute
#-----------------------------------------------------------------
skyline.data.api.sdk.keep-alive-duration-second=5
skyline.data.api.sdk.max-idle-conn=5
skyline.data.api.sdk.timeout-ms=5000
skyline.data.api.sdk.timeoutMs=5000
skyline.data.api.sdk.maxIdleConn=10
skyline.data.api.sdk.keepAliveDurationSecond=5
skyline.data.api.sdk.queryLabelDataApiId=api-me0vg2e4
skyline.data.api.sdk.queryApiDataApiId=api-2rl8e4vh
#-----------------------------------------------------------------
#skylab.zion.dict-table-name=dim_xxj_dic_model
#skylab.zion.dict-family=u
#skylab.zion.dict-qualifier=dicModel
#skylab.zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
#skylab.zion.dict-refresh-period-seconds=3600
#skylab.zion.dict-data-api-item.dataApiId=api-vimqibeu
#skylab.zion.dict-data-api-item.version=1
#skylab.zion.feature-data-api-item.dataApiId=api-x4znzm0l
##????data-api-item.version
#skylab.zion.feature-data-api-item.version=
zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.es-host=***********:9200,***********:9200,***********:9200
#zion.query-data-base=es
zion.dict-refresh-period-seconds=10
zion.query-data-base=hbase_data_api
zion.dict-table-name=dim_xxj_dic_model
zion.dict-data-api-item.dataApiId=api-vimqibeu
zion.feature-data-api-item.dataApiId=api-x4znzm0l
zion.app-key=app-69v3u6vj
zion.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
zion.url=http://**************:30890/api/v1/execute

#-----------------------------------------------------------------
logging.level.ROOT=INFO
logging.level.com.iflytek.skyline=DEBUG
logging.level.com.iflytek.skylab=DEBUG
#-----------------------------------------------------------------
skynet.api.swagger2.enabled=true
#-----------------------------------------------------------------
skyline.brave.kafka-enabled=true
skyline.brave.kafka-topic-prefix=snifer_XXJ_
skyline.brave.allowSubjectCodes=02
#-----------------------------------------------------------------
spring.kafka.bootstrap-servers=*************:9093,*************:9093,*************:9093
spring.kafka.producer.properties.security.protocol=SASL_PLAINTEXT
spring.kafka.producer.properties.sasl.mechanism=SCRAM-SHA-256
#-----------------------------------------------------------------
# mongoDB
skylab.data.api.graph.hosts=***********:9669
