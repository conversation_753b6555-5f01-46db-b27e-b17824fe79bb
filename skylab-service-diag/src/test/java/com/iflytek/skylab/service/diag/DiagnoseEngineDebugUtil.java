package com.iflytek.skylab.service.diag;

import cn.hutool.json.JSONUtil;
import com.iflytek.skylab.service.diag.controller.DiagnoseController;
import com.iflytek.skylab.service.diag.controller.DiagnoseSyncOsController;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 通过平台调试引擎
 * 使用步骤：
 * 1. 诊断引擎入参， 以json显示，存在resource中
 * - 通过目录诊断  engine-debug/masterFetch4Catalog.json
 * - 通过点来诊断  engine-debug/masterFetch4Node.json
 * 2. main方法，调用相应的方法
 * - 通过目录诊断  masterFetch4Catalog()
 * - 通过点来诊断  masterFetch4Node()
 *
 * <AUTHOR>
 * @date 2022/4/13 10:37
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DiagAppBoot.class)
public class DiagnoseEngineDebugUtil {
    @Autowired
    DiagnoseSyncOsController diagnoseSyncOsController;
    @Autowired
    DiagnoseController diagnoseController;

    @Test
    public void test() throws Exception {

        DispatchApiRequest dispatchApiRequest = JSONUtil.toBean("{\n" +
                "    \"header\": {\n" +
                "        \"code\": 0,\n" +
                "        \"traceId\": \"aadsdwdadadawdsdwadwa\"\n" +
                "    },\n" +
                "    \"parameter\": {\n" +
                "        \"sceneInfo\": {\n" +
                "            \"userId\": \"xiangzhang182_test\",\n" +
                "            \"studyCode\": \"SYNC_OS\",\n" +
                "            \"bizAction\": \"NONE\",\n" +
                "            \"bizCode\": \"ZSY_XXJ\",\n" +
                "            \"subjectCode\": \"02\",\n" +
                "            \"phaseCode\": \"04\",\n" +
                "            \"gradeCode\": \"07\",\n" +
                "            \"areaCode\": \"130000\",\n" +
                "            \"catalogCode\": \"01_08020101-002_06_002\",\n" +
                "            \"pressCode\": \"01\",\n" +
                "            \"bookCode\": \"01_08020101-002\",\n" +
                "            \"layerType\": \"conventional\",\n" +
                "            \"layerVersion\": \"SYNC_LEARN_UPDATE\",\n" +
                "            \"functionCode\": \"MASTER_SNAPSHOOT_FETCH\",\n" +
                "            \"graphVersion\": \"20240424_001\",\n" +
                "            \"test\": true\n" +
                "        }\n" +
                "    },\n" +
                "    \"payload\": {\n" +
                "        \"data\": [\n" +
                "            {\n" +
                "                \"code\": \"data\",\n" +
                "                \"data\": {\n" +
                "                    \"funcCode\": \"MASTER_SNAPSHOOT_FETCH\",\n" +
                "                    \"bookCode\": \"42_08020126-001\"\n" +
                "                }\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}", DispatchApiRequest.class);
        DispatchApiResponse diagnose = diagnoseSyncOsController.diagnose(dispatchApiRequest);
        log.info(JSONUtil.toJsonPrettyStr(diagnose));
    }

    @Test
    public void test1() throws Exception {
        DispatchApiRequest dispatchApiRequest = JSONUtil.toBean("{\n" +
                "    \"header\": {\n" +
                "        \"code\": 0,\n" +
                "        \"traceId\": \"41cf222d1e0a4861abe31a192b2a49d2\"\n" +
                "    },\n" +
                "    \"parameter\": {\n" +
                "        \"sceneInfo\": {\n" +
                "            \"userId\": \"80301377-8c68-4917-90b0-ea57a0cab199\",\n" +
                "            \"studyCode\": \"SYNC_LEARN\",\n" +
                "            \"bizAction\": \"SYNC_EVAL\",\n" +
                "            \"bizCode\": \"ZSY_XXJ\",\n" +
                "            \"subjectCode\": \"02\",\n" +
                "            \"phaseCode\": \"03\",\n" +
                "            \"gradeCode\": \"02\",\n" +
                "            \"areaCode\": \"000000\",\n" +
                "            \"functionCode\": \"STUDY_LOG\",\n" +
                "            \"graphVersion\": \"20250613_001\",\n" +
                "            \"catalogCode\": \"01_02020101-003_02_period30\",\n" +
                "            \"pressCode\": \"01\",\n" +
                "            \"bookCode\": \"01_02020101-003\",\n" +
                "            \"layerType\": \"highScoreAdvanced\",\n" +
                "            \"layerVersion\": \"SYNC_LEARN_UPDATE\",\n" +
                "            \"test\": false\n" +
                "        },\n" +
                "        \"SceneExtend\": {\n" +
                "            \"id\": \"6257c6a16171aa18a55d5369\",\n" +
                "            \"createdDate\": \"2022-04-14 15:00:49\",\n" +
                "            \"lastModifiedDate\": \"2025-03-20 14:14:49\",\n" +
                "            \"resourceCategoryId\": \"6257c5316171aa18a55d5368\",\n" +
                "            \"categoryName\": \"学习机_场景依赖影响配置\",\n" +
                "            \"name\": \"全局场景依赖\",\n" +
                "            \"version\": \"67dbb2597c36cb262f7765a9\",\n" +
                "            \"versionNumber\": 14,\n" +
                "            \"contentLength\": 817,\n" +
                "            \"parentId\": \"jingwang36_uuid\",\n" +
                "            \"desc\": \"资源配置/全局场景依赖\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"payload\": {\n" +
                "        \"data\": [\n" +
                "            {\n" +
                "                \"code\": \"data\",\n" +
                "                \"data\": {\n" +
                "                    \"funcCode\": \"STUDY_LOG\",\n" +
                "                    \"studyLogFuncEnum\": \"STUDY_LOG\",\n" +
                "                    \"items\": [\n" +
                "                        {\n" +
                "                            \"catalogCode\": \"01_02020101-003_02_period30\",\n" +
                "                            \"refTraceId\": \"fdbf37d957af4e3dac342f271adac799\",\n" +
                "                            \"correctTraceId\": \"8f015f2b374d42e5874a1ba210f4f2e7\",\n" +
                "                            \"nodeId\": \"3b391655-f3ec-40fb-9898-3910b5ab0497\",\n" +
                "                            \"nodeType\": \"ANCHOR_POINT\",\n" +
                "                            \"resNodeId\": \"c2d162ef-78e1-4938-a5f7-ed653a994dc0\",\n" +
                "                            \"resNodeType\": \"TOPIC\",\n" +
                "                            \"roundId\": \"18a8638e-290e-4f8c-b460-c783696efd91\",\n" +
                "                            \"roundIndex\": \"3\",\n" +
                "                            \"timeCost\": 3,\n" +
                "                            \"score\": 0.0,\n" +
                "                            \"standardScore\": 2.0,\n" +
                "                            \"feedbackTime\": \"2025-07-04 11:15:15\",\n" +
                "                            \"correctType\": \"3\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}", DispatchApiRequest.class);

        DispatchApiResponse diagnose = diagnoseController.diagnose(dispatchApiRequest);
        log.info(JSONUtil.toJsonPrettyStr(diagnose));
    }
}
