POST http://localhost:23336/skylab/api/v1/master/diagnose
Content-Type: application/json
SKYLINE-TRACE-DEBUG: true
SKYLINE-TRACE-ID: b23b024f-3c9e-47f6-8de6-d70e7acfdd21
SKYLINE-SPAN-ID: 008

{
  "header": {
    "traceId": "b23b024f-3c9e-47f6-8de6-d70e7acfdd21",
    "code": 0
  },
  "parameter": {
    "script4_groovy": {
      "createdDate": "2022-04-01T09:02:55.211Z",
      "lastModifiedDate": "2022-04-01T09:03:55.927Z",
      "resourceCategoryId": "6246bc874a8725736a60de77",
      "name": "C端_冒烟_groovy4",
      "contentLength": 917,
      "id": "6246bfbf4a8725736a60de88",
      "categoryName": "学习机_诊断能力脚本配置",
      "version": "6246bffb4a8725736a60de8b",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/C端_冒烟_groovy4"
    },
    "sceneInfo": {
      "functionCode": "MASTER_FETCH",
      "bookVolumeCode": "01_07020101-001",
      "test": false,
      "bizCode": "ZSY_XXJ",
      "userId": "b7e0abb4-3ce3-4844-b90f-3bc8690098f9",
      "bookVersionCode": "01",
      "areaCode": "010100",
      "graphVersion": "v1",
      "studyCode": "SYNC_LEARN",
      "phaseCode": "04",
      "subjectCode": "02"
    },
    "script2_groovy": {
      "createdDate": "2022-04-01T09:01:47.791Z",
      "lastModifiedDate": "2022-04-01T09:02:17.089Z",
      "resourceCategoryId": "6246bc874a8725736a60de77",
      "name": "C端_冒烟_groovy2",
      "contentLength": 1481,
      "id": "6246bf7b4a8725736a60de85",
      "categoryName": "学习机_诊断能力脚本配置",
      "version": "6246bf994a8725736a60de86",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/C端_冒烟_groovy2"
    },
    "script1_groovy": {
      "createdDate": "2022-04-01T09:00:20.292Z",
      "lastModifiedDate": "2022-04-01T09:01:31.857Z",
      "resourceCategoryId": "6246bc874a8725736a60de77",
      "name": "C端_冒烟_groovy1",
      "contentLength": 246,
      "id": "6246bf244a8725736a60de83",
      "categoryName": "学习机_诊断能力脚本配置",
      "version": "6246bf6b4a8725736a60de84",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/C端_冒烟_groovy1"
    },
    "script3_groovy": {
      "createdDate": "2022-04-01T09:02:37.996Z",
      "lastModifiedDate": "2022-04-01T09:03:30.741Z",
      "resourceCategoryId": "6246bc874a8725736a60de77",
      "name": "C端_冒烟_groovy3",
      "contentLength": 498,
      "id": "6246bfad4a8725736a60de87",
      "categoryName": "学习机_诊断能力脚本配置",
      "version": "6246bfdb4a8725736a60de8a",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/C端_冒烟_groovy3"
    },
    "script5_groovy": {
      "createdDate": "2022-04-01T09:03:02.382Z",
      "lastModifiedDate": "2022-04-01T09:04:10.130Z",
      "resourceCategoryId": "6246bc874a8725736a60de77",
      "name": "C端_冒烟_groovy5",
      "contentLength": 369,
      "id": "6246bfc64a8725736a60de89",
      "categoryName": "学习机_诊断能力脚本配置",
      "version": "6246c00a4a8725736a60de8c",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/C端_冒烟_groovy5"
    },
    "mainConfig": {
      "createdDate": "2022-04-01T08:58:56.931Z",
      "lastModifiedDate": "2022-04-01T12:43:53.344Z",
      "resourceCategoryId": "6246bc484a8725736a60de76",
      "name": "C端_冒烟",
      "contentLength": 1834,
      "id": "6246bed04a8725736a60de81",
      "categoryName": "学习机_诊断能力主体配置",
      "version": "6246f3894a8725736a60deb6",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/C端_冒烟"
    },
    "globalFeature": {
      "createdDate": "2022-04-01T09:06:25.746Z",
      "lastModifiedDate": "2022-04-01T09:06:39.227Z",
      "resourceCategoryId": "6246bcc04a8725736a60de78",
      "name": "全局特征",
      "contentLength": 3247,
      "id": "6246c0914a8725736a60de8e",
      "categoryName": "学习机推荐全局特征配置",
      "version": "6246c09f4a8725736a60de8f",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/全局特征"
    }
  },
  "payload": {
    "data": [
      {
        "code": "data",
        "data": {
          "catalogIds": [
            "01_09020201-002_001_000"
          ]
        }
      }
    ]
  }
}