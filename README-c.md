## Skylab Platform

学习机推荐-平台（多模块微服务工程）

### 1. 项目概述
- **项目名称**：Skylab Platform
- **项目描述**：面向学习机的推荐与画像平台，提供行为采集、特征查询、画像诊断、排序推荐等能力，对外暴露统一 HTTP 接口，并通过 Dubbo 提供内部能力服务。
- **版本信息**：2.0.9-SNAPSHOT（开发中）
- **许可证信息**：Apache License 2.0

### 2. 技术架构
- **框架与组件**：
  - Spring Boot（多模块微服务）
  - Spring Cloud Zookeeper（服务发现/注册）
  - OpenFeign（内部 HTTP 调用）
  - Dubbo + Zookeeper/EPAS（内部 RPC 能力暴露与消费）
  - Alibaba Sentinel（流量控制/熔断）
  - Kafka（日志/埋点，可选）
  - Redis（可选缓存：特征/画像缓存）
  - MongoDB（日志与追踪等文档存储）
  - MySQL（在部分模块的 dev 配置中出现）
  - Nebula Graph（图谱数据查询与维护）
  - MapStruct、Lombok 等
- **设计模式与风格**：
  - 分层架构 + 微服务划分（front/stand/diag/sort/clear-mastery/primary-migration 等）
  - 控制器层通过统一 `DispatchApiRequest/Response` 与 `SceneInfo` 进行上下文承载
  - 数据访问通过 `skylab-core-dataapi` 模块中 `DataHub` 统一入口管理
- **核心技术栈**：
  - Java 8+（父 POM：`skynet-boot-starter-parent` 4.0.18）
  - 数据库：MongoDB、MySQL（模块可选）
  - 缓存：Redis（Lettuce）
  - 消息队列：Kafka（可选）
  - 图数据库：Nebula Graph 3.0

#### 模块职责与依赖
- `skylab-core-contract`：对外契约与 DTO、SDK 等抽象；Dubbo 接口契约。
- `skylab-core-data`：平台公共数据结构、常量与领域模型。
- `skylab-core-dataapi`：统一数据访问句柄（Graph/Feature/Mastery/StudyLog 等），提供 `@EnableDataHub` 与自动配置；内置 `GraphController` 对外提供图谱 REST API。
- `skylab-core-data-adapter`：适配器层，将平台参数适配到引擎/下游。
- 服务模块：
  - `skylab-service-front`：HTTP 接入层，汇聚外部调用并转发到各能力（推荐、诊断、行为、特征、清空画像、小学迁移）。
  - `skylab-service-stand`：通用特征与行为服务（/feature、/behavior）。
  - `skylab-service-diag`：画像诊断（/master）、合并诊断（/merge）、精准学 OS 画像（/v2/master）。
  - `skylab-service-sort`：多维图谱排序推荐服务（/sort 和 /sort/errortopic）。
  - `skylab-service-clear-mastery`：用户画像清空能力。
  - `skylab-service-primary-migration`：小学精准学数据迁移代理。
  - `skylab-service-failover`：兜底组件（配置与 SPI）。
  - `skylab-service-front-consumer`：EPAS/Dubbo 消费端示例与本地联调。

#### 架构图（Mermaid）
```mermaid
graph TD
  subgraph Clients
    A[External Clients]
  end

  subgraph Front
    F[skylab-service-front<br/>HTTP Ingress + Dubbo Provider]
  end

  subgraph Capability
    S[skylab-service-stand<br/>Feature/Behavior]
    D1[skylab-service-diag<br/>/v1/master]
    D2[skylab-service-diag<br/>/v2/master]
    M[skylab-service-diag<br/>/merge]
    R[skylab-service-sort]
    C[skylab-service-clear-mastery]
    P[skylab-service-primary-migration]
  end

  subgraph Core
    H[skylab-core-dataapi<br/>DataHub + GraphController]
    CT[skylab-core-contract]
    DA[skylab-core-data-adapter]
    CD[skylab-core-data]
  end

  subgraph Infra
    ZK[Zookeeper]
    EPAS[EPAS/Dubbo]
    NG[Nebula Graph]
    RDS[Redis]
    MONGO[MongoDB]
    KFK[Kafka]
  end

  A --> F
  F -->|REST| S
  F -->|REST| D1
  F -->|REST| D2
  F -->|REST| R
  F -->|REST| C
  F -->|REST| P
  F ---|Dubbo| EPAS

  S --> H
  D1 --> H
  D2 --> H
  M --> H
  R --> H
  C --> H
  P --> H

  H --> NG
  H --> RDS
  H --> MONGO
  S -.-> KFK
  D1 -.-> KFK
  F -. Service Discovery .- ZK
```

### 3. 主要功能
- **HTTP 接入层（front）**：统一入口，封装请求头场景 `SceneInfo`、载荷 `payload` 与 `traceId`，路由到内部服务。
- **特征服务（stand/feature）**：特征查询、特征 Schema 查询、缓存清理。
- **行为服务（stand/behavior）**：答题记录上报/查询、批量查询、宏图谱日志上报。
- **画像服务（diag）**：点/目录画像诊断、查询、精准学 OS 画像、画像合并与调度。
- **排序服务（sort）**：多维图谱排序推荐、错题本推荐；与画像/学习记录联动。
- **图谱服务（core-dataapi/graph）**：子图查询、顶点/边 CRUD、逆推根节点。
- **清空画像（clear-mastery）**：基于函数码 CLEAR_MASTERY 的画像清理。
- **小学精准学迁移（primary-migration）**：上报数据处理与迁移。

#### 关键技术点
- `DataHub` 统一获取 Feature/Graph/Mastery/StudyLog 等服务实例，降低耦合。
- `SceneInfoSelector` 统一从请求中解析 `SceneInfo`，保证上下文一致性。
- 引擎参数适配器（Sort/Diag 等）隔离接口协议与引擎协议，便于扩展。
- Sentinel 本地规则与 Dashboard 支持，保护能力服务。
- Dubbo+Zookeeper/EPAS 混合注册，支持内部 RPC 与平台对接。

### 4. 业务处理逻辑分析
- 控制器统一入口：`DispatchApiRequest` 承载 `header/parameter/payload`，通过 `SceneInfoSelector.select` 获取 `SceneInfo`，进行参数校验与适配后调用 Service。
- 排序流程（`SortController#process`）：
  - 场景校验 -> 选择 `AbstractParamAdapter` 适配 -> 调用引擎 -> 缓存推荐结果 -> 存储学习日志 -> 更新用户画像 -> 适配响应。
- 行为上报流程（`BehaviorController#reportAnswerRecord`）：
  - 校验参数与依赖配置 -> 按书本分组作答记录 -> 分组调用扩展/OS 扩展 -> 汇总返回 ID 列表。
- 画像流程（`DiagnoseController#diagnose`）：
  - 场景与载荷校验 -> 适配引擎请求 -> 引擎诊断 -> 存储/埋点 -> 适配输出。
- 清空画像（`ClearMasteryController#process`）：
  - 校验 `SceneInfo.functionCode` 为 CLEAR_MASTERY -> 进入清理流程。

### 5. 主要对外接口（REST）
以下为主要模块与端点（均为 POST，`Content-Type: application/json`）：

| 模块 | 基础路径 | 端点 | 说明 |
|---|---|---|---|
| front | `/skylab/api/v2` | `/recommend` | 推荐入口，header 需包含 `sceneType`、`funcParam` |
| front | `/skylab/api/v2` | `/diagnose` | 画像诊断入口 |
| front | `/skylab/api/v2` | `/reportAnswerRecord` | 答题记录上报 |
| front | `/skylab/api/v2` | `/queryAnswerRecord` | 答题记录查询 |
| front | `/skylab/api/v2` | `/featureFetch` | 特征查询汇聚 |
| front | `/skylab/api/v2` | `/clearMastery` | 清空画像汇聚 |
| front | `/skylab/api/v2` | `/primary/behavior` | 小学精准学答题上报 |
| front | `/skylab/api/v2` | `/primary/mastery` | 小学精准学章节掌握度上报 |
| stand | `/skylab/api/v1/feature` | `/query` | 特征查询 |
| stand | `/skylab/api/v1/feature` | `/schema` | 特征 Schema 查询 |
| stand | `/skylab/api/v1/feature` | `/clearRedisCache` | 清理图谱缓存 |
| stand | `/skylab/api/v1/behavior` | `/query` | 答题记录查询 |
| stand | `/skylab/api/v1/behavior` | `/reportAnswerRecord` | 答题记录上报 |
| stand | `/skylab/api/v1/behavior` | `/reportAnswerRecordOS` | OS 版本答题上报 |
| stand | `/skylab/api/v1/behavior` | `/macrograph/reportAnswerRecord` | 大图谱日志上报 |
| stand | `/skylab/api/v1/behavior` | `/batchQuery` | 批量查询答题记录 |
| sort | `/skylab/api/v1/sort` | `/process` | 多维图谱排序推荐 |
| sort | `/skylab/api/v1/sort/errortopic` | `/process` | 错题本推荐 |
| diag | `/skylab/api/v1/master` | `/diagnose` | 画像诊断/获取 |
| diag | `/skylab/api/v1/master` | `/query` | 点画像查询 |
| diag | `/skylab/api/v2/master` | `/diagnose` | 精准学 OS 画像诊断 |
| diag | `/skylab/api/v1/merge` | `/diagnose` | 画像合并诊断（本地调度） |
| clear-mastery | `/skylab/api/v1/clearMastery` | `/process` | 清空画像 |
| primary-migration | `/skylab/api/v1/primaryMigration` | `/process` | 小学精准学迁移处理 |
| core-dataapi | `/skylab/api/v1/graph` | `querySubGraph/queryVertices/queryVerticesReverse/queryVertex/saveVertex/deleteVertex/saveEdge/deleteEdge` | 图谱 API |

- Swagger/Knife4j：各服务默认启用 Swagger2（`@EnableSkynetSwagger2`）。访问路径依据网关/部署而定。

#### 接口文档链接与示例
- 访问文档（Knife4j）：`http://<host>:<port>/doc.html`
- 访问 Swagger UI（如启用）：`http://<host>:<port>/swagger-ui.html`

- 示例请求体（通用结构 `DispatchApiRequest`）：
```json
{
  "traceId": "trace-123",
  "header": {},
  "parameter": { "SceneExtend": {"key": "value"} },
  "payload": {
    "data": [ { "data": {"any": "object"} } ]
  }
}
```

- 示例 cURL（特征查询）
```bash
curl -X POST \
  -H 'Content-Type: application/json' \
  'http://<host>:32183/skylab/api/v1/feature/query' \
  -d '{
    "traceId": "trace-123",
    "parameter": {},
    "payload": {"data": [{"data": {"featureName": "user_level"}}]}
  }'
```

### 6. 系统配置
- 运行环境：JDK 8+，Linux/macOS/Windows
- 依赖中间件：Zookeeper、MongoDB、Redis、Kafka（可选）、Nebula Graph、MySQL（可选）
- 端口（默认）：
  - front: 32181
  - stand: 32183
  - diag: 23336
  - sort: 27202
  - clear-mastery: 23453
  - primary-migration: 23454
- 关键配置文件：各模块 `src/main/resources/application.properties`；front 的 `front-spring-dubbo.xml`（Dubbo/EPAS）。
 - 资源建议：开发机内存≥8GB（推荐 16GB）、磁盘≥10GB；Nebula Graph/Redis/MongoDB 按数据量扩容。

### 7. 快速开始
1) 克隆与构建
```bash
git clone <repo>
cd skylab-platform
mvn -U -T 1C -DskipTests package
```
2) 本地启动（示例：stand）
```bash
cd skylab-service-stand
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```
3) 基础验证
- 访问特征查询：POST `/skylab/api/v1/feature/query`
- 访问图谱查询：POST `/skylab/api/v1/graph/queryVertices`

### 8. 开发指南
- 目录结构：多模块 Maven；服务模块遵循 Controller -> Service -> Adapter/DataHub 的分层。
- 包规范：`com.iflytek.skylab.<module>...`
- 代码规范：遵循 Java 规范、避免深嵌套、使用早返回与参数校验。
- 提交规范：遵守 Conventional Commits；分支策略建议 `main`/`develop`/`feature/*`。
- 测试：模块内 `test` 目录进行单元与集成测试；建议覆盖率≥70%。

---

## 附：优化报告

### 文档完整性评估
- 章节完整；架构、接口、配置齐全；业务逻辑覆盖核心流程，细节可结合具体引擎/业务扩展。

### 项目分析统计（概览）
- 模块：9 个服务 + 4 个核心模块
- 主要 REST 控制器：> 12 个
- 配置：各服务 `application.properties` + Sentinel/Dubbo XML

### 技术栈识别
- 架构：Spring Boot、Dubbo、Feign、Zookeeper、Sentinel、Kafka、Redis、MongoDB、Nebula Graph、MySQL
- 依赖管理：Maven；父依赖 `skynet-boot-starter-parent:4.0.18`

### 架构设计评估
- 职责清晰、能力解耦；`DataHub` 汇聚数据访问，`Adapter` 解耦协议；可扩展性良好。
- Dubbo 和 HTTP 双通道，兼容历史与新能力；需要注意配置一致性与环境隔离。

### 文档质量指标
- 结构清晰；接口表格与架构图可用；示例与端口信息可用于快速落地。

### 改进建议
- 引入统一网关与集中化 Swagger 文档聚合。
- 增强各模块的示例与 Postman 集合；补充熔断/降级演练说明。
- 加强配置脱敏与本地 `.env` 管理；引入 Docker Compose 进行一键启动。

### 文档维护
- 建议在 PR 模板中增加“更新文档”检查项；版本号随 `pom.xml` 的 `${revision}` 同步更新。


# Skylab Platform

学习机推荐-平台

## 模块说明

- skylab-core-contract：推荐平台对外契约接口
- skylab-core-dataapi：数据访问句柄
- skylab-core-zion：DataAPI访问SDK
- skylab-core-data-adapter：参数适配转发器
- skylab-service-front：平台服务-请求接入服务
- skylab-service-stand：平台服务-通用特征服务
- skylab-service-diag：平台能力服务-诊断服务
- skylab-service-sort：平台能力服务-排序服务
  [//]: # (- skylab-service-console：平台服务-Web控制台&#40;废弃&#41;)
- skylab-service-autopath 平台能力服务-自动化学习路径
- skylab-service-failover 平台能力服务-兜底服务
- skylab-adaptive-engine-proxy 平台能力服务-老平台适配代理服务
- skylab-service-clear-mastery 平台能力服务-画像清空服务
- skylab-service-primary-migration 平台能力服务-小学数据迁移代理服务
