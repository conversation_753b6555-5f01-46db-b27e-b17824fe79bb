## skylab-service-stand

通用特征与行为服务

### 1. 项目概述
- 项目名称：skylab-service-stand
- 项目描述：提供特征查询、特征字典查询、行为日志上报/查询、批量查询和宏图谱埋点等通用能力
- 版本信息：2.0.9-SNAPSHOT（开发中）
- 许可证：Apache License 2.0

### 2. 技术架构
- 框架与组件：Spring Boot、OpenFeign、Skyline Dispatcher、Knife4j/Swagger2
- 数据与中间件：Nebula Graph、MongoDB、Kafka（埋点）、Redis（可选缓存）
- 设计风格：Controller -> Service -> DataHub（统一数据访问）
- Java 版本：JDK 8+

#### 模块职责与依赖
- FeatureController：/skylab/api/v1/feature（特征查询/字典/缓存管理）
- BehaviorController：/skylab/api/v1/behavior（答题记录上报/查询/批量查询/宏图谱上报）
- 依赖 `skylab-core-dataapi` 的 DataHub 获取 Graph/StudyLog/Feature 等服务

#### 架构图（Mermaid）
```mermaid
flowchart TD
  C[Client] -->|REST JSON| STAND[skylab-service-stand]
  subgraph STAND
    FC[FeatureController]
    BC[BehaviorController]
  end
  STAND --> DH[DataHub]
  DH --> NG[Nebula Graph]
  DH --> MONGO[MongoDB]
  BC -.-> KFK[Kafka]
```

### 3. 主要功能
- 特征查询与字典：统一从特征系统/字典拉取数据，支持缓存清理
- 学习行为：答题记录上报与查询、批量查询、宏图谱专项上报
- 关键技术点：SceneInfoSelector 统一解析场景；DispatchApiRequest/Response 统一协议；异常与超时兜底

### 4. 业务处理逻辑分析
- Feature 查询（FeatureController#query）：
  - 解析 SceneInfo -> 调用 AcquireFeatureService.selectFeature -> 组装 FeatureResult 并返回
- 行为上报（BehaviorController#reportAnswerRecord/OS）：
  - 校验参数/依赖配置 -> 解析场景 -> 按书本分组日志 -> 调用业务扩展 extend/extendOS -> 汇总返回
- 批量查询（BehaviorController#batchQuery）：
  - 批量节点查询作答记录 -> 转换为 StudyLogRecord 列表 -> 返回 BatchStudyLogQueryResult

### 5. 对外接口（REST）
所有接口均为 POST，Content-Type: application/json

| 基础路径 | 接口 | 说明 |
|---|---|---|
| /skylab/api/v1/feature | /query | 特征查询（返回 FeatureResult） |
| /skylab/api/v1/feature | /schema | 特征 Schema 字典查询 |
| /skylab/api/v1/feature | /clearRedisCache | 清理图谱缓存 |
| /skylab/api/v1/behavior | /query | 答题记录查询 |
| /skylab/api/v1/behavior | /reportAnswerRecord | 答题记录上报 |
| /skylab/api/v1/behavior | /reportAnswerRecordOS | OS 版本答题上报 |
| /skylab/api/v1/behavior | /macrograph/reportAnswerRecord | 宏图谱日志上报 |
| /skylab/api/v1/behavior | /batchQuery | 批量查询答题记录 |

示例 cURL（特征查询）：
```bash
curl -X POST \
  -H 'Content-Type: application/json' \
  'http://<host>:32183/skylab/api/v1/feature/query' \
  -d '{
    "traceId": "trace-123",
    "parameter": {},
    "payload": {"data": [{"data": {"featureName": "user_level"}}]}
  }'
```

### 6. 系统配置
- 端口：32183（application.properties: server.port）
- 核心配置：
  - Zookeeper: spring.cloud.zookeeper.connect-string=${IP}:2181
  - Nebula Graph: skylab.data.api.graph.hosts=host:9669
  - Kafka 埋点：spring.kafka.* 与 skyline.brave.*
- 关键文件：src/main/resources/application.properties
- 环境：JDK 8+；MongoDB、Nebula、Zookeeper、Kafka（可选）

### 7. 快速开始
1) 构建
```bash
mvn -U -T 1C -DskipTests package
```
2) 启动（dev）
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev -pl skylab-service-stand
```
3) 验证
- 访问 /feature/query 与 /behavior/query 接口

### 8. 开发指南
- 启动类：com.iflytek.skylab.service.stand.StandAppBoot
- 包结构：com.iflytek.skylab.service.stand.controller|service
- 代码规范：参数校验（ParamNotExistException）、早返回、记录 traceId
- 测试：建议新增 Feature/Behavior 控制器的集成测试（使用 MockMvc）

---

文档维护：如接口或配置变更，请同步更新本文件；版本号随父 pom ${revision} 同步。
