<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skylab</groupId>
        <artifactId>skylab-platform</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>skylab-service-stand</artifactId>
    <version>${skylab-stand.version}</version>
    <packaging>jar</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <maven.source.skip>true</maven.source.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-dataapi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-data-adapter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-fea-process</artifactId>
            <version>${recommend-engine.version}</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}-${project.version}-Build${buildSid}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <echo message="copy *.jar to target ..."/>
                                <copy todir="${basedir}/../target">
                                    <fileset dir="${project.build.directory}">
                                        <include name="*.jar"/>
                                    </fileset>
                                </copy>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
