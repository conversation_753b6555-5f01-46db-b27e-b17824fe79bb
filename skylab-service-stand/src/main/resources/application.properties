IP=***********
SKYNET_CLUSTER=skynet
SKYNET_PLUGIN_CODE=skylab-platform
#-----------------------------------------------------------------
server.port=32183
spring.profiles.active=dev
#-----------------------------------------------------------------
skyline.brave.enabled=true
skyline.brave.aop-enabled=true
#-----------------------------------------------------------------
spring.application.name=skylab-stand
skynet.action-point=${spring.application.name}@${SKYNET_PLUGIN_CODE}
skynet.zookeeper.enabled=true
spring.cloud.zookeeper.enabled=true
spring.cloud.zookeeper.discovery.root=/${SKYNET_CLUSTER}/discovery/${SKYNET_PLUGIN_CODE}
spring.cloud.zookeeper.connect-string=${IP}:2181
feign.client.config.skyline-router.read-timeout=5000
#-----------------------------------------------------------------
spring.groovy.template.check-template-location=false


#-----------------------------------------------------------------
logging.level.ROOT=ERROR
logging.level.com.iflytek.skyline=INFO
logging.level.com.iflytek.skylab=INFO
#-----------------------------------------------------------------
skynet.api.swagger2.enabled=true
#-----------------------------------------------------------------
skyline.brave.kafka-enabled=true
skyline.brave.kafka-topic-prefix=snifer_XXJ_
skyline.brave.allowSubjectCodes=02
#-----------------------------------------------------------------
spring.kafka.bootstrap-servers=*************:9093,*************:9093,*************:9093
spring.kafka.producer.properties.security.protocol=SASL_PLAINTEXT
spring.kafka.producer.properties.sasl.mechanism=SCRAM-SHA-256
#-----------------------------------------------------------------
# mongoDB

skynet.logging.enabled=true
skynet.logging.debug.enabled=true
skynet.logging.debug.expression=$.scene.userId=="zhangsan"
skynet.logging.springmvc.enabled=true
skynet.logging.dubbo.enabled=false
skynet.logging.debug.enable.root=false
skynet.logging.debug.enable.com.iflytek.skylab.service=true
skynet.logging.debug.enable.com.iflytek.skylab.core=true
skynet.logging.debug.enable.com.iflytek.hy.rec=true
skylab.data.api.graph.hosts=***********:9669




zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.es-host=***********:9200,***********:9200,***********:9200
zion.query-data-base=es
zion.dict-refresh-period-seconds=10

