skylab.data.api.local-graph.enable=false
#画像查询软删除生效开关
skylab.data.api.mastery.useDeleteFlag=false
#图谱版本
skylab.front.default-graph-version=20250709_001
skylab.data.api.graph.cacheVersion=${skylab.front.default-graph-version}
skylab.data.api.graph.path=${SKYNET_PLUGIN_HOME}/nebulaData_${skylab.front.default-graph-version}.zip
skylab.primary.migration.properties.graphVersion=${skylab.front.default-graph-version}
#图谱多版本
skylab.front.multi-graph-version=${skylab.front.default-graph-version},vtest
#精准学os画像迁移灰度书本配置0402
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss.SSS
# -------------------------------------------
# zk discovery
spring.cloud.zookeeper.discovery.enabled=false
spring.cloud.zookeeper.discovery.register=false
spring.cloud.zookeeper.discovery.root=/${SKYNET_CLUSTER}/discovery/${SKYNET_PLUGIN_CODE}
# -------------------------------------------
# skyline-brave
skyline.brave.enabled=true
skyline.brave.trace-debug-enabled=true
skyline.brave.file-enabled=true
skyline.brave.aop-enabled=true
skyline.brave.kafka-enabled=true
skyline.brave.kafka-topic-prefix=snifer_XXJ_
skyline.brave.allowSubjectCodes=02,05,06,13,19
#skylab.data.api.graph.hosts=**************:9669
skylab.data.api.graph.hosts=***********:9669
skylab.data.api.graph.maxConnSize=1000
skylab.data.api.graph.username=root
skylab.data.api.graph.password=nebula
#图谱指定点属性返回-不配置就走默认
skylab.data.api.graph.nodeProps.ANCHOR_POINT=anchorPointType,difficulty,evaluatePoint,evaluations,examPoint,phaseCode,realLastLevelRelationCatas,subjectCode,tracePoints,clusterAttribute,extPoints,centerPoints
skylab.data.api.graph.nodeProps.EXAM_POINT=clusterPointScene,difficulty,level
skylab.data.api.graph.nodeProps.TOPIC=topic_difficulty,topic_type,xgkLabel,questionCategory
skylab.data.api.graph.nodeProps.LEARN_PATH=pathType
skylab.data.api.graph.nodeProps.LEARN_PATH_V2=pathType
skylab.data.api.graph.nodeProps.CHECK_POINT=checkPointName,checkPointType,difficulty,evaluatePoint,phaseCode,subjectCode,versionSupport
skylab.data.api.graph.nodeProps.REVIEW_POINT=examFrequency,areas,difficulty,areaSupport
# -------------------------------------------
# mongoDB
spring.data.mongodb.uri=*******************************************************************************************************************************************
#-----------------------------------------------------------------
# MySQL  依赖服务：路由调度和管理控制台
spring.datasource.url=*****************************,***********:3306/skyline?createDatabaseIfNotExist=true&serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&autoReconnect=true&useSSL=false
spring.datasource.username=skylinemysql
spring.datasource.password=skylinemysql
# -------------------------------------------
# kafka-pass
spring.kafka.bootstrap-servers=172.29.223.26:9093,172.29.223.27:9093,172.29.223.28:9093
spring.kafka.producer.properties.security.protocol=SASL_PLAINTEXT
spring.kafka.producer.properties.sasl.mechanism=SCRAM-SHA-256
spring.kafka.producer.properties.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="PG_snifer_skyline" password="830132";
#-----------------------------------------------------------------
#-----dataApi切ES   start----------
#------------zion common--------------
zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.dict-family=u
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.dict-refresh-period-seconds=10
#------------zion es--------------
zion.query-data-base=es
zion.es-host=***********:9200,***********:9200,***********:9200
zion.es-user-name=elastic
zion.es-password=bx90ZOw1IZbx8fWCIo64
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
#------------zion dataapi--------------
#zion.query-data-base=hbase_data_api
#zion.dict-table-name=dim_xxj_dic_model
#zion.dict-data-api-item.dataApiId=api-vimqibeu
#zion.feature-data-api-item.dataApiId=api-x4znzm0l
#zion.app-key=app-69v3u6vj
#zion.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
#zion.url=http://**************:30890/api/v1/execute
#-----dataApi切ES  end--------------
#------------------
skyline.data.api.sdk.app-key=app-69v3u6vj
skyline.data.api.sdk.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
skyline.data.api.sdk.url=http://**************:30890/api/v1/execute
skyline.data.api.sdk.keepAliveDurationSecond=5
skylab.zion.dict-table-name=dim_xxj_dic_model
skylab.zion.dict-family=u
skylab.zion.dict-qualifier=dicModel
skylab.zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
skylab.zion.dict-refresh-period-seconds=3600
skylab.zion.dict-data-api-item.dataApiId=api-vimqibeu
#配置为空data-api-item.version
skylab.zion.dict-data-api-item.version=
skylab.zion.cache-max-size=1000000
skylab.zion.feature-data-api-item.dataApiId=api-x4znzm0l
#配置为空data-api-item.version
skylab.zion.feature-data-api-item.version=
skylab.zion.post-process-fea-names=user_level,user_node_mastery_path
skylab.zion.cache-exclude-fea-names=user_level,anchor_similaryuser_mastery,check_similaryuser_mastery
skylab.data.api.study-log.featureVersion=1
skylab.data.api.study-log.graphVersion=v2022-03
#-----------------------------------------------------------------
#锚点实时预测 anc_c
#skylab.data.api.anchor-point-predict.url=http://172.31.164.15:8585
#-----------------------------------------------------------------
#开发阶段 暂时开启 swagger
skynet.api.swagger2.enabled=false
#logging.file.path=${SKYNET_HOME}/log
logging.file.max-size=256MB
logging.pattern.console=%d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]){cyan}%clr(:){faint} %m%n
####
skyline.brave.traceId.max-len=100
manager.endpoints.web.exposure.include=*
logging.config=classpath:brave-logback-for-skynet-host.xml
#-----------------------------------------------------------------
logging.level.ROOT=ERROR
logging.level.com.iflytek.skyline=INFO
logging.level.com.iflytek.skylab=DEBUG
logging.level.com.iflytek.cog2=INFO
#---------------------------------------
##########################redis-start##########################
#开启redis缓存
skylab.feature.redis.enabled=true
spring.redis.host=***********
spring.redis.port=6379
spring.redis.password=Y_zm9cHOfeYa_2cfAsQv
# Redis哨兵模式配置
# 主节点名称，需与sentinel.conf中的一致
#spring.redis.sentinel.master=mymaster
## 哨兵节点地址，逗号分隔
#spring.redis.sentinel.nodes=***********:6380,***********:6380,***********:6380
## # Redis密码（如有）
#spring.redis.password=Y_zm9cHOfeYa_2cfAsQv
# 连接超时时间（单位毫秒）
spring.redis.timeout=500
# Lettuce连接池配置
spring.redis.lettuce.pool.max-active=64
spring.redis.lettuce.pool.max-idle=32
spring.redis.lettuce.pool.min-idle=10
spring.redis.lettuce.pool.max-wait=500
# 连接数据库（如有分库需求，默认0）
spring.redis.database=0
#过期时间配置
skylab.feature.redis.expire.default-expire-second=14400
# 各特征自定义过期时间（秒）
skylab.feature.redis.expire.feature-expire-second.anchorpoint_area_frequency=86400
skylab.feature.redis.expire.feature-expire-second.anchorpoint_exam_frequency=86400
skylab.feature.redis.expire.feature-expire-second.anchorpoint_graph_layer=86400
skylab.feature.redis.expire.feature-expire-second.revise_graph_layer=86400
skylab.feature.redis.expire.feature-expire-second.revise_virtual_node=86400
skylab.feature.redis.expire.feature-expire-second.anchorpoint_test_frequency_nationwide=86400
skylab.feature.redis.expire.feature-expire-second.checkpoint_exam_layer_freq=86400
skylab.feature.redis.expire.feature-expire-second.checkpoint_frequency=86400
skylab.feature.redis.expire.feature-expire-second.checkpoint_graph_layer=86400
skylab.feature.redis.expire.feature-expire-second.checkpoint_prepare_graph_layer=86400
skylab.feature.redis.expire.feature-expire-second.anchorpoint_issubjective=14400
skylab.feature.redis.expire.feature-expire-second.anchorpoint_mastery=14400
skylab.feature.redis.expire.feature-expire-second.anchorpoint_single_frequency=14400
skylab.feature.redis.expire.feature-expire-second.anchorpoint_max_recommond_topic=14400
skylab.feature.redis.expire.feature-expire-second.anchorpoint_score=14400
skylab.feature.redis.expire.feature-expire-second.anchorpoint_single_score_frequency=14400
skylab.feature.redis.expire.feature-expire-second.can_correct=14400
skylab.feature.redis.expire.feature-expire-second.catalog_answer_time=14400
skylab.feature.redis.expire.feature-expire-second.catalog_max_topic_num=14400
skylab.feature.redis.expire.feature-expire-second.checkpoint_single_frequency=14400
skylab.feature.redis.expire.feature-expire-second.checkpoint_single_score_frequency=14400
skylab.feature.redis.expire.feature-expire-second.exam_type_freq=14400
skylab.feature.redis.expire.feature-expire-second.has_video=14400
skylab.feature.redis.expire.feature-expire-second.is_forbidden_topic=14400
skylab.feature.redis.expire.feature-expire-second.paper_type_codes=14400
skylab.feature.redis.expire.feature-expire-second.revise_avg_mastery=14400
skylab.feature.redis.expire.feature-expire-second.revisepoint_test_frequency=14400
skylab.feature.redis.expire.feature-expire-second.topic_areas=14400
skylab.feature.redis.expire.feature-expire-second.topic_avg_cost=14400
skylab.feature.redis.expire.feature-expire-second.topic_avg_diff_cost=14400
skylab.feature.redis.expire.feature-expire-second.topic_difficulty=14400
skylab.feature.redis.expire.feature-expire-second.topic_type=14400
skylab.feature.redis.expire.feature-expire-second.topic_year=14400
skylab.feature.redis.clear-feature-name=anchorpoint_area_frequency,anchorpoint_exam_frequency,anchorpoint_graph_layer,anchorpoint_test_frequency_nationwide,checkpoint_exam_layer_freq,checkpoint_frequency,checkpoint_graph_layer,checkpoint_prepare_graph_layer,revise_graph_layer,revise_virtual_node
# 批量操作配置
skylab.feature.redis.batch.size=500
# 内存监控配置
skylab.feature.redis.memory.monitor-enabled=true
skylab.feature.redis.memory.threshold=0.6
skylab.feature.redis.memory.check-interval=60000
##########################redis-end##########################
