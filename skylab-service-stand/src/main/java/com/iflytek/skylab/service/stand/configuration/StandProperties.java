package com.iflytek.skylab.service.stand.configuration;

import lombok.Getter;
import lombok.Setter;

/**
 * 服务配置项
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class StandProperties {

    /**
     * 场景依赖异步诊断线程配置
     */
    private int threadCorePoolSize = 512;
    /**
     * 场景依赖异步诊断线程配置
     */
    private int threadMaximumPoolSize = 1024;
    /**
     * 场景依赖异步诊断线程配置
     */
    private long threadKeepAliveTime = 0L;

    /**
     * 场景依赖异步诊断线程配置
     */
    private int threadWorkQueueSize = 2048;
}
