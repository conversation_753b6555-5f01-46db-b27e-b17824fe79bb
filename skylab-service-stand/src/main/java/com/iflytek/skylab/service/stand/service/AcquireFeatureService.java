package com.iflytek.skylab.service.stand.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.cog2.feaflow.sdk.data.ZionDictModel;
import com.iflytek.hy.rec.feaacquire.FeaAcquire;
import com.iflytek.hy.rec.feaacquire.interfaces.param.FeaAcquireRequest;
import com.iflytek.hy.rec.feaacquire.interfaces.param.FeaAcquireResponse;
import com.iflytek.hy.rec.feaprocess.FeaPostProcess;
import com.iflytek.skylab.core.data.AcquireFeatureParam;
import com.iflytek.skylab.core.data.FeatureParam;
import com.iflytek.skylab.core.data.FeatureResult;
import com.iflytek.skylab.core.data.adapter.feature.AcquireFeatureParamAdapter;
import com.iflytek.skylab.core.dataapi.data.FeatureData;
import com.iflytek.skylab.core.dataapi.data.FeatureDataItem;
import com.iflytek.skylab.core.dataapi.data.FeatureQuery;
import com.iflytek.skylab.core.dataapi.data.FeatureQueryItem;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.common.exception.ParamInvalidException;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.dispatcher.api.DispatchApiData;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import skynet.boot.logging.LoggingCost;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 诊断Service
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
public class AcquireFeatureService {

    public final static String SCHEMA_CONFIG_KEY = "SchemaRowKey";
    public final static String DATASOURCE_CONFIG_KEY = "DataSource";

    private final CopyOptions copyOptions = CopyOptions.create();

    private final FeatureService featureService;

    private final FeaPostProcess feaPostProcess;

    private final AcquireFeatureParamAdapter acquireFeatureParamAdapter;

    private final FeaAcquire feaAcquire;


    public AcquireFeatureService(FeatureService featureService, FeaPostProcess feaPostProcess, FeaAcquire feaAcquire) {
        this.featureService = featureService;
        this.feaPostProcess = feaPostProcess;
        this.feaAcquire = feaAcquire;
        this.acquireFeatureParamAdapter = new AcquireFeatureParamAdapter();
    }


    /**
     * 特征功能选择器
     *
     * @param sceneInfo
     * @param apiRequest
     * @return com.iflytek.skylab.core.data.FeatureResult
     * <AUTHOR>
     * @date 2023/2/3 14:45
     */
    @LoggingCost
    public FeatureResult selectFeature(SceneInfo sceneInfo, DispatchApiRequest apiRequest) throws Exception {
        switch (sceneInfo.getFunctionCode()) {
            case FeatureParam.FUNC_CODE:
                log.info("traceId={};通用特征查询", apiRequest.getTraceId());
                return featureQuery(sceneInfo, apiRequest);
            case AcquireFeatureParam.FUNC_CODE:
                log.info("traceId={};引擎特征查询", apiRequest.getTraceId());
                return acquireFeatureParam(sceneInfo, apiRequest);
            default:
                throw new ParamInvalidException("SceneInfo.functionCode");
        }
    }


    /**
     * 引擎特征查询
     *
     * @param sceneInfo
     * @param apiRequest
     * @return com.iflytek.skylab.core.data.FeatureResult
     * <AUTHOR>
     * @date 2023/2/3 15:03
     */
    @LoggingCost
    private FeatureResult acquireFeatureParam(SceneInfo sceneInfo, DispatchApiRequest apiRequest) {
        String traceId = apiRequest.getTraceId();
        DispatchApiPayload payload = apiRequest.getPayload();
        Assert.notNull(payload, () -> new ParamNotExistException("apiRequest.getPayload() of ApiRequest"));
        Assert.notEmpty(payload.getData(), () -> new ParamNotExistException("payload.getData() of DispatchApiPayload"));

        DispatchApiData dispatchApiData = payload.getData().get(0);
        JSONObject data = dispatchApiData.getData();
        Assert.notEmpty(data, () -> new ParamNotExistException("JSONObject data of dispatchApiData"));

        AcquireFeatureParam acquireFeatureParam = data.to(AcquireFeatureParam.class);
        Assert.notNull(acquireFeatureParam, "AcquireFeatureParam must not null");

        FeaAcquireRequest feaAcquireRequest = acquireFeatureParamAdapter.adapt(traceId, sceneInfo, payload);
        FeaAcquireResponse feaAcquireResponse = new FeaAcquireResponse();
        try {
            feaAcquireResponse = feaAcquire.acquireFea(feaAcquireRequest);
            if (log.isDebugEnabled()) {
                log.debug("traceId:{}, FeaAcquireResponse :{}", traceId, JSON.toJSONString(feaAcquireResponse));
            }
            if (feaAcquireResponse == null) {
                log.error("FeaAcquireResponse=null, traceId={}", traceId);
            }
        } catch (Exception e) {
            int len = Math.min(10, e.getStackTrace().length);
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < len; i++) {
                builder.append(e.getStackTrace()[i].toString()).append(" | ");
            }
            log.error("call FeaAcquire error! traceId={}, msg={}, stack={}", traceId, e.getMessage(), builder);
        }

        return feaAcquireResponse == null ? new FeatureResult() : feaAcquireResponse.getResult();
    }

    /**
     * 通用特征查询
     *
     * @param sceneInfo
     * @param apiRequest
     * @return com.iflytek.skylab.core.data.FeatureResult
     * <AUTHOR>
     * @date 2023/2/3 15:03
     */
    @LoggingCost
    private FeatureResult featureQuery(SceneInfo sceneInfo, DispatchApiRequest apiRequest) {
        //regionn 参数解析、校验
        String schemaRowKey = apiRequest.getParameter().getString(SCHEMA_CONFIG_KEY);

        JSONObject dataSource = apiRequest.getParameter().getJSONObject(DATASOURCE_CONFIG_KEY);
        Assert.notNull(dataSource, () -> new ParamNotExistException("parameter." + DATASOURCE_CONFIG_KEY + " of ApiRequest"));

//        DataApiItem dataApiItem = dataSource.to(DataApiItem.class);
//        Assert.notNull(dataApiItem, () -> new ParamNotExistException("parameter." + DATASOURCE_CONFIG_KEY + " of ApiRequest"));

        DispatchApiPayload payload = apiRequest.getPayload();
        Assert.notNull(payload, () -> new ParamNotExistException("apiRequest.getPayload() of ApiRequest"));
        Assert.notEmpty(payload.getData(), () -> new ParamNotExistException("payload.getData() of DispatchApiPayload"));

        DispatchApiData dispatchApiData = payload.getData().get(0);
        JSONObject data = dispatchApiData.getData();
        Assert.notEmpty(data, () -> new ParamNotExistException("JSONObject data of dispatchApiData"));

        FeatureParam featureParam = data.to(FeatureParam.class);
        Assert.notNull(featureParam, "FeatureParam must not null");
        //endregion

        Map<String, String> sceneMap = null;
        if (featureParam.isSimpleMode()) {
            sceneMap = BeanUtil.beanToMap(sceneInfo, true, true)
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().toString()));
        }

        List<FeatureQueryItem> items = new ArrayList<>();
        for (FeatureParam.FeatureParamItem item : featureParam.getItems()) {
            FeatureQueryItem featureQueryItem = new FeatureQueryItem();
            featureQueryItem.setGraphVersion(item.getGraphVersion());
            featureQueryItem.setFeatureName(item.getFeatureName());
            featureQueryItem.setParams(injectSceneToParam(item.getParams(), sceneMap));
            featureQueryItem.setFeatureVersion(item.getFeatureVersion());

            items.add(featureQueryItem);
        }

        FeatureQuery featureQuery = new FeatureQuery();
        featureQuery.setDictRowKey(schemaRowKey);
//        featureQuery.setDataApiItem(dataApiItem);
        featureQuery.setItems(items);

        //查询
        FeatureData featureData = featureService.query(apiRequest.getTraceId(), featureQuery);
        FeatureResult featureResult = new FeatureResult();
        for (FeatureDataItem item : featureData.getItems()) {
            FeatureResult.FeatureResultItem resultItem = new FeatureResult.FeatureResultItem();
            BeanUtil.copyProperties(item, resultItem, copyOptions);
            processBlank(resultItem, sceneInfo);
            featureResult.getItems().add(resultItem);
        }
        return postProcessIfNecessary(sceneInfo, apiRequest, featureParam, featureResult);
    }

    /**
     * 特征空处理
     *
     * @param resultItem
     */
    private void processBlank(FeatureResult.FeatureResultItem resultItem, SceneInfo sceneInfo) {
        if (resultItem != null && "user_level".equals(resultItem.getFeatureName())) {
            if (resultItem.getValues() == null) {
                resultItem.setValues(Lists.newArrayList(new HashMap<>()));
            }
            for (Map<String, String> value : resultItem.getValues()) {
                if (StrUtil.isEmpty(value.get("user_level"))) {
                    //初中
                    if ("04".equals(sceneInfo.getPhaseCode())) {
                        value.put("user_level", "highScoreAdvanced");
                    } else {
                        value.put("user_level", "conventional");
                    }
                    if (log.isDebugEnabled()) {
                        log.debug("FeatureName={},特征值为空，设置默认值：{}", "user_level", value.get("user_level"));
                    }
                }
            }
        }
    }


    @LoggingCost
    private FeatureResult postProcessIfNecessary(SceneInfo sceneInfo, DispatchApiRequest apiRequest, FeatureParam featureParam, FeatureResult featureResult) {
        String traceId = apiRequest.getTraceId();
        try {
            // 筛选需要后处理的特征
            Map<Boolean, List<FeatureResult.FeatureResultItem>> map = Optional.ofNullable(featureResult.getItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.partitioningBy(item -> featureService.needPostProcess(traceId, item.getFeatureName())));

            // 没有需要后处理的话返回
            List<FeatureResult.FeatureResultItem> items = map.get(Boolean.TRUE);
            if (CollUtil.isEmpty(items)) {
                return featureResult;
            }

            FeatureResult oldFea = new FeatureResult().setItems(items);

            if (log.isDebugEnabled()) {
                log.debug("traceId:{}, oldFea:{}", traceId, JSON.toJSONString(oldFea));
            }

            FeatureResult newFea = feaPostProcess.postProcess(traceId, sceneInfo, featureParam, oldFea);
            if (log.isDebugEnabled()) {
                log.debug("traceId:{}, newFea:{}", traceId, JSON.toJSONString(newFea));
            }

            // 组合 已经后处理的 以及 无需后处理的特征结果
            newFea.getItems().addAll(map.get(Boolean.FALSE));
            return newFea;
        } catch (Exception e) {
            int len = Math.min(10, e.getStackTrace().length);
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < len; i++) {
                builder.append(e.getStackTrace()[i].toString()).append(" | ");
            }
            log.error("call FeaPostProcess error! traceId={}, msg={}, stack={}", traceId, e.getMessage(), builder);
        }
        return featureResult;
    }


    private List<Map<String, String>> injectSceneToParam(List<Map<String, String>> params, Map<String, String> sceneMap) {
        if (CollUtil.isEmpty(sceneMap)) {
            return params;
        }

        if (CollUtil.isEmpty(params)) {
            params = Lists.newArrayList(new HashMap<>());
        }

        List<Map<String, String>> newParams = new ArrayList<>();
        params.forEach(map -> {
            Map<String, String> newParam = new HashMap<>(sceneMap);
            newParam.putAll(map);
            newParams.add(newParam);
        });
        return newParams;
    }

    /**
     * 特征Schema 查询
     *
     * @param apiRequest
     * @return com.iflytek.skylab.core.zion.data.ZionDictModel
     * <AUTHOR>
     * @date 2023/2/3 14:49
     */
    @LoggingCost
    public ZionDictModel querySchema(DispatchApiRequest apiRequest) throws Exception {
        //如果为空，将采用系统默认的 schema 配置
        String schemaRowKey = apiRequest.getParameter().getString(SCHEMA_CONFIG_KEY);
        //查询
        return featureService.querySchema(apiRequest.getTraceId(), schemaRowKey);
    }

    public Long clearRedisCache() {
        return featureService.clearRedisCache();
    }
}
