package com.iflytek.skylab.service.stand.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.ttl.TtlCallable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.service.StudyLogGroupService;
import com.iflytek.skylab.core.dataapi.service.StudyLogService;
import com.iflytek.skylab.core.dataapi.service.StudyMacrographLogService;
import com.iflytek.skylab.core.dataapi.util.OdeonUtil;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.service.stand.configuration.StandProperties;
import com.iflytek.skylab.service.stand.data.AnswerTopicTraceLog;
import com.iflytek.skylab.service.stand.data.SceneExtend;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.data.TraceHeaderUtils;
import com.iflytek.skyline.brave.data.TraceRecordGroup;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.feign.SkylineRouterFeign;
import com.iflytek.skyline.resource.domain.ResourceItem;
import com.iflytek.skyline.resource.exception.NotFoundResource;
import com.iflytek.skyline.resource.manager.ResourceManager;
import com.iflytek.skyline.resource.service.ResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import skynet.boot.exception.SkynetException;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 行为服务
 *
 * <AUTHOR>
 * @date 2022/3/21 2:27 下午
 */
@Slf4j
@Validated
public class BehaviorService {

    @Value("${spring.application.name:skylab-stand}")
    private String springApplicationName;

    private final CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();
    private final ResourceManager<List<SceneExtend>> resourceManager;
    private final SkylineRouterFeign skylineRouterFeign;
    private final StudyLogService studyLogService;

    private final StudyLogGroupService studyLogGroupService;

    private final StudyMacrographLogService studyMacrographLogService;
    private final GraphService graphService;
    private final ExecutorService pool;
    private final TraceUtils traceUtils;

    /**
     * 大图谱外部学习场景(错题本、精品密卷、同步测试)
     */
    private static final List<StudyCodeEnum> MACROGRAPH_EXTERNAL_STUDY_CODE = Arrays.asList(StudyCodeEnum.MACROGRAPH_ERROR_BOOK, StudyCodeEnum.MACROGRAPH_QUALITY_TEST_PAPER, StudyCodeEnum.MACROGRAPH_SYNC_TEST);

    public BehaviorService(SkylineRouterFeign skylineRouterFeign, ResourceService resourceService, StudyLogService studyLogService, StudyMacrographLogService studyMacrographLogService, GraphService graphService, StandProperties standProperties, StudyLogGroupService studyLogGroupService, TraceUtils traceUtils) {
        this.studyMacrographLogService = studyMacrographLogService;
        if (log.isDebugEnabled()) {
            log.debug("StandProperties={}", standProperties);
        }
        this.skylineRouterFeign = skylineRouterFeign;
        this.resourceManager = new ResourceManager<List<SceneExtend>>(resourceService) {
            @Override
            protected List<SceneExtend> convert(String configContent) {
                return JSON.parseObject(configContent, new TypeReference<List<SceneExtend>>() {
                });
            }
        };
        this.studyLogService = studyLogService;
        this.studyLogGroupService = studyLogGroupService;
        this.graphService = graphService;
        this.pool = new ThreadPoolExecutor(standProperties.getThreadCorePoolSize(), standProperties.getThreadMaximumPoolSize(), standProperties.getThreadKeepAliveTime(), TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(standProperties.getThreadWorkQueueSize()), new ThreadFactoryBuilder().setNameFormat("request-router-pool-%d").build(), new ThreadPoolExecutor.AbortPolicy());
        this.traceUtils = traceUtils;
    }

    /**
     * 答题记录上报的公共处理逻辑
     *
     * @param traceId       追踪ID
     * @param sceneInfo     场景信息
     * @param studyLogParam 学习日志参数
     * @param resConfig     资源配置
     * @param isOS          是否是OS版本
     * @return 学习日志结果
     * @throws Exception 异常
     */
    private StudyLogResult handleExtend(String traceId, SceneInfo sceneInfo, StudyLogParam studyLogParam, JSONObject resConfig, boolean isOS) throws Exception {
        long startTime = System.currentTimeMillis();

        if (log.isDebugEnabled()) {
            log.debug("traceId={};StudyLogParam={}", traceId, studyLogParam);
        }

        //保存答题记录，同步诊断当前场景，异步诊断依赖的场景。（调用路由诊断）
        List<String> nodeIdList = new ArrayList<>(studyLogParam.getItems().size());
        Set<String> catalogIds = new HashSet<>(studyLogParam.getItems().size());
        List<FeedbackLog> feedbackLogList = new ArrayList<>(studyLogParam.getItems().size());

        //兜底-更新catalog
        boolean updateCatalog = CollUtil.isNotEmpty(studyLogParam.getItems()) && studyLogParam.getItems().size() == 1 && StrUtil.isEmpty(studyLogParam.getItems().get(0).getCatalogCode());
        if (updateCatalog) {
            log.debug("StudyLogRecord is empty ,use sceneInfo.getCatalogCode()={}", sceneInfo.getCatalogCode());
            studyLogParam.getItems().get(0).setCatalogCode(sceneInfo.getCatalogCode());
        }

        for (StudyLogRecord studyLogRecord : studyLogParam.getItems()) {
            //学情答题记录中 不设值funcCode，由测评推题或者点推题设值。
            FeedbackLog feedbackLog = new FeedbackLog();
            BeanUtil.copyProperties(sceneInfo, feedbackLog, copyOptions);
            BeanUtil.copyProperties(studyLogRecord, feedbackLog, copyOptions);
            feedbackLog.setScoreRatio(repairScoreRatio(feedbackLog));
            //设置来源
            feedbackLog.setFrom(sceneInfo.getExt1());

            feedbackLogList.add(feedbackLog);
            nodeIdList.add(studyLogRecord.getNodeId());
            catalogIds.add(studyLogRecord.getCatalogCode());
        }

        // 保存答题记录
        long step2Start = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug("saveFeedbackLogListAndGet= {}", feedbackLogList);
        }
        if (StudyLogFuncEnum.STUDY_CORRECT_LOG.name().equals(sceneInfo.getFunctionCode()) || StudyLogFuncEnum.KC_STUDY_CORRECT_LOG.name().equals(sceneInfo.getFunctionCode())) {
            // 保存批改记录
            log.debug("save StudyCorrectLogList ");
            List<FeedbackLog> feedbackLogs = studyLogService.saveStudyCorrectLogList(traceId, feedbackLogList);
            long step2End = System.currentTimeMillis();
            log.info("handleExtend步骤2-保存批改记录完成, traceId={}, 耗时={}ms, 保存数={}", traceId, step2End - step2Start, feedbackLogs.size());
            return new StudyLogResult().setIdList(feedbackLogs.stream().map(FeedbackLog::getId).collect(Collectors.toList()));
        }
        List<FeedbackLog> savedFeedbackLogList = studyLogService.saveFeedbackLogListAndGet(traceId, feedbackLogList);
        List<String> idList = savedFeedbackLogList.stream().map(FeedbackLog::getId).collect(Collectors.toList());
        long step2End = System.currentTimeMillis();
        log.info("handleExtend步骤2-保存答题记录完成, traceId={}, 耗时={}ms, 保存数={}", traceId, step2End - step2Start, savedFeedbackLogList.size());
        
        if (log.isDebugEnabled()) {
            log.debug("saveFeedbackLogListAndGet return idList= {}", idList);
        }
        if (savedFeedbackLogList.size() != feedbackLogList.size()) {
            log.warn("Ooh, some things wrong happened saving feedbackLogs!");
        }

        String userId = sceneInfo.getUserId();
        boolean notRecord = StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX);
        // 推送到 Odeon 平台
        long step3Start = System.currentTimeMillis();
        savedFeedbackLogList.forEach(feedbackLog -> {
            JSONObject json = OdeonUtil.wrappedOrNull(feedbackLog);
            if (json != null) {
                if (notRecord) {
                    //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
                }else {
                    traceUtils.record(TraceRecordGroup.EVAL, "FEEDBACK_LOG", json);
                }
            }
        });
        long step3End = System.currentTimeMillis();
        log.info("handleExtend步骤3-推送Odeon平台完成, traceId={}, 耗时={}ms", traceId, step3End - step3Start);
        
        // 数据埋点
        long step4Start = System.currentTimeMillis();
        AnswerTopicTraceLog traceLog = new AnswerTopicTraceLog().setFeedbackLogs(savedFeedbackLogList);
        try {
            if (notRecord) {
                //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
            }else {
                traceUtils.record(TraceRecordGroup.FLOW, AnswerTopicTraceLog.TYPE, traceLog);
            }
            long step4End = System.currentTimeMillis();
            log.info("handleExtend步骤4-数据埋点完成, traceId={}, 耗时={}ms", traceId, step4End - step4Start);
        } catch (Exception e) {
            long step4End = System.currentTimeMillis();
            log.error("handleExtend步骤4-数据埋点异常, traceId={}, 耗时={}ms, message={}", traceId, step4End - step4Start, e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("AnswerTopicTraceLog:{}", traceLog);
            }
            log.error("AnswerTopicTraceLog 埋点日志记录异常，message={}", e.getMessage());
        }

        // 获取资源配置
        long step5Start = System.currentTimeMillis();
        ResourceItem resourceItem = resConfig.toJavaObject(ResourceItem.class);
        if (log.isDebugEnabled()) {
            log.debug("ResourceItem= {}", resourceItem);
        }
        List<SceneExtend> sceneExtendList = resourceManager.getParam(resourceItem.getId(), resourceItem.getVersion());
        if (log.isDebugEnabled()) {
            log.debug("sceneExtendList= {}", sceneExtendList);
        }
        if (sceneExtendList == null) {
            throw new NotFoundResource(resourceItem.getId(), resourceItem.getVersion());
        }
        long step5End = System.currentTimeMillis();
        log.info("handleExtend步骤5-获取资源配置完成, traceId={}, 耗时={}ms, 场景扩展数={}", traceId, step5End - step5Start, sceneExtendList.size());

        // 诊断处理
        long step6Start = System.currentTimeMillis();
        Optional<SceneExtend> currentSceneExtend = sceneExtendList.stream().filter(x -> sceneInfo.getStudyCode() == x.getStudyCode()).findFirst();
        if (currentSceneExtend.isPresent()) {
            if (log.isDebugEnabled()) {
                log.debug("本次场景下诊断(同步)[StudyCode={}({})]...", sceneInfo.getStudyCode(), sceneInfo.getStudyCode().getDesc());
            }
            Set<String> nodeIdSet = Sets.newHashSet(nodeIdList);
            Set<String> catalogIdsSet = Sets.newHashSet(catalogIds);
            ArrayList<String> nodeList = Lists.newArrayList(nodeIdSet);
            ArrayList<String> catalogIdsList = Lists.newArrayList(catalogIdsSet);
            
            long syncDiagnoseStart = System.currentTimeMillis();
            if (isOS) {
                extractedSyncOs(traceId, sceneInfo, nodeList, catalogIdsList);
            } else if (StudyCodeEnum.SYNC_OS.equals(sceneInfo.getStudyCode())) {
                extractedSyncOs(traceId, sceneInfo, nodeList, catalogIdsList);
            } else {
                //本次场景下诊断（同步调用）直接使用 答题记录中的关联的点
                this.extracted(traceId, sceneInfo, nodeList);
            }
            long syncDiagnoseEnd = System.currentTimeMillis();
            log.info("handleExtend步骤6-同步诊断完成, traceId={}, 耗时={}ms, 节点数={}, 章节数={}", traceId, syncDiagnoseEnd - syncDiagnoseStart, nodeList.size(), catalogIdsList.size());
            
            //其他场景下诊断（异步调用）
            for (SceneExtend sceneExtend : currentSceneExtend.get().getExtend()) {
                if (log.isDebugEnabled()) {
                    log.debug("依赖场景下诊断(异步)[StudyCode={}({})]...", sceneExtend.getStudyCode(), sceneExtend.getStudyCode().getDesc());
                }
                SceneInfo sceneInfoClone = JSON.parseObject(sceneInfo.toJson(), SceneInfo.class);
                String asyncTraceId = String.format("%s_%s", traceId, sceneExtend.getStudyCode());
                Future<ApiResponse> recommendFuture = pool.submit(TtlCallable.get(() -> {
                    try {
                        return extracted(asyncTraceId, sceneInfoClone, savedFeedbackLogList, sceneExtend);
                    } catch (SkynetException se) {
                        String msg = StrUtil.format("依赖场景下诊断(异步)[StudyCode={}({})] 失败：{}", sceneExtend.getStudyCode(), sceneExtend.getStudyCode().getDesc(), se.getMessage());
                        log.error(msg);
                    } catch (Exception e) {
                        log.error(String.format("依赖场景下诊断(异步)[StudyCode=%s(%s)] 失败：%s", sceneExtend.getStudyCode(), sceneExtend.getStudyCode().getDesc(), e.getMessage()), e);
                    }
                    return null;
                }));
                if (log.isDebugEnabled()) {
                    log.debug("AsyncTraceId= {}; RecommendFuture= {}", asyncTraceId, recommendFuture);
                }
            }
        }
        long step6End = System.currentTimeMillis();
        log.info("handleExtend步骤6-诊断处理完成, traceId={}, 耗时={}ms", traceId, step6End - step6Start);

        long totalEnd = System.currentTimeMillis();
        log.info("handleExtend执行完成, traceId={}, 总耗时={}ms", traceId, totalEnd - startTime);
        return new StudyLogResult().setIdList(idList);
    }

    public StudyLogResult extend(String traceId, SceneInfo sceneInfo, StudyLogParam studyLogParam, JSONObject resConfig) throws Exception {
        return handleExtend(traceId, sceneInfo, studyLogParam, resConfig, false);
    }

    public StudyLogResult extendOS(String traceId, SceneInfo sceneInfo, StudyLogParam studyLogParam, JSONObject resConfig) throws Exception {
        return handleExtend(traceId, sceneInfo, studyLogParam, resConfig, true);
    }

    /**
     * 大图谱学情上报
     *
     * @param traceId
     * @param sceneInfo
     * @param studyLogParam
     * @return
     * @throws Exception
     */
    public StudyLogResult macrographExtend(String traceId, SceneInfo sceneInfo, StudyLogParam studyLogParam) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("traceId={};StudyLogParam={}", traceId, studyLogParam);
        }
        //保存答题记录，同步诊断当前场景，异步诊断依赖的场景。（调用路由诊断）
        List<String> nodeIdList = new ArrayList<>(studyLogParam.getItems().size());
        List<FeedbackLog> feedbackLogList = new ArrayList<>(studyLogParam.getItems().size());
        for (StudyLogRecord studyLogRecord : studyLogParam.getItems()) {

            //学情答题记录中 不设值funcCode，由测评推题或者点推题设值。
            FeedbackLog feedbackLog = new FeedbackLog();
            BeanUtil.copyProperties(sceneInfo, feedbackLog, copyOptions);
            BeanUtil.copyProperties(studyLogRecord, feedbackLog, copyOptions);
            feedbackLog.setScoreRatio((feedbackLog.getStandardScore() == null || feedbackLog.getStandardScore() == 0) ? 0 : feedbackLog.getScore() / feedbackLog.getStandardScore());
            feedbackLogList.add(feedbackLog);
            nodeIdList.add(studyLogRecord.getNodeId());
        }


        List<FeedbackLog> savedFeedbackLogList = studyMacrographLogService.saveFeedbackLogListAndGet(traceId, feedbackLogList);
        List<String> idList = savedFeedbackLogList.stream().map(FeedbackLog::getId).collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.debug("saveFeedbackLogListAndGet return idList= {}", idList);
        }
        if (savedFeedbackLogList.size() != feedbackLogList.size()) {
            log.warn("Ooh, some things wrong happened saving feedbackLogs!");
        }

        String userId = sceneInfo.getUserId();
        boolean notRecord = StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX);
        // 推送到 Odeon 平台
        savedFeedbackLogList.forEach(feedbackLog -> {
            JSONObject json = OdeonUtil.wrappedOrNull(feedbackLog);
            if (json != null) {

                if (notRecord) {
                    //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
                }else {
                    traceUtils.record(TraceRecordGroup.EVAL, "FEEDBACK_LOG", json);
                }
            }
        });

        return new StudyLogResult().setIdList(idList);
    }

    /**
     * 修复得分率
     *
     * @param feedbackLog
     * @return
     */
    private double repairScoreRatio(FeedbackLog feedbackLog) {
        if (feedbackLog.getStandardScore() == null || feedbackLog.getStandardScore() == 0) {
            return 0;
        }
        double scoreRatio = feedbackLog.getScore() / feedbackLog.getStandardScore();
        if (scoreRatio > 1) {
            log.error("答题/批改记录得分率超标,feedbackLog = {}", JSON.toJSONString(feedbackLog));
        }
        return Math.min(scoreRatio, 1);
    }

    /**
     * 诊断
     *
     * @param traceId
     * @param sceneInfo
     * @param nodeIdList 学习点（考点，锚点） 列表
     * @return
     */
    private ApiResponse extracted(String traceId, SceneInfo sceneInfo, List<String> nodeIdList) {
        if (log.isDebugEnabled()) {
            log.debug("TraceId= {}; NodeIdList= {}", traceId, nodeIdList);
        }  //调用路由服务开始画像诊断
        MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();
        masterDiagnoseParam.setNodeIds(nodeIdList);
        sceneInfo.setFunctionCode(masterDiagnoseParam.getFuncCode());
        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setTraceId(traceId);
        apiRequest.setObjectPayload(masterDiagnoseParam);
        apiRequest.setObjectScene(sceneInfo);
        MultiValueMap<String, String> header = TraceHeaderUtils.buildHeader(traceId, springApplicationName);
        if (log.isDebugEnabled()) {
            log.debug("ApiRequest= {}", apiRequest);
        }
        ApiResponse apiResponse = skylineRouterFeign.routeCall(apiRequest, header);
        if (log.isDebugEnabled()) {
            log.debug("ApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }


    /**
     * 诊断 精准学OS
     *
     * @param traceId
     * @param sceneInfo
     * @param nodeIdList 学习点（考点，锚点） 列表
     * @return
     */
    private ApiResponse extractedSyncOs(String traceId, SceneInfo sceneInfo, List<String> nodeIdList, List<String> catalogIds) {
        if (log.isDebugEnabled()) {
            log.debug("TraceId= {}; NodeIdList= {}，catalogIds={}", traceId, nodeIdList, catalogIds);
        }
        //调用路由服务开始画像诊断
        MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();
        masterDiagnoseParam.setNodeIds(nodeIdList);
        //精准学os下点和章节都传
        if (CollUtil.isNotEmpty(catalogIds)) {
            masterDiagnoseParam.setCatalogIds(catalogIds);
        }
        //精准学os -单元复习-入门测 批量上报日志-做测评终止画像更新  allUpdate=true
        if (HisStudyCodeEnum.UNIT_REVIEW.equals(sceneInfo.getHisStudyCode()) && BizActionEnum.EXAM_UNIT_BASIS.equals(sceneInfo.getBizAction())) {
            masterDiagnoseParam.setAllUpdate(true);
        }

        //FunctionCode-诊断场景知识簇分流
        if (StudyLogFuncEnum.KC_STUDY_LOG.name().equals(sceneInfo.getFunctionCode())) {
            masterDiagnoseParam.setMasterFuncEnum(MasterFuncEnum.KC_MASTER_DIAGNOSE);
        }

        sceneInfo.setFunctionCode(masterDiagnoseParam.getFuncCode());
        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setTraceId(traceId);
        apiRequest.setObjectPayload(masterDiagnoseParam);
        apiRequest.setObjectScene(sceneInfo);
        MultiValueMap<String, String> header = TraceHeaderUtils.buildHeader(traceId, springApplicationName);
        if (log.isDebugEnabled()) {
            log.debug("ApiRequest= {}", apiRequest);
        }
        ApiResponse apiResponse = skylineRouterFeign.routeCall(apiRequest, header);
        if (log.isDebugEnabled()) {
            log.debug("ApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }

    private ApiResponse extracted(String traceId, SceneInfo sceneInfo, List<FeedbackLog> feedbackLogList, SceneExtend sceneExtend) {

        //将当前的答题记录 clone 出新点的答题记录，（修改 点id，点类型，clone=true，refTraceId = clone4_${refTraceId} ）
        List<FeedbackLog> cloneFeedbackLogList = new ArrayList<>();
        //学习点（考点，锚点） 列表
        List<String> nodeIdList = new ArrayList<>();

        //根据场景配置 根据题Id 反向查找 对应的考点或锚点
        for (FeedbackLog feedbackLog : feedbackLogList) {
            GraphQuery graphQuery = sceneExtend.getGraphQuery();
            graphQuery.setTraceId(traceId);
            graphQuery.setGraphVersion(sceneInfo.getGraphVersion());
            graphQuery.setRootVertexIdList(Arrays.asList(feedbackLog.getResNodeId()));
            if (log.isDebugEnabled()) {
                log.debug("GraphQuery={}", graphQuery);
            }
            GraphData graphData = graphService.queryVerticesReverse(graphQuery);
            if (log.isDebugEnabled()) {
                log.debug("GraphData={}", graphData);
            }
            // 可能存在多个 考点和锚点，要为每个 考点clone答题记录
            List<GraphData.GraphVertex> vertices = graphData.getVertices();
            int index = 0;
            for (GraphData.GraphVertex item : vertices) {
                feedbackLog.setNodeId(item.getId());
                feedbackLog.setNodeType(NodeTypeEnum.parse(item.getLabel()));
                feedbackLog.setCloneFlag(1);
                //不用关联原推荐记录，所以此处的RefTraceId 需要重新赋值
                feedbackLog.setRefTraceId(String.format("clone4_%d_%s", index++, feedbackLog.getRefTraceId()));

                nodeIdList.add(item.getId());
                FeedbackLog feedbackLogClone = JSON.parseObject(feedbackLog.toJson(), FeedbackLog.class);
                feedbackLogClone.setId(null);
                cloneFeedbackLogList.add(feedbackLogClone);
            }
        }

        if (CollectionUtil.isEmpty(cloneFeedbackLogList)) {
            log.warn("no clone feedbackLog. traceId={}", traceId);
            return null;
        }

        String userId = sceneInfo.getUserId();
        boolean notRecord = StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX);

        //推送到 Odeon 平台
        cloneFeedbackLogList.forEach(feedbackLog -> {
            JSONObject json = OdeonUtil.wrappedOrNull(feedbackLog);
            if (json != null) {
                if (notRecord) {
                    //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
                } else {
                    traceUtils.record(TraceRecordGroup.EVAL, "FEEDBACK_LOG", json);
                }
            }
        });
        // 数据埋点
        AnswerTopicTraceLog traceLog = new AnswerTopicTraceLog().setFeedbackLogs(cloneFeedbackLogList);
        if (notRecord) {
            //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
        }else {
            traceUtils.record(TraceRecordGroup.FLOW, AnswerTopicTraceLog.TYPE, traceLog);
        }
        //保存 Clone 的答题记录
        if (log.isDebugEnabled()) {
            log.debug("Clone FeedbackLogList= {}", cloneFeedbackLogList);
        }
        List<String> idList = this.studyLogService.saveFeedbackLogList(traceId, cloneFeedbackLogList);
        if (log.isDebugEnabled()) {
            log.debug("Clone FeedbackLogList Return idList= {}", idList);
        }

        sceneInfo.setStudyCode(sceneExtend.getStudyCode());
        return extracted(traceId, sceneInfo, nodeIdList);

    }

    /**
     * 作答日志查询
     *
     * @param traceId
     * @param sceneInfo
     * @param studyLogParam
     * @return
     */
    public StudyLogQueryResult query(String traceId, SceneInfo sceneInfo, StudyLogQueryParam studyLogParam) {
        if (log.isDebugEnabled()) {
            log.debug("traceId={};StudyLogParam={}", traceId, studyLogParam);
        }

        //目录下锚点
        List<String> anchor = querySubGraphWithCata(traceId, sceneInfo.getGraphVersion(), studyLogParam.getCatalogId());
        StudyLogQuery query = new StudyLogQuery()
                .setUserId(sceneInfo.getUserId())
                .setSubjectCode(sceneInfo.getSubjectCode())
                .setPhaseCode(sceneInfo.getPhaseCode())
                .setNodeType(NodeTypeEnum.ANCHOR_POINT)
                .setNodeIdList(anchor)
                .setBizCodeList(Lists.newArrayList(sceneInfo.getBizCode()));

        boolean existStudentLog = studyLogService.existStudentLog(traceId, query);


        return new StudyLogQueryResult().setExistStudentLog(existStudentLog);
    }

    /**
     * 作答日志查询
     *
     * @param traceId
     * @param sceneInfo
     * @param behaviorQueryParam
     * @return
     */
    public Map<String, List<StudyLogData>> batchQuery(String traceId, SceneInfo sceneInfo, BehaviorQueryParam behaviorQueryParam) {
        if (log.isDebugEnabled()) {
            log.debug("traceId={};behaviorQueryParam={}", traceId, behaviorQueryParam);
        }

        if (null == behaviorQueryParam || CollectionUtils.isEmpty(behaviorQueryParam.getNodeInfos())) {
            return Maps.newHashMapWithExpectedSize(0);
        }

        if (null != behaviorQueryParam.getMaxNum() && behaviorQueryParam.getMaxNum() > 100) {
            log.error("MaxNum 數量超過限制");
            throw new IllegalArgumentException("MaxNum 数量不得超过100");
        }

        // 取出入参里的锚点ID
        List<String> anchorPoints = behaviorQueryParam.getNodeInfos().stream()
                .filter(Objects::nonNull)
                .filter(queryParam -> NodeTypeEnum.ANCHOR_POINT.equals(queryParam.getNodeType()))
                .map(NodeInfo::getNodeId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 取出入参里没有锚点id但是有章节信息的
        List<String> catalogs = behaviorQueryParam.getNodeInfos().stream()
                .filter(Objects::nonNull)
                .filter(queryParam -> NodeTypeEnum.ANCHOR_POINT.equals(queryParam.getNodeType()) && StringUtils.isBlank(queryParam.getNodeId()))
                .map(NodeInfo::getCatalogId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 查询章节下锚点然后并入锚点列表
        if (CollectionUtils.isNotEmpty(catalogs)) {
            //查询目录下锚点
            String graphVer = sceneInfo.getGraphVersion();
            List<String> catalogAnchorPoints = catalogs.stream().map(catalogId -> querySubGraphWithCata(traceId, graphVer, catalogId)).flatMap(List::stream).distinct().collect(Collectors.toList());
            anchorPoints.addAll(catalogAnchorPoints);
        }

        // 没有查询到锚点的，返回空
        if (CollectionUtils.isEmpty(anchorPoints)) {
            return Maps.newHashMapWithExpectedSize(0);
        }

        // 查询锚点下的作答记录
        StudyLogQuery query = new StudyLogQuery()
                .setUserId(sceneInfo.getUserId())
                .setSubjectCode(sceneInfo.getSubjectCode())
                .setPhaseCode(sceneInfo.getPhaseCode())
                .setNodeType(NodeTypeEnum.ANCHOR_POINT)
                .setNodeIdList(anchorPoints)
                .setBizCodeList(Lists.newArrayList(sceneInfo.getBizCode()));

        List<StudyLogData> studyLogs = studyLogGroupService.queryFeedbackLogsWithFeedBackTime(traceId, query, behaviorQueryParam.getStartDate(), behaviorQueryParam.getEndDate(), behaviorQueryParam.getMaxNum());

        if (CollectionUtil.isEmpty(studyLogs)) {
            return Maps.newHashMapWithExpectedSize(0);
        } else {
            // 根据NodeId进行分组后返回
            return studyLogs.stream().collect(Collectors.groupingBy(StudyLogBase::getNodeId));
        }
    }

    /**
     * 图谱查询-获取锚点下面题目
     *
     * @param traceId
     * @param graphVersion
     * @param cata
     * @return
     */
    private List<String> querySubGraphWithCata(String traceId, String graphVersion, String cata) {

        List<String> anchors = new ArrayList<>();

        GraphVertexesQuery query = new GraphVertexesQuery();
        query.setTraceId(traceId);
        query.setIds(Lists.newArrayList(cata));
        query.setGraphVersion(graphVersion);
        GraphData graphData = graphService.queryVertexByIds(query);
        List<GraphData.GraphVertex> vertices = graphData.getVertices();

        if (CollectionUtil.isNotEmpty(vertices)) {
            for (GraphData.GraphVertex vertex : vertices) {
                String label = vertex.getLabel();

                SubGraphQuery subGraphQuery = new SubGraphQuery();
                subGraphQuery.setTraceId(traceId);
                subGraphQuery.setGraphVersion(graphVersion);
                subGraphQuery.setRootVertexLabel(label);
                subGraphQuery.setRootVertexIdList(Lists.newArrayList(cata));
                List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
                edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(label).target("ANCHOR_POINT").build());
                subGraphQuery.setEdgeLabels(edgeLabels);
                if (log.isDebugEnabled()) {
                    log.debug("GraphQuery={}", subGraphQuery);
                }
                GraphData subGraph = graphService.querySubGraph(subGraphQuery);
                if (log.isDebugEnabled()) {
                    log.debug("GraphData={}", subGraph);
                }
                List<GraphData.GraphEdge> edges = subGraph.getEdges();
                if (CollUtil.isNotEmpty(edges)) {
                    List<String> collect = edges.stream().map(GraphData.GraphEdge::getTarget).collect(Collectors.toList());
                    anchors.addAll(collect);
                    if (log.isDebugEnabled()) {
                        log.debug("anchors={}", anchors.size());
                    }
                }
            }
        }
        return anchors;
    }
}
