package com.iflytek.skylab.service.stand.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.hy.rec.framework.exception.EngineException;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.data.adapter.scene.SceneInfoSelector;
import com.iflytek.skylab.core.dataapi.data.StudyLogData;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import com.iflytek.skylab.service.stand.feign.EngineProxyFeign;
import com.iflytek.skylab.service.stand.service.BehaviorService;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.logging.LoggingCost;

import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用特征服务
 *
 * <AUTHOR> 2022年03月01日14:03:31
 */
@Slf4j
@Api(tags = "1.行为服务")
@RestController
@EnableSkynetSwagger2
@RequestMapping(value = "/skylab/api/v1/behavior", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class BehaviorController {

    private final static String SENSE_DEPEND_CONFIG_KEY = "SceneExtend";
    private final BehaviorService behaviorService;

    @Autowired
    private EngineProxyFeign engineProxyFeign;

    public BehaviorController(BehaviorService behaviorService) {
        this.behaviorService = behaviorService;
    }


    /**
     * 答题记录查询
     *
     * @param apiRequest
     * @return
     * @throws
     */
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "答题记录查询")
    @PostMapping(value = "/query")
    @LoggingCost
    public DispatchApiResponse query(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        //入参 出参类型是 StudyLogParam  StudyLogResult
        if (log.isDebugEnabled()) {
            log.debug("BehaviorController#query,DispatchApiRequest= {}", apiRequest);
        }
        DispatchApiPayload payload = apiRequest.getPayload();
        if (payload == null) {
            throw new ParamNotExistException("apiRequest.getPayload() of ApiRequest");
        }

        StudyLogQueryParam studyLogParam = payload.getData(StudyLogQueryParam.class);
        Assert.notNull(studyLogParam, "StudyLogQueryParam must not null");

        DispatchApiResponse apiResponse = new DispatchApiResponse();
        try {
            //endregion
            SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);

            StudyLogQueryResult studyLogQueryResult = behaviorService.query(apiRequest.getTraceId(), sceneInfo, studyLogParam);

            apiResponse.setPayload(studyLogQueryResult);
            if (log.isDebugEnabled()) {
                log.debug("BehaviorController#query,DispatchApiResponse= {}", apiResponse);
            }
        } catch (EngineException ee) {
            log.error("Call reportAnswerRecord error code= {}; message= {}; failover={}", ee.getErrorCode(), ee.getMessage(), ee.getRedirect());

            ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
            if (ee.getRedirect()) {
                header.setErrorNodes(Lists.newArrayList(new ApiResponseHeader.ErrorNode("failover", "true")));
            }
            apiResponse.setHeader(header);
        }
        apiResponse.setTraceId(apiRequest.getTraceId());
        return apiResponse;
    }


    /**
     * 答题记录上报的公共处理逻辑
     * @param apiRequest 请求参数
     * @param isOS 是否是OS版本
     * @return 响应结果
     * @throws Exception 异常
     */
    private DispatchApiResponse handleReportAnswerRecord(DispatchApiRequest apiRequest, boolean isOS) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("BehaviorController#handleReportAnswerRecord,DispatchApiRequest= {}", apiRequest);
        }

        // region  参数校验
        JSONObject senseDependConfig = apiRequest.getParameter().getJSONObject(SENSE_DEPEND_CONFIG_KEY);
        if (senseDependConfig == null) {
            throw new ParamNotExistException("parameter." + SENSE_DEPEND_CONFIG_KEY + " of ApiRequest");
        }

        DispatchApiPayload payload = apiRequest.getPayload();
        if (payload == null) {
            throw new ParamNotExistException("apiRequest.getPayload() of ApiRequest");
        }

        StudyLogParam studyLogParam = payload.getData(StudyLogParam.class);
        Assert.notNull(studyLogParam, "StudyLogParam must not null");

        DispatchApiResponse apiResponse = new DispatchApiResponse();
        try {
            //endregion
            SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);
            StudyLogResult studyLogResult = new StudyLogResult();

            if (CollectionUtils.isNotEmpty(studyLogParam.getItems())){
                List<StudyLogRecord> studyLogRecords = studyLogParam.getItems();

                // 记录按bookCode分组
                Map<String,List<StudyLogRecord>> studyLogBookMap = groupStudyLogByBook(sceneInfo,studyLogRecords);

                // 分组调用诊断
                List<String> ids = Lists.newArrayListWithExpectedSize(studyLogRecords.size());
                for (Map.Entry<String,List<StudyLogRecord>> entry : studyLogBookMap.entrySet()){
                    SceneInfo callSceneInfo = new SceneInfo();
                    StudyLogParam callParam = new StudyLogParam();
                    BeanUtil.copyProperties(sceneInfo,callSceneInfo);
                    BeanUtil.copyProperties(studyLogParam,callParam);
                    callSceneInfo.setBookCode(entry.getKey());
                    callParam.setItems(entry.getValue());
                    studyLogResult = isOS ? 
                        behaviorService.extendOS(apiRequest.getTraceId(), callSceneInfo, callParam, senseDependConfig) :
                        behaviorService.extend(apiRequest.getTraceId(), callSceneInfo, callParam, senseDependConfig);
                    ids.addAll(studyLogResult.getIdList());
                }

                studyLogResult.setIdList(ids);
            }

            apiResponse.setPayload(studyLogResult);
            if (log.isDebugEnabled()) {
                log.debug("BehaviorController#handleReportAnswerRecord,DispatchApiResponse= {}", apiResponse);
            }
        } catch (EngineException ee) {
            log.error("Call reportAnswerRecord error code= {}; message= {}; failover={}", ee.getErrorCode(), ee.getMessage(), ee.getRedirect());

            ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
            if (ee.getRedirect()) {
                header.setErrorNodes(Lists.newArrayList(new ApiResponseHeader.ErrorNode("failover", "true")));
            }
            apiResponse.setHeader(header);
        }
        apiResponse.setTraceId(apiRequest.getTraceId());
        return apiResponse;
    }

    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "答题记录上报")
    @PostMapping(value = "/reportAnswerRecord")
    @LoggingCost
    public DispatchApiResponse reportAnswerRecord(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        return handleReportAnswerRecord(apiRequest, false);
    }

    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "答题记录上报OS")
    @PostMapping(value = "/reportAnswerRecordOS")
    @LoggingCost
    public DispatchApiResponse reportAnswerRecordOS(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        return handleReportAnswerRecord(apiRequest, true);
    }

    /**
     * 将作答记录按bookCode分组
     * @param sceneInfo
     * @param studyLogRecords
     * @return
     */
    private Map<String,List<StudyLogRecord>>  groupStudyLogByBook(SceneInfo sceneInfo, List<StudyLogRecord> studyLogRecords){
        String sceneBook = sceneInfo.getBookCode();

        // 如果报文头的bookCode是空的，用catalog
        if (StringUtils.isBlank(sceneBook) && StringUtils.isNotBlank(sceneInfo.getCatalogCode())){
            String[] catalogSplit = StringUtils.split(sceneInfo.getCatalogCode(),"_");
            if (catalogSplit.length >= 2){
                sceneBook = catalogSplit[0]+ "_" + catalogSplit[1];
            }
        }

        Map<String,List<StudyLogRecord>> studyLogBookMap = new HashMap<>();
        for (StudyLogRecord record : studyLogRecords){
            // 取记录上的bookCode或者catalogCode
            String recordBook = record.getBookCode();
            if (StringUtils.isBlank(recordBook) && StringUtils.isNotBlank(record.getCatalogCode())){
                String[] catalogSplit = StringUtils.split(record.getCatalogCode(),"_");
                if (catalogSplit.length >= 2){
                    recordBook = catalogSplit[0]+ "_" + catalogSplit[1];
                }
            }

            // 都没有的，填充报文头
            if (StringUtils.isBlank(recordBook)){
                recordBook = sceneBook;
            }


            List<StudyLogRecord> mapValRecords = studyLogBookMap.get(recordBook);
            if (null == mapValRecords) {
                mapValRecords = new ArrayList<>();
                studyLogBookMap.put(recordBook,mapValRecords);
            }

            mapValRecords.add(record);
        }

        return studyLogBookMap;
    }


    /**
     * 大图谱日志上报，收集特定学习场景日志，不影响主线，做物理隔离存储
     *
     * @param apiRequest
     * @return
     * @throws
     */
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "答题记录上报-大图谱")
    @PostMapping(value = "/macrograph/reportAnswerRecord")
    @LoggingCost
    public DispatchApiResponse macrographReportAnswerRecord(@RequestBody DispatchApiRequest apiRequest) throws Exception {

        if (log.isDebugEnabled()) {
            log.debug("BehaviorController#macrographReportAnswerRecord,DispatchApiRequest= {}", apiRequest);
        }

        DispatchApiPayload payload = apiRequest.getPayload();
        if (payload == null) {
            throw new ParamNotExistException("apiRequest.getPayload() of ApiRequest");
        }

        StudyLogParam studyLogParam = payload.getData(StudyLogParam.class);
        Assert.notNull(studyLogParam, "StudyLogParam must not null");
        DispatchApiResponse apiResponse = new DispatchApiResponse();
        try {
            //endregion
            SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);

            StudyLogResult studyLogResult = behaviorService.macrographExtend(apiRequest.getTraceId(), sceneInfo, studyLogParam);

            apiResponse.setTraceId(apiRequest.getTraceId());
            apiResponse.setPayload(studyLogResult);
            if (log.isDebugEnabled()) {
                log.debug("BehaviorController#macrographReportAnswerRecord,DispatchApiResponse= {}", apiResponse);
            }
        } catch (EngineException ee) {
            log.error("Call macrograph reportAnswerRecord error code= {}; message= {}; failover={}", ee.getErrorCode(), ee.getMessage(), ee.getRedirect());

            ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
            if (ee.getRedirect()) {
                header.setErrorNodes(Lists.newArrayList(new ApiResponseHeader.ErrorNode("failover", "true")));
            }
            apiResponse.setHeader(header);
        }
        apiResponse.setTraceId(apiRequest.getTraceId());
        return apiResponse;
    }

    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "批量查询答题记录查询")
    @PostMapping(value = "/batchQuery")
    @LoggingCost
    public DispatchApiResponse batchQuery(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("BehaviorController#batchQuery,DispatchApiRequest= {}", apiRequest);
        }

        DispatchApiPayload payload = apiRequest.getPayload();
        if (payload == null) {
            throw new ParamNotExistException("apiRequest.getPayload() of ApiRequest");
        }

        BehaviorQueryParam behaviorQueryParam = payload.getData(BehaviorQueryParam.class);
        Assert.notNull(behaviorQueryParam, "behaviorQueryParam must not null");
        Assert.notNull(behaviorQueryParam.getNodeInfos(), "node info must not null");
        DispatchApiResponse apiResponse = new DispatchApiResponse();


        try {
            //endregion
            SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);

            // 查询批量点的作答记录
            Map<String, List<StudyLogData>> nodeStudyLogMap = behaviorService.batchQuery(apiRequest.getTraceId(), sceneInfo, behaviorQueryParam);



            // 构建返回的点作答记录列表
            List<StudyLogQueryResult> queryResultList ;

            if (null == nodeStudyLogMap || nodeStudyLogMap.isEmpty()){
                queryResultList = Lists.newArrayListWithExpectedSize(0);
            }else {
                queryResultList = Lists.newArrayListWithExpectedSize(behaviorQueryParam.getNodeInfos().size());
                for (NodeInfo node : behaviorQueryParam.getNodeInfos()) {
                    // 初始化返回的点作答记录查询结果
                    StudyLogQueryResult queryResult = new StudyLogQueryResult();
                    queryResult.setNodeId(node.getNodeId());

                    List<StudyLogData> studyLogDatas = nodeStudyLogMap.get(node.getNodeId());
                    if (null == studyLogDatas) {
                        queryResult.setExistStudentLog(false);
                        queryResult.setStudyLogs(Lists.newArrayList());
                    } else {
                        queryResult.setExistStudentLog(true);
                        queryResult.setStudyLogs(studyLogDatas.stream().filter(Objects::nonNull).map(logData -> {
                            StudyLogRecord logRecord = new StudyLogRecord();
                            logRecord.setNodeId(logData.getNodeId());
                            logRecord.setResNodeId(logData.getResNodeId());
                            logRecord.setResNodeType(logData.getResNodeType());
                            logRecord.setNodeType(logData.getNodeType());
                            logRecord.setBookCode(logData.getBookCode());
                            logRecord.setCatalogCode(logData.getCatalogCode());
                            logRecord.setScore(logData.getScore());
                            logRecord.setStandardScore(logData.getStandardScore());
                            logRecord.setFrom(null == logData.getBizAction() ? "" : logData.getBizAction().name());
                            logRecord.setFeedbackTime(null == logData.getFeedbackTime() ? null : Date.from(logData.getFeedbackTime()));
                            return logRecord;
                        }).collect(Collectors.toList()));
                    }

                    queryResultList.add(queryResult);
                }
            }


            BatchStudyLogQueryResult batchStudyLogQueryResult = new BatchStudyLogQueryResult();
            batchStudyLogQueryResult.setStudyLogQueryResults(queryResultList);

            apiResponse.setPayload(batchStudyLogQueryResult);
            if (log.isDebugEnabled()) {
                log.debug("BehaviorController#batchQuery,DispatchApiResponse= {}", apiResponse);
            }
        } catch (EngineException ee) {
            log.error("Call reportAnswerRecord error code= {}; message= {}; failover={}", ee.getErrorCode(), ee.getMessage(), ee.getRedirect());

            ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
            if (ee.getRedirect()) {
                header.setErrorNodes(Lists.newArrayList(new ApiResponseHeader.ErrorNode("failover", "true")));
            }
            apiResponse.setHeader(header);
        }
        apiResponse.setTraceId(apiRequest.getTraceId());
        return apiResponse;
    }

}
