package com.iflytek.skylab.service.stand.endpoint;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.service.stand.configuration.StandProperties;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;

import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2022/3/25 12:04 下午
 */
@Endpoint(id = "skyline-stand")
public class StandEndpoint {

    private final StandProperties standProperties;

    public StandEndpoint(StandProperties standProperties) {
        this.standProperties = standProperties;
    }

    @ReadOperation
    public Object invoke() {
        Map<String, Object> status = new TreeMap<>();
        status.put("stand-properties", standProperties);

        return JSON.toJSONString(status);
    }
}
