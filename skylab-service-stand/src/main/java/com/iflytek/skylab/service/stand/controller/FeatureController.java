package com.iflytek.skylab.service.stand.controller;

import com.iflytek.cog2.feaflow.sdk.data.ZionDictModel;
import com.iflytek.skylab.core.data.FeatureResult;
import com.iflytek.skylab.core.data.adapter.scene.SceneInfoSelector;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.stand.service.AcquireFeatureService;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.exception.SkynetException;
import skynet.boot.logging.LoggingCost;

/**
 * 通用特征服务
 *
 * <AUTHOR> 2022年03月01日14:03:31
 */
@Slf4j
@Api(tags = "2.通用特征服务")
@RestController
@EnableSkynetSwagger2
@RequestMapping(value = "/skylab/api/v1/feature", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class FeatureController {


    private final AcquireFeatureService acquireFeatureService;


    public FeatureController(AcquireFeatureService acquireFeatureService) {
        this.acquireFeatureService = acquireFeatureService;
    }


    /**
     * 特征查询
     * <p>
     * 配置 ：
     * 1、SchemaRowKey：特征Schema字典版本
     * eg：xxj.feature.dic.model#v2022-03
     * <p>
     * 2、DataSource：自定义数据源
     * <p>
     * {
     * "name": "特征数据源AppId",
     * "dataApiId": "api-x4znzm0l",
     * "version": "3"
     * }
     *
     * @param apiRequest
     * @return
     * @throws SkynetException
     */
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "特征查询")
    @PostMapping(value = "/query")
    @LoggingCost
    public DispatchApiResponse query(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        //入参 出参类型是 FeatureParam  FeatureResult
        if (log.isDebugEnabled()) {
            log.debug("FeatureController#query,DispatchApiRequest= {}", apiRequest);
        }


        //获取场景信息 对象
        // SceneInfo sceneInfo = apiRequest.getScene(SceneInfo.class);
        SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);
        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }

        DispatchApiResponse apiResponse = new DispatchApiResponse();
        FeatureResult featureResult = new FeatureResult();
        try {
            featureResult = acquireFeatureService.selectFeature(sceneInfo, apiRequest);
        } catch (Exception e) {
            log.error("traceId=" + apiRequest.getTraceId() + " FeatureController#query error ", e);
        }
        apiResponse.setPayload(featureResult);
        apiResponse.setTraceId(apiRequest.getTraceId());
        if (log.isDebugEnabled()) {
            log.debug("FeatureController#query,DispatchApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }


    /**
     * 特征Schema 查询
     * <p>
     * 1、SchemaRowKey：特征Schema字典版本
     *
     * @param apiRequest
     * @return
     * @throws SkynetException
     */
    @SkylineMetric
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "特征Schema字典查询")
    @PostMapping(value = "/schema")
    @LoggingCost
    public DispatchApiResponse schema(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("FeatureController#schema,DispatchApiRequest= {}", apiRequest);
        }

        //查询
        ZionDictModel zionDictModel = acquireFeatureService.querySchema(apiRequest);

        DispatchApiResponse apiResponse = new DispatchApiResponse();
        apiResponse.setTraceId(apiRequest.getTraceId());
        apiResponse.setPayload(zionDictModel);

        if (log.isDebugEnabled()) {
            log.debug("FeatureController#schema,DispatchApiResponse= {}", apiResponse);
        }
        return apiResponse;
    }

    @SkylineMetric
    @SkylineTraceStarter
    @ApiOperation(value = "清理redis图谱缓存")
    @PostMapping(value = "/clearRedisCache")
    @LoggingCost
    public Long clearRedisCache() {
        return acquireFeatureService.clearRedisCache();
    }
}
