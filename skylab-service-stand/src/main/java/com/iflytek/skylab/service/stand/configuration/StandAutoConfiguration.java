package com.iflytek.skylab.service.stand.configuration;

import com.iflytek.hy.rec.feaacquire.FeaAcquire;
import com.iflytek.hy.rec.feaacquire.annotation.EnableFeaAcquire;
import com.iflytek.hy.rec.feaprocess.FeaPostProcess;
import com.iflytek.hy.rec.feaprocess.annotation.EnableFeaProcess;
import com.iflytek.skylab.core.dataapi.annotation.*;
import com.iflytek.skylab.core.dataapi.service.*;
import com.iflytek.skylab.service.stand.service.AcquireFeatureService;
import com.iflytek.skylab.service.stand.service.BehaviorService;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import com.iflytek.skyline.common.EnableSkylineFeign;
import com.iflytek.skyline.common.feign.SkylineRouterFeign;
import com.iflytek.skyline.resource.annotation.EnableSkylineResource;
import com.iflytek.skyline.resource.service.ResourceService;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetLogging;


/**
 * 接入服务  相关Bean 配置
 *
 * <AUTHOR>
 */
@EnableSkynetLogging
@EnableSkylineBrave
@EnableSkylineFeign
@EnableSkylineResource
@EnableApiException
@EnableFeatureAPI
@EnableDataApiRedis
@EnableBizDataAPI
@EnableGraphAPI
@Configuration(proxyBeanMethods = false)
@EnableFeignClients({"com.iflytek.skylab.service.stand.feign","com.iflytek.skyline.resource.feign"})
@EnableFeaProcess
@EnableFeaAcquire
public class StandAutoConfiguration {

    @Bean
    @ConfigurationProperties("skylab.stand")
    public StandProperties standProperties() {
        return new StandProperties();
    }

    @Bean
    public BehaviorService behaviorService(SkylineRouterFeign skylineRouterFeign, ResourceService resourceService,
                                           StudyLogService studyLogService, StudyMacrographLogService studyMacrographLogService, GraphService graphService,
                                           StandProperties standProperties, StudyLogGroupService studyLogGroupService, TraceUtils traceUtils) {
        return new BehaviorService(skylineRouterFeign, resourceService, studyLogService, studyMacrographLogService, graphService, standProperties,studyLogGroupService, traceUtils);
    }

    @Bean
    public AcquireFeatureService acquireFeatureService(FeatureService featureService, FeaPostProcess feaPostProcess, FeaAcquire feaAcquire) {
        return new AcquireFeatureService(featureService,feaPostProcess,feaAcquire);
    }
}
