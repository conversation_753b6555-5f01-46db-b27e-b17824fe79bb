package com.iflytek.skylab.service.stand.data;

import com.iflytek.skylab.core.data.Jsonable;
import com.iflytek.skylab.core.dataapi.data.FeedbackLog;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/9 09:56
 */
@Getter
@Setter
@Accessors(chain = true)
public class AnswerTopicTraceLog extends Jsonable {

    public static final String TYPE = "ANSWER_TOPIC_TRACE_LOG";

    private List<FeedbackLog> feedbackLogs;

    private Date sendTime = new Date();
}
