package com.iflytek.skylab.service.stand.data;

import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.data.GraphQuery;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 场景依赖
 *
 * <AUTHOR>
 * @date 2022/3/21 11:55 上午
 */
@Getter
@Setter
public class SceneExtend {

    /**
     * 学习场景
     */
    private StudyCodeEnum studyCode;

    private String graphType;

    /**
     * 检索条件
     */
    private GraphQuery graphQuery;

    /**
     * 影响的场景  SceneExtend
     */
    private List<SceneExtend> extend;
}
