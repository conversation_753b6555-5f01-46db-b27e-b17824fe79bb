package com.iflytek.skylab.service.stand.feign;

import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/14 15:02
 */
@FeignClient("skylab-engine-proxy")
public interface EngineProxyFeign {

    @PostMapping("/skylab/proxy/v1/behavior/reportAnswerRecord")
    DispatchApiResponse extend(@RequestBody DispatchApiRequest apiRequest);
}
