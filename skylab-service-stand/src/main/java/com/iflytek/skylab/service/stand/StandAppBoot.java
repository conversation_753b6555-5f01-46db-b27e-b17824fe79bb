package com.iflytek.skylab.service.stand;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import skynet.boot.AppUtils;

/**
 * 通用特征服务
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {
        // disable mysql
        DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class})
public class StandAppBoot {

    public static void main(String[] args) {
        AppUtils.run(StandAppBoot.class, args);
    }
}
