[{"studyCode": "SYNC_LEARN", "graphType": "ANCHOR_POINT", "graphQuery": {"edgeLabels": [{"source": "ANCHOR_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}, "extend": [{"studyCode": "MID_EXAM", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}}, {"studyCode": "UNIT_REVIEW", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}}, {"studyCode": "FINAL_EXAM", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}}]}, {"studyCode": "MID_EXAM", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}, "extend": [{"studyCode": "FINAL_EXAM", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}}, {"studyCode": "UNIT_REVIEW", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}}]}, {"studyCode": "FINAL_EXAM", "graphType": "EXAM_POINT", "extend": [{"studyCode": "MID_EXAM", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}}, {"studyCode": "UNIT_REVIEW", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}}]}, {"studyCode": "UNIT_REVIEW", "graphType": "EXAM_POINT", "extend": [{"studyCode": "MID_EXAM", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}}, {"studyCode": "FINAL_EXAM", "graphType": "EXAM_POINT", "graphQuery": {"edgeLabels": [{"source": "EXAM_POINT", "target": "TOPIC"}], "rootVertexLabel": "TOPIC"}}]}]