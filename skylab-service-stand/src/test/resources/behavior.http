#学习行为收集
#POST http://************:32183/skylab/api/v1/behavior/reportAnswerRecord
POST http://localhost:32183/skylab/api/v1/behavior/reportAnswerRecord
Content-Type: application/json
SKYLINE-SPAN-ID: {{$timestamp}}

{
    "header": {
        "traceId": "{{$uuid}}",
        "code": 0
    },
    "parameter": {
        "sceneInfo": {
            "functionCode": "STUDY_LOG",
            "bookVolumeCode": "07020101-001",
            "test": false,
            "bizCode": "ZSY_XXJ",
            "userId": "lanbo",
            "bookVersionCode": "01",
            "areaCode": "010100",
            "bizAction": "SYNC_EVAL",
            "graphVersion": "000",
            "studyCode": "SYNC_LEARN",
            "phaseCode": "04",
            "gradeCode": "07",
            "subjectCode": "02"
        },
        "SceneExtend": {
            "createdDate": "2022-03-23T02:09:15.436Z",
            "lastModifiedDate": "2022-04-01T06:05:43.775Z",
            "resourceCategoryId": "623a81317d6b6628e6ff88cf",
            "name": "全局配置",
            "contentLength": 1582,
            "id": "623a814b7d6b6628e6ff88d0",
            "categoryName": "学习机_画像诊断依赖",
            "version": "624696374a8725736a60de75",
            "parentId": "jingwang36_uuid",
            "desc": "资源配置/全局配置"
        }
    },
    "payload": {
        "data": [
            {
                "code": "data",
                "data": {
                    "funcCode": "STUDY_LOG",
                    "items": [
                        {
                            "feedbackTime": "2022-03-31T04:00:18.121Z",
                            "roundIndex": "1",
                            "score": 3,
                            "resNodeType": "TOPIC",
                            "refTraceId": "1234567890",
                            "nodeType": "ANCHOR_POINT",
                            "resNodeId": "222222222",
                            "standardScore": 3,
                            "timeCost": 10,
                            "nodeId": "1111111111",
                            "roundId": "mission-id"
                        }
                    ]
                }
            }
        ]
    }
}