<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>skylab-platform</artifactId>
        <groupId>com.iflytek.skylab</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>skylab-service-clear-mastery</artifactId>
    <description>清空画像能力服务</description>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.deploy.skip>true</maven.deploy.skip>
        <maven.source.skip>true</maven.source.skip>
        <!--        <topic-adaptive-recommend-api.version>1.5.30</topic-adaptive-recommend-api.version>-->
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-dataapi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!--        &lt;!&ndash; dubbo &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.iflytek.edu</groupId>-->
        <!--            <artifactId>epas-dubbo</artifactId>-->
        <!--            <version>${epas-dubbo.version}</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.springframework</groupId>-->
        <!--                    <artifactId>spring-context</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.springframework</groupId>-->
        <!--                    <artifactId>spring-core</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.springframework</groupId>-->
        <!--                    <artifactId>spring-beans</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.springframework</groupId>-->
        <!--                    <artifactId>spring-expression</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.springframework</groupId>-->
        <!--                    <artifactId>spring-web</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.springframework</groupId>-->
        <!--                    <artifactId>spring-aop</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>aopalliance</groupId>-->
        <!--                    <artifactId>aopalliance</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>log4j</groupId>-->
        <!--                    <artifactId>log4j</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>slf4j-api</artifactId>-->
        <!--                    <groupId>org.slf4j</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.slf4j</groupId>-->
        <!--                    <artifactId>slf4j-log4j12</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>io.netty</groupId>-->
        <!--                    <artifactId>netty</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.apache.httpcomponents</groupId>-->
        <!--                    <artifactId>httpclient</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.apache.httpcomponents</groupId>-->
        <!--                    <artifactId>httpcore</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>javax.servlet</groupId>-->
        <!--                    <artifactId>javax.servlet-api</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.github.sgroschupf</groupId>-->
        <!--            <artifactId>zkclient</artifactId>-->
        <!--            <version>0.1</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.apache.zookeeper</groupId>-->
        <!--            <artifactId>zookeeper</artifactId>-->
        <!--            <version>3.4.6</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.slf4j</groupId>-->
        <!--                    <artifactId>slf4j-log4j12</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>log4j</groupId>-->
        <!--                    <artifactId>log4j</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

        <!--        &lt;!&ndash; ZooKeeper 3.4.x 已经不维护了，所以不能和 Curator 最新版本兼容，需要使用 Curator 4.2.x 的兼容模式 &ndash;&gt;-->
        <!--        &lt;!&ndash; https://curator.apache.org/zk-compatibility-34.html &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.curator</groupId>-->
        <!--            <artifactId>curator-recipes</artifactId>-->
        <!--            <version>4.2.0</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.apache.zookeeper</groupId>-->
        <!--                    <artifactId>zookeeper</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.curator</groupId>-->
        <!--            <artifactId>curator-framework</artifactId>-->
        <!--            <version>4.2.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.curator</groupId>-->
        <!--            <artifactId>curator-client</artifactId>-->
        <!--            <version>4.2.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.curator</groupId>-->
        <!--            <artifactId>curator-x-discovery</artifactId>-->
        <!--            <version>4.2.0</version>-->
        <!--        </dependency>-->
        <!-- 老平台推荐api -->
        <!--        <dependency>-->
        <!--            <groupId>com.iflytek.epd.engineservice</groupId>-->
        <!--            <artifactId>topic-adaptive-recommend-api</artifactId>-->
        <!--            <version>${topic-adaptive-recommend-api.version}</version>-->
        <!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}-Build${buildSid}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <echo message="copy *.jar to target ..."/>
                                <copy todir="${basedir}/../target">
                                    <fileset dir="${project.build.directory}">
                                        <include name="*.jar"/>
                                    </fileset>
                                </copy>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
