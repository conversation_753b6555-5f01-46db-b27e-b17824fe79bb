IP=***********
#-----------------------------------------------------------------
logging.level.ROOT=INFO
logging.level.com.iflytek.skyline=DEBUG
logging.level.com.iflytek.skylab=DEBUG
#-----------------------------------------------------------------
skynet.api.swagger2.enabled=true
#-----------------------------------------------------------------
skyline.brave.kafka-enabled=true
skyline.brave.kafka-topic-prefix=snifer_XXJ_
skyline.brave.allowSubjectCodes=02
#-----------------------------------------------------------------
spring.kafka.bootstrap-servers=*************:9093,*************:9093,*************:9093
spring.kafka.producer.properties.security.protocol=SASL_PLAINTEXT
spring.kafka.producer.properties.sasl.mechanism=SCRAM-SHA-256
#-----------------------------------------------------------------
# mongoDB

skyline.debugging.script=$.parameter.sceneInfo.userId=='Patricia'

skylab.data.api.graph.hosts=***********:9669

#ADAPTIVE EPAS
skylab.epas.appKey=topic-adaptive-recommend-service
skylab.epas.appSecret=9d9c353e95233307
skylab.address.server.url=http://pre.epas.changyan.com/address
