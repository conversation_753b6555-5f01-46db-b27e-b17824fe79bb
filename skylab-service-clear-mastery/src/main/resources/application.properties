IP=***********
SKYNET_CLUSTER=skynet
SKYNET_PLUGIN_CODE=skylab-platform
#-----------------------------------------------------------------
server.port=23453
spring.profiles.active=dev
#-----------------------------------------------------------------
skyline.brave.enabled=true
skyline.brave.aop-enabled=true
#-----------------------------------------------------------------
spring.application.name=skylab-clear-mastery
skynet.action-point=${spring.application.name}@${SKYNET_PLUGIN_CODE}
skynet.zookeeper.enabled=true
spring.cloud.zookeeper.enabled=true
spring.cloud.zookeeper.discovery.root=/${SKYNET_CLUSTER}/discovery/${SKYNET_PLUGIN_CODE}
spring.cloud.zookeeper.connect-string=${IP}:2181
#-----------------------------------------------------------------
logging.level.ROOT=INFO
logging.level.com.iflytek.skyline=INFO
logging.level.com.iflytek.skylab=INFO
#-----------------------------------------------------------------
#mongodb

#-----dataApi?ES   start----------
#------------zion common--------------
zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.dict-family=u
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.dict-refresh-period-seconds=10
#------------zion es--------------
zion.query-data-base=es
zion.es-host=***********:9200,***********:9200,***********:9200
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
#------------zion dataapi--------------
#zion.query-data-base=hbase_data_api
#zion.dict-refresh-period-seconds=10
#zion.dict-table-name=dim_xxj_dic_model
#zion.dict-data-api-item.dataApiId=api-vimqibeu
#zion.feature-data-api-item.dataApiId=api-x4znzm0l
#zion.app-key=app-ywqysanx
#zion.app-secret=3e9e317892f039a6a15ad4316bcf6123ac9a7074
#zion.url=http://**************:30890/api/v1/execute
#-----dataApi?ES  end--------------
