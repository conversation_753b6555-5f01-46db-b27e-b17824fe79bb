package com.iflytek.skylab.service.clear.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.iflytek.skylab.core.dataapi.exception.SkylabException;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.clear.service.ClearMasteryService;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiErrorCode;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.logging.LoggingCost;

/**
 * <AUTHOR>
 * @date 2022/5/7 17:55
 */
@Slf4j
@Api(tags = "画像清空服务")
@RestController
@RequestMapping("/skylab/api/v1/clearMastery")
@EnableSkynetSwagger2
public class ClearMasteryController {

    @Autowired
    private ClearMasteryService sortFailoverService;

    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "能力运行")
    @LoggingCost
    @PostMapping("/process")
    public ApiResponse process(@RequestBody ApiRequest apiRequest) {
        log.debug("apiRequest= {}", apiRequest);
        String traceId = apiRequest.getTraceId();
        SceneInfo sceneInfo = apiRequest.getScene(SceneInfo.class);

        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }

        ApiResponse apiResponse = null;
        String funcCode = sceneInfo.getFunctionCode();
        try {
            if (StringUtils.equals(funcCode, "CLEAR_MASTERY")) {
                apiResponse = sortFailoverService.clearMastery(traceId, sceneInfo);
            } else {
                throw new SkylabException(ApiErrorCode.ERROR.getCode(), CharSequenceUtil.format("{}不是清空画像请求", funcCode));
            }
        } catch (Exception e) {
            log.error("清空画像服务错误：" + e.getMessage(), e);
            apiResponse = new ApiResponse(new ApiResponseHeader(ApiErrorCode.ERROR.getCode(), ApiErrorCode.ERROR.getMessage()));
            apiResponse.setTraceId(traceId);
        }
        return apiResponse;
    }


}
