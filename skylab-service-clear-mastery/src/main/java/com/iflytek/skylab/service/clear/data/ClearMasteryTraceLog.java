package com.iflytek.skylab.service.clear.data;

import com.iflytek.skylab.core.data.Jsonable;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-12-20 10:34:40
 */
@Getter
@Setter
@Accessors(chain = true)
public class ClearMasteryTraceLog extends Jsonable {

    /**
     * 清空画像埋点日志类型
     */
    public static final String TYPE = "CLEARMASTERY_LOG";

    private SceneInfo sceneInfo;

    private Date sendTime = new Date();

    public ClearMasteryTraceLog(SceneInfo sceneInfo) {
        this.sceneInfo = sceneInfo;
    }
}
