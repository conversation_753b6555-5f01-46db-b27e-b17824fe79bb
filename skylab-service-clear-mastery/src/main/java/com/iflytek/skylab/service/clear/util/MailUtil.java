package com.iflytek.skylab.service.clear.util;

import cn.hutool.extra.mail.Mail;
import cn.hutool.extra.mail.MailAccount;
import com.sun.mail.util.MailSSLSocketFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MailUtil {
    public static void sendLogMail(String mailTitle, String content) {

        try {
            // 初始化邮件账户配置
            MailAccount mailAccount = new MailAccount();
            mailAccount.setAuth(true);
            mailAccount.setSslEnable(true);
            mailAccount.setHost(SpringUtils.getProperties("task.mail.host"));
            mailAccount.setPort(Integer.valueOf(SpringUtils.getProperties("task.mail.port")));
            mailAccount.setFrom(SpringUtils.getProperties("task.mail.from"));
            mailAccount.setUser(SpringUtils.getProperties("task.mail.user"));
            mailAccount.setPass(SpringUtils.getProperties("task.mail.pass"));
            // 设置SSL socket工厂，以支持SSL加密
            MailSSLSocketFactory sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
            mailAccount.setCustomProperty("mail.smtp.ssl.socketFactory", sf);
            // 创建邮件对象，并配置收件人、主题和内容
            Mail mail = Mail.create(mailAccount);
            mail.setTos(SpringUtils.getProperties("task.mail.tos").split(";"));

            mail.setTitle(mailTitle);
            mail.setContent(content, false);

            // 发送邮件，并记录日志
            String send = mail.send();
            log.info("发送成功：{}", send);
        } catch (Exception e) {
            log.error("发送邮件异常", e);
        }
    }
}
