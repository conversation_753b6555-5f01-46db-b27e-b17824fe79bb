package com.iflytek.skylab.service.clear.service;

import com.iflytek.skylab.core.constant.TraceConstant;
import com.iflytek.skylab.core.data.ClearMasteryResult;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.clear.data.ClearMasteryTraceLog;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.data.TraceRecordGroup;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/5/9 15:40
 */
@Slf4j
public class ClearMasteryService {

    private final TraceUtils traceUtils;

    public ClearMasteryService(TraceUtils traceUtils) {
        this.traceUtils = traceUtils;
    }


    public ApiResponse clearMastery(String traceId, SceneInfo sceneInfo) {
        log.debug("traceId:{}, sceneInfo:{}", traceId, sceneInfo);
        String userId = sceneInfo.getUserId();

        //1、删除用户实时学情
        DataHub.getStudyLogService().deleteStudyLog(traceId, userId);
        //2、删除用户画像
        DataHub.getMasterService().deleteMasterDataByUserId(traceId, userId);
        //3、删除odeon特征数据
        if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
            //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
        }else {
            traceUtils.record(TraceRecordGroup.FLOW, ClearMasteryTraceLog.TYPE, new ClearMasteryTraceLog(sceneInfo));
        }

        // 组装返回结果
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setTraceId(traceId);
        apiResponse.setHeader(new ApiResponseHeader());
        apiResponse.setObjectPayload(new ClearMasteryResult().setUserId(userId));
        return apiResponse;
    }

}
