package com.iflytek.skylab.service.clear.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 多数据源MongoDB配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class MongoConfig {

    @Value("${spring.data.mongodb.uri}")
    private String primaryMongoUri;


//    @Primary
    @Bean(name = "primaryMongoClient")
    public MongoClient primaryMongoClient() {
        return MongoClients.create(primaryMongoUri);
    }
}