package com.iflytek.skylab.service.clear.util;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * spring上下文工具类
 *
 * @title SpringUtils
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public final class SpringUtils implements ApplicationContextAware, Ordered {
    /**
     * spring上下文
     * -- GETTER --
     *  获取ApplicationContext.
     *
     * @return ApplicationContext

     */
    @Getter
    private static ApplicationContext context;

    /**
     * @param applicationContext 上下文
     *                           实现ApplicationContextAware接口的context注入函数, 将其存入静态变量
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        log.info("初始化上下文：{}", applicationContext);
        SpringUtils.context = applicationContext;
    }

    /**
     * 从ApplicationContext中取得Bean
     *
     * @param clazz bean
     * @return 对象
     */
    public static <T> T getBean(Class<T> clazz) {
        return getContext().getBean(clazz);
    }

    public static <T> T getBean(String name, Class<T> clazz) {
        return getContext().getBean(name, clazz);
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }


    public static String getProperties(String key) {
        return getProperties(key, null);
    }

    public static String getProperties(String key, String defaultValue) {
        return context.getEnvironment().getProperty(key, defaultValue);
    }

}
