package com.iflytek.skylab.service.clear.config;

import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.service.clear.service.ClearMasteryService;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import com.iflytek.skyline.resource.annotation.EnableSkylineResource;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetLogging;

/**
 * <AUTHOR>
 * @date 2022/5/9 15:47
 */
@EnableSkynetLogging
@Configuration(proxyBeanMethods = false)
@EnableSkylineBrave
@EnableSkylineResource
@EnableDataHub
@EnableDiscoveryClient
public class ClearMasteryConfiguration {

    @Bean
    public ClearMasteryService clearMasteryService(TraceUtils traceUtils) {
        return new ClearMasteryService(traceUtils);
    }


}
