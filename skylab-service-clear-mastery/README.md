## skylab-service-clear-mastery

画像清空服务

### 1. 项目概述
- 项目名称：skylab-service-clear-mastery
- 项目描述：提供 CLEAR_MASTERY 功能码触发的用户画像清理能力
- 版本信息：2.0.9-SNAPSHOT（开发中）
- 端口：23453（dev）

### 2. 技术架构
- 框架：Spring Boot、Knife4j
- 控制器：ClearMasteryController（/skylab/api/v1/clearMastery/process）

### 3. 接口（REST）
- POST `/skylab/api/v1/clearMastery/process` 清空画像

### 4. 业务处理逻辑
- 校验 SceneInfo 与 functionCode==CLEAR_MASTERY -> 执行业务清理 -> 返回 ApiResponse

### 5. 系统配置
- 端口：`server.port=23453`
- Zookeeper：spring.cloud.zookeeper.connect-string=${IP}:2181

### 6. 启动与验证
```bash
mvn -pl skylab-service-clear-mastery spring-boot:run -Dspring-boot.run.profiles=dev
```

### 7. 开发指南
- 启动类与包：controller/service 位于 com.iflytek.skylab.service.clear
- 为 CLEAR_MASTERY 流程补充单元测试与异常路径测试

