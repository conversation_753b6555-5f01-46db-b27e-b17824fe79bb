<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-boot-starter-parent</artifactId>
        <version>4.0.18</version>
    </parent>

    <groupId>com.iflytek.skylab</groupId>
    <artifactId>skylab-platform</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>skylab-platform</name>

    <properties>
        <revision>2.0.9-SNAPSHOT</revision>
        <!--推荐算法引擎-->
        <recommend-engine.version>1.4.6-SNAPSHOT</recommend-engine.version>

        <maven.deploy.skip>false</maven.deploy.skip>
        <maven.source.skip>true</maven.source.skip>
        <skyline.version>3.0.7-SNAPSHOT</skyline.version>
        <feaflow-sdk.version>1.0.5-SNAPSHOT</feaflow-sdk.version>

        <org.mapstruct.version>1.5.2.Final</org.mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <epas-dubbo.version>1.0.11</epas-dubbo.version>
    </properties>

    <repositories>
        <repository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <layout>default</layout>
        </repository>
    </repositories>

    <modules>
        <!--        协议-->
        <module>skylab-core-data</module>
        <module>skylab-core-contract</module>
        <!--        基础包-->
        <module>skylab-core-dataapi</module>
        <module>skylab-core-data-adapter</module>

        <!--服务包-->
        <module>skylab-service-front</module>
        <module>skylab-service-stand</module>
        <module>skylab-service-diag</module>
        <module>skylab-service-sort</module>
        <module>skylab-service-failover</module>
        <module>skylab-service-clear-mastery</module>
        <module>skylab-service-primary-migration</module>
        <module>skylab-service-front-consumer</module>
    </modules>

    <!--指定依赖库新域名，skynet-framework升级后可删除-->
    <distributionManagement>
        <repository>
            <id>mvn-releases</id>
            <name>iflytek-nexus</name>
            <url>https://depend.iflytek.com/artifactory/ebg-mvn-release-private</url>
        </repository>
        <snapshotRepository>
            <id>mvn-snapshots</id>
            <name>iflytek-snapshots</name>
            <url>https://depend.iflytek.com/artifactory/ebg-mvn-snapshot-private</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.iflytek.cog2</groupId>
                <artifactId>feaflow-sdk</artifactId>
                <version>${feaflow-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.iflytek.skylab</groupId>
                <artifactId>skylab-core-data</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.skylab</groupId>
                <artifactId>skylab-core-contract</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.skylab</groupId>
                <artifactId>skylab-core-data-adapter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.skylab</groupId>
                <artifactId>skylab-core-dataapi</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.iflytek.skylab</groupId>
                <artifactId>skylab-service-front</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.skylab</groupId>
                <artifactId>skylab-service-failover</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.iflytek.skyline</groupId>
                <artifactId>skyline-context</artifactId>
                <version>${skyline.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>groovy-all</artifactId>
                        <groupId>org.codehaus.groovy</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>5.3.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>shaded-com.google.common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.3.7</version>
                <configuration>
                    <configFile>src/main/resources/smart-doc.json</configFile>
                    <projectName>学习机推荐-平台</projectName>
                </configuration>
                <executions>
                    <execution>
                        <!--如果不需要在执行编译时启动smart-doc，则将phase注释掉-->
                        <!--                        <phase>compile</phase>-->
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
