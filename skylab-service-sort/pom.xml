<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skylab</groupId>
        <artifactId>skylab-platform</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>skylab-service-sort</artifactId>
    <version>${skylab-sort.version}</version>
    <packaging>jar</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <maven.source.skip>true</maven.source.skip>
        <skipTests>true</skipTests>
    </properties>

    <dependencies>
        <!--        <dependency>-->
        <!--            <groupId>com.bestvike</groupId>-->
        <!--            <artifactId>linq</artifactId>-->
        <!--            <version>5.0.0</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-dataapi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-sort</artifactId>
            <version>${recommend-engine.version}</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-errortopic-sort</artifactId>
            <version>${recommend-engine.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-data-adapter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}-Build${buildSid}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <echo message="copy *.jar to target ..."/>
                                <copy todir="${basedir}/../target">
                                    <fileset dir="${project.build.directory}">
                                        <include name="*.jar"/>
                                    </fileset>
                                </copy>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source> <!-- depending on your project -->
                    <target>1.8</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <!-- additional annotation processor required as of Lombok 1.18.16 -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>
                            -Amapstruct.suppressGeneratorTimestamp=true
                        </arg>
                        <arg>
                            -Amapstruct.suppressGeneratorVersionInfoComment=true
                        </arg>
                        <arg>
                            -Amapstruct.verbose=true
                        </arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
