## skylab-service-sort

多维图谱排序推荐服务

### 1. 项目概述
- 项目名称：skylab-service-sort
- 项目描述：提供排序推荐与错题本推荐能力
- 版本信息：2.0.9-SNAPSHOT（开发中）
- 端口：27202（dev）

### 2. 技术架构
- 框架：Spring Boot、Knife4j、Skyline Dispatcher
- 数据：Nebula Graph、MongoDB
- 控制器：`SortController`（/skylab/api/v1/sort）、`ErrorTopicSortController`（/skylab/api/v1/sort/errortopic）

### 3. 接口（REST）
- POST `/skylab/api/v1/sort/process` 排序推荐
- POST `/skylab/api/v1/sort/errortopic/process` 错题本推荐

### 4. 业务处理逻辑分析
- 通过 `SceneInfoSelector` 解析场景，选择 `SortParamAdapterSelector`/`ErrorTopicSortParamAdapterSelector`
- 调用排序引擎，埋点并持久化推荐与学习记录

### 5. 系统配置
- 端口：`server.port=27202`
- Nebula Graph：`skylab.data.api.graph.*` 支持本地缓存与版本控制

### 6. 启动与验证
```bash
mvn -pl skylab-service-sort spring-boot:run
```

