package com.iflytek.skylab.service.sort;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.RecEval4InOutParam;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/3/17 7:27 下午
 */
@Slf4j
public class SortControllerTest {

    public static void main(String[] args) {
        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
        sceneInfo.setSubjectCode("02");
        sceneInfo.setPhaseCode("04");
        sceneInfo.setBookCode("01");
        sceneInfo.setPressCode("11111");
        sceneInfo.setAreaCode("010100");
        sceneInfo.setUserId(UUID.randomUUID().toString());
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setGraphVersion("");
        log.debug("SceneInfo= {}", sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setCatalogIds(Arrays.asList(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        recEval4InOutParam.setRoundId(UUID.randomUUID().toString());
//        recEval4InOutParam.setTopicPackageType(TopicPackageType.CONCEPT_CARDING);
        recEval4InOutParam.setTopicOrderNumber(1);

        DispatchApiPayload dispatchApiPayload = new DispatchApiPayload();
        dispatchApiPayload.putData("data", (JSONObject) JSON.toJSON(recEval4InOutParam));

        DispatchApiRequest dispatchApiRequest = new DispatchApiRequest();
        dispatchApiRequest.setTraceId(UUID.randomUUID().toString());
        dispatchApiRequest.setObjectScene(sceneInfo);
        dispatchApiRequest.setPayload(dispatchApiPayload);

        log.info("DispatchApiRequest= {}", dispatchApiRequest);
    }
}
