package com.iflytek.skylab.service.sort;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.hy.rec.sort.annotation.EnableMultiLayerGraphRecommend;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.RecEval4InOutParam;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.sort.controller.SortController;
import com.iflytek.skylab.service.sort.service.SortService;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import com.iflytek.skyline.common.EnableSkylineFeign;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import com.iflytek.skyline.resource.annotation.EnableSkylineResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.UUID;

@Slf4j
@Configuration(proxyBeanMethods = false)
@EnableSkylineBrave
@EnableSkylineFeign
@EnableSkylineResource
@EnableDataHub
@EnableMultiLayerGraphRecommend
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SortAppBoot.class)
@RunWith(SpringRunner.class)
public class SortAppTest {

    private String traceId = UUID.randomUUID().toString();

    @Autowired
    private SortService sortService;

    @Autowired
    private SortController sortController;

    /**
     * 测试 MapStruct对象转换库
     */
    @Test
    public void testMapStruct() {

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
        sceneInfo.setSubjectCode("02");
        sceneInfo.setPhaseCode("04");
        sceneInfo.setBookCode("01");
        sceneInfo.setPressCode("11111");
        sceneInfo.setAreaCode("010100");
        sceneInfo.setUserId(UUID.randomUUID().toString());
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setGraphVersion("");
        log.info("SceneInfo= {}", sceneInfo);

        //转换引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);
        log.info("engineScene = {}", JSON.toJSONString(engineScene));

    }

    /**
     * 排序Service业务自测
     *
     * @return
     */
    @Test
    public void testSortService() {

        MultiLayerGraphRecommendRequest request = initRequest();
        log.info("诊断请求 = {}" + JSON.toJSONString(request));
        JSONObject configJSONObject = new JSONObject();
        configJSONObject.put("resourceId", "111");
        configJSONObject.put("resourceVersion", "v1");

        MultiLayerGraphRecommendResponse recommend = null;
        try {
            recommend = sortService.sort(traceId, request, configJSONObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(recommend);

        log.info("recommend = {}", JSON.toJSONString(recommend));
    }

    @Test
    public void testSortControllerProcess() throws Exception {
        DispatchApiRequest dispatchApiRequest = buildDispatchApiRequest();
        DispatchApiResponse apiResponse = sortController.process(dispatchApiRequest);
        log.info("DispatchApiResponse = {}", apiResponse);
    }

    /**
     * @Description: 初始化请求体
     * 必填参数:
     * 选填参数:
     * 返回参数:
     * 业务逻辑:
     * </p>
     * @Param:
     * @Return:
     */
    private MultiLayerGraphRecommendRequest initRequest() {
        return new MultiLayerGraphRecommendRequest().setInNodeIdList(Arrays.asList("node_id_01", "node_id_02"))
                .setSceneInfo(
                        new com.iflytek.hy.rec.domain.model.valueobj.SceneInfo().setBizCode("xxj")
                                .setGraphVersion("graph_version_02")
                                .setBookCode("23665")
                                .setBookVersion("002")
                                .setAreaCode("256")
                                .setPhaseCode("02")
                                .setSubjectCode("05")
                                .setUserId("652424234112343232")
                                .setStudyCode("tbx")
                ).setSessionInfo(
                        new SessionInfo().setTraceId(UUID.randomUUID().toString())
                                .setRoundId(UUID.randomUUID().toString())
                                .setRoundRecNum(1)
                                .setStrategyId("strategy01")
                );

    }

    private DispatchApiRequest buildDispatchApiRequest() {

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setBizCode(BizCodeEnum.ZSY_BYOD);
        sceneInfo.setSubjectCode("02");
        sceneInfo.setPhaseCode("04");
        sceneInfo.setBookCode("01");
        sceneInfo.setPressCode("11111");
        sceneInfo.setAreaCode("010100");
        sceneInfo.setUserId(UUID.randomUUID().toString());
        sceneInfo.setStudyCode(StudyCodeEnum.SENIOR_EXAM_1);
        sceneInfo.setGraphVersion("");

        log.debug("SceneInfo= {}", sceneInfo);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setCatalogIds(Arrays.asList(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
        recEval4InOutParam.setRoundId(UUID.randomUUID().toString());
//        recEval4InOutParam.setTopicPackageType(TopicPackageType.CONCEPT_CARDING);
        recEval4InOutParam.setTopicOrderNumber(1);
        //功能编码
        sceneInfo.setFunctionCode(recEval4InOutParam.getFuncCode());

        DispatchApiPayload dispatchApiPayload = new DispatchApiPayload();
        dispatchApiPayload.putData("data", (JSONObject) JSON.toJSON(recEval4InOutParam));

        DispatchApiRequest dispatchApiRequest = new DispatchApiRequest();
        //span_id
        dispatchApiRequest.putHeader("SKYLINE-TRACE-ID", "009009009009009009009");
        dispatchApiRequest.putHeader("SKYLINE-SPAN-ID", "009");
        dispatchApiRequest.putHeader("spanId", "009");

        dispatchApiRequest.setTraceId(UUID.randomUUID().toString());
        dispatchApiRequest.setObjectScene(sceneInfo);
        dispatchApiRequest.setPayload(dispatchApiPayload);

        return dispatchApiRequest;
    }
}
