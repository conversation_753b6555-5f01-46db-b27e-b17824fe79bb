package com.iflytek.skylab.service.sort;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.RecEvalEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 通过平台调试引擎
 * 使用步骤：
 * 1. 排序引擎入参， 以json显示，存在resource中
 * - 测评推题(入门测|出门测)  engine-debug/recEval.json
 * - 测评推题(限时测评)      engine-debug/recLimitEval.json
 * - 点搜索                engine-debug/recNode.json
 * - 点推题                engine-debug/recTopic.json
 * <p>
 * 2. main方法，调用相应的方法。入参解释见方法注释
 * - 测评推题(入门测|出门测)  recEval()
 * - 测评推题(限时测评)      recLimitEval()
 * - 点搜索                recNode()
 * - 点推题                recTopic()
 *
 * <AUTHOR>
 * @date 2022/4/13 10:37
 */
@Slf4j
public class SortEngineDebugUtil {

    // 这是开发环境地址
    private static final String ADDRESS = "************:32182";
    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .callTimeout(1, TimeUnit.MINUTES)
            .connectTimeout(1, TimeUnit.MINUTES)
            .readTimeout(1, TimeUnit.MINUTES)
            .writeTimeout(1, TimeUnit.MINUTES)
            .build();


    /**
     * main方法， 入口
     *
     * @param args
     * @throws IOException
     */
    public static void main(String[] args) throws IOException {
        // recEval(RecEvalEnum.REC_EVAL4IN);
        // recEval(RecEvalEnum.REC_EVAL4OUT);
        // recLimitEval();
        // recNode(true);
        recTopic();
    }


    /**
     * 入门测、出门测
     *
     * @param inOrOut 区分入门测还是出门测
     * @throws IOException
     */
    public static void recEval(RecEvalEnum inOrOut) throws IOException {
        String json = ResourceUtil.readUtf8Str("engine-debug/recEval.json");
        MultiLayerGraphRecommendRequest request = JSON.parseObject(json, MultiLayerGraphRecommendRequest.class);
        Optional<SessionInfo> sessionInfo = Optional.ofNullable(request.getSessionInfo());

        RecEval4InOutParam payload = new RecEval4InOutParam()
                .setRecEvalEnum(inOrOut)
                .setCatalogIds(request.getInNodeIdList())
                .setRoundId(sessionInfo.map(SessionInfo::getRoundId).orElse(null))
                .setTopicOrderNumber(sessionInfo.map(SessionInfo::getRoundRecNum).orElse(-1));

        SkylabRequest<RecEval4InOutParam> httpRequest = new SkylabRequest<>();
        httpRequest.setTraceId(IdUtil.fastUUID());
        httpRequest.setScene(getSceneInfo(request));
        httpRequest.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/recEval";
        post(url, JSON.toJSONString(httpRequest), RecEval4InOutResult.class);
    }


    /**
     * 限时测
     *
     * @throws IOException
     */
    public static void recLimitEval() throws IOException {
        String json = ResourceUtil.readUtf8Str("engine-debug/recLimitEval.json");
        MultiLayerGraphRecommendRequest request = JSON.parseObject(json, MultiLayerGraphRecommendRequest.class);
        Optional<SessionInfo> sessionInfo = Optional.ofNullable(request.getSessionInfo());

        RecEval4LimitParam payload = new RecEval4LimitParam()
                .setCatalogIds(request.getInNodeIdList())
                .setRoundId(sessionInfo.map(SessionInfo::getRoundId).orElse(null))
                .setTopicOrderNumber(sessionInfo.map(SessionInfo::getRoundRecNum).orElse(-1));

        SkylabRequest<RecEval4LimitParam> httpRequest = new SkylabRequest<>();
        httpRequest.setTraceId(IdUtil.fastUUID());
        httpRequest.setScene(getSceneInfo(request));
        httpRequest.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/recLimitEval";
        post(url, JSON.toJSONString(httpRequest), RecEval4LimitResult.class);
    }


    /**
     * 点搜索
     *
     * @param useThinkingExpansion 是否使用 思维拓展点（仅锚点体系，同步学场景使用） 默认true
     * @throws IOException
     */
    public static void recNode(boolean useThinkingExpansion) throws IOException {
        String json = ResourceUtil.readUtf8Str("engine-debug/recNode.json");
        MultiLayerGraphRecommendRequest request = JSON.parseObject(json, MultiLayerGraphRecommendRequest.class);

        RecNodeParam payload = new RecNodeParam()
                .setCatalogIds(request.getInNodeIdList())
                .setTargetMastery(request.getTargetMastery())
                .setUseThinkingExpansion(useThinkingExpansion);

        SkylabRequest<RecNodeParam> httpRequest = new SkylabRequest<>();
        httpRequest.setTraceId(IdUtil.fastUUID());
        httpRequest.setScene(getSceneInfo(request));
        httpRequest.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/recNode";
        post(url, JSON.toJSONString(httpRequest), RecNodeResult.class);
    }


    /**
     * 点推题
     *
     * @throws IOException
     */
    public static void recTopic() throws IOException {
        String json = ResourceUtil.readUtf8Str("engine-debug/recTopic.json");
        MultiLayerGraphRecommendRequest request = JSON.parseObject(json, MultiLayerGraphRecommendRequest.class);
        Optional<SessionInfo> sessionInfo = Optional.ofNullable(request.getSessionInfo());

        RecTopicParam payload = new RecTopicParam()
                .setNodeId(request.getInNodeIdList().get(0))
                .setRoundId(sessionInfo.map(SessionInfo::getRoundId).orElse(null))
                .setTopicOrderNumber(sessionInfo.map(SessionInfo::getRoundRecNum).orElse(-1));

        SkylabRequest<RecTopicParam> httpRequest = new SkylabRequest<>();
        httpRequest.setTraceId(IdUtil.fastUUID());
        httpRequest.setScene(getSceneInfo(request));
        httpRequest.setPayload(payload);

        String url = "http://" + ADDRESS + "/skylab/api/v1/recTopic";
        post(url, JSON.toJSONString(httpRequest), RecTopicResult.class);
    }


    /**
     * http请求封装
     *
     * @param url
     * @param json
     * @param clazz
     * @param <T>
     * @return
     * @throws IOException
     */
    private static <T extends FuncResult> T post(String url, String json, Class<?> clazz) throws IOException {
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), json);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        log.info("http request= {}", json);
        Response response = httpClient.newCall(request).execute();

        String responseJson = response.body().string();
        log.info("http response= {}", responseJson);

        TypeReference<SkylabResponse<T>> typeReference = new TypeReference<SkylabResponse<T>>(clazz) {
        };
        SkylabResponse<T> result = JSON.parseObject(responseJson, typeReference);
        return result.getPayload();
    }


    /**
     * 转换获得类型信息
     *
     * @param request
     * @return
     */
    private static SceneInfo getSceneInfo(MultiLayerGraphRecommendRequest request) {
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineSceneInfo = request.getSceneInfo();

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setUserId(engineSceneInfo.getUserId());
        sceneInfo.setStudyCode(StudyCodeEnum.parse(engineSceneInfo.getStudyCode()));
        sceneInfo.setBizAction(BizActionEnum.parse(engineSceneInfo.getBizAction()));
        sceneInfo.setBizCode(BizCodeEnum.parse(engineSceneInfo.getBizCode()));
        sceneInfo.setSubjectCode(engineSceneInfo.getSubjectCode());
        sceneInfo.setPhaseCode(engineSceneInfo.getPhaseCode());
        sceneInfo.setGradeCode("07");
        sceneInfo.setAreaCode(engineSceneInfo.getAreaCode());
        sceneInfo.setFunctionCode(engineSceneInfo.getFunctionCode());
        sceneInfo.setGraphVersion(engineSceneInfo.getGraphVersion());
        // sceneInfo.setSchoolId();
        // sceneInfo.setClassId();
        // sceneInfo.setCatalogCode();
        sceneInfo.setPressCode(engineSceneInfo.getBookVersion());
        sceneInfo.setBookCode(engineSceneInfo.getBookCode());
        sceneInfo.setLayerType(engineSceneInfo.getLayerType());
        // sceneInfo.setDeviceVersion();
        // sceneInfo.setDeviceId();
        // sceneInfo.setExt1();
        // sceneInfo.setExt2();
        // sceneInfo.setExt3();
        // sceneInfo.setTest();
        // sceneInfo.setStrategyCode();
        // sceneInfo.setPlanVersionId();
        return sceneInfo;
    }
}
