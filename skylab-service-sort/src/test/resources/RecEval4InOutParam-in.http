# 推荐功能 - 测评推题
POST http://localhost:27202/skylab/api/v1/sort/process
Content-Type: application/json

{
  "header": {
    "traceId": "fb25261b-dc08-4db0-8428-442e13d5b850",
    "code": 0
  },
  "parameter": {
    "script4_groovy": {
      "createdDate": "2022-04-01T09:45:33.968Z",
      "lastModifiedDate": "2022-04-02T11:53:10.581Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "groovy4",
      "contentLength": 731,
      "id": "6246c9bd4a8725736a60dea2",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "624839264a8725736a60debd",
      "parentId": "6246c4914a8725736a60de9c",
      "desc": "资源配置/同步学_冒烟/测评/groovy4"
    },
    "sceneInfo": {
      "functionCode": "REC_EVAL4IN",
      "bookVolumeCode": "01_07020101-001",
      "test": false,
      "bizCode": "ZSY_XXJ",
      "userId": "b7e0abb4-3ce3-4844-b90f-3bc8690098f9",
      "bookVersionCode": "area_code_1",
      "areaCode": "010100",
      "graphVersion": "v1",
      "studyCode": "SYNC_LEARN",
      "phaseCode": "04",
      "subjectCode": "02",
      "gradeCode": "11"
    },
    "script2_groovy": {
      "createdDate": "2022-04-01T09:45:19.930Z",
      "lastModifiedDate": "2022-04-01T09:50:48.447Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "groovy2",
      "contentLength": 483,
      "id": "6246c9af4a8725736a60dea0",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "6246caf84a8725736a60deac",
      "parentId": "6246c4914a8725736a60de9c",
      "desc": "资源配置/同步学_冒烟/测评/groovy2"
    },
    "script1_groovy": {
      "createdDate": "2022-04-01T09:45:08.348Z",
      "lastModifiedDate": "2022-04-01T09:50:18.919Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "groovy1",
      "contentLength": 569,
      "id": "6246c9a44a8725736a60de9f",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "6246cab24a8725736a60deab",
      "parentId": "6246c4914a8725736a60de9c",
      "desc": "资源配置/同步学_冒烟/测评/groovy1"
    },
    "script3_groovy": {
      "createdDate": "2022-04-01T09:45:27.384Z",
      "lastModifiedDate": "2022-04-01T09:50:56.176Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "groovy3",
      "contentLength": 483,
      "id": "6246c9b74a8725736a60dea1",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "6246cb004a8725736a60dead",
      "parentId": "6246c4914a8725736a60de9c",
      "desc": "资源配置/同步学_冒烟/测评/groovy3"
    },
    "mainConfig": {
      "createdDate": "2022-04-01T09:16:09.503Z",
      "lastModifiedDate": "2022-04-02T11:57:35.085Z",
      "resourceCategoryId": "623ab8af7d6b6628e6ff88d3",
      "name": "测评",
      "contentLength": 1398,
      "id": "6246c2d94a8725736a60de92",
      "categoryName": "学习机_排序能力主体配置",
      "version": "62483a2f4a8725736a60dec0",
      "parentId": "6246c2c44a8725736a60de91",
      "desc": "资源配置/同步学_冒烟/测评"
    },
    "globalFeature": {
      "createdDate": "2022-04-01T09:06:25.746Z",
      "lastModifiedDate": "2022-04-01T09:06:39.227Z",
      "resourceCategoryId": "6246bcc04a8725736a60de78",
      "name": "全局特征",
      "contentLength": 3247,
      "id": "6246c0914a8725736a60de8e",
      "categoryName": "学习机推荐全局特征配置",
      "version": "6246c09f4a8725736a60de8f",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/全局特征"
    }
  },
  "payload": {
    "data": [
      {
        "code": "data",
        "data": {
          "funcCode": "REC_EVAL4IN",
          "topicOrderNumber": 1,
          "recEvalEnum": "REC_EVAL4IN",
          "roundId": "5003f35d-a434-45e9-a3b6-f767db026e54",
          "catalogIds": [
            "01_07020101-001_02_001"
          ]
        }
      }
    ]
  }
}
