# 点排序 = 点搜索
POST http://localhost:27202/skylab/api/v1/sort/process
Content-Type: application/json

{
  "header": {
    "traceId": "eb5a6308-be20-4657-b6e3-200d751a79d0",
    "code": 0
  },
  "parameter": {
    "sceneInfo": {
      "functionCode": "REC_NODE",
      "bookVolumeCode": "01",
      "test": false,
      "bizCode": "ZSY_XXJ",
      "userId": "709f7776-ad73-4468-bacf-4511d9845be3",
      "bookVersionCode": "11111",
      "areaCode": "010100",
      "graphVersion": "v1",
      "studyCode": "SYNC_LEARN",
      "phaseCode": "04",
      "subjectCode": "02"
    },
    "script2_groovy": {
      "createdDate": "2022-04-01T09:48:14.948Z",
      "lastModifiedDate": "2022-04-01T09:52:30.689Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "点排序_groovy2",
      "contentLength": 766,
      "id": "6246ca5e4a8725736a60dea7",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "6246cb5e4a8725736a60deb3",
      "parentId": "6246c4e74a8725736a60de9e",
      "desc": "资源配置/同步学_冒烟/点排序/点排序_groovy2"
    },
    "script1_groovy": {
      "createdDate": "2022-04-01T09:47:39.885Z",
      "lastModifiedDate": "2022-04-01T09:52:39.723Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "点排序_groovy1",
      "contentLength": 577,
      "id": "6246ca3b4a8725736a60dea6",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "6246cb674a8725736a60deb4",
      "parentId": "6246c4e74a8725736a60de9e",
      "desc": "资源配置/同步学_冒烟/点排序/点排序_groovy1"
    },
    "script3_groovy": {
      "createdDate": "2022-04-01T09:48:43.312Z",
      "lastModifiedDate": "2022-04-01T09:52:19.654Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "点排序_groovy3",
      "contentLength": 729,
      "id": "6246ca7b4a8725736a60dea9",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "6246cb534a8725736a60deb2",
      "parentId": "6246c4e74a8725736a60de9e",
      "desc": "资源配置/同步学_冒烟/点排序/点排序_groovy3"
    },
    "mainConfig": {
      "createdDate": "2022-04-01T09:16:16.651Z",
      "lastModifiedDate": "2022-04-02T02:36:17.697Z",
      "resourceCategoryId": "623ab8af7d6b6628e6ff88d3",
      "name": "点排序",
      "contentLength": 1121,
      "id": "6246c2e04a8725736a60de93",
      "categoryName": "学习机_排序能力主体配置",
      "version": "6247b6a14a8725736a60deb8",
      "parentId": "6246c2c44a8725736a60de91",
      "desc": "资源配置/同步学_冒烟/点排序"
    },
    "globalFeature": {
      "createdDate": "2022-04-01T09:06:25.746Z",
      "lastModifiedDate": "2022-04-01T09:06:39.227Z",
      "resourceCategoryId": "6246bcc04a8725736a60de78",
      "name": "全局特征",
      "contentLength": 3247,
      "id": "6246c0914a8725736a60de8e",
      "categoryName": "学习机推荐全局特征配置",
      "version": "6246c09f4a8725736a60de8f",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/全局特征"
    }
  },
  "payload": {
    "data": [
      {
        "code": "data",
        "data": {
          "targetMastery": 0.8,
          "catalogIds": [
            "92_07020174-001_09_003"
          ]
        }
      }
    ]
  }
}


