# 点推题
POST http://localhost:27202/skylab/api/v1/sort/process
Content-Type: application/json

{
  "header": {
    "traceId": "e7e0c41f-4fa4-43f3-977d-5130bcff1a46",
    "code": 0
  },
  "parameter": {
    "sceneInfo": {
      "functionCode": "REC_TOPIC",
      "bookVolumeCode": "01",
      "test": false,
      "bizCode": "ZSY_XXJ",
      "userId": "1500000100109685624",
      "bookVersionCode": "11111",
      "areaCode": "010100",
      "graphVersion": "v1",
      "studyCode": "SYNC_LEARN",
      "phaseCode": "04",
      "subjectCode": "02"
    },
    "script2_groovy": {
      "createdDate": "2022-04-01T09:47:19.815Z",
      "lastModifiedDate": "2022-04-01T09:53:17.273Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "点推题_groovy2",
      "contentLength": 766,
      "id": "6246ca274a8725736a60dea4",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "6246cb1c4a8725736a60deb0",
      "parentId": "6246c4d84a8725736a60de9d",
      "desc": "资源配置/同步学_冒烟/点推题/点推题_groovy2"
    },
    "script1_groovy": {
      "createdDate": "2022-04-01T09:47:01.590Z",
      "lastModifiedDate": "2022-04-01T09:53:08.618Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "点推题_groovy1",
      "contentLength": 577,
      "id": "6246ca154a8725736a60dea3",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "6246cb844a8725736a60deb5",
      "parentId": "6246c4d84a8725736a60de9d",
      "desc": "资源配置/同步学_冒烟/点推题/点推题_groovy1"
    },
    "script3_groovy": {
      "createdDate": "2022-04-01T09:47:26.223Z",
      "lastModifiedDate": "2022-04-01T09:53:37.467Z",
      "resourceCategoryId": "623ab8eb7d6b6628e6ff88d4",
      "name": "点推题_groovy3",
      "contentLength": 729,
      "id": "6246ca2e4a8725736a60dea5",
      "categoryName": "学习机_排序能力脚本配置",
      "version": "6246cb244a8725736a60deb1",
      "parentId": "6246c4d84a8725736a60de9d",
      "desc": "资源配置/同步学_冒烟/点推题/点推题_groovy3"
    },
    "mainConfig": {
      "createdDate": "2022-04-01T09:16:24.750Z",
      "lastModifiedDate": "2022-04-02T02:36:34.633Z",
      "resourceCategoryId": "623ab8af7d6b6628e6ff88d3",
      "name": "点推题",
      "contentLength": 1112,
      "id": "6246c2e84a8725736a60de94",
      "categoryName": "学习机_排序能力主体配置",
      "version": "6247b6b24a8725736a60deb9",
      "parentId": "6246c2c44a8725736a60de91",
      "desc": "资源配置/同步学_冒烟/点推题"
    },
    "globalFeature": {
      "createdDate": "2022-04-01T09:06:25.746Z",
      "lastModifiedDate": "2022-04-01T09:06:39.227Z",
      "resourceCategoryId": "6246bcc04a8725736a60de78",
      "name": "全局特征",
      "contentLength": 3247,
      "id": "6246c0914a8725736a60de8e",
      "categoryName": "学习机推荐全局特征配置",
      "version": "6246c09f4a8725736a60de8f",
      "parentId": "jingwang36_uuid",
      "desc": "资源配置/全局特征"
    }
  },
  "payload": {
    "data": [
      {
        "code": "data",
        "data": {
          "nodeId": "9c7e86cc-bae5-42c0-aab5-8b6ecf215a9e",
          "roundId": "1",
          "topicOrderNumber": 1
        }
      }
    ]
  }
}



