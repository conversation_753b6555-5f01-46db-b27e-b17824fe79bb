IP=***********
#-----------------------------------------------------------------
skyline.data.api.sdk.url=http://*************:30888/api/v1/execute
skyline.data.api.sdk.app-key=app-mih20vnu
skyline.data.api.sdk.app-secret=8f94fc5721b5d4edce2bf00abb723b26179b7e4d
skyline.data.api.sdk.keep-alive-duration-second=5
skyline.data.api.sdk.max-idle-conn=5
skyline.data.api.sdk.timeout-ms=5000
#-----------------------------------------------------------------
#skylab.zion.dict-table-name=zion_dict_info_test_v2
#skylab.zion.dict-family=c
#skylab.zion.dict-qualifier=info
#skylab.zion.dict-default-row-key=001
#skylab.zion.dict-refresh-period-seconds=3600
#skylab.zion.dict-data-api-item.dataApiId=api-78wnc6h9
#skylab.zion.dict-data-api-item.version=1
#-----------------------------------------------------------------
logging.level.ROOT=WARN
logging.level.com.iflytek.skyline=WARN
logging.level.com.iflytek.skylab=WARN
#-----------------------------------------------------------------
skynet.api.swagger2.enabled=true
#-----------------------------------------------------------------
skyline.brave.allowSubjectCodes=02
#-----------------------------------------------------------------
# mongoDB

skynet.logging.enabled=true
skynet.logging.debug.enabled=true
skynet.logging.debug.expression=$.scene.userId=="zhangsan"

skynet.logging.springmvc.enabled=true
skynet.logging.dubbo.enabled=false

skynet.logging.debug.enable.root=false
skynet.logging.debug.enable.com.iflytek.skylab.service=true
skynet.logging.debug.enable.com.iflytek.skylab.core=true
skynet.logging.debug.enable.com.iflytek.hy.rec=true

