IP=***********
SKYNET_CLUSTER=skynet
SKYNET_PLUGIN_CODE=skylab-platform
#-----------------------------------------------------------------
server.port=27202
# spring.profiles.active=dev
#-----------------------------------------------------------------
skyline.brave.enabled=true
skyline.brave.aop-enabled=true
#-----------------------------------------------------------------
spring.application.name=skylab-sort
skynet.action-point=${spring.application.name}@${SKYNET_PLUGIN_CODE}
skynet.zookeeper.enabled=true
spring.cloud.zookeeper.enabled=true
spring.cloud.zookeeper.discovery.root=/${SKYNET_CLUSTER}/discovery/${SKYNET_PLUGIN_CODE}
spring.cloud.zookeeper.connect-string=${IP}:2181
#-----------------------------------------------------------------
logging.level.ROOT=INFO
logging.level.com.iflytek.skyline=INFO
logging.level.com.iflytek.skylab=INFO
logging.level.com.iflytek.skyline.resource.manager=ERROR
#-----------------------------------------------------------------
#mongodb
#-----------------------------------------------------------------
skylab.data.api.graph.hosts=***********:9669
skylab.data.api.graph.maxConnSize=1000
skylab.data.api.graph.localCacheEnabled=true
skylab.data.api.graph.cacheVersion=20250219_001
skylab.data.api.graph.path=D://nebulaLite_20250219_001.zip
#?????????-???????
skylab.data.api.graph.nodeProps.ANCHOR_POINT=anchorPointType,difficulty,evaluatePoint,evaluations,examPoint,phaseCode,realLastLevelRelationCatas,subjectCode,tracePoints
skylab.data.api.graph.nodeProps.LEARN_PATH=pathType
skylab.data.api.graph.nodeProps.CHECK_POINT=checkPointName,checkPointType,difficulty,evaluatePoint,phaseCode,subjectCode,versionSupport
skylab.data.api.graph.nodeProps.LEARN_PATH_V2=pathType
skylab.data.api.graph.nodeProps.REVIEW_POINT=areas,difficulty,areaSupport
skylab.data.api.graph.nodeProps.TOPIC=topic_difficulty,topic_type
#-----------------------------------------------------------------
#debuging
skynet.logging.enabled=true
skynet.logging.debug.springmvc.enabled=true
skynet.logging.debug.enable.com.iflytek=true

#-----------------------------------------------------------------
#skyline.data.api.sdk.app-key=app-69v3u6vj
#skyline.data.api.sdk.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
#skyline.data.api.sdk.url=http://**************:30890/api/v1/execute
#skyline.data.api.sdk.keepAliveDurationSecond=5
#skylab.zion.dict-table-name=dim_xxj_dic_model
#skylab.zion.dict-family=u
#skylab.zion.dict-qualifier=dicModel
#skylab.zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
#skylab.zion.dict-refresh-period-seconds=3600
#skylab.zion.dict-data-api-item.dataApiId=api-vimqibeu
#------------------
zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.es-host=***********:9200,***********:9200,***********:9200
#zion.query-data-base=es
zion.dict-refresh-period-seconds=10
zion.query-data-base=hbase_data_api
zion.dict-table-name=dim_xxj_dic_model
zion.dict-data-api-item.dataApiId=api-vimqibeu
zion.feature-data-api-item.dataApiId=api-x4znzm0l
zion.app-key=app-69v3u6vj
zion.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
zion.url=http://**************:30890/api/v1/execute