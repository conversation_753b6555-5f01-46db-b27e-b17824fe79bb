package com.iflytek.skylab.service.sort.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.iflytek.hy.rec.domain.model.valueobj.EngineConfigItem;
import com.iflytek.hy.rec.domain.model.valueobj.SceneInfo;
import com.iflytek.hy.rec.errtopsort.ErrorTopicGraphRecommend;
import com.iflytek.hy.rec.errtopsort.interfaces.param.ErrorTopicGraphRecommendRequest;
import com.iflytek.hy.rec.sort.MultiLayerGraphRecommend;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.NodeInfo;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.TraceConstant;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.common.api.ApiRequestBase;
import com.iflytek.skyline.resource.domain.ConfigItem;
import com.iflytek.skyline.resource.domain.ResourcePackage;
import com.iflytek.skyline.resource.manager.ParameterConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import skynet.boot.logging.LoggingCost;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;


/**
 * 排序引擎能力服务
 *
 * <AUTHOR>
 * @date 2022/3/9 5:26 下午
 */
@Slf4j
@Validated
public class SortService {

    private final MultiLayerGraphRecommend mlgRecommend;
    private final ErrorTopicGraphRecommend errorTopicGraphRecommend;
    private final ParameterConverter parameterConverter;
    private final TraceUtils traceUtils;

    public SortService(MultiLayerGraphRecommend mlgRecommend, ErrorTopicGraphRecommend errorTopicGraphRecommend, ParameterConverter parameterConverter, TraceUtils traceUtils) {
        this.mlgRecommend = mlgRecommend;
        this.errorTopicGraphRecommend = errorTopicGraphRecommend;
        this.parameterConverter = parameterConverter;
        this.traceUtils = traceUtils;
    }

    /**
     * 测评推题、点搜索、点推题 能力 统一接口
     *
     * @param traceId          跟踪Id
     * @param sortRequest      引擎入参
     * @param configJsonObject 引擎配置
     * @return MultiLayerGraphRecommendResponse
     * @throws Exception 业务异常
     */
    @LoggingCost
    public MultiLayerGraphRecommendResponse sort(@Valid @NotBlank String traceId, @NotNull MultiLayerGraphRecommendRequest sortRequest, @NotNull JSONObject configJsonObject) throws Exception {
        if (log.isTraceEnabled()) {
            log.trace("traceId= {}; configJsonObject= {};", traceId, configJsonObject);
        }

        Assert.hasText(traceId, "The traceId must not blank.");
        Assert.notNull(sortRequest, "The MultiLayerGraphRecommendRequest must not null.");
        Assert.notNull(configJsonObject, "The configJsonObject must not null.");

        //参数配置管理
        ResourcePackage resourcePackage = this.parameterConverter.convertPackage(configJsonObject);

        if (log.isDebugEnabled()) {
            log.debug("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
            log.debug("ResourcePackage md5={}, List<ConfigItem> size={}", resourcePackage.getMd5(), resourcePackage.getItems().size());
            for (int i = 0; i < resourcePackage.getItems().size(); i++) {
                ConfigItem item = resourcePackage.getItems().get(i);
                log.debug("ResourcePackage md5={}, List<ConfigItem>.get({}): key={}, md5={}", resourcePackage.getMd5(), i, item.getKey(), item.getMd5());
            }
            log.debug("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
        }

        List<EngineConfigItem> confItems = new ArrayList<>();
        for (ConfigItem item : resourcePackage.getItems()) {
            confItems.add(new EngineConfigItem().setKey(item.getKey()).setConfig(item.getContent()).setMd5(item.getMd5()));
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        //加载配置
        this.mlgRecommend.loadConfig(resourcePackage.getMd5(), confItems);

        String userId = Optional.of(sortRequest)
                .map(MultiLayerGraphRecommendRequest::getSceneInfo)
                .map(SceneInfo::getUserId)
                .orElse(null);
        if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
            //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
        }else {
            traceUtils.record("mlgRecommend#loadConfig", Collections.EMPTY_MAP, Collections.singletonMap("cost", String.valueOf(stopwatch.elapsed(TimeUnit.MILLISECONDS))));
        }
        log.info("this.mlgRecommend.loadConfig(resourcePackage.getMd5(), confItems);耗时 = {}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        //设置StrategyId
        sortRequest.getSessionInfo().setStrategyId(resourcePackage.getMd5());

        if (isLoopNodeList(sortRequest)) {
            // 全学科 全学段 os+非os  一轮复习 点排序场景
            // OS场景 期中备考 期末备考 点排序场景
            // 业务的catalog是一轮复习二级目录（专题）id的列表，实现每个专题推一个点，需要循环调用引擎
            // 其他场景catalog列表由引擎自主处理

            List<MultiLayerGraphRecommendRequest> loopRequestList = Lists.newArrayListWithExpectedSize(sortRequest.getInNodeIdList().size());
            for (String catalog : sortRequest.getInNodeIdList()) {
                MultiLayerGraphRecommendRequest copy = deepCopyWithoutNodeList(sortRequest);
                copy.setInNodeIdList(Lists.newArrayList(catalog));
                loopRequestList.add(copy);
            }

            stopwatch.reset().start();

            MultiLayerGraphRecommendResponse engineResponse = null;
            List<NodeInfo> recNodeInfoList = Lists.newArrayList();
            for (MultiLayerGraphRecommendRequest request : loopRequestList) {
                engineResponse = this.mlgRecommend.recommend(request);
                if (null != engineResponse && null != engineResponse.getOutNodeInfos()) {
                    recNodeInfoList.addAll(engineResponse.getOutNodeInfos());
                }
            }

            if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
                //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
            }else {
                traceUtils.record("mlgRecommend#recommend", Collections.EMPTY_MAP, Collections.singletonMap("cost", String.valueOf(stopwatch.elapsed(TimeUnit.MILLISECONDS))));
            }

            if (null != engineResponse) {
                engineResponse.setOutNodeInfos(recNodeInfoList);
            } else {
                engineResponse = new MultiLayerGraphRecommendResponse();
            }

            log.debug("loop mlgRecommend.recommend(sortRequest); cost= {} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return engineResponse;
        } else {
            stopwatch.reset().start();
            MultiLayerGraphRecommendResponse engineResponse = this.mlgRecommend.recommend(sortRequest);

            if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
                //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
            }else {
                traceUtils.record("mlgRecommend#recommend", Collections.EMPTY_MAP, Collections.singletonMap("cost", String.valueOf(stopwatch.elapsed(TimeUnit.MILLISECONDS))));
            }
            log.debug("mlgRecommend.recommend(sortRequest); cost= {} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return engineResponse;
        }
    }

    /**
     * 临时方案，引擎不支持批量目录点排序，针对特殊场景，由服务做循环调用
     * 下一期引擎支持批量点排序，删除此逻辑
     *
     * @param sortRequest
     * @return
     */
    private boolean isLoopNodeList(MultiLayerGraphRecommendRequest sortRequest) {
        return null != sortRequest.getSceneInfo() && null != sortRequest.getInNodeIdList()
                && (BizActionEnum.FIRST_REVISE_SORT.name().equals(sortRequest.getSceneInfo().getBizAction())
                || BizActionEnum.EXAM_OS_MID_STAGE_SEARCH_WEAK.name().equals(sortRequest.getSceneInfo().getBizAction())
                || BizActionEnum.EXAM_OS_FINAL_STAGE_SEARCH_WEAK.name().equals(sortRequest.getSceneInfo().getBizAction())
                || BizActionEnum.EXAM_STAGE_SEARCH_WEAK.name().equals(sortRequest.getSceneInfo().getBizAction()));
    }


    /**
     * 大图谱推荐 统一接口
     *
     * @param traceId          跟踪Id
     * @param sortRequest      引擎入参
     * @param configJsonObject 引擎配置
     * @return MultiLayerGraphRecommendResponse
     * @throws Exception 业务异常
     */
    @LoggingCost
    public com.iflytek.hy.rec.errtopsort.interfaces.param.MultiLayerGraphRecommendResponse macrographSort(@Valid @NotBlank String traceId, @NotNull ErrorTopicGraphRecommendRequest sortRequest, @NotNull JSONObject configJsonObject) throws Exception {
        if (log.isTraceEnabled()) {
            log.trace("traceId= {}; configJsonObject= {};", traceId, configJsonObject);
        }
        Assert.hasText(traceId, "The traceId must not blank.");
        Assert.notNull(sortRequest, "The MultiLayerGraphRecommendRequest must not null.");
        Assert.notNull(configJsonObject, "The configJsonObject must not null.");

        //参数配置管理
        ResourcePackage resourcePackage = this.parameterConverter.convertPackage(configJsonObject);

        if (log.isDebugEnabled()) {
            log.debug("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
            log.debug("ResourcePackage md5={}, List<ConfigItem> size={}", resourcePackage.getMd5(), resourcePackage.getItems().size());
            for (int i = 0; i < resourcePackage.getItems().size(); i++) {
                ConfigItem item = resourcePackage.getItems().get(i);
                log.debug("ResourcePackage md5={}, List<ConfigItem>.get({}): key={}, md5={}", resourcePackage.getMd5(), i, item.getKey(), item.getMd5());
            }
            log.debug("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
        }

        List<EngineConfigItem> confItems = new ArrayList<>();
        for (ConfigItem item : resourcePackage.getItems()) {
            confItems.add(new EngineConfigItem().setKey(item.getKey()).setConfig(item.getContent()).setMd5(item.getMd5()));
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        //加载配置
        this.errorTopicGraphRecommend.loadConfig(resourcePackage.getMd5(), confItems);
        log.info("this.mlgRecommend.loadConfig(resourcePackage.getMd5(), confItems);耗时 = {}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));

        //设置StrategyId
        sortRequest.getSessionInfo().setStrategyId(resourcePackage.getMd5());

        stopwatch.reset().start();
        com.iflytek.hy.rec.errtopsort.interfaces.param.MultiLayerGraphRecommendResponse recommend = this.errorTopicGraphRecommend.recommend(sortRequest);

        log.debug("mlgRecommend.recommend(sortRequest); cost= {} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return recommend;
    }


    private MultiLayerGraphRecommendRequest deepCopyWithoutNodeList(MultiLayerGraphRecommendRequest request) {
        MultiLayerGraphRecommendRequest copy = new MultiLayerGraphRecommendRequest();
        if (null != request.getSceneInfo()) {
            copy.setSceneInfo(BeanUtil.copyProperties(request.getSceneInfo(), SceneInfo.class));
        }

        if (null != request.getSessionInfo()) {
            copy.setSessionInfo(BeanUtil.copyProperties(request.getSessionInfo(), SessionInfo.class));
        }

        if (null != request.getInNodeChapterMap()) {
            copy.setInNodeChapterMap(new HashMap<>(request.getInNodeChapterMap()));
        }

        if (null != request.getTargetMastery()) {
            copy.setTargetMastery(new Double(request.getTargetMastery()));
        }

        if (null != request.getRelationNodeType()) {
            copy.setRelationNodeType(new CopyOnWriteArrayList<>(request.getRelationNodeType()));
        }

        if (null != request.getNodePropertFilter()) {
            copy.setNodePropertFilter(new HashMap<>(request.getNodePropertFilter()));
        }
        if (null != request.getForbiddenNodes()) {
            copy.setForbiddenNodes(new CopyOnWriteArrayList<>(request.getForbiddenNodes()));
        }
        if (StrUtil.isNotBlank(request.getSelectedCenterPoint())) {
            copy.setSelectedCenterPoint(request.getSelectedCenterPoint());
        }
        return copy;
    }
}
