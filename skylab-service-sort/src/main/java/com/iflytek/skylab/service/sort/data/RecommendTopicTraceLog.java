package com.iflytek.skylab.service.sort.data;

import com.iflytek.hy.rec.domain.model.valueobj.SceneInfo;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.Jsonable;
import com.iflytek.skylab.core.dataapi.data.RecLog;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/9 10:05
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecommendTopicTraceLog extends Jsonable {

    public static final String TYPE = "RECOMMEND_TOPIC_TRACE_LOG";

    private SceneInfo sceneInfo;

    private SessionInfo sessionInfo;

    private String engineType = "sortEngine";

    private Boolean terminationFlag;

    private Boolean doTerminateTopic;

    private List<RecLog> recLogs;

    private Date sendTime = new Date();

}
