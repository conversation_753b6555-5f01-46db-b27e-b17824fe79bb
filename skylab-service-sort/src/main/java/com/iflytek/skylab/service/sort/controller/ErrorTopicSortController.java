package com.iflytek.skylab.service.sort.controller;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.iflytek.hy.rec.errtopsort.interfaces.param.ErrorTopicGraphRecommendRequest;
import com.iflytek.hy.rec.errtopsort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.errtopsort.interfaces.param.NodeInfo;
import com.iflytek.hy.rec.framework.exception.EngineException;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.adapter.scene.SceneInfoSelector;
import com.iflytek.skylab.core.data.adapter.sort.errortopic.ErrorTopicAbstractParamAdapter;
import com.iflytek.skylab.core.data.adapter.sort.errortopic.ErrorTopicSortParamAdapterSelector;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.sort.service.SortService;
import com.iflytek.skylab.service.sort.service.StudyLogBusinessService;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.logging.LoggingCost;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/7 15:43
 */
@Slf4j
@Api(tags = "多维图谱排序推荐服务")
@RestController
@RequestMapping("/skylab/api/v1/sort/errortopic")
@EnableSkynetSwagger2
public class ErrorTopicSortController {


    private final ErrorTopicSortParamAdapterSelector adapterSelector;
    private final SortService sortService;
    private final StudyLogBusinessService studyLogBusinessService;

    public ErrorTopicSortController(ErrorTopicSortParamAdapterSelector adapterSelector,
                                    SortService sortService, StudyLogBusinessService studyLogBusinessService) {

        this.adapterSelector = adapterSelector;
        this.sortService = sortService;
        this.studyLogBusinessService = studyLogBusinessService;
    }

    /**
     * 大图谱-错题本推荐
     *
     * @param apiRequest 请求参数
     * @return DispatchApiResponse 请求响应
     */
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "大图谱-能力运行")
    @PostMapping("/process")
    @LoggingCost
    public DispatchApiResponse errorTopicProcess(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("ApiRequest= {}", apiRequest);
        }
        String traceId = apiRequest.getTraceId();

        // SceneInfo sceneInfo = apiRequest.getScene(SceneInfo.class);
        SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);
        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }

        //引擎参数转换
        ErrorTopicAbstractParamAdapter<?, ?> paramAdapter = adapterSelector.select(sceneInfo, apiRequest.getPayload());

        ErrorTopicGraphRecommendRequest engineRequest = paramAdapter.adapt(traceId, sceneInfo, apiRequest.getPayload());
        if (log.isDebugEnabled()) {
            log.debug("EngineRequest= {}", engineRequest);
            log.debug("traceId={}; MultiLayerGraphRecommendRequest:Json= {}", traceId, JSON.toJSONString(engineRequest));
        }

        DispatchApiResponse response = new DispatchApiResponse();
        try {
            //移除 场景信息
            if (apiRequest.getParameter().containsKey(DispatchApiRequest.SCENE_INFO_KEY)) {
                apiRequest.getParameter().remove(DispatchApiRequest.SCENE_INFO_KEY);
            }

            MultiLayerGraphRecommendResponse engineResponse = sortService.macrographSort(traceId, engineRequest, apiRequest.getParameter());

            if (engineResponse == null) {
                log.error("MultiLayerGraphRecommendResponse=null, traceId={}", traceId);
                throw new ParamNotExistException("MultiLayerGraphRecommendResponse");
            }

            if (log.isDebugEnabled()) {
                log.debug("EngineResponse= {}", engineResponse);
                log.debug("traceId={}; MultiLayerGraphRecommendResponse:Json= {}", traceId, JSON.toJSONString(engineResponse));
            }
            FuncResult adapt = paramAdapter.adapt(engineRequest, engineResponse);

            // outNodeInfos 至少是空数据，避免是 null
            List<NodeInfo> outNodeInfos = Optional.ofNullable(engineResponse.getOutNodeInfos()).orElse(Lists.newArrayList());
            engineResponse.setOutNodeInfos(outNodeInfos);

            //推题场景，存储推荐记录
            studyLogBusinessService.storeErrorTopicRecommendLogs(traceId, sceneInfo, engineRequest, engineResponse);

            //设值响应数据
            response.setPayload(adapt);
        } catch (EngineException ee) {
            log.error("Call SortEngine error code= {}; message= {}; failover={}", ee.getErrorCode(), ee.getMessage(), ee.getRedirect());

            ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
            if (ee.getRedirect()) {
                header.setErrorNodes(Lists.newArrayList(new ApiResponseHeader.ErrorNode("failover", "true")));
            }
            response.setHeader(header);
        }
        response.setTraceId(traceId);
        return response;
    }
}
