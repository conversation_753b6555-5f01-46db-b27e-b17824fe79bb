package com.iflytek.skylab.service.sort.service.mapstruct;

import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.dataapi.data.RecLog;
import com.iflytek.skylab.core.domain.SceneInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * StudyLogData mapstruct对象转换
 *
 * <AUTHOR>
 */
@Mapper
public interface StudyLogDataMapper {

    StudyLogDataMapper INSTANCE = Mappers.getMapper(StudyLogDataMapper.class);

    /**
     * 设值 场景数据
     *
     * @param traceId 跟踪Id
     * @param sceneInfo 场景信息
     * @return RecLog
     */
    @Mapping(source = "sceneInfo.userId", target = "userId")
    @Mapping(source = "sceneInfo.phaseCode", target = "phaseCode")
    @Mapping(source = "sceneInfo.schoolId", target = "schoolId")
    @Mapping(source = "sceneInfo.gradeCode", target = "gradeCode")
    @Mapping(source = "sceneInfo.classId", target = "classId")
    @Mapping(source = "sceneInfo.subjectCode", target = "subjectCode")
    @Mapping(source = "sceneInfo.bookCode", target = "bookCode")
    @Mapping(source = "sceneInfo.graphVersion", target = "graphVersion")
    @Mapping(source = "sceneInfo.studyCode", target = "studyCode")
    @Mapping(source = "sceneInfo.bizCode", target = "bizCode")
    @Mapping(source = "sceneInfo.bizAction", target = "bizAction")
    @Mapping(source = "sceneInfo.functionCode", target = "funcCode")
    RecLog toRecommendLog(String traceId, SceneInfo sceneInfo);

    @Mapping(source = "sceneInfo.userId", target = "userId")
    @Mapping(source = "sceneInfo.phaseCode", target = "phaseCode")
    @Mapping(source = "sceneInfo.schoolId", target = "schoolId")
    @Mapping(source = "sceneInfo.gradeCode", target = "gradeCode")
    @Mapping(source = "sceneInfo.classId", target = "classId")
    @Mapping(source = "sceneInfo.subjectCode", target = "subjectCode")
    @Mapping(source = "sceneInfo.bookCode", target = "bookCode")
    @Mapping(source = "sceneInfo.graphVersion", target = "graphVersion")
    @Mapping(source = "sceneInfo.studyCode", target = "studyCode")
    @Mapping(source = "sceneInfo.bizCode", target = "bizCode")
    @Mapping(source = "sceneInfo.bizAction", target = "bizAction")
    @Mapping(source = "sceneInfo.functionCode", target = "funcCode")
    @Mapping(source = "sceneInfo.ext1", target = "from")
    @Mapping(source = "sessionInfo.roundId", target = "roundId")
    @Mapping(source = "sessionInfo.roundRecNum", target = "roundIndex")
    @Mapping(constant = "0", target = "cloneFlag")
    RecLog toRecommendLog(String traceId, SceneInfo sceneInfo, SessionInfo sessionInfo);

    @Mapping(source = "sceneInfo.userId", target = "userId")
    @Mapping(source = "sceneInfo.phaseCode", target = "phaseCode")
    @Mapping(source = "sceneInfo.schoolId", target = "schoolId")
    @Mapping(source = "sceneInfo.gradeCode", target = "gradeCode")
    @Mapping(source = "sceneInfo.classId", target = "classId")
    @Mapping(source = "sceneInfo.subjectCode", target = "subjectCode")
    @Mapping(source = "sceneInfo.bookCode", target = "bookCode")
    @Mapping(source = "sceneInfo.graphVersion", target = "graphVersion")
    @Mapping(source = "sceneInfo.studyCode", target = "studyCode")
    @Mapping(source = "sceneInfo.bizCode", target = "bizCode")
    @Mapping(source = "sceneInfo.bizAction", target = "bizAction")
    @Mapping(source = "sceneInfo.functionCode", target = "funcCode")
    @Mapping(source = "sessionInfo.roundId", target = "roundId")
    @Mapping(source = "sessionInfo.roundRecNum", target = "roundIndex")
    @Mapping(constant = "0", target = "cloneFlag")
    RecLog toErrorTopicRecommendLog(String traceId, SceneInfo sceneInfo, com.iflytek.hy.rec.errtopsort.interfaces.param.SessionInfo sessionInfo);
}
