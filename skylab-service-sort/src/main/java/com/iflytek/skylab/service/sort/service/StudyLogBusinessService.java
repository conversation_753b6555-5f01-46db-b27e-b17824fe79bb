package com.iflytek.skylab.service.sort.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.iflytek.hy.rec.errtopsort.interfaces.param.ErrorTopicGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.*;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.data.MasterDiagnoseParam;
import com.iflytek.skylab.core.data.RecTraceNodeParam;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.RecLog;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.sort.data.RecommendTopicTraceLog;
import com.iflytek.skylab.service.sort.service.mapstruct.StudyLogDataMapper;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.data.TraceHeaderUtils;
import com.iflytek.skyline.brave.data.TraceRecordGroup;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.feign.SkylineRouterFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;

import java.util.*;

/**
 * 学情日志 相关业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
public class StudyLogBusinessService {

    @Value("${spring.application.name:skylab-sort}")
    private String springApplicationName;

    private final SkylineRouterFeign skylineRouterFeign;
    private final TraceUtils traceUtils;

    public StudyLogBusinessService(SkylineRouterFeign skylineRouterFeign, TraceUtils traceUtils) {
        this.skylineRouterFeign = skylineRouterFeign;
        this.traceUtils = traceUtils;
    }

    private static final CopyOptions COPY_OPTIONS = CopyOptions.create()
            .ignoreNullValue()
            .ignoreError();

    //知识簇终止测评funcCode
    private static final List<String> KC_EVAL_END_FUNCODES = Arrays.asList(
            RecEvalEnum.KC_REC_EVAL4IN.name(),
            RecEvalEnum.KC_REC_EVAL4CTN.name(),
            RecEvalEnum.KC_REC_EVAL4OUT.name(),
            RecEvalEnum.KC_REC_EVAL4SYNC.name());

    //测评终止: 出门测 || 点排序场景 || 点推题场景，不更新用户画像
    private static final Set<String> NOT_EVAL_END_FUNCODES = new HashSet<>();

    static {
        // 初始化NOT_EVAL_END_FUNCODES，包含所有RecNodeEnum的值
        RecNodeEnum[] recNodeEnums = RecNodeEnum.values();
        for (RecNodeEnum value : recNodeEnums) {
            NOT_EVAL_END_FUNCODES.add(value.name());
        }

        RecTopicEnum[] recTopicEnums = RecTopicEnum.values();
        for (RecTopicEnum value : recTopicEnums) {
            NOT_EVAL_END_FUNCODES.add(value.name());
        }
        //出门测
        NOT_EVAL_END_FUNCODES.add(RecEvalEnum.REC_EVAL4OUT.name());
        NOT_EVAL_END_FUNCODES.add(RecEvalEnum.REC_EVAL4SYNC.name());
        NOT_EVAL_END_FUNCODES.add(RecEvalEnum.KC_REC_EVAL4OUT.name());
        NOT_EVAL_END_FUNCODES.add(RecEvalEnum.KC_REC_EVAL4SYNC.name());

    }

    /**
     * 推题场景，存储推荐记录
     *
     * @param traceId   跟踪Id
     * @param sceneInfo 场景信息
     * @param request   引擎入参
     * @param response  引擎返回
     */
    public void storeRecommendLogs(String traceId,
                                   SceneInfo sceneInfo,
                                   MultiLayerGraphRecommendRequest request,
                                   MultiLayerGraphRecommendResponse response) {

        // 点排序不存推荐记录
        RecNodeEnum[] values = RecNodeEnum.values();
        for (RecNodeEnum value : values) {
            if (value.name().equals(sceneInfo.getFunctionCode())) {
                return;
            }
        }
//        if (RecNodeParam.FUNC_CODE.equals(sceneInfo.getFunctionCode()) || RecTraceNodeParam.FUNC_CODE.equals(sceneInfo.getFunctionCode())) {
//            return;
//        }
        if (RecTraceNodeParam.FUNC_CODE.equals(sceneInfo.getFunctionCode())) {
            return;
        }

        //多轮测评推题 集合仅有1个推荐资源
        List<NodeInfo> outNodeInfos = response.getOutNodeInfos();
        Optional<RecommendInfo> recommendInfo = Optional.ofNullable(response.getRecommendInfo());
        if (CollectionUtils.isEmpty(outNodeInfos)) {
            log.error("MultiLayerGraphRecommendResponse|推荐点列表为空，traceId={}", traceId);
        }

        // 组装recProps
        Long recTime = System.currentTimeMillis() / 1000;
        Boolean isTermination = recommendInfo.map(RecommendInfo::getIsTermination).orElse(false);
        Long recEndTime = isTermination ? recTime : null;
        Long unitTimes = recommendInfo.map(RecommendInfo::getCostTime).orElse(null);
        if (log.isDebugEnabled()) {
            log.debug("recommendInfo:{}", response.getRecommendInfo());
            log.debug("recTime:{}, isTermination:{}, recEndTime:{}, unitTimes:{}", recTime, isTermination, recEndTime, unitTimes);
        }
        StudyLogRecordEntity.RecProps recProps = new StudyLogRecordEntity.RecProps();
        recProps.setRecTraceId(traceId);
        recProps.setRecTime(recTime);
        recProps.setRecContext(null);
        recProps.setRecEndTime(recEndTime);
        recProps.setUnitTimes(unitTimes);

        // 初始化RecLog， 填充场景信息数据
        RecLog source = StudyLogDataMapper.INSTANCE.toRecommendLog(traceId, sceneInfo, request.getSessionInfo());
        source.setRecProps(recProps);
        if (log.isDebugEnabled()) {
            log.debug("source RecLog:{}", source);
        }

        // 测评推题  点推题    -> nodeinfo -> resNodeId       nodeinfo.relation -> 关联点
        List<RecLog> candidates = Lists.newArrayList();
        for (NodeInfo nodeInfo : outNodeInfos) {
            Optional<RelationNodeInfo> relationNodeInfo = Optional.ofNullable(nodeInfo.getRelationNodes());
            String nodeId = relationNodeInfo.map(RelationNodeInfo::getNodeId).orElse(null);
            NodeTypeEnum nodeType = relationNodeInfo.map(RelationNodeInfo::getNodeType).map(NodeTypeEnum::parse).orElse(null);

            RecLog recLog = new RecLog();
            BeanUtil.copyProperties(source, recLog, COPY_OPTIONS);
            recLog.setNodeId(nodeId);
            recLog.setNodeType(nodeType);
            recLog.setResNodeId(nodeInfo.getNodeId());
            recLog.setResNodeType(ResourceTypeEnum.TOPIC);

            candidates.add(recLog);
        }

        if (CollectionUtil.isNotEmpty(candidates)) {
            DataHub.getStudyLogService().saveRecLogList(traceId, candidates);

            // 数据埋点
            RecommendTopicTraceLog traceLog = new RecommendTopicTraceLog()
                    .setSceneInfo(request.getSceneInfo())
                    .setSessionInfo(request.getSessionInfo())
                    .setRecLogs(candidates)
                    .setTerminationFlag(isTermination)
                    .setDoTerminateTopic(recommendInfo.map(RecommendInfo::getIsRecommendDoTerminateTopic).orElse(false));
            try {
                String userId = sceneInfo.getUserId();
                if (StringUtils.isNotBlank(userId) && userId.startsWith(TraceConstant.EXCLUDE_PREFIX)) {
                    //  测试用户不投递到kafka
            log.info("notsend_kafka:{}",userId);
                }else {
                    traceUtils.record(TraceRecordGroup.FLOW, RecommendTopicTraceLog.TYPE, traceLog);
                }
            } catch (Exception e) {
                if (log.isDebugEnabled()) {
                    log.debug("RecommendTopicTraceLog:{}", traceLog);
                }
                log.error("RecommendTopicTraceLog 埋点日志记录异常，message={}", e.getMessage());
            }
        }
    }

    /**
     * 根据引擎推荐结果，更新用户画像
     *
     * @param traceId    跟踪Id
     * @param sceneInfo  场景信息
     * @param catalogIds 当前测评的目录
     * @param response   引擎返回
     */
    public void updateUserMasterInfo(String traceId, SceneInfo sceneInfo, List<String> catalogIds, Map<String, String> inNodeChapterMap, MultiLayerGraphRecommendResponse response) {
        RecommendInfo recommendInfo = response.getRecommendInfo();
        // 测评未终止
        if (recommendInfo == null || !Boolean.TRUE.equals(recommendInfo.getIsTermination())) {
            return;
        }

        if (NOT_EVAL_END_FUNCODES.contains(sceneInfo.getFunctionCode())) {
            log.info("测评终止: 出门测 || 点排序场景 || 点推题场景，不更新用户画像");
            return;
        }

        //精准学os
        if (StudyCodeEnum.SYNC_OS.equals(sceneInfo.getStudyCode()) && MapUtil.isNotEmpty(inNodeChapterMap)) {
            //ai诊断 自场景 -比如 题包推荐
            catalogIds = Lists.newArrayList(inNodeChapterMap.values());
        }

        //调用路由服务开始更新用户画像
        MasterDiagnoseParam masterDiagnoseParam = new MasterDiagnoseParam();
        //设值 画像更新范围
        masterDiagnoseParam.setCatalogIds(catalogIds);
        //整理当前场景画像更新服务输入(全量更新参数为1)
        masterDiagnoseParam.setAllUpdate(true);

        if (KC_EVAL_END_FUNCODES.contains(sceneInfo.getFunctionCode())) {
            //知识簇终止测评
            masterDiagnoseParam.setMasterFuncEnum(MasterFuncEnum.KC_MASTER_DIAGNOSE);
        }
        sceneInfo.setFunctionCode(masterDiagnoseParam.getFuncCode());
        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setTraceId(traceId);
        apiRequest.setObjectPayload(masterDiagnoseParam);
        apiRequest.setObjectScene(sceneInfo);
        MultiValueMap<String, String> header = TraceHeaderUtils.buildHeader(traceId, springApplicationName);
        if (log.isDebugEnabled()) {
            log.debug("traceId={},ApiRequest={}", traceId, apiRequest);
        }
        ApiResponse apiResponse = skylineRouterFeign.routeCall(apiRequest, header);
        if (log.isDebugEnabled()) {
            log.debug("traceId={},ApiResponse= {}", traceId, apiResponse);
        }
    }


    public void storeErrorTopicRecommendLogs(String traceId, SceneInfo sceneInfo, ErrorTopicGraphRecommendRequest request, com.iflytek.hy.rec.errtopsort.interfaces.param.MultiLayerGraphRecommendResponse response) {
        // 点排序不存推荐记录
//        if (RecNodeParam.FUNC_CODE.equals(sceneInfo.getFunctionCode())) {
//            return;
//        }
        RecNodeEnum[] values = RecNodeEnum.values();
        for (RecNodeEnum value : values) {
            if (value.name().equals(sceneInfo.getFunctionCode())) {
                return;
            }
        }
        //多轮测评推题 集合仅有1个推荐资源
        List<com.iflytek.hy.rec.errtopsort.interfaces.param.NodeInfo> outNodeInfos = response.getOutNodeInfos();
        Optional<com.iflytek.hy.rec.errtopsort.interfaces.param.RecommendInfo> recommendInfo = Optional.ofNullable(response.getRecommendInfo());
        if (CollectionUtils.isEmpty(outNodeInfos)) {
            log.error("MultiLayerGraphRecommendResponse|推荐点列表为空，traceId={}", traceId);
        }

        // 组装recProps
        Long recTime = System.currentTimeMillis() / 1000;
        Boolean isTermination = recommendInfo.map(com.iflytek.hy.rec.errtopsort.interfaces.param.RecommendInfo::getIsTermination).orElse(false);
        Long recEndTime = isTermination ? recTime : null;
        Long unitTimes = recommendInfo.map(com.iflytek.hy.rec.errtopsort.interfaces.param.RecommendInfo::getCostTime).orElse(null);
        if (log.isDebugEnabled()) {
            log.debug("ErrorTopicrecommendInfo:{}", response.getRecommendInfo());
            log.debug("recTime:{}, isTermination:{}, recEndTime:{}, unitTimes:{}", recTime, isTermination, recEndTime, unitTimes);
        }
        StudyLogRecordEntity.RecProps recProps = new StudyLogRecordEntity.RecProps();
        recProps.setRecTraceId(traceId);
        recProps.setRecTime(recTime);
        recProps.setRecContext(null);
        recProps.setRecEndTime(recEndTime);
        recProps.setUnitTimes(unitTimes);

        // 初始化RecLog， 填充场景信息数据
        RecLog source = StudyLogDataMapper.INSTANCE.toErrorTopicRecommendLog(traceId, sceneInfo, request.getSessionInfo());
        source.setRecProps(recProps);
        if (log.isDebugEnabled()) {
            log.debug("source RecLog:{}", source);
        }

        // 测评推题  点推题    -> nodeinfo -> resNodeId       nodeinfo.relation -> 关联点
        List<RecLog> candidates = Lists.newArrayList();
        for (com.iflytek.hy.rec.errtopsort.interfaces.param.NodeInfo nodeInfo : outNodeInfos) {
            Optional<com.iflytek.hy.rec.errtopsort.interfaces.param.RelationNodeInfo> relationNodeInfo = Optional.ofNullable(nodeInfo.getRelationNodes());
            String nodeId = relationNodeInfo.map(com.iflytek.hy.rec.errtopsort.interfaces.param.RelationNodeInfo::getNodeId).orElse(null);
            NodeTypeEnum nodeType = relationNodeInfo.map(com.iflytek.hy.rec.errtopsort.interfaces.param.RelationNodeInfo::getNodeType).map(NodeTypeEnum::parse).orElse(null);

            RecLog recLog = new RecLog();
            BeanUtil.copyProperties(source, recLog, COPY_OPTIONS);
            recLog.setNodeId(nodeId);
            recLog.setNodeType(nodeType);
            recLog.setResNodeId(nodeInfo.getNodeId());
            recLog.setResNodeType(ResourceTypeEnum.TOPIC);

            candidates.add(recLog);
        }

        if (CollectionUtil.isNotEmpty(candidates)) {
            DataHub.getStudyMacrographLogService().saveRecLogList(traceId, candidates);
//
//            // 数据埋点
//            RecommendTopicTraceLog traceLog = new RecommendTopicTraceLog()
//                    .setSceneInfo(request.getSceneInfo())
//                    .setSessionInfo(request.getSessionInfo())
//                    .setRecLogs(candidates)
//                    .setTerminationFlag(isTermination)
//                    .setDoTerminateTopic(recommendInfo.map(RecommendInfo::getIsRecommendDoTerminateTopic).orElse(false));
//            try {
//                traceUtils.record(TraceRecordGroup.FLOW, RecommendTopicTraceLog.TYPE, traceLog);
//            } catch (Exception e) {
//                log.debug("RecommendTopicTraceLog:{}", traceLog);
//                log.error("RecommendTopicTraceLog 埋点日志记录异常，message={}", e.getMessage());
//            }
        }
    }
}
