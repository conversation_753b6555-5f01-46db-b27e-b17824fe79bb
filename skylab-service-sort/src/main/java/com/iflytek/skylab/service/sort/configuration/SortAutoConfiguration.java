package com.iflytek.skylab.service.sort.configuration;

import com.iflytek.cog2.feaflow.sdk.annotation.EnableSkylabZion;
import com.iflytek.hy.rec.errtopsort.ErrorTopicGraphRecommend;
import com.iflytek.hy.rec.sort.MultiLayerGraphRecommend;
import com.iflytek.hy.rec.sort.annotation.EnableMultiLayerGraphRecommend;
import com.iflytek.skylab.core.dataapi.annotation.EnableApiException;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataApiRedis;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.service.sort.service.SortService;
import com.iflytek.skylab.service.sort.service.StudyLogBusinessService;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import com.iflytek.skyline.common.EnableSkylineFeign;
import com.iflytek.skyline.common.feign.SkylineRouterFeign;
import com.iflytek.skyline.resource.annotation.EnableSkylineResource;
import com.iflytek.skyline.resource.manager.ParameterConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetLogging;

/**
 * <AUTHOR>
 * @date 2022/3/9 4:31 下午
 */
@EnableSkynetLogging
@Configuration(proxyBeanMethods = false)
@EnableSkylineBrave
@EnableSkylineFeign
@EnableSkylineResource
@EnableApiException
@EnableDataHub
@EnableMultiLayerGraphRecommend
@EnableSkylabZion
@EnableDataApiRedis
@com.iflytek.hy.rec.errtopsort.annotation.EnableMultiLayerGraphRecommend
public class SortAutoConfiguration {

    @Bean
    public SortService sortService(MultiLayerGraphRecommend mlgRecommend, ErrorTopicGraphRecommend errorTopicGraphRecommend, ParameterConverter parameterConverter, TraceUtils traceUtils) {
        return new SortService(mlgRecommend, errorTopicGraphRecommend, parameterConverter, traceUtils);
    }

    @Bean
    public StudyLogBusinessService studyLogBusinessService(SkylineRouterFeign skylineRouterFeign, TraceUtils traceUtils) {
        return new StudyLogBusinessService(skylineRouterFeign, traceUtils);
    }
}
