package com.iflytek.skylab.service.sort.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.hy.rec.framework.exception.EngineException;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.NodeInfo;
import com.iflytek.hy.rec.sort.interfaces.param.RecommendInfo;
import com.iflytek.skylab.core.constant.RecEvalEnum;
import com.iflytek.skylab.core.constant.RecTopicEnum;
import com.iflytek.skylab.core.data.adapter.scene.SceneInfoSelector;
import com.iflytek.skylab.core.data.adapter.sort.AbstractParamAdapter;
import com.iflytek.skylab.core.data.adapter.sort.SortParamAdapterSelector;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyForbiddenRecordEntity;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.sort.service.SortService;
import com.iflytek.skylab.service.sort.service.StudyLogBusinessService;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.api.ApiResponseHeader.ErrorNode;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import com.iflytek.skyline.common.feign.SkylineRouterFeign;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.logging.LoggingCost;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 排序服务-v1接口
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "多维图谱排序推荐服务")
@RestController
@RequestMapping("/skylab/api/v1/sort")
@EnableSkynetSwagger2
public class SortController {

    @Value("${spring.application.name:skylab-sort}")
    private String springApplicationName;

    private final SortParamAdapterSelector adapterSelector;
    private final SortService sortService;
    private final StudyLogBusinessService studyLogBusinessService;
    private final SkylineRouterFeign skylineRouterFeign;

    public SortController(SortParamAdapterSelector adapterSelector,
                          SortService sortService,
                          StudyLogBusinessService studyLogBusinessService,
                          SkylineRouterFeign skylineRouterFeign) {

        this.adapterSelector = adapterSelector;
        this.sortService = sortService;
        this.studyLogBusinessService = studyLogBusinessService;
        this.skylineRouterFeign = skylineRouterFeign;
    }

    //支持换题的funcode
    private static final List<String> SUPPORT_CHANGE_FUNCODE = Lists.newArrayList(
            RecEvalEnum.KC_REC_EVAL4IN.name(),
            RecEvalEnum.KC_REC_EVAL4CTN.name(),
            RecEvalEnum.REC_EVAL4IN.name(),
            RecEvalEnum.REC_EVAL4CTN.name(),
            RecEvalEnum.REC_EVAL4SYNC.name(),
            RecEvalEnum.KC_REC_EVAL4SYNC.name(),
            RecTopicEnum.KC_REC_TOPIC_PLAN.name(),
            RecTopicEnum.KC_REC_TOPIC.name(),
            RecTopicEnum.REC_TOPIC.name()

    );

    /**
     * json：
     * header:
     * parameter:
     * payload:
     *
     * @param apiRequest 请求参数
     * @return DispatchApiResponse 请求响应
     */
    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "能力运行")
    // @SkylineMetric(value = "40.多维图谱排序推荐", provider = SceneInfoMetricProvider.class)
    @PostMapping("/process")
    @LoggingCost
    public DispatchApiResponse process(@RequestBody DispatchApiRequest apiRequest) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("ApiRequest= {}", apiRequest);
        }
        String traceId = apiRequest.getTraceId();

        // SceneInfo sceneInfo = apiRequest.getScene(SceneInfo.class);
        SceneInfo sceneInfo = SceneInfoSelector.select(apiRequest);
        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }

        //引擎参数转换
        AbstractParamAdapter<?, ?> paramAdapter = adapterSelector.select(sceneInfo, apiRequest.getPayload());
        MultiLayerGraphRecommendRequest engineRequest = paramAdapter.adapt(traceId, sceneInfo, apiRequest.getPayload());
        if (log.isDebugEnabled()) {
            log.debug("EngineRequest= {}", engineRequest);
            log.debug("traceId={}; MultiLayerGraphRecommendRequest:Json= {}", traceId, JSON.toJSONString(engineRequest));
        }

        DispatchApiResponse response = new DispatchApiResponse();
        try {

            //查询禁用题列表
            if (SUPPORT_CHANGE_FUNCODE.contains(sceneInfo.getFunctionCode())) {
                List<String> forbiddenNodes = engineRequest.getForbiddenNodes();
                if (CollUtil.isNotEmpty(forbiddenNodes)) {
                    DataHub.getForbiddenRecordService().save(sceneInfo.getUserId(), sceneInfo.getCatalogCode(), forbiddenNodes);
                }
                List<StudyForbiddenRecordEntity> userIdAndCatalogId = DataHub.getForbiddenRecordService().findByUserIdAndCatalogId(sceneInfo.getUserId(), sceneInfo.getCatalogCode());
                if (CollUtil.isNotEmpty(userIdAndCatalogId)) {
                    Set<String> collect = userIdAndCatalogId.stream().map(StudyForbiddenRecordEntity::getNodeId).collect(Collectors.toSet());
                    engineRequest.setForbiddenNodes(new ArrayList<>(collect));
                }
            }

            //移除 场景信息
            if (apiRequest.getParameter().containsKey(DispatchApiRequest.SCENE_INFO_KEY)) {
                apiRequest.getParameter().remove(DispatchApiRequest.SCENE_INFO_KEY);
            }

            // 应学点业务使用，应学点业务已废弃，暂是注释调用
            // 备考场景，先触发一次画像
//            callSkylabDiagIfNecessary(traceId, sceneInfo, engineRequest.getInNodeIdList());

            MultiLayerGraphRecommendResponse engineResponse = sortService.sort(traceId, engineRequest, apiRequest.getParameter());
            if (engineResponse == null) {
                log.error("MultiLayerGraphRecommendResponse=null, traceId={}", traceId);
                throw new ParamNotExistException("MultiLayerGraphRecommendResponse");
            }
            if (log.isDebugEnabled()) {
                log.debug("EngineResponse= {}", engineResponse);
                log.debug("traceId={}; MultiLayerGraphRecommendResponse:Json= {}", traceId, JSON.toJSONString(engineResponse));
            }


            // 缓存推荐结果
            if (StrUtil.isNotBlank(engineResponse.getCacheKey())) {
                JSONObject engineResponseJson = (JSONObject) JSON.toJSON(engineResponse);
                DataHub.getStudyLogService().saveRecommendRecordCache(traceId, engineResponse.getCacheKey(), engineResponseJson);
            }

            // outNodeInfos 至少是空数据，避免是 null
            List<NodeInfo> outNodeInfos = Optional.ofNullable(engineResponse.getOutNodeInfos()).orElse(Lists.newArrayList());
            engineResponse.setOutNodeInfos(outNodeInfos);
            // RecommendInfo，避免是 null
            if (engineResponse.getRecommendInfo() == null) {
                engineResponse.setRecommendInfo(new RecommendInfo());
            }
            //推题场景，存储推荐记录
            studyLogBusinessService.storeRecommendLogs(traceId, sceneInfo, engineRequest, engineResponse);
            //根据引擎推荐结果，更新用户画像
            studyLogBusinessService.updateUserMasterInfo(traceId, sceneInfo, engineRequest.getInNodeIdList(), engineRequest.getInNodeChapterMap(), engineResponse);

            //设值响应数据
            response.setPayload(paramAdapter.adapt(engineRequest, engineResponse));
        } catch (EngineException ee) {
            log.error("Call SortEngine error code= {}; message= {}; failover={}", ee.getErrorCode(), ee.getMessage(), ee.getRedirect());

            ApiResponseHeader header = new ApiResponseHeader(ee.getErrorCode(), ee.getMessage());
            if (ee.getRedirect()) {
                header.setErrorNodes(Lists.newArrayList(new ErrorNode("failover", "true")));
            }
            response.setHeader(header);
        }
        response.setTraceId(traceId);
        return response;
    }


//    private void callSkylabDiagIfNecessary(String traceId, SceneInfo sceneInfo, List<String> catalogIds) throws SkylabException {
//        StudyCodeEnum studyCode = sceneInfo.getStudyCode();
//        if (log.isDebugEnabled()) {
//            log.debug("traceId:{}, studyCode:{}, funcCode:{}", traceId, studyCode, sceneInfo.getFunctionCode());
//        }
//
//        boolean exam = false;
//
//        if (studyCode == StudyCodeEnum.UNIT_REVIEW ||
//                studyCode == StudyCodeEnum.MID_EXAM ||
//                studyCode == StudyCodeEnum.FINAL_EXAM) {
//            // 备考场景
//            exam = true;
//
//        } else if (studyCode == StudyCodeEnum.SYNC_OS) {
//            if (HisStudyCodeEnum.UNIT_REVIEW == sceneInfo.getHisStudyCode() ||
//                    HisStudyCodeEnum.MID_EXAM == sceneInfo.getHisStudyCode() ||
//                    HisStudyCodeEnum.FINAL_EXAM == sceneInfo.getHisStudyCode()
//            ) {
//                // 精准学os下的 - 备考场景
//                exam = true;
//            }
//        }
//
//        if (exam) {
//            // 推点功能
//            if (RecNodeParam.FUNC_CODE.equals(sceneInfo.getFunctionCode())) {
//                log.debug("traceId={},触发备考考点推荐应学点计算", traceId);
//                //调用路由服务开始画像诊断
//                MasterFetch4CatalogParam param = new MasterFetch4CatalogParam();
//                param.setCatalogIds(catalogIds);
//                param.setLearnPointUpdate(true);
//
//                SceneInfo sceneInfoMasterFetch = JSON.parseObject(JSON.toJSONString(sceneInfo), SceneInfo.class);
//                sceneInfoMasterFetch.setFunctionCode(param.getFuncCode());
//
//                ApiRequest apiRequest = new ApiRequest();
//                apiRequest.setTraceId(traceId);
//                apiRequest.setObjectPayload(param);
//                apiRequest.setObjectScene(sceneInfoMasterFetch);
//
//                Stopwatch stopwatch = Stopwatch.createStarted();
//                log.info("traceId={}, extracted start", traceId);
//                if (log.isDebugEnabled()) {
//                    log.debug("ApiRequest= {}", apiRequest);
//                }
//                ApiResponse apiResponse = skylineRouterFeign.routeCall(apiRequest, TraceHeaderUtils.buildHeader(traceId, springApplicationName));
//                if (log.isDebugEnabled()) {
//                    log.debug("ApiResponse= {}", apiResponse);
//                }
//
//                log.info("traceId={},extracted cost: {} ms", traceId, stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
//
//                int respCode = Optional.ofNullable(apiResponse)
//                        .map(ApiResponseGeneric::getHeader)
//                        .map(ApiResponseHeader::getCode)
//                        .orElse(-999999);
//
//                if (respCode != 0) {
//                    throw new SkylabException(respCode, "备考场景推点前调用画像失败", apiResponse);
//                }
//            }
//        }
//    }
}
