package com.iflytek.skylab.core.contract.sdk.handler;

import com.iflytek.skylab.core.contract.enums.SkylabHttpPathEnums;
import com.iflytek.skylab.core.data.*;
import org.apache.http.impl.client.CloseableHttpClient;

public class SkylabPrimaryMigrationHandler extends AbstractSkylabRequestHandler {
    public SkylabPrimaryMigrationHandler(CloseableHttpClient httpClient, String protocol, String domain, String baseUrl) {
        super(httpClient, protocol, domain, baseUrl);
    }

    public SkylabResponse<FuncResult> behavior(SkylabRequest<PrimaryMigrationBehaviorParam> skylabRequest) {
        return executeRequest(skylabRequest, SkylabHttpPathEnums.PRIMARY_BEHAVIOR, FuncResult.class);
    }
    public SkylabResponse<FuncResult> mastery(SkylabRequest<PrimaryMigrationMasteryParam> skylabRequest) {
        return executeRequest(skylabRequest, SkylabHttpPathEnums.PRIMARY_MASTERY, FuncResult.class);
    }
} 