package com.iflytek.skylab.core.contract.service;

import com.iflytek.skylab.core.data.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/4/11 19:34
 * @description小学精准学数据迁移
 */
public interface SkylabPrimaryMigrationService {

    /**
     * 答题记录上报请求
     *
     * @param skylabRequest 小学精准学答题记录入参
     * @return SkylabResponse<FuncResult>
     */
    SkylabResponse<FuncResult> behavior(@Valid @NotNull SkylabRequest<PrimaryMigrationBehaviorParam> skylabRequest);


    /**
     * 章节掌握度记录上报请求
     *
     * @param skylabRequest 小学精准学章节掌握度入参
     * @return SkylabResponse<FuncResult>
     */
    SkylabResponse<FuncResult> mastery(@Valid @NotNull SkylabRequest<PrimaryMigrationMasteryParam> skylabRequest);
}
