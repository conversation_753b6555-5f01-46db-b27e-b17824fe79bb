package com.iflytek.skylab.core.contract.service;


import com.iflytek.skylab.core.data.ExtFuncParam;
import com.iflytek.skylab.core.data.ExtFuncResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 扩展请求 接口
 *
 * <AUTHOR>
 * @dubbo
 */
public interface SkylabExtService {

    /**
     * 扩展功能请求
     *
     * @param skylabRequest 场景、点诊断或推题 交互上下文、推荐信息
     * @return 推荐结果
     */
    SkylabResponse<ExtFuncResult> call(@Valid @NotNull SkylabRequest<ExtFuncParam> skylabRequest);
}
