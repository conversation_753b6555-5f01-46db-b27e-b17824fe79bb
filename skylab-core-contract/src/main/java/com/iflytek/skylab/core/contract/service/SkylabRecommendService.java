package com.iflytek.skylab.core.contract.service;


import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 推荐功能 对外统一接口
 * <p>
 * 包括：
 * <p>
 * 测评推题
 * 点排序
 * 点推题
 *
 * <AUTHOR>
 */
public interface SkylabRecommendService {

    /**
     * 推荐接口
     *
     * @param skylabRequest 场景、点诊断或推题 交互上下文、推荐信息
     * @param clazz 推荐结果具体clazz
     * @return 推荐结果
     */
    <T extends FuncResult> SkylabResponse<T> recommend(@Valid @NotNull SkylabRequest<? extends FuncParam> skylabRequest, @Valid @NotNull Class<T> clazz);
}
