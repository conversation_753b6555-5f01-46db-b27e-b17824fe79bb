package com.iflytek.skylab.core.contract.service;


import com.iflytek.skylab.core.data.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 非推荐相关的功能 服务接口
 * <p>
 * 如 获取同水平用户掌握度 similarUserMastery [float]
 *
 * <AUTHOR>
 */
public interface SkylabStandService {

    /**
     * 业务端获取通用特征数据
     *
     * @param skylabRequest 场景信息等
     * @return SkylabResponse<FeatureResult>
     */
    SkylabResponse<FeatureResult> featureFetch(@Valid @NotNull SkylabRequest<? extends FuncParam> skylabRequest);
}
