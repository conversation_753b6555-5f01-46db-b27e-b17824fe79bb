package com.iflytek.skylab.core.contract.sdk.handler;

import com.iflytek.skylab.core.contract.enums.SkylabHttpPathEnums;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.FeatureResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import org.apache.http.impl.client.CloseableHttpClient;

public class SkylabStandHandler extends AbstractSkylabRequestHandler {
    public SkylabStandHandler(CloseableHttpClient httpClient, String protocol, String domain, String baseUrl) {
        super(httpClient, protocol, domain, baseUrl);
    }

    public SkylabResponse<FeatureResult> featureFetch(SkylabRequest<? extends FuncParam> skylabRequest) {
        return executeRequest(skylabRequest, SkylabHttpPathEnums.FEATURE_FETCH, FeatureResult.class);
    }
} 