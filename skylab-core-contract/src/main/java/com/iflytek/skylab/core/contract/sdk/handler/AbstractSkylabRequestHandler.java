package com.iflytek.skylab.core.contract.sdk.handler;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iflytek.skylab.core.contract.enums.SkylabHttpHeaderEnums;
import com.iflytek.skylab.core.contract.enums.SkylabHttpPathEnums;
import com.iflytek.skylab.core.contract.exception.ServiceException;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;

import java.text.SimpleDateFormat;
import java.util.Objects;

public abstract class AbstractSkylabRequestHandler {
    public static final String PARAMS_EXCEPTION = "参数异常";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String APPLICATION_JSON = "application/json";

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String FORMAT = "%s://%s%s%s";

    static {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        OBJECT_MAPPER.setDateFormat(sdf);
    }
    protected final CloseableHttpClient httpClient;
    protected final String protocol;
    protected final String domain;
    protected final String baseUrl;

    public AbstractSkylabRequestHandler(CloseableHttpClient httpClient, String protocol, String domain, String baseUrl) {
        this.httpClient = httpClient;
        this.protocol = protocol;
        this.domain = domain;
        this.baseUrl = baseUrl;
    }

    protected String buildUrl(String path) {
        return String.format(FORMAT, protocol, domain, baseUrl, path);
    }

    protected <T extends FuncResult> SkylabResponse<T> executeRequest(SkylabRequest<? extends FuncParam> skylabRequest, SkylabHttpPathEnums pathEnums, Class<T> respClazz) {
        if (Objects.isNull(skylabRequest) || Objects.isNull(skylabRequest.getScene())
                || Objects.isNull(skylabRequest.getPayload()) || Objects.isNull(respClazz)) {
            throw new IllegalArgumentException(PARAMS_EXCEPTION);
        }
        HttpPost httpPost = new HttpPost(buildUrl(pathEnums.getPath()));
        httpPost.setHeader(CONTENT_TYPE, APPLICATION_JSON);
        httpPost.setHeader(SkylabHttpHeaderEnums.SCENE_TYPE.getName(), skylabRequest.getScene().getClass().getSimpleName());
        httpPost.setHeader(SkylabHttpHeaderEnums.FUNC_PARAM.getName(), skylabRequest.getPayload().getClass().getSimpleName());
        httpPost.setHeader(SkylabHttpHeaderEnums.FUNC_RESULT.getName(),respClazz.getSimpleName());

        try {
            String requestBody = OBJECT_MAPPER.writeValueAsString(skylabRequest);
            StringEntity entity = new StringEntity(requestBody, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String resp = EntityUtils.toString(responseEntity);
                JavaType type = OBJECT_MAPPER.getTypeFactory().constructParametricType(SkylabResponse.class, respClazz);
                return OBJECT_MAPPER.readValue(resp, type);
            }
        } catch (Exception e) {
            throw new ServiceException(-1, e.getMessage(), e);
        }
    }
} 