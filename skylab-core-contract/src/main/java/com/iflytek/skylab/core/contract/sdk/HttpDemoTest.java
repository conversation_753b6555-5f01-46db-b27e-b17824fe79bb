package com.iflytek.skylab.core.contract.sdk;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.contract.sdk.handler.SkylabBehaviorHandler;
import com.iflytek.skylab.core.contract.sdk.handler.SkylabRecommendHandler;
import com.iflytek.skylab.core.contract.sdk.handler.SkylabStandHandler;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.domain.SceneInfo;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/21 15:12
 */
public class HttpDemoTest {

    private final SkylabRecommendHandler skylabRecommendHandler;
    private final SkylabBehaviorHandler skylabBehaviorHandler;
    private final SkylabStandHandler skylabStandHandler;

    HttpDemoTest() {
        SkylabHttpSdkConfig sdkConfig = new SkylabHttpSdkConfig();
        // 1. sdk配置
        sdkConfig.setProtocol("http");
        sdkConfig.setDomain("172.30.8.25:32100");
//        sdkConfig.setBaseUrl("/skylab/api/v1/dialing-test");
        sdkConfig.setBaseUrl("/skylab/api/v2");
        sdkConfig.setMaxTotal(100);
        sdkConfig.setDefaultMaxPerRoute(100);
        sdkConfig.setConnectTimeout(5000);
        sdkConfig.setSocketTimeout(5000);
        sdkConfig.setConnectionRequestTimeout(5000);
        SkylabHandlerFactory skylabHandlerFactory = new SkylabHandlerFactory(sdkConfig);
        this.skylabRecommendHandler = skylabHandlerFactory.getSkylabRecommendHandler();
        this.skylabBehaviorHandler = skylabHandlerFactory.getSkylabBehaviorHandler();
        this.skylabStandHandler = skylabHandlerFactory.getSkylabStandHandler();

    }

    public static void main(String[] args) {
        HttpDemoTest httpDemoTest = new HttpDemoTest();

        //查询答题记录
        httpDemoTest.queryAnswerRecord();

        //特征
        httpDemoTest.featureFetch();
        httpDemoTest.featureFetch2();

        //点推题
        httpDemoTest.recTopic();
    }

    public void featureFetch() {
        SkylabRequest<AcquireFeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("zx182-7");
        //教材
        sceneInfo.setPressCode("01");
        sceneInfo.setBookCode("01_08020101-002");
        sceneInfo.setCatalogCode("01_08020101-002_06_003");
        sceneInfo.setLayerVersion("SYNC_LEARN_UPDATE");
        skylabRequest.setScene(sceneInfo);

        AcquireFeatureParam acquireFeatureParam = new AcquireFeatureParam();

        acquireFeatureParam.setFeatureNames(Arrays.asList("catalog_predict_answer_time"));
        acquireFeatureParam.setParams(Arrays.asList("01_08020101-002_06_003"));

        skylabRequest.setPayload(acquireFeatureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandHandler.featureFetch(skylabRequest);
        System.out.println("featureFetch = " + skylabResponse.toJson());
    }

    public void featureFetch2() {
        SkylabRequest<FeatureParam> skylabRequest = new SkylabRequest<>();
        String traceId = UUID.randomUUID().toString();
        skylabRequest.setTraceId(traceId);

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setAreaCode("000000");
        sceneInfo.setBizAction(BizActionEnum.SYNC_EVAL);
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("xiangzhang182-21");

        skylabRequest.setScene(sceneInfo);

        FeatureParam featureParam = new FeatureParam();

        FeatureParam.FeatureParamItem featureParamItem = new FeatureParam.FeatureParamItem();

        featureParamItem.setFeatureName("anchorpoint_graph_layer");

        JSONArray objects = JSON.parseArray(("[{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"253847b3-6d4d-4660-8acc-0ac56afb574d\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"253847b3-6d4d-4660-8acc-0ac56afb574d\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"ae70514d-c78c-4cc2-b80b-aa679f8dac20\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"ae70514d-c78c-4cc2-b80b-aa679f8dac20\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"e034f57b-11b5-4073-92d0-c7f6113f5c58\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"e034f57b-11b5-4073-92d0-c7f6113f5c58\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"195f7487-8d99-4c6f-aa53-c1aaf150ab38\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"195f7487-8d99-4c6f-aa53-c1aaf150ab38\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"076378be-3b3a-4fa6-86c4-ba4f3451f2b5\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"076378be-3b3a-4fa6-86c4-ba4f3451f2b5\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"8a8be044-6743-49fb-858b-4f7de41780d6\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"8a8be044-6743-49fb-858b-4f7de41780d6\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"fc377c5f-848e-494f-b32b-df01a18e6593\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"fc377c5f-848e-494f-b32b-df01a18e6593\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"ed2250f2-5ffd-436b-980a-e37559dc1857\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"ed2250f2-5ffd-436b-980a-e37559dc1857\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"7f4f2863-abc1-4d2d-a337-d973a38c74fa\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"7f4f2863-abc1-4d2d-a337-d973a38c74fa\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"b4ece79c-131b-4d84-ba3d-67650a1c48bc\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"b4ece79c-131b-4d84-ba3d-67650a1c48bc\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"dd125b82-aa27-4bd3-8de4-fc7a71b6238d\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"dd125b82-aa27-4bd3-8de4-fc7a71b6238d\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"eabfb891-931a-45da-bc7a-ad4f385bc1de\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"eabfb891-931a-45da-bc7a-ad4f385bc1de\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"ef92862a-7f52-47d6-b653-f6d8efa07ff4\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"ef92862a-7f52-47d6-b653-f6d8efa07ff4\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"5560f240-ae3e-47ef-8d39-b6484c4d20f4\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"5560f240-ae3e-47ef-8d39-b6484c4d20f4\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"4fe9d4bb-587e-4613-8b0e-73d4daef9929\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"4fe9d4bb-587e-4613-8b0e-73d4daef9929\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"95a91f47-73f2-4cc2-a704-4f511af697f6\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"95a91f47-73f2-4cc2-a704-4f511af697f6\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"bc8196d9-424b-4556-a071-5505d613c676\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"bc8196d9-424b-4556-a071-5505d613c676\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"51e4fc84-4770-4fee-a4f1-2cf7cfd9e361\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"51e4fc84-4770-4fee-a4f1-2cf7cfd9e361\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"2364f351-fe5f-4813-a149-7ac62ab43151\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"2364f351-fe5f-4813-a149-7ac62ab43151\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"7304793b-646f-4734-b058-e33fb43f5ac5\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"7304793b-646f-4734-b058-e33fb43f5ac5\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"9e1741ab-9d3f-46cd-b32a-7b91e3bf8e4a\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"9e1741ab-9d3f-46cd-b32a-7b91e3bf8e4a\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"fc51ef64-41ba-4b5b-ae49-87e3f9146be5\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"fc51ef64-41ba-4b5b-ae49-87e3f9146be5\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"9c8b5e9f-f99f-4e42-a818-ef2b8b9e25af\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"9c8b5e9f-f99f-4e42-a818-ef2b8b9e25af\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"50cec173-3202-460c-bd8f-bd5917f7806f\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"50cec173-3202-460c-bd8f-bd5917f7806f\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"a750e90f-8557-492e-91ee-4a808f9981a9\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"a750e90f-8557-492e-91ee-4a808f9981a9\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"faa29fed-6d8c-4a45-aee7-e00939f7a292\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"faa29fed-6d8c-4a45-aee7-e00939f7a292\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"1bfbcf17-f593-4923-bccd-95829b4c616f\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"1bfbcf17-f593-4923-bccd-95829b4c616f\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"58d6671e-757a-4a50-83c1-d0beaa1a2e47\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"58d6671e-757a-4a50-83c1-d0beaa1a2e47\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"c5946ebb-3625-4354-afb8-17c0452de1ad\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"c5946ebb-3625-4354-afb8-17c0452de1ad\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"348b74a4-b77b-4c61-8f97-e0d1152a8715\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"348b74a4-b77b-4c61-8f97-e0d1152a8715\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"8bddbc1d-72e4-45da-959b-715748e888ed\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"8bddbc1d-72e4-45da-959b-715748e888ed\",\"study_code\":\"SYNC_OS\"},{\"subject_code\":\"02\",\"phase_code\":\"04\",\"biz_code\":\"ZSY_XXJ\",\"area_code\":\"000000\",\"item_identify_key\":\"9c5231fa-439a-47cf-a2d7-b39a98172072\",\"catalog_code\":\"272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period20\",\"anchorpoint_code\":\"9c5231fa-439a-47cf-a2d7-b39a98172072\",\"study_code\":\"SYNC_OS\"}]"));

        List<Map<String, String>> list = JSON.parseObject(objects.toString(), new TypeReference<List<Map<String, String>>>() {
        });
        featureParamItem.setParams(list);

        featureParam.setItems(Arrays.asList(featureParamItem));
        featureParam.setSimpleMode(false);
        skylabRequest.setPayload(featureParam);

        SkylabResponse<FeatureResult> skylabResponse = skylabStandHandler.featureFetch(skylabRequest);
        System.out.println("featureFetch2 = " + skylabResponse.toJson());
    }

    public void queryAnswerRecord() {
        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setAreaCode("420000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("test2025b3b93b41-0215-497a-b1d2-9c004c501216");
        //教材
        sceneInfo.setCatalogCode("01_07020101-001_02_005_period20");
        sceneInfo.setBookCode("01_07020101-001");
        sceneInfo.setPressCode("01");

        // 4. 推荐、诊断等调用
        // 4.1 recommend
        SkylabRequest<BehaviorQueryParam> skylabRequest = new SkylabRequest<>();
        skylabRequest.setTraceId(UUID.randomUUID().toString());
        skylabRequest.setScene(sceneInfo);
        BehaviorQueryParam behaviorQueryParam = new BehaviorQueryParam();
        behaviorQueryParam.setMaxNum(10);
        NodeInfo nodeInfo = new NodeInfo();
        nodeInfo.setNodeId("c8d3d7b9-7472-439e-b262-73cdfba04fd9");
        nodeInfo.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        nodeInfo.setCatalogId("01_07020101-001_02_005_period20");

        behaviorQueryParam.setNodeInfos(Arrays.asList(nodeInfo));
        skylabRequest.setPayload(behaviorQueryParam);
        System.out.println(skylabRequest);
        SkylabResponse<BatchStudyLogQueryResult> recommend = skylabBehaviorHandler.queryAnswerRecord(skylabRequest);
        System.out.println("queryAnswerRecord = " + recommend.toJson());
    }

    public void recTopic() {

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setBizCode(BizCodeEnum.ZSY_XXJ);
        sceneInfo.setStudyCode(StudyCodeEnum.SYNC_OS);
        sceneInfo.setHisStudyCode(HisStudyCodeEnum.SYNC_LEARN);
        sceneInfo.setBizAction(BizActionEnum.SYNC_REC);
        sceneInfo.setAreaCode("420000");

        sceneInfo.setGradeCode("07");
        //学科
        sceneInfo.setSubjectCode("02");
        //学段
        sceneInfo.setPhaseCode("04");
        //用户id
        sceneInfo.setUserId("test123");
        //教材
        sceneInfo.setCatalogCode("272_07020101272-6314_07020101272-6314-188912_07020101272-6314-188915_period10");
        sceneInfo.setBookCode("272_07020101272-6314");
        sceneInfo.setPhaseCode("272");

        // 4. 推荐、诊断等调用
        // 4.1 recommend
        SkylabRequest<RecTopicParam> skylabRequest = new SkylabRequest<>();
        skylabRequest.setTraceId(UUID.randomUUID().toString());
        skylabRequest.setScene(sceneInfo);

        RecTopicParam recTopicParam = new RecTopicParam();
        recTopicParam.setNodeId("ae70514d-c78c-4cc2-b80b-aa679f8dac20");
        recTopicParam.setTopicOrderNumber(1);
        recTopicParam.setRecTopicEnum(RecTopicEnum.REC_TOPIC);
        skylabRequest.setPayload(recTopicParam);
        System.out.println(skylabRequest.toJson());
        SkylabResponse<RecTopicResult> recommend = skylabRecommendHandler.recommend(skylabRequest, RecTopicResult.class);
        System.out.println("recTopic = " + recommend.toJson());

    }
}
