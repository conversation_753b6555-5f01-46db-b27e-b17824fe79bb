package com.iflytek.skylab.core.contract.sdk;

/**
 * skylab-HTTP-SDK配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 13:52
 */
public class SkylabHttpSdkConfig {
    /**
     * http协议
     */
    private String protocol = "http";
    /**
     * http域名
     */
    private String domain = "localhost:8181";
    /**
     * http基础路径
     */
    private String baseUrl = "/skylab/api/v2";
    /**
     * 连接池最大连接数
     */
    private int maxTotal = 500;
    /**
     * 每个路由默认最大连接数
     */
    private int defaultMaxPerRoute = 100;
    /**
     * 连接超时时间
     */
    private int connectTimeout = 6000;
    /**
     * socket超时时间
     */
    private int socketTimeout = 30000;
    /**
     * 从连接池获取连接的超时时间
     */
    private int connectionRequestTimeout = 1000;

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public int getMaxTotal() {
        return maxTotal;
    }

    public void setMaxTotal(int maxTotal) {
        this.maxTotal = maxTotal;
    }

    public int getDefaultMaxPerRoute() {
        return defaultMaxPerRoute;
    }

    public void setDefaultMaxPerRoute(int defaultMaxPerRoute) {
        this.defaultMaxPerRoute = defaultMaxPerRoute;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getSocketTimeout() {
        return socketTimeout;
    }

    public void setSocketTimeout(int socketTimeout) {
        this.socketTimeout = socketTimeout;
    }

    public int getConnectionRequestTimeout() {
        return connectionRequestTimeout;
    }

    public void setConnectionRequestTimeout(int connectionRequestTimeout) {
        this.connectionRequestTimeout = connectionRequestTimeout;
    }

    @Override
    public String toString() {
        return "SdkConfig{" +
                "protocol='" + protocol + '\'' +
                ", domain='" + domain + '\'' +
                ", baseUrl='" + baseUrl + '\'' +
                ", maxTotal=" + maxTotal +
                ", defaultMaxPerRoute=" + defaultMaxPerRoute +
                ", connectTimeout=" + connectTimeout +
                ", socketTimeout=" + socketTimeout +
                ", connectionRequestTimeout=" + connectionRequestTimeout +
                '}';
    }


}