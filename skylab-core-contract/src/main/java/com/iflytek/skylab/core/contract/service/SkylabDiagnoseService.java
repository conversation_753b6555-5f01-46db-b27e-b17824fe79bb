package com.iflytek.skylab.core.contract.service;


import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 诊断功能 对外统一接口
 * <p>
 * 包括：
 * <p>
 * 画像获取
 *
 * <AUTHOR>
 */
public interface SkylabDiagnoseService {
    /**
     * 诊断接口
     *
     * @param skylabRequest 场景、点诊断或推题 交互上下文、推荐信息
     * @return SkylabResponse<MasterFetchResult>
     */
    SkylabResponse<MasterFetchResult> diagnose(@Valid @NotNull SkylabRequest<? extends FuncParam> skylabRequest);
}
