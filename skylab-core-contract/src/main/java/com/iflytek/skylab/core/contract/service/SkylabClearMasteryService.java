package com.iflytek.skylab.core.contract.service;


import com.iflytek.skylab.core.data.ClearMasteryParam;
import com.iflytek.skylab.core.data.ClearMasteryResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 清空画像业务接口
 *
 * <AUTHOR>
 */
public interface SkylabClearMasteryService {

    /**
     * 清空画像业务请求
     *
     * @param skylabRequest 场景信息等
     * @return SkylabResponse<FuncResult>
     */
    SkylabResponse<ClearMasteryResult> clearMastery(@Valid @NotNull SkylabRequest<ClearMasteryParam> skylabRequest);
}
