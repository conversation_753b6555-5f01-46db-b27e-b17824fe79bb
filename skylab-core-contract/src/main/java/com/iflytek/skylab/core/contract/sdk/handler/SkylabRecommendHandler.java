package com.iflytek.skylab.core.contract.sdk.handler;

import com.iflytek.skylab.core.contract.enums.SkylabHttpPathEnums;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import org.apache.http.impl.client.CloseableHttpClient;

public class SkylabRecommendHandler extends AbstractSkylabRequestHandler {
    public SkylabRecommendHandler(CloseableHttpClient httpClient, String protocol, String domain, String baseUrl) {
        super(httpClient, protocol, domain, baseUrl);
    }

    public <T extends FuncResult> SkylabResponse<T> recommend(SkylabRequest<? extends FuncParam> skylabRequest, Class<T> respClazz) {
        return executeRequest(skylabRequest, SkylabHttpPathEnums.RECOMMEND, respClazz);
    }
} 