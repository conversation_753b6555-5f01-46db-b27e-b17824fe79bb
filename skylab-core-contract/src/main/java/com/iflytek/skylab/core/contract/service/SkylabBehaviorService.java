package com.iflytek.skylab.core.contract.service;


import com.iflytek.skylab.core.data.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 学习行为收集 接口
 *
 * <AUTHOR>
 */
public interface SkylabBehaviorService {

    /**
     * 汇报 答题记录
     * @param skylabRequest 场景信息等
     * @return SkylabResponse<StudyLogResult>
     */
    SkylabResponse<StudyLogResult> reportAnswerRecord(@Valid @NotNull SkylabRequest<? extends StudyLogParam> skylabRequest);

    /**
     * 查重 用户在一批锚点下作答记录
     * @param skylabRequest
     * @return
     */
    SkylabResponse<BatchStudyLogQueryResult> queryAnswerRecord(@Valid @NotNull SkylabRequest<? extends BehaviorQueryParam> skylabRequest);
}
