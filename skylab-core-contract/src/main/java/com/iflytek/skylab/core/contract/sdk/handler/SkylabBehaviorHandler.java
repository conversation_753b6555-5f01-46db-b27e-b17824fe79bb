package com.iflytek.skylab.core.contract.sdk.handler;

import com.iflytek.skylab.core.contract.enums.SkylabHttpPathEnums;
import com.iflytek.skylab.core.data.BehaviorQueryParam;
import com.iflytek.skylab.core.data.BatchStudyLogQueryResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.data.StudyLogParam;
import com.iflytek.skylab.core.data.StudyLogResult;
import org.apache.http.impl.client.CloseableHttpClient;

public class Skylab<PERSON>ehavior<PERSON>and<PERSON> extends AbstractSkylabRequestHandler {
    public SkylabBehaviorHandler(CloseableHttpClient httpClient, String protocol, String domain, String baseUrl) {
        super(httpClient, protocol, domain, baseUrl);
    }

    public SkylabResponse<StudyLogResult> reportAnswerRecord(SkylabRequest<? extends StudyLogParam> skylabRequest) {
        return executeRequest(skylabRequest, SkylabHttpPathEnums.REPORT_ANSWER_RECORD, StudyLogResult.class);
    }

    public SkylabResponse<BatchStudyLogQueryResult> queryAnswerRecord(SkylabRequest<? extends BehaviorQueryParam> skylabRequest) {
        return executeRequest(skylabRequest, SkylabHttpPathEnums.QUERY_ANSWER_RECORD, BatchStudyLogQueryResult.class);
    }
} 