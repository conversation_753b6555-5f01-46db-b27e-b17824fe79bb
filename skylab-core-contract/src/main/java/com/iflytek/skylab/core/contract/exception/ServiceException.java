package com.iflytek.skylab.core.contract.exception;

/**
 * ServiceException
 *
 * <AUTHOR>
 */
public class ServiceException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private final int code;

    public ServiceException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public ServiceException(int code, String message, Object... args) {
        this(code, String.format(message, args));
    }

    public ServiceException(int code, String message) {
        super(message);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
