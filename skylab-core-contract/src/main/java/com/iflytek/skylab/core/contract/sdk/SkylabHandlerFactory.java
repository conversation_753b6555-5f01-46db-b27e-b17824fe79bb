package com.iflytek.skylab.core.contract.sdk;

// 导入各类处理器以及 HTTP 客户端相关类

import com.iflytek.skylab.core.contract.sdk.handler.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

/**
 * SkylabHandlerFactory 类用于创建各类 Skylab SDK 的 HTTP 请求处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 13:54
 */
public class SkylabHandlerFactory {
    // HTTP 客户端实例，用于发送 HTTP 请求
    private final CloseableHttpClient httpClient;
    // 协议（如 HTTP 或 HTTPS）
    private final String protocol;
    // 域名或 IP 地址
    private final String domain;
    // 基础 URL，用于拼接请求路径
    private final String baseUrl;

    /**
     * 构造函数，初始化 HTTP 客户端和基础配置
     *
     * @param sdkConfig SDK 配置对象，包含连接和请求相关参数
     */
    public SkylabHandlerFactory(SkylabHttpSdkConfig sdkConfig) {
        // 初始化协议、域名和基础 URL
        this.protocol = sdkConfig.getProtocol();
        this.domain = sdkConfig.getDomain();
        this.baseUrl = sdkConfig.getBaseUrl();

        // 创建一个连接池管理器，并设置最大连接数和每个路由的最大连接数
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
        // 设置整个连接池的最大连接数
        connManager.setMaxTotal(sdkConfig.getMaxTotal());
        // 设置每个路由（即每个目标主机）的最大连接数
        connManager.setDefaultMaxPerRoute(sdkConfig.getDefaultMaxPerRoute());

        // 创建请求配置，并设置连接、Socket 和请求超时时间
        RequestConfig requestConfig = RequestConfig.custom()
                // 设置连接超时时间（建立连接的最大等待时间）
                .setConnectTimeout(sdkConfig.getConnectTimeout())
                // 设置 Socket 超时时间（数据传输的最大等待时间）
                .setSocketTimeout(sdkConfig.getSocketTimeout())
                // 设置从连接池获取连接的最大等待时间
                .setConnectionRequestTimeout(sdkConfig.getConnectionRequestTimeout())
                .build();


        // 构建 HTTP 客户端实例
        httpClient = HttpClients.custom()
                .setConnectionManager(connManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
    }

    /**
     * 获取推荐处理器实例
     */
    public SkylabRecommendHandler getSkylabRecommendHandler() {
        return new SkylabRecommendHandler(httpClient, protocol, domain, baseUrl);
    }

    /**
     * 获取诊断处理器实例
     */
    public SkylabDiagnoseHandler getSkylabDiagnoseHandler() {
        return new SkylabDiagnoseHandler(httpClient, protocol, domain, baseUrl);
    }

    /**
     * 获取行为处理器实例
     */
    public SkylabBehaviorHandler getSkylabBehaviorHandler() {
        return new SkylabBehaviorHandler(httpClient, protocol, domain, baseUrl);
    }

    /**
     * 获取特征值处理器实例
     */
    public SkylabStandHandler getSkylabStandHandler() {
        return new SkylabStandHandler(httpClient, protocol, domain, baseUrl);
    }

    /**
     * 获取清空掌握度处理器实例
     */
    public SkylabClearMasteryHandler getSkylabClearMasteryHandler() {
        return new SkylabClearMasteryHandler(httpClient, protocol, domain, baseUrl);
    }

    /**
     * 获取大数据迁移处理器实例
     */
    public SkylabPrimaryMigrationHandler getSkylabPrimaryBehaviorHandler() {
        return new SkylabPrimaryMigrationHandler(httpClient, protocol, domain, baseUrl);
    }
}
