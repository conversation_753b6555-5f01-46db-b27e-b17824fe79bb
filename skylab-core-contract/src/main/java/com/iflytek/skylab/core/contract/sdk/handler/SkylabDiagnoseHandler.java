package com.iflytek.skylab.core.contract.sdk.handler;

import com.iflytek.skylab.core.contract.enums.SkylabHttpPathEnums;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import com.iflytek.skylab.core.data.MasterFetchResult;
import org.apache.http.impl.client.CloseableHttpClient;

public class SkylabDiagnoseHandler extends AbstractSkylabRequestHandler {
    public SkylabDiagnoseHandler(CloseableHttpClient httpClient, String protocol, String domain, String baseUrl) {
        super(httpClient, protocol, domain, baseUrl);
    }

    public SkylabResponse<MasterFetchResult> diagnose(SkylabRequest<? extends FuncParam> skylabRequest) {
        return executeRequest(skylabRequest, SkylabHttpPathEnums.DIAGNOSE, MasterFetchResult.class);
    }
} 