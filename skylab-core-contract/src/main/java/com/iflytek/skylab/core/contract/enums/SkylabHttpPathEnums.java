package com.iflytek.skylab.core.contract.enums;

import lombok.Getter;

@Getter
public enum SkylabHttpPathEnums {
    RECOMMEND("/recommend","推题接口"),
    DIAGNOSE("/diagnose","诊断接口"),
    REPORT_ANSWER_RECORD("/reportAnswerRecord","汇报 答题记录"),
    QUERY_ANSWER_RECORD("/queryAnswerRecord","答题记录查询"),
    FEATURE_FETCH("/featureFetch","业务端获取通用特征数据"),
    CLEAR_MASTERY("/clearMastery","清空画像业务请求"),

    PRIMARY_BEHAVIOR("/primary/behavior","小学精准学答题记录上报"),
    PRIMARY_MASTERY("/primary/mastery","小学精准学章节掌握度上报"),
    ;


    private final String path;
    private final String desc;

    SkylabHttpPathEnums(String path, String desc) {
        this.path = path;
        this.desc = desc;
    }
}
