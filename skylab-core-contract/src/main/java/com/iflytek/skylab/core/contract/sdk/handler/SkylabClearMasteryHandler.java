package com.iflytek.skylab.core.contract.sdk.handler;

import com.iflytek.skylab.core.contract.enums.SkylabHttpPathEnums;
import com.iflytek.skylab.core.data.ClearMasteryParam;
import com.iflytek.skylab.core.data.ClearMasteryResult;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.data.SkylabResponse;
import org.apache.http.impl.client.CloseableHttpClient;

public class Skylab<PERSON>learMasteryHandler extends AbstractSkylabRequestHandler {
    public SkylabClearMasteryHandler(CloseableHttpClient httpClient, String protocol, String domain, String baseUrl) {
        super(httpClient, protocol, domain, baseUrl);
    }

    public SkylabResponse<ClearMasteryResult> clearMastery(SkylabRequest<ClearMasteryParam> skylabRequest) {
        return executeRequest(skylabRequest, SkylabHttpPathEnums.CLEAR_MASTERY, ClearMasteryResult.class);
    }
} 