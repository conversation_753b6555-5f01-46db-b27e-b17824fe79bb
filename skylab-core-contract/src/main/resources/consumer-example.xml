<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans.xsd
     http://code.alibabatech.com/schema/dubbo
     http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:application name="skylab-dubbo-consumer"/>

    <dubbo:provider timeout="${dubbo.timeout}"/>

    <dubbo:registry id="app" protocol="zookeeper" address="${skylab.zookeeper.address}"
                    file="skylab-front-dubbo.cache"/>
    <dubbo:protocol name="dubbo" port="${dubbo.port}"/>

    <dubbo:reference registry="app" check="false" id="SkylabRecommendServiceConsumer"
                     interface="com.iflytek.skylab.core.contract.service.SkylabRecommendService"
                     timeout="${dubbo.timeout}"/>
    <dubbo:reference registry="app" check="false" id="SkylabBehaviorServiceConsumer"
                     interface="com.iflytek.skylab.core.contract.service.SkylabBehaviorService"
                     timeout="${dubbo.timeout}"/>
    <dubbo:reference registry="app" check="false" id="SkylabStandServiceConsumer"
                     interface="com.iflytek.skylab.core.contract.service.SkylabStandService"
                     timeout="${dubbo.timeout}"/>
</beans>