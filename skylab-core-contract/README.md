# skylab-core-contract

学习机推荐平台 对外接口契约

## 主要功能

- 画像诊断

    包含多场景的诊断功能。

- 推荐功能

    包含测评推题、点搜索(薄弱点搜索、复习点推荐)、点下推题功能。

- 学习行为收集

    上传学生答题记录。

- 通用特征查询

    支持查询业务端需要的通用特征，比如同水平掌握度等。


## 使用指南

POM依赖
```xml
    <repositories>
        <repository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <layout>default</layout>
        </repository>
    </repositories>

    <dependency>
        <groupId>com.iflytek.skylab</groupId>
        <artifactId>skylab-core-contract</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </dependency>
```

### 主要接口

*dubbo接口在com.iflytek.skylab.core.contract.service包下*

#### 1、诊断功能 对外统一接口（获取画像）

```java
SkylabResponse<MasterFetchResult> diagnose(@Valid @NotNull SkylabRequest<? extends FuncParam> skylabRequest);
```

#### 2、推荐功能 对外统一接口，包含测评推题、点搜索、点推题功能。

```java
<T extends FuncResult> SkylabResponse<T> recommend(@Valid @NotNull SkylabRequest<? extends FuncParam> skylabRequest, @Valid @NotNull Class<T> clazz);
```

#### 3、学习行为收集接口

```java
SkylabResponse<StudyLogResult> reportAnswerRecord(@Valid @NotNull SkylabRequest<StudyLogParam> skylabRequest);
```

#### 4、通用特征查询接口

```java
SkylabResponse<FeatureResult> featureFetch(@Valid @NotNull SkylabRequest<FeatureParam> skylabRequest);
```

### 请求方式

​	dubbo



### 业务流程示意

推荐业务主线流程（AI同步学业务场景）

![AI同步学业务场景](https://git.iflytek.com/Y_RDG-TURING/ai-edu/skylab-sln/skylab-platform/-/raw/dev_1.x/doc/img/tongbuxue.png?inline=false)

推荐业务主线流程（单元备考业务场景）

![单元备考业务场景](https://git.iflytek.com/Y_RDG-TURING/ai-edu/skylab-sln/skylab-platform/-/raw/dev_1.x/doc/img/dybk.png?inline=false)

推荐业务主线流程（阶段备考业务场景）

![阶段备考业务场景](https://git.iflytek.com/Y_RDG-TURING/ai-edu/skylab-sln/skylab-platform/-/raw/dev_1.x/doc/img/jdbk.png?inline=false)

推荐业务主线流程（中考复习业务场景）

![中考复习业务场景](https://git.iflytek.com/Y_RDG-TURING/ai-edu/skylab-sln/skylab-platform/-/raw/dev_1.x/doc/img/zkfx.png?inline=false)

#### 公共参数对象说明

1、公共输入参数对象：SkylabRequest（com.iflytek.skylab.core.data.SkylabRequest）

对象定义如下所示，泛型T是具体的接口输入参数对象。

```java
public class SkylabRequest<T extends FuncParam> extends Jsonable implements Serializable
```

|序号  | 字段名称 | 必须 | 类型                | 说明             |
|:-:  | :--------: | :-: | ------------------- | ---------------- |
|   1  | traceId  | Y    | String              | 追踪Id           |
|   2  | scene    | Y    | SceneInfo           | 场景信息         |
|   3  | payload  | Y    | T extends FuncParam | 具体接口输入对象 |

2、公共输出参数对象：SkylabResponse（com.iflytek.skylab.core.data.SkylabResponse）

对象定义如下所示，泛型T是具体的接口输出参数对象。

```java
public class SkylabResponse<T extends FuncResult> extends Jsonable implements Serializable
```

|序号 | 字段名称   | 类型                 | 说明                               |
|:-:  | :--------: | -------------------- | ---------------------------------- |
|   1 | traceId    | String               | 追踪Id                             |
|   2 | code       | int                  | 返回码                             |
|   3 | message    | String               | 返回信息                           |
|   4 | payload    | T extends FuncResult | 引擎输出结果                       |
|   *5* | *context*    | *JSONObject*           | *推荐上下文,业务不用关心*           |
|   *6* | *errorNodes* | *JSONArray*            | *调用异常节点列表（业务侧不用关心）* |

3、公共场景信息：SceneInfo（com.iflytek.skylab.core.domain.SceneInfo）

```java
public class SceneInfo extends Jsonable implements Serializable
```

| 字段名称     | 必须 | 类型              | 说明                                                         |
| ------------ | ---- | ----------------- | ------------------------------------------------------------ |
| userId       | Y    | String            | 用户Id                                                       |
| studyCode    | Y    | StudyCodeEnum枚举 | 学习场景 <br/>根据业务需求传入场景枚举<br/>例如同步学设值：StudyCodeEnum.SYNC_LEARN<br>AI诊断AI_DIAG、单元复习UNIT_REVIEW、期中备考MID_EXAM<br>期末备考FINAL_EXAM、中考一轮SENIOR_EXAM_1、中考二轮SENIOR_EXAM_2 |
| bizAction    | Y    | BizActionEnum枚举 | 业务功能<br>同步场景-测评同步场景-推荐提升<br>同步场景-针对学<br>。。。 |
| bizCode      | Y    | BizCodeEnum枚举   | 应用业务方代码   <br/>学习机：BizCodeEnum.ZSY_XXJ<br/>知学宝：BizCodeEnum.ZSY_ZXB<br/>内部测试：BizCodeEnum.ADAPTIVE_TEST<br/>中学作业：BizCodeEnum.ZSY_XKT<br/>全场景BYOD：BizCodeEnum.ZSY_BYOD <br/>课后服务：BizCodeEnum.ZSY_KHSDB |
| subjectCode  | Y    | String            | 学科编码                                                     |
| phaseCode    | Y    | String            | 学段编码                                                     |
| gradeCode    | Y    | String            | 年级编码                                                     |
| areaCode     | N    | String            | 区域编码，6位地址码，省/市<br>必传（老系统内部兼容，设置默认值） |
| graphVersion | N    | String            | 图谱版本                                                     |
| schoolId     | N    | String            | 学校Id(B端传入)                                              |
| classId      | N    | String            | 班级Id(B端传入)                                              |
| catalogCode  | N    | String            | 教材_书_章_节。（同步学场景需传入）                          |
| pressCode    | N    | String            | 教材版本编码（同步学、备考场景 有该数据就传入）              |
| bookCode     | N    | String            | 教材册别  教材书本 code（同步学、备考场景 有该数据就传入）   |
| layerType    | N    | String            | 学生需求 分层类型，学习机调用特征接口获得后传入（同步学场景使用）<br>conventional：普通<br>highScoreAdvanced：进阶<br>thinkingExpansion：思维拓展 |
| devVersion   | N    | String            | 终端版本,如：T10                                             |
| deviceId     | N    | String            | 设备标识                                                     |

业务功能[bizAction]与学习场景[studyCode] 映射表

| 学习场景studyCode                                            | 业务功能bizAction                                            | 功能编码funcCode |
| ------------------------------------------------------------ | ------------------------------------------------------------ | ---------------- |
| 同步学StudyCodeEnum.SYNC_LEARN                               | 精准找弱项：BizActionEnum.SYNC_EVAL<br>拍照测评：BizActionEnum.EVALUATION_BY_PHOTO_RESULT | REC_EVAL4IN      |
| 同步学                                                       | 高亮薄弱锚点：BizActionEnum.SYNC_SEARCH_WEAK                 | REC_NODE         |
| 同步学                                                       | 消除弱项：BizActionEnum.SYNC_REC<br>针对学：BizActionEnum.SYNC_AIM | REC_TOPIC        |
| 单元复习StudyCodeEnum.UNIT_REVIEW                            | 基础巩固：BizActionEnum.EXAM_UNIT_BASIS                      | REC_EVAL4IN      |
| 单元复习                                                     | 单元模拟：BizActionEnum.EXAM_UNIT_SIMULATION                 | REC_EVAL4OUT     |
| 单元复习                                                     | 高亮薄弱考点：BizActionEnum.EXAM_UNIT_SEARCH_WEAK            | REC_NODE         |
| 单元复习                                                     | 考点提升：BizActionEnum.EXAM_UNIT_REC<br>针对学：BizActionEnum.EXAM_UNIT_AIM | REC_TOPIC        |
| 期中备考、期末备考<br>StudyCodeEnum.MID_EXAM<br>/StudyCodeEnum.FINAL_EXAM | 知识回顾：BizActionEnum.EXAM_STAGE_REVIEW                    | REC_EVAL4IN      |
| 期中备考、期末备考                                           | 考前过招：BizActionEnum.EXAM_STAGE_DEALING                   | REC_EVAL4OUT     |
| 期中备考、期末备考                                           | 高亮薄弱考点：BizActionEnum.EXAM_STAGE_SEARCH_WEAK           | REC_NODE         |
| 期中备考、期末备考                                           | 考前突破：BizActionEnum.EXAM_STAGE_BREAK_THROUGH             | REC_TOPIC        |
| 中考复习-一轮                                                | 模块水平测试：BizActionEnum.FIRST_REVISE_EVAL                | REC_EVAL4IN      |
| 中考复习-一轮                                                | 模块复习点排序：BizActionEnum.FIRST_REVISE_SORT              | REC_NODE         |
| 中考复习-一轮                                                | 复习点习题练习：BizActionEnum.FIRST_REVISE_REC               | REC_TOPIC        |
| 中考复习-二轮                                                | 水平测试：BizActionEnum.SECOND_REVISE_EVAL                   | REC_EVAL4IN      |
| 中考复习-二轮                                                | 薄弱复习点搜索：BizActionEnum.SECOND_REVISE_SORT             | REC_NODE         |
| 中考复习-二轮                                                | 复习点习题练习：典例题题BizActionEnum.SECOND_REVISE_TYP<br>变式题BizActionEnum.SECOND_REVISE_VARY | REC_TOPIC        |
| AI诊断<br>StudyCodeEnum.AI_DIAG                              | 针对学：BizActionEnum.SYNC_AIM                               | REC_TOPIC        |



### 请求参数说明

接口输入输出参数列表

参数均在com.iflytek.skylab.core.data包下。

| 接口                    | 输入对象                                          | 输出对象               | 备注（功能编码）                                                         |
| ----------------------- | ------------------------------------------------- | ---------------------- | ------------------------------------------------------------ |
| 用户画像诊断功能            | MasterFetch4CatalogParam<br>MasterFetch4NodeParam | MasterFetchResult      | MasterFetch4CatalogParam用于在目录下诊断<br>MasterFetch4NodeParam用于在点列表下诊断（MASTER_FETCH） |
| 推荐功能 - 测评推题     | RecEval4InOutParam                                | RecEval4InOutResult | REC_EVAL4IN入门测、REC_EVAL4OUT出门测场景                                       |
| 推荐功能 - 限时测评推题 | RecEval4LimitParam                             | RecEval4LimitResult | REC_EVAL4LIMIT包含 单元限时测场景                                          |
| 推荐功能 - 点搜索       | RecNodeParam                                      | RecNodeResult          |REC_NODE                                                              |
| 推荐功能 - 点推题       | RecTopicParam                                     | RecTopicResult         |REC_TOPIC                                                              |
| 推荐功能 - 点属性诊断       | RecNodePropParam                                     | RecNodePropResult         |REC_NODE_PROP                                                              |
| 推荐功能 - 推题包       | RecTopicPackParam                                     | RecTopicPackResult         |REC_TOPIC_PACK                                                              |
| 学习行为收集接口        | StudyLogParam                                     | StudyLogResult         |STUDY_LOG                                                              |
| 通用特征查询接口        | FeatureParam                                      | FeatureResult          |COM_FEA                                                              |



#### 1、诊断功能 对外统一接口

##### 1.1、画像获取 功能 请求参数-MasterFetch4CatalogParam数据结构，用于在目录下测评

| 字段名称   | 必须 | 类型         | 说明                                                         |
| ---------- | :---: | ------------ | ------------------------------------------------------------ |
| catalogIds | Y    | List<String> | 输入点范围 目录<br>节/章/复习点一级点列表<br>点类型通过SceneInfo学习场景推断 |

##### 1.2、画像获取 功能 请求参数-MasterFetch4NodeParam数据结构，用于在点列表下测评

| 字段名称 | 必须 | 类型         | 说明                                                         |
| -------- | :---: | ------------ | ------------------------------------------------------------ |
| nodeIds  | Y    | List<String> | 点标识Id列表<br>锚点、考点、复习点<br>点类型通过SceneInfo学习场景推断 |

##### 1.3、画像获取 功能 返回结果-MasterFetchResult数据结构，

| 序号 | 字段名称  | 类型              | 说明               |
| :--: | --------- | ----------------- | ------------------ |
|  1   | masteries | List<MasteryInfo> | 画像掌握度汇总信息 |

MasteryInfo数据结构

| 字段名称             | 类型            | 说明                                              |
| -------------------- | --------------- | ------------------------------------------------- |
| catalogId            | String          | 父目录                                            |
| catalogType          | CatalogTypeEnum | 父目录类型（目录章、目录节、目录一级复习点）      |
| nodeId               | String          | 点标识                                            |
| nodeType             | NodeTypeEnum    | 点类型（图谱锚点、图谱考点、复习点）              |
| useThinkingExpansion | Boolean         | 是否使用思维拓展点 （仅锚点体系，同步学场景使用） |
| masteryScore         | Double          | 画像得分                                          |
| computeType          | String          | 画像计算方式                                      |
| fusion               | Double          | 融合画像值                                        |
| real                 | Double          | 真实画像                                          |
| predict              | Double          | 预测画像                                          |
| shouldFlag           | Boolean         | 是否是应学点                                      |
| updateTime           | Instant         | 更新时间，时间戳                                  |
| lastAnswerTime       | Instant         | 最后一题答题时间                                  |

#### 2、推荐功能 对外统一接口

##### 2.1、测评推题

推荐功能 - 测评推题

######   输入对象-RecEval4InOutParam

课时包类型通过Scene中的studyCode+functionCode能区分，不需传入了。

|序号| 字段名称         | 必须 | 类型         | 说明                                                         |
|:-: | ---------------- | :---: | ------------ | ------------------------------------------------------------ |
|   1| catalogIds | Y    | List<String> | 测评输入点范围<br>节/章/复习点一级点列表<br>点类型通过SceneInfo学习场景推断 |
|   2| roundId | N   | String       | 测评任务Id<br>调用端生成和传入，多次测评属于一次任务的，传入相同任务Id |
|   3| topicOrderNumber | Y    | int          | 测评题号,题号不能小于1                                       |

######   输出对象-RecEval4InOutResult

| 字段名称         | 类型                 | 说明                                                         |
| ---------------- | -------------------- | ------------------------------------------------------------ |
| evaluationItems  | List<EvaluationItem> | 测评推荐结果列表<br>1、多轮测评推题 集合仅有1个推荐资源<br>2、单次测评推题 集合包含全部推荐资源 |
| terminationFlag  | boolean              | 是否可终止本次测评（推荐终止标识）                           |
| recTotalNum      | int                  | 本次推荐流程-将推荐试题总题量                                |
| updateTime       | Instant              | 推荐时间戳                                                   |
| doTerminateTopic | boolean              | 建议终止时最后一题是否做标识：<br>true：建议终止时最后一题需要做<br>false：建议终止时最后一题不需要做。 |

推荐功能 - 限时测评推题

###### 输入对象-RecTimeLimitEvalParam

| 序号 | 字段名称         | 必须 | 类型         | 说明                                                         |
| :--: | ---------------- | :--: | ------------ | ------------------------------------------------------------ |
|  1   | catalogIds       |  Y   | List<String> | 测评输入点范围<br>节/章/复习点一级点列表<br>点类型通过SceneInfo学习场景推断 |
|  2   | roundId          |  N   | String       | 测评任务Id<br>调用端生成和传入，多次测评属于一次任务的，传入相同任务Id |
|  3   | topicOrderNumber |  Y   | int          | 测评题号,题号不能小于1                                       |

###### 输出对象-RecTimeLimitEvalResult

| 字段名称         | 类型                 | 说明                                                         |
| ---------------- | -------------------- | ------------------------------------------------------------ |
| evaluationItems  | List<EvaluationItem> | 测评推荐结果列表<br>1、多轮测评推题 集合仅有1个推荐资源<br>2、单次测评推题 集合包含全部推荐资源 |
| terminationFlag  | boolean              | 是否可终止本次测评（推荐终止标识）                           |
| recTotalNum      | int                  | 本次推荐流程-将推荐试题总题量                                |
| updateTime       | Instant              | 推荐时间戳                                                   |
| unitTimes        | long                 | 测试总时长-单位秒 (单元限时测使用)                           |
| doTerminateTopic | boolean              | 建议终止时最后一题是否做标识：<br>true：建议终止时最后一题需要做<br>false：建议终止时最后一题不需要做。 |

###### 测评推荐的资源Item对象 - EvaluationItem

| 序号 | 字段名称    | 类型             | 说明                                     |
| :--: | ----------- | ---------------- | ---------------------------------------- |
|  1   | resNodeId   | String           | 推荐的资源id，题/视频/卡片               |
|  2   | resNodeType | ResourceTypeEnum | 资源类型（资源题/资源卡片/资源视频）     |
|  3   | nodeId      | String           | 推荐的关联点id（锚点/考点/复习点）       |
|  4   | nodeName    | String           | 点名称（锚点/考点/复习点）               |
|  5   | nodeType    | NodeTypeEnum     | 关联点类型（图谱锚点、图谱考点、复习点） |

##### 2.2、点搜索功能

######   输入对象-RecNodeParam

| 字段名称   | 必须 | 类型         | 说明                                                         |
| ---------- | :---: | ------------ | ------------------------------------------------------------ |
| catalogIds | Y    | List<String> | 点的目录列表<br>节/章/复习点一级点列表<br>点类型通过SceneInfo学习场景推断 |
| targetMastery | N | Double | 目标掌握度值<br>可选，中考复习 场景需传入设值。 |
| useThinkingExpansion | N | boolean | 是否使用 思维拓展点（仅锚点体系，同步学场景使用） |

######   输出对象-RecNodeResult

| 字段名称          | 类型                 | 说明                                                         |
| ----------------- | -------------------- | ------------------------------------------------------------ |
| nodeIds           | List<EvaluationItem> | 推荐点<br>薄弱锚点列表<br>或 单个推荐考点、复习点            |
| pointRecommendEnd | boolean              | 推荐点建议终止<br>true：建议终止，此时推荐考点id不建议继续做题 false：不建议终止，建议在推荐考点id下进行做题 |
| learnPoints       | List<String>         | 应学点列表                                                   |

##### 2.3、点推题功能

######   输入对象-RecTopicParam

| 字段名称  | 必须 | 类型   | 说明                                                         |
| --------- | :---: | ------ | ------------------------------------------------------------ |
| nodeId    | Y    | String | 点标识id（锚点/考点/四级复习点/复习树）                         |
| roundId | N   | String | 任务id（标识该点推荐流程）<br>调用端生成和传入，多次推荐属于一次任务的，传入相同任务Id |
| topicOrderNumber | Y | int | 推荐题号,题号不能小于1 |

######   输出对象-RecTopicResult

| 字段名称         | 类型    | 说明                                                         |
| ---------------- | ------- | ------------------------------------------------------------ |
| topicId          | String  | 题目id                                                       |
| updateTime       | Instant | 推荐时间戳                                                   |
| nodeId           | String  | 点标识id（锚点/考点/复习点）                                 |
| terminationFlag  | boolean | 是否可终止本次测评                                           |
| doTerminateTopic | boolean | 建议终止时最后一题是否做标识<br>true：建议终止时最后一题需要做，false：建议终止时最后一题不需要做。 |
| recTotalNum      | int     | 本次推荐流程-将推荐试题总题量                                |

##### 2.4、点属性诊断

###### 输入对象-RecNodePropParam

| 字段名称 | 必须 | 类型          | 说明       |
| -------- | :--: | ------------- | ---------- |
| nodeIds  |  Y   | List\<String> | 锚点ID集合 |

###### 输出对象-RecNodePropResult

| 字段名称  | 类型            | 说明           |
| --------- | --------------- | -------------- |
| nodeInfos | List\<NodeInfo> | 点属性结果集合 |

NodeInfo

| 字段名称  | 类型   | 说明     |
| --------- | ------ | -------- |
| nodeId    | String | 锚点ID   |
| logicType | String | 锚点类型 |



##### 2.5 推题包

###### 输入对象-RecTopicPackParam

| 字段名称 | 必须 | 类型          | 说明       |
| -------- | :--: | ------------- | ---------- |
| nodeIds  |  Y   | List\<String> | 锚点ID集合 |

###### 输出对象-RecTopicPackResult

| 字段名称   | 类型             | 说明     |
| ---------- | ---------------- | -------- |
| topicInfos | List\<TopicInfo> | 推题集合 |

TopicInfo

| 字段名称  | 类型   | 说明     |
| --------- | ------ | -------- |
| topicId   | String | 试题ID   |
| topicType | String | 试题类型 |
| nodeId    | String | 锚点ID   |



### 3、学习行为收集接口

######   输入对象-StudyLogParam

| 字段名称 | 必须 | 类型                 | 说明         |
| -------- | :---: | -------------------- | ------------ |
| items    | Y    | List<StudyLogRecord> | 答题记录列表 |

StudyLogRecord数据结构

| 字段名称       | 必须 | 类型             | 说明                                                         |
| -------------- | :---: | ---------------- | ------------------------------------------------------------ |
| refTraceId | Y | String | 推题时的关联 TraceId |
| nodeId         | Y    | String           | 点标识id（锚点/考点/复习点）                                 |
| nodeType       | Y    | NodeTypeEnum     | 关联点类型（图谱锚点、图谱考点、复习点）                     |
| resNodeId      | Y    | String           | 资源id                                                       |
| resNodeType    | Y    | ResourceTypeEnum | 资源类型（资源题/资源卡片/资源视频）                         |
| roundId | N | String           | 轮标识，如测评任务                                           |
| roundIndex | Y  | String           | 轮序号，一次测评任务中的序号                                 |
| timeCost | Y | Integer | 答题耗时(单位：秒) |
| score | Y | Double | 得分 |
| standardScore | Y | Double | 标准得分 |
| feedbackExt | N   | JSONObject       | 反馈扩展 |
| feedBackTime   | N    | Instant      | 反馈时间，客户端提交时间                                     |
| from   | N    | String      | 学情记录来源                          |

######   输出对象-StudyLogResult

主要见SkylabResponse内内容。

#### 4、通用特征查询接口

  输入对象-FeatureParam

| 字段名称       | 必须 | 类型                      | 说明     |
| -------------- | :---: | ------------------------- | -------- |
| featureName    | Y    | String                    | 特征名称 |
| params         | Y    | List<Map<String, String>> | 请求参数 |
| graphVersion   | N    | String                    | 图谱版本 |
| featureVersion | N    | int                       | 特征版本 |

  输出对象-FeatureResult

| 字段名称    | 类型                      | 说明     |
| ----------- | ------------------------- | -------- |
| featureName | String                    | 特征名称 |
| values      | List<Map<String, String>> | 特征值   |

同水平掌握度：[样例数据]

```json
{
    "traceId": "{{$uuid}}",
    "scene": {
        "subjectCode": "02",
        "classId": "string",
        "deviceId": "string",
        "test": false,
        "userId": "string",
        "userLevel": "string"
    },
    "payload": {
        "items": [
                    {
                "featureName": "anchor_similaryuser_mastery",
                "params":[
                    {
                      "user_id": "yinqingPhotoTest01",
                      "biz_code": "zsy_xxj",
                      "study_code": "SYNC_LEARN",
                      "catalog_code": "01_07020101-001_02_002",
                      "subject_code":"02"
                      "anchorpoint_code": "92a344dc-8e8c-49fa-af45-8b4a2beaa083"
                    },
                    {
                      ...
                    }
                ],
                "featureVersion": 1,
                "graphVersion": "v2022-03"
            }
        ]
    }
}
```

学尖生章节数据：[样例数据]

```json
{
    "traceId": "{{$uuid}}",
    "scene": {
        "subjectCode": "02",
        "classId": "string",
        "deviceId": "string",
        "test": false,
        "userId": "string",
        "userLevel": "string"
    },
    "payload": {
        "items": [
            {
                "featureName": "is_top_student",
                "params": [
                    {
                      "user_id": "4e595f87-ba42-4f3b-8dc7-9173d1768585",
                      "biz_code": "zsy_xxj",
                      "catalog_code": "30_07020113-002_03_001"
                    },
                    {
                      "user_id": "4e595f87-ba42-4f3b-8dc7-9173d1768585",
                      "biz_code": "zsy_xxj",
                      "catalog_code": "30_07020113-002_02_002"
                    },
                    {
                      "user_id": "4e595f87-ba42-4f3b-8dc7-9173d1768585",
                      "biz_code": "zsy_xxj",
                      "catalog_code": "30_07020113-002_02_003"
                    },
                    {
                      "user_id": "4e595f87-ba42-4f3b-8dc7-9173d1768585",
                      "biz_code": "zsy_xxj",
                      "catalog_code": "30_07020113-002_02_004"
                    },
                    {
                      "user_id": "4e595f87-ba42-4f3b-8dc7-9173d1768585",
                      "biz_code": "zsy_xxj",
                      "catalog_code": "30_07020113-002_02_005"
                    },
                    {
                      "user_id": "4e595f87-ba42-4f3b-8dc7-9173d1768585",
                      "biz_code": "zsy_xxj",
                      "catalog_code": "30_07020113-002_02_006"
                    },
                    {
                      "user_id": "4e595f87-ba42-4f3b-8dc7-9173d1768585",
                      "biz_code": "zsy_xxj",
                      "catalog_code": "30_07020113-002_02_007"
                    }
                ],
                "featureVersion": 1,
                "graphVersion": "v2022-03",
                "tags": {
                    "scene": "xxj"
                }
            }
        ]
    }
}
```

