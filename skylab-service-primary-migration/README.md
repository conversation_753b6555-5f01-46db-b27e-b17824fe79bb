## skylab-service-primary-migration

小学精准学数据迁移代理服务

### 1. 项目概述
- 项目名称：skylab-service-primary-migration
- 项目描述：处理小学精准学的答题与画像迁移上报数据
- 版本信息：2.0.9-SNAPSHOT（开发中）
- 端口：23454（dev）

### 2. 技术架构
- 框架：Spring Boot、Knife4j
- 控制器：PrimaryMigrationController（/skylab/api/v1/primaryMigration/process）

### 3. 接口（REST）
- POST `/skylab/api/v1/primaryMigration/process` 处理迁移数据

### 4. 业务处理逻辑
- 接收 ApiRequest -> 调用 PrimaryMigrationService.process 进行入库/迁移

### 5. 系统配置
- 端口：`server.port=23454`
- Zookeeper：spring.cloud.zookeeper.connect-string=${IP}:2181

### 6. 启动与验证
```bash
mvn -pl skylab-service-primary-migration spring-boot:run -Dspring-boot.run.profiles=dev
```

### 7. 开发指南
- 启动类：com.iflytek.skylab.service.primary.PrimaryMigrationAppBoot
- 为 /process 编写样例报文与回归测试

