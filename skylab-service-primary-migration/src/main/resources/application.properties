IP=************
SKYNET_CLUSTER=skynet
SKYNET_PLUGIN_CODE=skylab-platform
#-----------------------------------------------------------------
server.port=23454
spring.profiles.active=dev
#-----------------------------------------------------------------
skyline.brave.enabled=true
skyline.brave.aop-enabled=true
#-----------------------------------------------------------------
spring.application.name=skylab-primary-migration
skynet.action-point=${spring.application.name}@${SKYNET_PLUGIN_CODE}
skynet.zookeeper.enabled=true
spring.cloud.zookeeper.enabled=true
spring.cloud.zookeeper.discovery.root=/${SKYNET_CLUSTER}/discovery/${SKYNET_PLUGIN_CODE}
spring.cloud.zookeeper.connect-string=${IP}:2181
#-----------------------------------------------------------------
logging.level.ROOT=INFO
logging.level.com.iflytek.skyline=INFO
logging.level.com.iflytek.skylab=INFO
#-----------------------------------------------------------------
#mongodb
