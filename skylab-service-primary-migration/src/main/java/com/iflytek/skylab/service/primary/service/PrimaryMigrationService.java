package com.iflytek.skylab.service.primary.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.data.PrimaryMigrationBehaviorParam;
import com.iflytek.skylab.core.data.PrimaryMigrationMasteryParam;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.data.UpperLayerQuery;
import com.iflytek.skylab.core.dataapi.exception.SkylabException;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.domain.PrimaryMigrationBehavior;
import com.iflytek.skylab.core.domain.PrimaryMigrationMastery;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.service.primary.config.PrimaryMigrationProperties;
import com.iflytek.skylab.service.primary.constant.AnchorPointType;
import com.iflytek.skylab.service.primary.constant.CheckPointType;
import com.iflytek.skylab.service.primary.constant.PointMastery;
import com.iflytek.skylab.service.primary.param.CatalogTreeParam;
import com.iflytek.skyline.common.api.ApiErrorCode;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.common.exception.ParamNotExistException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.util.StopWatch;

import java.io.File;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2023/4/6 10:53
 * @description
 */
@Slf4j
public class PrimaryMigrationService {
    private final ExecutorService executorService;

    private final PrimaryMigrationProperties primaryMigrationProperties;

    private final KafkaTemplate<Object, Object> kafkaTemplate;

    private final GraphService graphService;

    /**
     * 锚点类型映射，key:锚点id  value: -1 (课内锚点) 1 (课外锚点)
     */
    private ConcurrentHashMap<String, Integer> anchorPointTypeMap = new ConcurrentHashMap<>();


    /**
     * 考点类型映射，key:考点id  value: -1 (课内锚点) 1 (课外锚点)
     */
    private ConcurrentHashMap<String, Integer> checkPointTypeMap = new ConcurrentHashMap<>();

    /**
     * 章节标签映射
     */
    private ConcurrentHashMap<String, String> catalogLabelMap = new ConcurrentHashMap<>();

    /**
     * 书本目录 key:书本 value: 书本目录树
     */
    private ConcurrentHashMap<String, CatalogTreeParam> bookTreeMap = new ConcurrentHashMap<>();

    /**
     * 书本目录 key:章节 value: 考点列表
     */
    private ConcurrentHashMap<String, List<String>> catalogCheckPointMap = new ConcurrentHashMap<>();

    /**
     * 书本目录 key:章节+"|"+考点 value: 锚点列表
     * 在一本书下，同样的考点只会挂在一个章节下，同样的锚点只会挂在一个章节或者一个考点下。
     * 在不同的出版社下的书本，同样的考点可能会挂在不同章节下。
     */
    private ConcurrentHashMap<String, List<String>> checkPointAnchorPointMap = new ConcurrentHashMap<>();


    /**
     * 书本目录 key:章节 value: 可用的锚点列表
     */
    private ConcurrentHashMap<String, List<String>> catalogAnchorPointAvailableMap = new ConcurrentHashMap<>();


    /**
     * key：章 或者 节 或者 小节  value： 图谱下面直接挂的课时   默认值不能为null，应该为new ArrayList
     */
    private ConcurrentHashMap<String, List<String>> catalogDirectPeriodListMap = new ConcurrentHashMap<>();

    /**
     * key：章 或者 节 或者 小节  value： 下面非课时目录挂的所有课时
     */
    private ConcurrentHashMap<String, List<String>> catalogIndirectPeriodListMap = new ConcurrentHashMap<>();

    /**
     * 锚点最末级目录
     */
    private Map<String, List<String>> anchorFinalUpperLayerMap = new HashMap<>();

    /**
     * 需要复制的最末级目录
     */
    private Set<String> needCopyFinalCataSet = new HashSet<>();

    public PrimaryMigrationService(ExecutorService executorService, PrimaryMigrationProperties primaryMigrationProperties, KafkaTemplate<Object, Object> kafkaTemplate, GraphService graphService) {
        this.executorService = executorService;
        this.primaryMigrationProperties = primaryMigrationProperties;
        this.kafkaTemplate = kafkaTemplate;
        this.graphService = graphService;
        init();
    }

    /**
     * 初始化
     */
    private void init() {
        log.info("start init books ===");
        String graphVersion = primaryMigrationProperties.getGraphVersion();
        String books = primaryMigrationProperties.getBooks();
        List<String> bookList = StrUtil.split(books, ",");
        if (CollectionUtil.isNotEmpty(bookList)) {
            for (String book : bookList) {
                processOneBook(book, graphVersion);
            }
            for (String book : bookList) {
                calculateOneBook(book);
            }
        }
        calculateCatalogAnchorPointAvailableMap();

        /**
         * 获取书下所有锚点，再查询锚点最末级目录,最后获取到需要复制的最末级目录
         */
        HashSet<String> hashSet = new HashSet<>();
        anchorFinalUpperLayerMap = findFinalUpperLayer(anchorPointTypeMap.keySet());
        for (Map.Entry<String, List<String>> entry : anchorFinalUpperLayerMap.entrySet()) {
            hashSet.addAll(entry.getValue());
        }

        needCopyFinalCataSet.addAll(parseCollection(new ArrayList<>(hashSet)));

        log.info("end init books ===");
    }

    /**
     * 处理单本书
     *
     * @param bookCode
     * @param version
     */
    private void processOneBook(String bookCode, String version) {
        if (!bookTreeMap.containsKey(bookCode)) {
            // 如果之前没有计算过这本书，则计算
            log.info("process book is {}", bookCode);
            CatalogTreeParam bookTreeParam = new CatalogTreeParam(bookCode, GraphVertexType.BOOK);
            SubGraphQuery subBookGraphQuery = buildSubGraphQuery(Arrays.asList(bookCode), version, GraphVertexType.BOOK, Arrays.asList(GraphVertexType.UNIT));
            GraphData subBookGraph = graphService.querySubGraph(subBookGraphQuery);
            if (null != subBookGraph && null != subBookGraph.getVertices() && null != subBookGraph.getEdges()) {
                List<GraphData.GraphVertex> subBookVertices = subBookGraph.getVertices();
                for (GraphData.GraphVertex subBookGraphVertex : subBookVertices) {
                    String subBookLabel = subBookGraphVertex.getLabel();
                    if (GraphVertexType.UNIT.equals(subBookLabel)) {
                        String unitCode = subBookGraphVertex.getId();
                        // 获取章的目录结构
                        CatalogTreeParam unitTreeParam = processOneUnit(version, unitCode);
                        bookTreeParam.getChildren().add(unitTreeParam);
                    }
                }
            }
            bookTreeMap.put(bookCode, bookTreeParam);
        }
    }

    /**
     * 处理单个章
     *
     * @param version
     * @param unitCode
     * @return
     */
    private CatalogTreeParam processOneUnit(String version, String unitCode) {
        log.info("process unit is {}", unitCode);
        catalogLabelMap.put(unitCode, GraphVertexType.UNIT);
        CatalogTreeParam unitTreeParam = new CatalogTreeParam(unitCode, GraphVertexType.UNIT);
        List<String> directPeriodList = new ArrayList<>();
        List<String> checkPointList = new ArrayList<>();
        SubGraphQuery unitGraphQuery = buildSubGraphQuery(Arrays.asList(unitCode), version, GraphVertexType.UNIT, Arrays.asList(GraphVertexType.CHECK_POINT, GraphVertexType.ANCHOR_POINT, GraphVertexType.COURSE, CatalogTypeEnum.PERIOD.toString()));
        GraphData unitGraph = graphService.querySubGraph(unitGraphQuery);
        if (null != unitGraph && null != unitGraph.getVertices() && null != unitGraph.getEdges()) {
            List<GraphData.GraphVertex> unitVertices = unitGraph.getVertices();
            for (GraphData.GraphVertex unitGraphVertex : unitVertices) {
                String unitLabel = unitGraphVertex.getLabel();
                if (GraphVertexType.CHECK_POINT.equals(unitLabel)) {
                    //  获取点类型属性并设置
                    fillPointType(unitGraphVertex);
                    String checkPointCode = unitGraphVertex.getId();
                    checkPointList.add(checkPointCode);
                    getAnchorPointsUnderCheckPoint(version, checkPointCode, unitCode);
                } else if (GraphVertexType.ANCHOR_POINT.equals(unitLabel)) {
                    //  获取点类型属性并设置
                    fillPointType(unitGraphVertex);
                } else if (GraphVertexType.COURSE.equals(unitLabel)) {
                    String courseCode = unitGraphVertex.getId();
                    // 获取节的目录结构
                    CatalogTreeParam courseTreeParam = processOneCourse(version, courseCode);
                    unitTreeParam.getChildren().add(courseTreeParam);
                } else if (CatalogTypeEnum.PERIOD.toString().equals(unitLabel)) {
                    String periodCode = unitGraphVertex.getId();
                    directPeriodList.add(periodCode);
                    // 获取课时的目录结构
                    processOnePeriod(version, periodCode);
                }
            }
            catalogDirectPeriodListMap.put(unitCode, directPeriodList);
            catalogCheckPointMap.put(unitCode, checkPointList);
        }
        return unitTreeParam;
    }

    /**
     * 处理单个节
     *
     * @param version
     * @param courseCode
     * @return
     */
    private CatalogTreeParam processOneCourse(String version, String courseCode) {
        log.info("process course is {}", courseCode);
        catalogLabelMap.put(courseCode, GraphVertexType.COURSE);
        CatalogTreeParam courseTreeParam = new CatalogTreeParam(courseCode, GraphVertexType.COURSE);
        List<String> directPeriodList = new ArrayList<>();
        List<String> checkPointList = new ArrayList<>();
        SubGraphQuery courseGraphQuery = buildSubGraphQuery(Arrays.asList(courseCode), version, GraphVertexType.COURSE, Arrays.asList(GraphVertexType.CHECK_POINT, GraphVertexType.ANCHOR_POINT, CatalogTypeEnum.L2COURSE.toString(), CatalogTypeEnum.PERIOD.toString()));
        GraphData courseGraph = graphService.querySubGraph(courseGraphQuery);
        if (null != courseGraph && null != courseGraph.getVertices() && null != courseGraph.getEdges()) {
            List<GraphData.GraphVertex> courseVertices = courseGraph.getVertices();
            for (GraphData.GraphVertex courseGraphVertex : courseVertices) {
                String courseLabel = courseGraphVertex.getLabel();
                if (GraphVertexType.CHECK_POINT.equals(courseLabel)) {
                    //  获取点类型属性并设置
                    fillPointType(courseGraphVertex);
                    String checkPointCode = courseGraphVertex.getId();
                    checkPointList.add(checkPointCode);
                    getAnchorPointsUnderCheckPoint(version, checkPointCode, courseCode);
                } else if (GraphVertexType.ANCHOR_POINT.equals(courseLabel)) {
                    //  获取点类型属性并设置
                    fillPointType(courseGraphVertex);
                } else if (CatalogTypeEnum.L2COURSE.toString().equals(courseLabel)) {
                    String L2CourseCode = courseGraphVertex.getId();
                    // 获取小节的目录结构
                    CatalogTreeParam L2courseTreeParam = processOneL2Course(version, L2CourseCode);
                    courseTreeParam.getChildren().add(L2courseTreeParam);
                } else if (CatalogTypeEnum.PERIOD.toString().equals(courseLabel)) {
                    String periodCode = courseGraphVertex.getId();
                    directPeriodList.add(periodCode);
                    // 获取课时的目录结构
                    processOnePeriod(version, periodCode);
                }
            }
            catalogDirectPeriodListMap.put(courseCode, directPeriodList);
            catalogCheckPointMap.put(courseCode, checkPointList);
        }
        return courseTreeParam;
    }

    /**
     * 处理单个小节
     *
     * @param version
     * @param L2CourseCode
     * @return
     */
    private CatalogTreeParam processOneL2Course(String version, String L2CourseCode) {
        log.info("process L2course is {}", L2CourseCode);
        catalogLabelMap.put(L2CourseCode, CatalogTypeEnum.L2COURSE.toString());
        CatalogTreeParam L2courseTreeParam = new CatalogTreeParam(L2CourseCode, CatalogTypeEnum.L2COURSE.toString());
        List<String> directPeriodList = new ArrayList<>();
        List<String> checkPointList = new ArrayList<>();
        SubGraphQuery L2CourseGraphQuery = buildSubGraphQuery(Arrays.asList(L2CourseCode), version, CatalogTypeEnum.L2COURSE.toString(), Arrays.asList(GraphVertexType.CHECK_POINT, GraphVertexType.ANCHOR_POINT));
        GraphData L2CourseGraph = graphService.querySubGraph(L2CourseGraphQuery);
        if (null != L2CourseGraph && null != L2CourseGraph.getVertices() && null != L2CourseGraph.getEdges()) {
            List<GraphData.GraphVertex> L2CourseVertices = L2CourseGraph.getVertices();
            for (GraphData.GraphVertex L2CourseGraphVertex : L2CourseVertices) {
                String L2CourseLabel = L2CourseGraphVertex.getLabel();
                if (GraphVertexType.CHECK_POINT.equals(L2CourseLabel)) {
                    //  获取点类型属性并设置
                    fillPointType(L2CourseGraphVertex);
                    String checkPointCode = L2CourseGraphVertex.getId();
                    checkPointList.add(checkPointCode);
                    getAnchorPointsUnderCheckPoint(version, checkPointCode, L2CourseCode);
                } else if (GraphVertexType.ANCHOR_POINT.equals(L2CourseLabel)) {
                    //  获取点类型属性并设置
                    fillPointType(L2CourseGraphVertex);
                }
            }
            catalogDirectPeriodListMap.put(L2CourseCode, directPeriodList);
            catalogCheckPointMap.put(L2CourseCode, checkPointList);
        }
        return L2courseTreeParam;
    }

    /**
     * 处理单个课时
     *
     * @param version
     * @param periodCode
     * @return
     */
    private CatalogTreeParam processOnePeriod(String version, String periodCode) {
        log.info("process period is {}", periodCode);
        catalogLabelMap.put(periodCode, CatalogTypeEnum.PERIOD.toString());
        CatalogTreeParam periodTreeParam = new CatalogTreeParam(periodCode, CatalogTypeEnum.PERIOD.toString());
        List<String> checkPointList = new ArrayList<>();
        SubGraphQuery periodGraphQuery = buildSubGraphQuery(Arrays.asList(periodCode), version, CatalogTypeEnum.PERIOD.toString(), Arrays.asList(GraphVertexType.CHECK_POINT, GraphVertexType.ANCHOR_POINT));
        GraphData periodGraph = graphService.querySubGraph(periodGraphQuery);
        if (null != periodGraph && null != periodGraph.getVertices() && null != periodGraph.getEdges()) {
            List<GraphData.GraphVertex> periodVertices = periodGraph.getVertices();
            for (GraphData.GraphVertex periodGraphVertex : periodVertices) {
                String periodLabel = periodGraphVertex.getLabel();
                if (GraphVertexType.CHECK_POINT.equals(periodLabel)) {
                    //  获取点类型属性并设置
                    fillPointType(periodGraphVertex);
                    String checkPointCode = periodGraphVertex.getId();
                    checkPointList.add(checkPointCode);
                    getAnchorPointsUnderCheckPoint(version, checkPointCode, periodCode);
                } else if (GraphVertexType.ANCHOR_POINT.equals(periodLabel)) {
                    //  获取点类型属性并设置
                    fillPointType(periodGraphVertex);
                }
            }
            catalogCheckPointMap.put(periodCode, checkPointList);
        }
        return periodTreeParam;
    }


    /**
     * 获取考点点下的锚点
     *
     * @param version
     * @param checkPointCode
     * @param catalogId
     */
    private void getAnchorPointsUnderCheckPoint(String version, String checkPointCode, String catalogId) {
        List<String> anchorPointList = new ArrayList<>();
        SubGraphQuery checkPointGraphQuery = buildSubGraphQuery(Arrays.asList(checkPointCode), version, GraphVertexType.CHECK_POINT, Arrays.asList(GraphVertexType.ANCHOR_POINT));
        GraphData checkPointGraph = graphService.querySubGraph(checkPointGraphQuery);
        if (null != checkPointGraph && null != checkPointGraph.getVertices() && null != checkPointGraph.getEdges()) {
            List<GraphData.GraphVertex> checkPointVertices = checkPointGraph.getVertices();
            for (GraphData.GraphVertex checkGraphVertex : checkPointVertices) {
                String checkPointLabel = checkGraphVertex.getLabel();
                if (GraphVertexType.ANCHOR_POINT.equals(checkPointLabel)) {
                    String anchorPointCode = checkGraphVertex.getId();
                    // 获取锚点类型属性并设置
//                    fillPointType(checkGraphVertex);
                    anchorPointList.add(anchorPointCode);
                }
            }
            checkPointAnchorPointMap.put(catalogId + "|" + checkPointCode, anchorPointList);
        }
    }

    /**
     * 构建子图查询
     *
     * @param rootVertexIdList
     * @param version
     * @param rootVertexLabel
     * @param targetVertexLabelList
     * @return
     */
    private SubGraphQuery buildSubGraphQuery(List<String> rootVertexIdList, String version, String rootVertexLabel, List<String> targetVertexLabelList) {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(java.util.UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion(version);
        subGraphQuery.setRootVertexLabel(rootVertexLabel);
        subGraphQuery.setRootVertexIdList(rootVertexIdList);
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        for (String targetVertexLabel : targetVertexLabelList) {
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(rootVertexLabel).target(targetVertexLabel).build());
        }
        subGraphQuery.setEdgeLabels(edgeLabels);
        return subGraphQuery;
    }

    /**
     * 填充点类型
     *
     * @param graphVertex
     */
    private void fillPointType(GraphData.GraphVertex graphVertex) {
        String pointCode = graphVertex.getId();
        JSONObject properties = graphVertex.getProperties();
        String label = graphVertex.getLabel();
        if (GraphVertexType.ANCHOR_POINT.equals(label)) {
            // 获取锚点类型属性并设置
            if (null != graphVertex.getProperties() && StrUtil.isNotEmpty(properties.getString("anchorPointType"))) {
                String anchorPointType = properties.getString("anchorPointType");
                if (AnchorPointType.getInsideAnchorPointType().contains(anchorPointType)) {
                    // 课内锚点
                    anchorPointTypeMap.put(pointCode, AnchorPointType.INSIDE);
                } else if (AnchorPointType.getOutsideAnchorPointType().contains(anchorPointType)) {
                    // 课外锚点
                    anchorPointTypeMap.put(pointCode, AnchorPointType.OUTSIDE);
                } else {
                    // 常规锚点 算课内
//                    anchorPointTypeMap.put(pointCode, AnchorPointType.INSIDE);
                }
            }
        } else if (GraphVertexType.CHECK_POINT.equals(label)) {
            // 获取考点类型属性并设置
            if (null != graphVertex.getProperties() && StrUtil.isNotEmpty(properties.getString("checkPointType"))) {
                String checkPointType = properties.getString("checkPointType");
                if (CheckPointType.getInsideCheckPointType().contains(checkPointType)) {
                    // 课内考点
                    checkPointTypeMap.put(pointCode, CheckPointType.INSIDE);
                } else if (CheckPointType.getOutsideCheckPointType().contains(checkPointType)) {
                    // 课外考点
                    checkPointTypeMap.put(pointCode, CheckPointType.OUTSIDE);
                } else {
                    // 常规考点 算课内
//                    checkPointTypeMap.put(pointCode, CheckPointType.INSIDE);
                }
            }
        }
    }

    /**
     * 计算单本书
     *
     * @param bookCode
     */
    private void calculateOneBook(String bookCode) {
        log.info("calculateOneBook book is {}", bookCode);
        CatalogTreeParam bookTreeParam = bookTreeMap.get(bookCode);
        if (null != bookTreeParam) {
            List<CatalogTreeParam> unitList = bookTreeParam.getChildren();
            if (CollectionUtil.isNotEmpty(unitList)) {
                for (CatalogTreeParam unitTreeParam : unitList) {
                    calculateOneCatalog(unitTreeParam);
                }
            }
        }
    }

    /**
     * 计算单章节递归
     *
     * @param catalogTreeParam
     * @return
     */
    private List<String> calculateOneCatalog(CatalogTreeParam catalogTreeParam) {
        String catalogId = catalogTreeParam.getCode();
        if (catalogIndirectPeriodListMap.containsKey(catalogId)) {
            return catalogIndirectPeriodListMap.get(catalogId);
        }
        List<String> periodList = new ArrayList<>();
        List<CatalogTreeParam> children = catalogTreeParam.getChildren();
        // 如果下面挂了非课时目录
        if (CollectionUtil.isNotEmpty(children)) {
            for (CatalogTreeParam child : children) {
                periodList.addAll(calculateOneCatalog(child));
            }
        } else {
            periodList = catalogDirectPeriodListMap.get(catalogId);
        }
        catalogIndirectPeriodListMap.put(catalogId, periodList);
        return periodList;
    }

    /**
     * 计算catalogAnchorPointAvailableMap
     */
    private void calculateCatalogAnchorPointAvailableMap() {
        if (CollectionUtil.isNotEmpty(catalogCheckPointMap)) {
            for (String catalog : catalogCheckPointMap.keySet()) {
                List<String> anchorPointList = new ArrayList<>();
                List<String> checkPointList = catalogCheckPointMap.get(catalog);
                if (CollectionUtil.isNotEmpty(checkPointList)) {
                    for (String checkPoint : checkPointList) {
                        List<String> list = checkPointAnchorPointMap.get(catalog + "|" + checkPoint);
                        if (CollectionUtil.isNotEmpty(list)) {
                            anchorPointList.addAll(list);
                        }
                    }
                }
                catalogAnchorPointAvailableMap.put(catalog, anchorPointList);
            }
        }
    }


    public ApiResponse process(ApiRequest apiRequest) {
        String traceId = apiRequest.getTraceId();
        SceneInfo sceneInfo = apiRequest.getScene(SceneInfo.class);

        if (sceneInfo == null) {
            throw new ParamNotExistException("SceneInfo");
        }
        if (StringUtils.isBlank(sceneInfo.getFunctionCode())) {
            throw new ParamNotExistException("SceneInfo.functionCode");
        }

        ApiResponse apiResponse = null;
        String funcCode = sceneInfo.getFunctionCode();
        try {
            if (StringUtils.equals(funcCode, "PRIMARY_MIGRATION_BEHAVIOR")) {
                // 处理小学精准学学情同步
                apiResponse = processBehavior(apiRequest);
            } else if (StringUtils.equals(funcCode, "PRIMARY_MIGRATION_MASTERY")) {
                //  处理小学精准学章节掌握度同步
                apiResponse = processMastery(apiRequest);
            } else {
                throw new SkylabException(ApiErrorCode.ERROR.getCode(), CharSequenceUtil.format("{}不是小学精准学请求", funcCode));
            }
        } catch (Exception e) {
            log.error("小学精准学同步数据服务错误：" + e.getMessage(), e);
            apiResponse = new ApiResponse(new ApiResponseHeader(ApiErrorCode.ERROR.getCode(), ApiErrorCode.ERROR.getMessage()));
            apiResponse.setTraceId(traceId);
        }
        return apiResponse;
    }


    /**
     * 处理小学精准学学情同步
     *
     * @param apiRequest
     * @return
     */
    public ApiResponse processBehavior(ApiRequest apiRequest) {
        SceneInfo sceneInfo = apiRequest.getScene(SceneInfo.class);
        //来源
        String ext1 = sceneInfo.getExt1();
        String graphVersion = apiRequest.getScene().getString("graphVersion");
        PrimaryMigrationBehaviorParam primaryMigrationBehaviorParam = JSON.to(PrimaryMigrationBehaviorParam.class, apiRequest.getPayload());
        List<PrimaryMigrationBehavior> items = primaryMigrationBehaviorParam.getItems();
        Instant now = Instant.now();
        Instant beginDate = DateUtil.beginOfDay(DateUtil.yesterday()).toInstant();
        for (PrimaryMigrationBehavior primaryMigrationBehavior : items) {
            handleOnePrimaryMigrationBehavior(graphVersion, now, beginDate, primaryMigrationBehavior, ext1);
        }
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setTraceId(apiRequest.getTraceId());
        return apiResponse;
    }

    private void handleOnePrimaryMigrationBehavior(String graphVersion, Instant now, Instant beginDate, PrimaryMigrationBehavior primaryMigrationBehavior, String ext1) {
        String userId = primaryMigrationBehavior.getUserId();
        String nodeId = primaryMigrationBehavior.getNodeId();
        String resNodeId = primaryMigrationBehavior.getResNodeId();
        Double standardScore = primaryMigrationBehavior.getStandardScore();
        Double score = primaryMigrationBehavior.getScore();
        Double scoreRatio = repairScoreRatio(primaryMigrationBehavior);
        Instant createTime = Instant.ofEpochMilli(primaryMigrationBehavior.getCreateTime());
        StudyLogRecordEntity studyLogRecordEntity = new StudyLogRecordEntity();
        String traceId = UUID.randomUUID().toString();
        String unique = userId + "|" + nodeId + "|" + resNodeId;
        String id = DigestUtil.md5Hex(unique);
        studyLogRecordEntity.setId(id);
        studyLogRecordEntity.setUserId(userId);
        studyLogRecordEntity.setTraceId(traceId);
        // 图谱版本取配置里面的默认值版本
        studyLogRecordEntity.setGraphVersion(graphVersion);
        studyLogRecordEntity.setSubjectCode("02");
        studyLogRecordEntity.setPhaseCode("03");
        studyLogRecordEntity.setBizCode(BizCodeEnum.ZSY_XXJ);
        studyLogRecordEntity.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        studyLogRecordEntity.setBizAction(BizActionEnum.NONE);
        studyLogRecordEntity.setNodeId(nodeId);
        studyLogRecordEntity.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        studyLogRecordEntity.setResNodeId(resNodeId);
        studyLogRecordEntity.setResNodeType(ResourceTypeEnum.TOPIC);
        studyLogRecordEntity.setRoundIndex(1);
        studyLogRecordEntity.setTimeCost(1);
        studyLogRecordEntity.setStandardScore(standardScore);
        studyLogRecordEntity.setScore(score);
        studyLogRecordEntity.setScoreRatio(scoreRatio);
        studyLogRecordEntity.setFeedbackTime(createTime);
        // 如果设置了id，@CreatedDate注解会失效，这里需要手动设置
        studyLogRecordEntity.setCreateTime(createTime);
        studyLogRecordEntity.setFrom(ext1);
        // 只保存近两天的学情
        if (createTime.isAfter(beginDate) && createTime.isBefore(now)) {
            log.debug("saveStudyLogRecord id is {}", id);
            DataHub.getStudyLogService().saveStudyLogRecord(traceId, studyLogRecordEntity);
        }
        // 给默认值
        studyLogRecordEntity.setSchoolId("bg-1");
        studyLogRecordEntity.setClassId("bg-1");
        studyLogRecordEntity.setGradeCode("bg-1");
        studyLogRecordEntity.setRoundId("bg-1");
        studyLogRecordEntity.setFuncCode("bg-1");
        studyLogRecordEntity.setBookCode("bg-1");
        // 投递至odeon
        log.debug("send to odeon id is {}", id);
        kafkaTemplate.send(primaryMigrationProperties.getStudyLogTopic(), JSON.toJSONString(studyLogRecordEntity));
    }

    /**
     * 修复得分率
     *
     * @param primaryMigrationBehavior
     * @return
     */
    private double repairScoreRatio(PrimaryMigrationBehavior primaryMigrationBehavior) {
        if (primaryMigrationBehavior.getStandardScore() == null || primaryMigrationBehavior.getStandardScore() == 0) {
            return 0;
        }
        double scoreRatio = primaryMigrationBehavior.getScore() / primaryMigrationBehavior.getStandardScore();
        if (scoreRatio > 1) {
            log.error("小学答题记录得分率超标,primaryMigrationBehavior = {}", JSON.toJSONString(primaryMigrationBehavior));
        }
        return Math.min(scoreRatio, 1);
    }

    /**
     * 处理小学精准学章节掌握度同步
     *
     * @param apiRequest
     * @return
     */
    public ApiResponse processMastery(ApiRequest apiRequest) {
        PrimaryMigrationMasteryParam primaryMigrationMasteryParam = JSON.to(PrimaryMigrationMasteryParam.class, apiRequest.getPayload());
        String graphVersion = primaryMigrationProperties.getGraphVersion();
        processPrimaryMigrationMasteryParam(primaryMigrationMasteryParam, graphVersion, true);
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setTraceId(apiRequest.getTraceId());
        return apiResponse;
    }

    @Deprecated
    public long readStudyLogFile() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        AtomicLong atomicLong = new AtomicLong();
        Instant now = Instant.now();
        Instant beginDate = DateUtil.beginOfDay(DateUtil.yesterday()).toInstant();
        String path = primaryMigrationProperties.getStudyLogFilePath();
        log.debug("readStudyLogFile path is {}", path);
        String graphVersion = primaryMigrationProperties.getGraphVersion();
        File file = FileUtil.newFile(path);
        File[] files = file.listFiles();
        log.info("readStudyLogFile files size = {}", files.length);
        List<Future> futureList = new ArrayList<>();
        for (File subFile : files) {
            Future future = executorService.submit(() -> {
                try {
                    long totalCount = handleOneStudyLogFile(now, beginDate, graphVersion, subFile);
                    atomicLong.addAndGet(totalCount);
                } catch (Exception e) {
                    log.error("handleOneStudyLogFile failed ，file is {}", subFile.getAbsolutePath(), e);
                }
            });
            futureList.add(future);
        }
        for (Future f : futureList) {
            try {
                f.get();
            } catch (Exception e) {
                log.error("readStudyLogFile f.get() error ", e);
            }
        }
        log.info("readStudyLogFile totalCount is {}", atomicLong);
        stopWatch.stop();
        log.info("readStudyLogFile cost {}", stopWatch);
        return atomicLong.get();
    }

    @Deprecated
    private long handleOneStudyLogFile(Instant now, Instant beginDate, String graphVersion, File subFile) {
        log.info("handleOneStudyLogFile subFile is {}", subFile.getAbsolutePath());
        List<String> strings = FileUtil.readUtf8Lines(subFile);
        long totalCount = strings.size();
        for (int i = 0; i < strings.size(); i++) {
            if ((i + 1) % 100000 == 0) {
                // 提升效率，每十万条数据打印一次
                log.info("handleOneStudyLogFile process is {}/{}", i + 1, totalCount);
            }
            String line = strings.get(i);
            List<String> split = StrUtil.split(line, "\t");
            if (CollUtil.isNotEmpty(split)) {
                // 增加字段校验
                if (!studyLogFileLineIsAvailable(line)) {
                    log.info("handleOneStudyLogFile subFile is {},error line index is {}", subFile.getAbsolutePath(), i);
                    continue;
                }
                PrimaryMigrationBehavior primaryMigrationBehavior = getPrimaryMigrationBehavior(split);
                handleOnePrimaryMigrationBehavior(graphVersion, now, beginDate, primaryMigrationBehavior, null);
            }
        }
        log.info("handleOneStudyLogFile subFile is {},totalCount is {}", subFile.getAbsolutePath(), totalCount);
        return totalCount;
    }

    @Deprecated
    private PrimaryMigrationBehavior getPrimaryMigrationBehavior(List<String> split) {
        PrimaryMigrationBehavior primaryMigrationBehavior = new PrimaryMigrationBehavior();
        primaryMigrationBehavior.setUserId(split.get(0));
        primaryMigrationBehavior.setSubjectCode(split.get(1));
        primaryMigrationBehavior.setPhaseCode(split.get(2));
        primaryMigrationBehavior.setNodeId(split.get(3));
        primaryMigrationBehavior.setResNodeId(split.get(4));
        primaryMigrationBehavior.setScore(Double.valueOf(split.get(5)));
        primaryMigrationBehavior.setStandardScore(Double.valueOf(split.get(6)));
        primaryMigrationBehavior.setCreateTime(Long.valueOf(split.get(7)));
        return primaryMigrationBehavior;
    }

    /**
     * 检验数据
     *
     * @param line
     * @return
     */
    private boolean studyLogFileLineIsAvailable(String line) {
        return true;
    }

    @Deprecated
    public long readMasteryFile() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        AtomicLong atomicLong = new AtomicLong();
        String path = primaryMigrationProperties.getMasteryFilePath();
        String graphVersion = primaryMigrationProperties.getGraphVersion();
        File file = FileUtil.newFile(path);
        File[] files = file.listFiles();
        List<Future> futureList = new ArrayList<>();
        for (File subFile : files) {
            Future future = executorService.submit(() -> {
                try {
                    long totalCount = handleOneMasteryFile(graphVersion, subFile);
                    atomicLong.addAndGet(totalCount);
                } catch (Exception e) {
                    log.error("readMasteryFile failed ，file is {}", subFile.getAbsolutePath(), e);
                }
            });
            futureList.add(future);
        }
        for (Future f : futureList) {
            try {
                f.get();
            } catch (Exception e) {
                log.error("readMasteryFile f.get() error ", e);
            }
        }
        log.info("readMasteryFile totalCount is {}", atomicLong);
        stopWatch.stop();
        log.info("readMasteryFile cost {}", stopWatch);
        return atomicLong.get();
    }

    @Deprecated
    private long handleOneMasteryFile(String graphVersion, File subFile) {
        log.info("handleOneMasteryFile subFile is {}", subFile.getAbsolutePath());
        List<String> strings = FileUtil.readUtf8Lines(subFile);
        long totalCount = strings.size();
        // 初始化primaryMigrationMasteryParam
        PrimaryMigrationMasteryParam primaryMigrationMasteryParam = new PrimaryMigrationMasteryParam();
        List<PrimaryMigrationMastery> primaryMigrationMasteryList = new ArrayList<>();
        primaryMigrationMasteryParam.setItems(primaryMigrationMasteryList);
        for (int i = 0; i < strings.size(); i++) {
            if ((i + 1) % 100000 == 0) {
                // 提升效率，每十万条数据打印一次
                log.info("handleOneMasteryFile process is {}/{}", i + 1, totalCount);
            }
            String line = strings.get(i);
            List<String> split = StrUtil.split(line, "\t");
            if (CollUtil.isNotEmpty(split)) {
                // 增加字段校验
                if (!masteryFileLineIsAvailable(line)) {
                    log.info("handleOneMasteryFile subFile is {},error line index is {}", subFile.getAbsolutePath(), i);
                    continue;
                }
                String userId = split.get(0);
                String catalogId = split.get(2);
                if (i == 0) {
                    // 第一行
                    buildPrimaryMigrationMasteryParamByArrays(primaryMigrationMasteryParam, split, userId, catalogId);

                    PrimaryMigrationMastery primaryMigrationMastery = new PrimaryMigrationMastery();
                    buildPrimaryMigrationMasteryByArrays(split, primaryMigrationMastery);
                    primaryMigrationMasteryList.add(primaryMigrationMastery);
                    if (1 == totalCount) {
                        // 如果总行数只有一行
                        // 处理primaryMigrationMasteryParam
                        processPrimaryMigrationMasteryParam(primaryMigrationMasteryParam, graphVersion, false);
                    }
                } else {
                    if (userId.equals(primaryMigrationMasteryParam.getUserId()) && catalogId.equals(primaryMigrationMasteryParam.getCatalogId())) {
                        // 和上一行的userId和catalogId一样，就认为是同一组
                        PrimaryMigrationMastery primaryMigrationMastery = new PrimaryMigrationMastery();
                        buildPrimaryMigrationMasteryByArrays(split, primaryMigrationMastery);
                        primaryMigrationMasteryList.add(primaryMigrationMastery);
                    } else {
                        // 处理primaryMigrationMasteryParam
                        processPrimaryMigrationMasteryParam(primaryMigrationMasteryParam, graphVersion, false);
                        // 处理完重置primaryMigrationMasteryParam
                        primaryMigrationMasteryParam = new PrimaryMigrationMasteryParam();
                        primaryMigrationMasteryList = new ArrayList<>();
                        primaryMigrationMasteryParam.setItems(primaryMigrationMasteryList);

                        buildPrimaryMigrationMasteryParamByArrays(primaryMigrationMasteryParam, split, userId, catalogId);

                        PrimaryMigrationMastery primaryMigrationMastery = new PrimaryMigrationMastery();
                        buildPrimaryMigrationMasteryByArrays(split, primaryMigrationMastery);
                        primaryMigrationMasteryList.add(primaryMigrationMastery);
                    }
                    if (i == totalCount - 1) {
                        // 如果是最后一行
                        // 处理primaryMigrationMasteryParam
                        processPrimaryMigrationMasteryParam(primaryMigrationMasteryParam, graphVersion, false);
                    }
                }
            }
        }
        log.info("handleOneMasteryFile subFile is {},totalCount is {}", subFile.getAbsolutePath(), totalCount);
        return totalCount;
    }

    private boolean masteryFileLineIsAvailable(String line) {
        return true;
    }

    @Deprecated
    private void buildPrimaryMigrationMasteryParamByArrays(PrimaryMigrationMasteryParam primaryMigrationMasteryParam, List<String> split, String userId, String catalogId) {
        primaryMigrationMasteryParam.setUserId(userId);
        primaryMigrationMasteryParam.setSubjectCode(split.get(3));
        primaryMigrationMasteryParam.setPhaseCode(split.get(4));
        primaryMigrationMasteryParam.setBookCode(split.get(1));
        primaryMigrationMasteryParam.setCatalogId(catalogId);
    }

    @Deprecated
    private void buildPrimaryMigrationMasteryByArrays(List<String> split, PrimaryMigrationMastery primaryMigrationMastery) {
        primaryMigrationMastery.setNodeId(split.get(6));
        primaryMigrationMastery.setReal(Double.valueOf(split.get(7)));
        primaryMigrationMastery.setPredict(Double.valueOf(split.get(8)));
    }

    /**
     * 处理构造好的PrimaryMigrationMasteryParam
     *
     * @param primaryMigrationMasteryParam
     */
    private void processPrimaryMigrationMasteryParam(PrimaryMigrationMasteryParam primaryMigrationMasteryParam, String graphVersion, boolean isRealTimeFlag) {
        String userId = primaryMigrationMasteryParam.getUserId();
        String catalogId = primaryMigrationMasteryParam.getCatalogId();
        Instant now = Instant.now();
        List<PrimaryMigrationMastery> primaryMigrationMasteryList = primaryMigrationMasteryParam.getItems();
        processOneCatalogMastery(graphVersion, isRealTimeFlag, userId, catalogId, primaryMigrationMasteryList, now);
        copyToPeriodMastery(graphVersion, isRealTimeFlag, userId, catalogId, now, primaryMigrationMasteryList);
    }

    /**
     * 复制到课时画像
     *
     * @param graphVersion
     * @param isRealTimeFlag
     * @param userId
     * @param catalogId
     * @param now
     * @param primaryMigrationMasteryList
     */
    private void copyToPeriodMastery(String graphVersion, boolean isRealTimeFlag, String userId, String catalogId, Instant now, List<PrimaryMigrationMastery> primaryMigrationMasteryList) {
        List<String> split = StrUtil.split(catalogId, "_");
        if (split.size() < 2) {
            return;
        }
        String bookCode = split.get(0) + "_" + split.get(1);
        //最末级
        if (bookTreeMap.containsKey(bookCode) && !needCopyFinalCataSet.contains(catalogId)) {
            for (String cata : needCopyFinalCataSet) {
                if (cata.startsWith(catalogId)) {
                    // 复制可用的锚点画像
                    List<PrimaryMigrationMastery> periodAvailableAnchorPointMasteryList = buildPeriodAvailableAnchorPointMasteryList(primaryMigrationMasteryList, cata);
                    if (CollectionUtil.isNotEmpty(periodAvailableAnchorPointMasteryList)) {
                        processOneCatalogMastery(graphVersion, isRealTimeFlag, userId, cata, periodAvailableAnchorPointMasteryList, now);
                    }
                }
            }
        }
    }

    /**
     * 复制可用的锚点画像
     *
     * @param primaryMigrationMasteryList
     * @param period
     * @return
     */
    private List<PrimaryMigrationMastery> buildPeriodAvailableAnchorPointMasteryList
    (List<PrimaryMigrationMastery> primaryMigrationMasteryList, String period) {
        List<PrimaryMigrationMastery> periodAvailableAnchorPointMasteryList = new ArrayList<>();
        List<String> anchorPointList = catalogAnchorPointAvailableMap.get(period);
        if (CollectionUtil.isNotEmpty(anchorPointList)) {
            for (PrimaryMigrationMastery primaryMigrationMastery : primaryMigrationMasteryList) {
                String nodeId = primaryMigrationMastery.getNodeId();
                if (anchorPointList.contains(nodeId)) {
                    periodAvailableAnchorPointMasteryList.add(primaryMigrationMastery);
                }
            }
        }
        return periodAvailableAnchorPointMasteryList;
    }

    /**
     * 处理单章节画像
     *
     * @param graphVersion
     * @param isRealTimeFlag
     * @param userId
     * @param catalogId
     * @param primaryMigrationMasteryList
     * @param now
     */
    private void processOneCatalogMastery(String graphVersion, boolean isRealTimeFlag, String userId,
                                          String catalogId, List<PrimaryMigrationMastery> primaryMigrationMasteryList, Instant now) {
        List<UserMasteryRecord> userMasteryRecordList = new ArrayList<>();
        List<String> split = StrUtil.split(catalogId, "_");
        if (split.size() < 2) {
            return;
        }
        String bookCode = split.get(0) + "_" + split.get(1);
        CatalogTypeEnum catalogTypeEnum = getCatalogTypeByCatalogId(catalogId, split);
        // key: 锚点id ， value: 对应锚点融合画像值
        HashMap<String, Double> anchorPointMasteryMap = new HashMap<>();
        for (int i = 0; i < primaryMigrationMasteryList.size(); i++) {
            PrimaryMigrationMastery primaryMigrationMastery = primaryMigrationMasteryList.get(i);
            UserMasteryRecord anchorPointMasteryRecord = processAnchorPointMastery(primaryMigrationMastery, userId, catalogId, graphVersion, bookCode, catalogTypeEnum, now);
            anchorPointMasteryMap.put(anchorPointMasteryRecord.getNodeId(), anchorPointMasteryRecord.getFusion());
            userMasteryRecordList.add(anchorPointMasteryRecord);
        }
        log.info("userId is {},catalogId is {},anchorPointMasteryMap is {}", userId, catalogId, anchorPointMasteryMap);
        List<String> checkPointList = catalogCheckPointMap.get(catalogId);
        if (CollectionUtil.isNotEmpty(checkPointList)) {
            String label = catalogLabelMap.get(catalogId);
            List<UserMasteryRecord> checkPointMasteryList = new ArrayList<>();
            for (String checkPoint : checkPointList) {
                UserMasteryRecord checkPointMasteryRecord = processCheckPointMastery(userId, catalogId, graphVersion, bookCode, anchorPointMasteryMap, label, checkPoint, now);
                if (null == checkPointMasteryRecord) {
                    continue;
                }
                checkPointMasteryList.add(checkPointMasteryRecord);
            }
            userMasteryRecordList.addAll(checkPointMasteryList);
        } else {
            log.info("catalog:{} 下无考点，跳过处理", catalogId);
        }
        DataHub.getMasterService().savePrimaryMigrationUserMasteryRecordAndGet(userId, userMasteryRecordList);
    }

    /**
     * 处理锚点画像
     *
     * @param primaryMigrationMastery
     * @param userId
     * @param catalogId
     * @param graphVersion
     * @param bookCode
     * @param catalogTypeEnum
     * @param now
     * @return
     */
    private UserMasteryRecord processAnchorPointMastery(PrimaryMigrationMastery
                                                                primaryMigrationMastery, String userId, String catalogId, String graphVersion, String
                                                                bookCode, CatalogTypeEnum catalogTypeEnum, Instant now) {
        String nodeId = primaryMigrationMastery.getNodeId();
        String unique = userId + "|" + catalogId + "|" + nodeId;
        String id = DigestUtil.md5Hex(unique);
        UserMasteryRecord userMasteryRecord = buildBaseUserMasteryRecord(userId, catalogId, graphVersion, bookCode, id, now);
        userMasteryRecord.setCatalogType(catalogTypeEnum);
        userMasteryRecord.setNodeId(nodeId);
        userMasteryRecord.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        // 设置默认值
        Double fusion = -1D;
        Double real = -1D;
        Double predict = -1D;
        // 获取学习机侧给过来的值
        Double primaryReal = primaryMigrationMastery.getReal();
        Double primaryPredict = primaryMigrationMastery.getPredict();
        if (bookTreeMap.containsKey(bookCode)) {
            // 如果是给定书本范围内的，画像=-100或者画像>=0，画像=画像除以100后截取两位有效位
            if (primaryReal == -100 || primaryReal >= 0) {
                primaryReal = primaryReal / 100;
            } else {
                primaryReal = 0D;
            }
            if (primaryPredict == -100 || primaryPredict >= 0) {
                primaryPredict = primaryPredict / 100;
            } else {
                primaryPredict = 0D;
            }
        }
        // 计算锚点画像值
        // 课外锚点
        // 锚点融合画像=锚点真实画像=锚点预测画像=-1
        if (!AnchorPointType.OUTSIDE.equals(anchorPointTypeMap.get(nodeId))) {
            // 如果是非课内/课外锚点 ，则按照课内逻辑处理
            // 课内锚点
            if (primaryReal >= 0 || primaryPredict >= 0) {
                // 锚点有历史真实画像或者历史预测画像【即历史真实画像>=0或者历史预测画像>=0】：
                // 锚点真实画像=max(锚点历史真实画像,-1)
                // 锚点预测画像=max(锚点历史预测画像,-1)
                // 锚点融合画像=max(锚点真实画像, 锚点预测画像)
                // 其中【历史真实画像】和【历史预测画像】是指学习机侧给过来的章节掌握度里面的真实掌握度和预测掌握度
                real = Math.max(primaryReal, -1);
                predict = Math.max(primaryPredict, -1);
                fusion = Math.max(real, predict);
            } else {
                // 锚点融合画像=锚点真实画像=锚点预测画像=-1
            }
        }
        userMasteryRecord.setFusion(fusion);
        userMasteryRecord.setReal(real);
        userMasteryRecord.setPredict(predict);
        if (bookTreeMap.containsKey(bookCode)) {
            // 如果是给定书本范围内的,填充algo前缀字段
            userMasteryRecord.setAlgoFusion(userMasteryRecord.getFusion());
            userMasteryRecord.setAlgoReal(userMasteryRecord.getReal());
            userMasteryRecord.setAlgoPredict(userMasteryRecord.getPredict());
        }
        postProcessUserMasteryRecord(userMasteryRecord);
        return userMasteryRecord;
    }

    /**
     * UserMasteryRecord的后置处理
     *
     * @param userMasteryRecord
     */
    private void postProcessUserMasteryRecord(UserMasteryRecord userMasteryRecord) {
        if (-1 == userMasteryRecord.getReal()) {
            // 真实画像为-1，用预测画像；否则用融合画像
            userMasteryRecord.setMasteryScore(userMasteryRecord.getPredict());
            // 真实画像为-1，用PREDICT；否则用REAL
            userMasteryRecord.setMasteryType("PREDICT");
        } else {
            userMasteryRecord.setMasteryScore(userMasteryRecord.getFusion());
            userMasteryRecord.setMasteryType("REAL");
        }
    }

    /**
     * 处理考点画像
     *
     * @param userId
     * @param catalogId
     * @param graphVersion
     * @param bookCode
     * @param anchorPointMasteryMap
     * @param label
     * @param checkPoint
     * @param now
     * @return
     */
    private UserMasteryRecord processCheckPointMastery(String userId, String catalogId, String
            graphVersion, String bookCode, HashMap<String, Double> anchorPointMasteryMap, String
                                                               label, String checkPoint, Instant now) {
        String key = catalogId + "|" + checkPoint;
        List<String> anchorPointList = checkPointAnchorPointMap.get(key);
        if (CollectionUtil.isEmpty(anchorPointList)) {
            log.info("checkPoint:{} 图谱下无锚点，跳过处理", checkPoint);
            return null;
        }
        Double checkPointMasteryValue = calCheckPointMasteryValue(anchorPointMasteryMap, checkPoint, anchorPointList);
        log.debug("userId is {},catalogId is {},checkPoint is {},masteryValue is {}", userId, catalogId, checkPoint, checkPointMasteryValue);
        if (checkPointMasteryValue == null) {
            return null;
        }
        String unique = userId + "|" + catalogId + "|" + checkPoint;
        String id = DigestUtil.md5Hex(unique);
        UserMasteryRecord userMasteryRecord = buildBaseUserMasteryRecord(userId, catalogId, graphVersion, bookCode, id, now);
        userMasteryRecord.setCatalogType(CatalogTypeEnum.parse(label));
        userMasteryRecord.setNodeId(checkPoint);
        userMasteryRecord.setNodeType(NodeTypeEnum.CHECK_POINT);
        userMasteryRecord.setFusion(checkPointMasteryValue);
        userMasteryRecord.setReal(checkPointMasteryValue);
        userMasteryRecord.setPredict(checkPointMasteryValue);
        userMasteryRecord.setAlgoFusion(userMasteryRecord.getFusion());
        userMasteryRecord.setAlgoReal(userMasteryRecord.getReal());
        userMasteryRecord.setAlgoPredict(userMasteryRecord.getPredict());
        postProcessUserMasteryRecord(userMasteryRecord);
        return userMasteryRecord;
    }

    /**
     * 构造基础的UserMasteryRecord属性
     *
     * @param userId
     * @param catalogId
     * @param graphVersion
     * @param bookCode
     * @param id
     * @return
     */
    private UserMasteryRecord buildBaseUserMasteryRecord(String userId, String
            catalogId, String graphVersion, String bookCode, String id, Instant now) {
        UserMasteryRecord userMasteryRecord = new UserMasteryRecord();
        userMasteryRecord.setId(id);
        userMasteryRecord.setUserId(userId);
        userMasteryRecord.setBizCode(BizCodeEnum.ZSY_XXJ);
        userMasteryRecord.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        userMasteryRecord.setGraphVersion(graphVersion);
        // 这个学科学段暂时写死，小学  数学
        userMasteryRecord.setSubjectCode("02");
        userMasteryRecord.setPhaseCode("03");
        userMasteryRecord.setBookCode(bookCode);
        userMasteryRecord.setCatalogId(catalogId);
        userMasteryRecord.setCreateTime(now);
        userMasteryRecord.setUpdateTime(now);
        return userMasteryRecord;
    }

    /**
     * 计算考点画像值
     *
     * @param anchorPointMasteryMap
     * @param checkPoint
     * @param anchorPointList
     * @return
     */
    private Double calCheckPointMasteryValue(HashMap<String, Double> anchorPointMasteryMap, String
            checkPoint, List<String> anchorPointList) {
        Double checkPointMasteryValue = -1D;
        if (CheckPointType.OUTSIDE.equals(checkPointTypeMap.get(checkPoint))) {
            // 如果是课外考点
            // 考点融合画像=考点真实画像=考点预测画像=-1
        } else if (CheckPointType.INSIDE.equals(checkPointTypeMap.get(checkPoint))) {
            // 如果是课内考点
            // 初始化 不存在融合画像=-1的锚点
            boolean existFusionNegative = false;
            boolean existNonGreenPoint = false;
            Double anchorPointMasterySumValue = 0D;
            for (String anchorPoint : anchorPointList) {
                Double anchorPointMastery = anchorPointMasteryMap.get(anchorPoint);
                if (null == anchorPointMastery || -1 == anchorPointMastery) {
                    // 如果存在没有画像数据的锚点，考点的画像也是-1
                    existFusionNegative = true;
                    break;
                }

                if (anchorPointMastery.compareTo(PointMastery.MIN_LIGHT_GREEN_POINT) < 0){
                    existNonGreenPoint = true;
                }
                anchorPointMasterySumValue += anchorPointMastery;
            }
            // 课内考点下存在融合画像=-1的锚点
            // 考点融合画像=考点真实画像=考点预测画像=-1
            if (!existFusionNegative) {
                // 课内考点下不存在融合画像=-1的锚点
                // 考点融合画像=考点真实画像=考点预测画像=所有锚点画像平均值，如果算出小数值，保留小数点后两位
                checkPointMasteryValue = new BigDecimal(anchorPointMasterySumValue / anchorPointList.size()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

                // 如果画像中存在非绿点，则最终画像不能出现深绿点
                if (existNonGreenPoint) {
                    checkPointMasteryValue = checkPointMasteryValue.compareTo(PointMastery.MIN_GREEN_POINT) >= 0 ? PointMastery.MAX_LIGHT_GREEN_POINT : checkPointMasteryValue;
                }
            }
        } else {
            // 如果是非课内/课外考点 ，则不做处理
            return null;
        }
        return checkPointMasteryValue;
    }

    /**
     * 通过章节id获取章节type
     *
     * @param catalogId
     * @return
     */
    private CatalogTypeEnum getCatalogTypeByCatalogId(String catalogId, List<String> split) {
        if (catalogId.contains("period")) {
            return CatalogTypeEnum.PERIOD;
        }
        if (split.size() == 3) {
            return CatalogTypeEnum.UNIT;
        }
        if (split.size() == 4) {
            return CatalogTypeEnum.COURSE;
        }
        if (split.size() == 5) {
            return CatalogTypeEnum.L2COURSE;
        }
        if (split.size() == 6) {
            return CatalogTypeEnum.L3COURSE;
        }
        return CatalogTypeEnum.COURSE;
    }

    /**
     * k 锚点
     * v 最末级目录
     *
     * @param list
     * @return
     */
    private Map<String, List<String>> findFinalUpperLayer(Set list) {

        Map<String, List<String>> anchorMaps = new HashMap<>();
        for (Object o : list) {
            List<String> cata = new ArrayList<>();
            UpperLayerQuery query = new UpperLayerQuery().setTraceId(IdUtil.fastSimpleUUID()).setGraphVersion(primaryMigrationProperties.getGraphVersion()).setTargetIds(Lists.newArrayList(o.toString()))
                    .setEdgeLabels(Lists.newArrayList(
                            new UpperLayerQuery.EdgeLabel().setSource("UNIT").setTarget("ANCHOR_POINT"),
                            new UpperLayerQuery.EdgeLabel().setSource("COURSE").setTarget("ANCHOR_POINT"),
                            new UpperLayerQuery.EdgeLabel().setSource("L2COURSE").setTarget("ANCHOR_POINT"),
                            new UpperLayerQuery.EdgeLabel().setSource("PERIOD").setTarget("ANCHOR_POINT")));

            GraphData graphData = graphService.findUpperLayer(query);
            for (GraphData.GraphVertex vertex : graphData.getVertices()) {
                if (!"ANCHOR_POINT".equals(vertex.getLabel())) {
                    cata.add(vertex.getId());
                }
            }
            List<String> newCata = new ArrayList<>(parseCollection(cata));
            log.info("anchor=" + o + ";original=" + cata + ";newCata= " + newCata);
            if (!anchorMaps.containsKey(o.toString())) {
                anchorMaps.put(o.toString(), new ArrayList<>());
            }
            anchorMaps.get(o.toString()).addAll(newCata);
        }
        return anchorMaps;
    }

    /**
     * 获取最末级
     *
     * @param input
     * @return
     */
    public static List<String> parseCollection(List<String> input) {
        Set<String> parsedStrings = new HashSet<>(input); // 用于存储解析后的字符串，使用Set来去重
        for (String str : input) {
            Iterator<String> iterator = parsedStrings.iterator();
            while (iterator.hasNext()) {
                String ps = iterator.next();
                if (!ps.equals(str)) {
                    if (ps.split("_").length != str.split("_").length && str.contains(ps)) {
                        iterator.remove();
                    }
                }
            }
        }
        // 存储最终结果的列表
        return new ArrayList<>(parsedStrings);
    }
}
