package com.iflytek.skylab.service.primary.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.service.primary.service.PrimaryMigrationService;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import com.iflytek.skyline.resource.annotation.EnableSkylineResource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;
import skynet.boot.annotation.EnableSkynetLogging;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/4/6 10:56
 * @description
 */
@EnableSkynetLogging
@Configuration(proxyBeanMethods = false)
@EnableSkylineBrave
@EnableSkylineResource
@EnableDataHub
public class PrimaryMigrationConfiguration {

    @Bean
    public PrimaryMigrationService PrimaryMigrationService(ExecutorService executorService, PrimaryMigrationProperties primaryMigrationProperties, KafkaTemplate<Object, Object> kafkaTemplate, GraphService graphService) {
        return new PrimaryMigrationService(executorService, primaryMigrationProperties, kafkaTemplate,graphService);
    }

    @Bean
    @ConfigurationProperties("skylab.primary.migration.properties")
    public PrimaryMigrationProperties PrimaryMigrationProperties() {
        return new PrimaryMigrationProperties();
    }

    @Bean
    public ExecutorService executorService(PrimaryMigrationProperties PrimaryMigrationProperties) {
        return new ThreadPoolExecutor(PrimaryMigrationProperties.getCorePoolSize(), PrimaryMigrationProperties.getMaximumPoolSize(), PrimaryMigrationProperties.getKeepAliveMilliseconds(), TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(PrimaryMigrationProperties.getQueueCapacity()), new ThreadFactoryBuilder().setNameFormat("primary-migration-pool-%d").build(), new ThreadPoolExecutor.AbortPolicy());
    }
}
