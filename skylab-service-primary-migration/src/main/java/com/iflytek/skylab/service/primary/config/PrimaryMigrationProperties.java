package com.iflytek.skylab.service.primary.config;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/4/6 10:57
 * @description
 */
@Getter
@Setter
public class PrimaryMigrationProperties {

    /**
     * 核心线程数
     */
    private Integer corePoolSize = 1;

    /**
     * 最大线程数
     */
    private Integer maximumPoolSize = 1;

    /**
     * 空闲非核心线程存活时间(ms)
     */
    private Long keepAliveMilliseconds = 0L;

    /**
     * 阻塞队列容量
     */
    private Integer queueCapacity = 1024;

    /**
     * 小学精准学学情文件路劲
     */
    private String studyLogFilePath = "";

    /**
     * 小学精准学章节掌握度文件路劲
     */
    private String masteryFilePath = "";

    /**
     * 默认图谱版本，备注：每一次上线图谱的时候，需要更新
     */
    private String graphVersion = "";

    /**
     * 需要治理的书本code，用逗号分隔
     */
    private String books = "";

    /**
     * 默认小学精准学学情Topic
     */
    private String studyLogTopic  = "";

}
