package com.iflytek.skylab.service.primary.controller;

import com.iflytek.skylab.service.primary.service.PrimaryMigrationService;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.common.api.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.logging.LoggingCost;

/**
 * <AUTHOR>
 * @date 2023/4/6 10:51
 * @description
 */
@Slf4j
@Api(tags = "小学精准学数据迁移服务")
@RestController
@RequestMapping("/skylab/api/v1/primaryMigration")
@EnableSkynetSwagger2
public class PrimaryMigrationController {

    @Autowired
    private PrimaryMigrationService primaryMigrationService;

    @SkylineTraceStarter(isSync4Request = false)
    @ApiOperation(value = "处理小学精准学上报的数据")
    @LoggingCost
    @PostMapping("/process")
    public ApiResponse process(@RequestBody ApiRequest apiRequest) {
        if (log.isDebugEnabled()) {
            log.debug("PrimaryMigrationController apiRequest= {}", apiRequest);
        }
        return primaryMigrationService.process(apiRequest);
    }


    @ApiOperation(value = "处理小学精准学答题记录文件")
    @Deprecated
    public long readStudyLogFile() {
        return primaryMigrationService.readStudyLogFile();
    }


    @ApiOperation(value = "处理小学精准学章节掌握度文件")
    @Deprecated
    public long readMasteryFile() {
        return primaryMigrationService.readMasteryFile();
    }


}
