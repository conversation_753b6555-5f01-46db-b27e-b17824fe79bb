package com.iflytek.skylab.service.primary.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 10:51
 * @description 考点类型
 */
public class CheckPointType {

    /**
     * 私有构造函数，工具类，不可实例化
     */
    private CheckPointType() {
        throw new IllegalStateException("Constant class");
    }

    /**
     * 课内考点
     */
    public static final Integer INSIDE = -1;

    /**
     * 课外考点
     */
    public static final Integer OUTSIDE = 1;

    /**
     * 常规
     */
    public static final String CONVENTIONAL = "conventional";
    /**
     * 高分进阶
     */
    public static final String HIGH_SCORE_ADVANCED = "highScoreAdvanced";
    /**
     * 思维拓展
     */
    public static final String THINKING_EXPANSION = "thinkingExpansion";
    /**
     * 基础概念
     */
    public static final String FUNDAMENTAL = "fundamental";
    /**
     * 综合创新
     */
    public static final String INNOVATIVE = "innovative";
    /**
     * 综合提升
     */
    public static final String PENTAGRAM = "pentagram";

    /**
     * 课内考点类型
     *
     * @return
     */
    public static List<String> getInsideCheckPointType() {
        List<String> checkTypeList = new ArrayList<>();
        //基础概念
        checkTypeList.add(FUNDAMENTAL);
        //综合提升
        checkTypeList.add(PENTAGRAM);
        //高分进阶锚点
        checkTypeList.add(HIGH_SCORE_ADVANCED);
        return checkTypeList;
    }

    /**
     * 课外考点类型
     *
     * @return
     */
    public static List<String> getOutsideCheckPointType() {
        List<String> checkTypeList = new ArrayList<>();
        //思维拓展锚点
        checkTypeList.add(THINKING_EXPANSION);
        //综合创新
        checkTypeList.add(INNOVATIVE);
        return checkTypeList;
    }

}
