package com.iflytek.skylab.service.primary;

import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;
import skynet.boot.AppUtils;

/**
 * <AUTHOR>
 * @date 2023/4/6 10:48
 * @description 小学精准学数据迁移服务
 */
@SpringBootApplication
@EnableDataHub
public class PrimaryMigrationAppBoot {
    public static void main(String[] args) {
        AppUtils.run(PrimaryMigrationAppBoot.class, args);
    }
}