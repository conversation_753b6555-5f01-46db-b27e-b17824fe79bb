package com.iflytek.skylab.service.primary.param;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/30 10:43
 * @description 章节目录树
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CatalogTreeParam implements Serializable {
    private static final long serialVersionUID = 1L;

    public CatalogTreeParam(String code, String label) {
        this.code = code;
        this.label = label;
    }

    /**
     * 目录code
     */
    private String code;

    /**
     * 目录标签
     */
    private String label;

    /**
     * 子目录树
     */
    private List<CatalogTreeParam> children = new ArrayList<>();

}
