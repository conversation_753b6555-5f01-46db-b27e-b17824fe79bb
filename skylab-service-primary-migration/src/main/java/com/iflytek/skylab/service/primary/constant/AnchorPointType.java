package com.iflytek.skylab.service.primary.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/6 10:51
 * @description 锚点类型
 */
public class AnchorPointType {

    /**
     * 私有构造函数，工具类，不可实例化
     */
    private AnchorPointType() {
        throw new IllegalStateException("Constant class");
    }

    /**
     * 课内锚点
     */
    public static final Integer INSIDE = -1;

    /**
     * 课外锚点
     */
    public static final Integer OUTSIDE = 1;

    /**
     * 常规
     */
    public static final String CONVENTIONAL = "conventional";
    /**
     * 高分进阶
     */
    public static final String HIGH_SCORE_ADVANCED = "highScoreAdvanced";
    /**
     * 思维拓展
     */
    public static final String THINKING_EXPANSION = "thinkingExpansion";
    /**
     * 基础概念
     */
    public static final String FUNDAMENTAL = "fundamental";
    /**
     * 综合创新
     */
    public static final String INNOVATIVE = "innovative";
    /**
     * 综合提升
     */
    public static final String PENTAGRAM = "pentagram";

    /**
     * 课内锚点类型
     *
     * @return
     */
    public static List<String> getInsideAnchorPointType() {
        List<String> anchorTypeList = new ArrayList<>();
        //基础概念
        anchorTypeList.add(FUNDAMENTAL);
        //综合提升
        anchorTypeList.add(PENTAGRAM);
        //高分进阶锚点
        anchorTypeList.add(HIGH_SCORE_ADVANCED);
        return anchorTypeList;
    }

    /**
     * 课外锚点类型
     *
     * @return
     */
    public static List<String> getOutsideAnchorPointType() {
        List<String> anchorTypeList = new ArrayList<>();
        //思维拓展锚点
        anchorTypeList.add(THINKING_EXPANSION);
        //综合创新
        anchorTypeList.add(INNOVATIVE);
        return anchorTypeList;
    }

}
