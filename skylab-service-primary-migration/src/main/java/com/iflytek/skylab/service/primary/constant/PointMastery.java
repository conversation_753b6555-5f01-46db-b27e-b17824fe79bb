package com.iflytek.skylab.service.primary.constant;

public class PointMastery {

    private PointMastery() {
        throw new IllegalStateException("Constant class");
    }


    public static final Double MIN_GREEN_POINT = 0.80D;

    public static final Double MAX_LIGHT_GREEN_POINT = 0.79D;

    public static final Double MIN_LIGHT_GREEN_POINT = 0.70D;

    public static final Double MAX_YELLOW_POINT = 0.69D;

    public static final Double MIN_YELLOW_POINT = 0.60D;

    public static final Double MAX_RED_POINT = 0.59D;

    public static final Double MIN_RED_POINT = 0.00D;

    public static final Double MAX_GREY_POINT = -0.00D;
}
