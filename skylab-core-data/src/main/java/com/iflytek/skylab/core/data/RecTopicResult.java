package com.iflytek.skylab.core.data;

import com.iflytek.skylab.core.domain.StandardTopicLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/***
 * 点推题 功能 统一返回结果
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecTopicResult extends FuncResult {
    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return super.toJson();
    }

    /**
     * 题目id
     */
    private String topicId;

    /**
     * 推荐时间戳
     */
    // private Instant updateTime;
    private Date updateTime;

    /**
     * 关联点id
     */
    private String nodeId;

    /**
     * 是否可终止本次任务
     */
    private boolean terminationFlag;

    /**
     * 建议终止时最后一题是否做标识：true：建议终止时最后一题需要做，false：建议终止时最后一题不需要做。
     * <p>
     * 该标识位当前仅用于薄弱点推荐接口
     */
    private boolean doTerminateTopic;

    /**
     * 本次推荐流程-将推荐试题总题量
     */
    private int recTotalNum;

    /**
     * 预计作答时长(秒)
     */
    private long unitTimes;

    /**
     * 即将结束测评
     */
    private Boolean isAlmostTermination = false;

    /**
     * 试题标签
     */
    private List<StandardTopicLabel> standardTopicLabels;
}
