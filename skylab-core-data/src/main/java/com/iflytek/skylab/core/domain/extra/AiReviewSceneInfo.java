package com.iflytek.skylab.core.domain.extra;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/6 13:57
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class AiReviewSceneInfo extends SceneInfo {

    public AiReviewSceneInfo() {
        super();
        super.setStudyCode(StudyCodeEnum.AI_REVIEW);
    }

    /**
     * 学生水平 normal good excellent
     */
    @NotNull
    private StudentLevelEnum studentLevel;

    /**
     * 考试类型 week month mid final
     */
    @NotNull
    private ExamType examType;

    /**
     * 复习计划难度  easy normal hard
     */
    @NotNull
    private PlanDiffEnum planDiff;

    /**
     * 点类型
     */
    @NotNull
    private NodeTypeEnum pointType;

    /**
     * 用于获取画像时，传入点所在的目录
     */
    private List<String> catalogIds;

    /**
     * 用于题包推荐时，传入 点、章节关系
     */
    @JSONField(ordinal = 20)
    private Map<@NotBlank String, @NotBlank String> nodeCatalogMap;
}
