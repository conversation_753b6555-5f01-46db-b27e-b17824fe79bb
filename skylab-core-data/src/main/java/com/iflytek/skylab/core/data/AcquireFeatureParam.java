package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class AcquireFeatureParam extends FuncParam {
    public static final String FUNC_CODE = "FEA_ACQUIRE";
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 特征名
     */
    @NotEmpty
    @Valid
    private List<String> featureNames;


    /**
     * 查询范围
     */
    @JSONField(ordinal = 20)
    private List<String> params = new ArrayList<>();

}
