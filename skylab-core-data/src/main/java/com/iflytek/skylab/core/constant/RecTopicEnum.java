package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 测评类型
 *
 * <AUTHOR>
 * @date 2022/3/22 3:58 下午
 */
public enum RecTopicEnum implements EnumDescable {
    /**
     * 推题
     */
    REC_TOPIC("推题"),

    /**
     * 再推一题
     */
    REC_TOPIC_ONEMORE("再推一题"),

    /**
     * 错题本-变式题-推错题
     */
    REC_ERROR_TOPIC("推错题"),
    /**
     * 错题本-溯源点推题
     */
    REC_TOPIC_TRACE_POINT("溯源点推题"),

    /**
     * 測評-推易錯題
     */
    REC_PRONE_WRONG_TOPIC("易错题推题"),


    /**
     * 同步学-簇下练习-AI规划学推题
     */
    KC_REC_TOPIC_PLAN("知识簇-AI规划学推题"),
    KC_REC_TOPIC("知识簇-自主学点推题"),
    KC_REC_TOPIC_ONEMORE("知识簇-再推一题");

    @Getter
    private final String desc;

    RecTopicEnum(String desc) {
        this.desc = desc;
    }

    public static RecTopicEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (RecTopicEnum e : RecTopicEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
