package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.RecEvalEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/***
 * 测评推题 功能 请求参数
 *
 * 包含入门测、出门测场景
 * <AUTHOR>
 *
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RecEval4InOutParam extends FuncParam {
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return recEvalEnum.toString();
    }

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 推测类型：入门测 | 出门测
     */
    @JSONField(ordinal = 8)
    private RecEvalEnum recEvalEnum = RecEvalEnum.REC_EVAL4IN;

    /**
     * 测评输入点范围
     * <p>
     * 节/章/复习点一级点列表/
     * <p>
     * 必须
     * <p>
     * 点类型通过SceneInfo学习场景推断
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    List<String> catalogIds;

    /**
     * 测评任务Id
     * 调用端生成和传入，多次测评属于一次任务的，传入相同任务Id
     * <p>
     */
    @JSONField(ordinal = 15)
    private String roundId;

    /**
     * 测评题号,题号不能小于1
     * <p>
     * 必须
     */
    @Min(1)
    @JSONField(ordinal = 20)
    private int topicOrderNumber;


    /**
     * 透传扩展
     */
    // private JSONObject recExt;
    private Map<String, Object> recExt;

    /**
     * 禁用点
     */
    private List<String> forbiddenNodes;
}
