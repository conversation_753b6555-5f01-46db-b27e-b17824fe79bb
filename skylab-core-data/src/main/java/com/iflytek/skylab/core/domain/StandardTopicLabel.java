package com.iflytek.skylab.core.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 新课标试题标签
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/27 11:14
 */
@Getter
@Setter
@Accessors(chain = true)
public class StandardTopicLabel implements Serializable {
    private static final long serialVersionUID = 1L;
    private String code;
    private String name;
    private List<StandardTopicLabel> children;

}
