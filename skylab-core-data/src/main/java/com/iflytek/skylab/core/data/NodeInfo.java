package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/1 19:22
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class NodeInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 点ID
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private String nodeId;
    /**
     * 点类型
     */
    @NotEmpty
    @JSONField(ordinal = 20)
    private NodeTypeEnum nodeType;

    /**
     * 点所在的目录
     */
    @NotEmpty
    @JSONField(ordinal = 30)
    private String catalogId;


    private String logicType;

}
