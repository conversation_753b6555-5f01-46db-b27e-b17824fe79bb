package com.iflytek.skylab.core.constant;


import lombok.Getter;

/**
 * 学习能力
 *
 * <AUTHOR>
 * @date 2022-02-12 10:12:11
 */
public enum StudyBehaviorEnum implements EnumDescable {

    /**
     * 测评
     */
    EVAL_REC("测评推题"),

    /**
     * 薄弱点
     */
    EXERCISE_REC("练习推题"),

    /**
     * 上报作答记录
     */
    REPORT_ANSWER("上报作答记录"),

    /**
     * 测评终止
     */
    EVAL_STOP("测评终止"),


    //region
//    /**
//     * 未知
//     */
//    NONE("none", "未知"),

    /**
     * 扩展1
     */
    EXT1ST("扩展1"),

    /**
     * 扩展2
     */
    EXT2ND("扩展2"),

    /**
     * 扩展3
     */
    EXT3RD("扩展3"),

    //endregion
    ;

    @Getter
    private final String desc;

    StudyBehaviorEnum(String desc) {
        this.desc = desc;
    }

    public static StudyBehaviorEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (StudyBehaviorEnum e : StudyBehaviorEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }

}
