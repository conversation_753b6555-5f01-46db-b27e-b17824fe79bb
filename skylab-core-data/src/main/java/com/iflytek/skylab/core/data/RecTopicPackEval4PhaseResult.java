package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/27 17:49
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecTopicPackEval4PhaseResult extends FuncResult {
    private static final long serialVersionUID = 1L;

    List<TopicInfo> topicInfos = new ArrayList<>();

    @Override
    public String toString() {
        return super.toJson();
    }
}
