package com.iflytek.skylab.core.domain.extra;

import com.iflytek.skylab.core.constant.ExamType;
import com.iflytek.skylab.core.constant.StudentLevelEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/9/27 17:37
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class AiExamSceneInfo extends SceneInfo {

    public AiExamSceneInfo() {
        super();
        super.setStudyCode(StudyCodeEnum.AI_EXAM);
    }

    /**
     * 枚举值 normal good excellent
     */
    @NotNull
    private StudentLevelEnum studentLevel;

    /**
     * 考试类型: 01 期中 02 期末
     */
    @NotNull
    private ExamType examType;
}
