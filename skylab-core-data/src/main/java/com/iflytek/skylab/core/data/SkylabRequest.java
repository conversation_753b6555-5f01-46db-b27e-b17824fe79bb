package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.iflytek.skylab.core.domain.SceneInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 场景和候选点信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SkylabRequest<T extends FuncParam> extends Jsonable implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank
    @ApiModelProperty(position = 10, notes = "追踪Id")
    @JSONField(ordinal = 10)
    @JsonProperty(index = 10)
    private String traceId;

    @NotNull
    @Valid
    @ApiModelProperty(position = 20, notes = "场景信息")
    @JSONField(ordinal = 20)
    @JsonProperty(index = 20)
    private SceneInfo scene;

    @NotNull
    @Valid
    @ApiModelProperty(position = 30, notes = "数据体")
    @JSONField(ordinal = 30)
    @JsonProperty(index = 30)
    private T payload;

    @Override
    public String toString() {
        return super.toString();
    }
}
