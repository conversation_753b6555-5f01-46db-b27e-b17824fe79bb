package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 通用特征查询 功能 返回结果
 * <p>
 * <p>
 * 数据示例：
 * <p>
 * {
 * "traceId": "e7b75062-ee18-4d62-b8fe-553fd1937a8b",
 * "featureName": "fc_score",
 * "values": [
 * {
 * "user_id": "15000000",
 * "biz_code": "xxj",
 * "subject_code": "02",
 * "phase_code": "05",
 * "anchor_id": "a",
 * "fc_score": "0.5"
 * },
 * {
 * "user_id": "15000000",
 * "biz_code": "xxj",
 * "subject_code": "02",
 * "phase_code": "05",
 * "anchor_id": "b",
 * "fc_score": "0.8"
 * }
 * ]
 * }
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class FeatureResult extends FuncResult {

    private static final long serialVersionUID = 1L;


    @Override
    public String toString() {
        return super.toJson();
    }

    /**
     * 特征查询结果列表
     */
    private List<FeatureResultItem> items = new ArrayList<>();

    /**
     * 特征查询项目
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class FeatureResultItem implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 特征名称
         */
        private String featureName;

        /***
         * 特征值
         */
        private List<Map<String, String>> values = new ArrayList<>();
    }
}
