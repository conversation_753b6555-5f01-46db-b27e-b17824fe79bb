package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 推题包 请求参数
 *
 * <AUTHOR>
 * @date 2022/6/8 10:56
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecTopicPackParam extends FuncParam {
    public static final String FUNC_CODE = "REC_TOPIC_PACK";
    private static final long serialVersionUID = 1L;

    /**
     * 锚点ID集合
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private List<String> nodeIds;

    /**
     * 透传扩展
     */
    // private JSONObject recExt;
    private Map<String, Object> recExt;

    @Override
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
