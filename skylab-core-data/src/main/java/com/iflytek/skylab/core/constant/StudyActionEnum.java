package com.iflytek.skylab.core.constant;


import lombok.Getter;

/**
 * 学习能力
 *
 * <AUTHOR>
 * @date 2022-02-12 10:12:11
 */
public enum StudyActionEnum implements EnumDescable {

    /**
     * 测评
     */
    EVAL("测评"),

    /**
     * 薄弱点
     */
    WEAK("薄弱点"),

    /**
     * 扩展1
     */
    EXT1ST("扩展1"),

    /**
     * 扩展2
     */
    EXT2ND("扩展2"),
    /**
     * 扩展3
     */
    EXT3RD("扩展3"),

    /**
     * 扩展4
     */
    EXT4TH("扩展4"),
    ;

    @Getter
    private final String desc;

    StudyActionEnum(String desc) {
        this.desc = desc;
    }

    public static StudyActionEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (StudyActionEnum e : StudyActionEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
