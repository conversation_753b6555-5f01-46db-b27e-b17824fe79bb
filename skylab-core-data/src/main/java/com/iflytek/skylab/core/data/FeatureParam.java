package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 点推题 功能 请求参数
 * <p>
 * <p>
 * 示例数据：
 * <p>
 * [
 * {
 * "featureName": "catalog_learn_path",
 * "params": [{"catalog_code":"01_0702010101-1372_0702010101-1372-51031_0702010101-1372-51035","biz_code":"zsy_xxj","study_code":"sync_learn"}],
 * "featureVersion": 1,
 * "graphVersion": "v2022-01",
 * "tags":{
 * "scene":"xxj"
 * }
 * },
 * {
 * "featureName": "anchorpoint_difficulty",
 * "params": [{"anchorpoint_code":"0de31d53-f9a8-4c46-9be7-a7c3d9378e12"}],
 * "featureVersion": 1,
 * "graphVersion": "v2022-01",
 * "tags":{
 * "scene":"xxj"
 * }
 * },
 * {
 * "featureName": "anchorpoint_depth",
 * "params": [{"anchorpoint_code":"0de31d53-f9a8-4c46-9be7-a7c3d9378e12"}],
 * "featureVersion": 1,
 * "graphVersion": "v2022-01",
 * "tags":{
 * "scene":"xxj"
 * }
 * }
 * ]
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class FeatureParam extends FuncParam {
    public static final String FUNC_CODE = "COM_FEA";
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 特征查询列表
     */
    @NotEmpty
    @Valid
    private List<FeatureParamItem> items;


    /**
     * 简单传参模式。会从场景信息中获取部分字段。
     */
    private boolean simpleMode;

    /**
     * 特征查询项目
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class FeatureParamItem implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 特征名称
         */
        @NotBlank
        @JSONField(ordinal = 10)
        private String featureName;

        /**
         * 请求参数
         */
        @JSONField(ordinal = 20)
        private List<Map<String, String>> params = new ArrayList<>();

        /**
         * 图谱版本
         */
        @JSONField(ordinal = 30)
        private String graphVersion;

        /**
         * 特征版本
         */
        @JSONField(ordinal = 40)
        private int featureVersion;
    }
}
