package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 测评类型
 *
 * <AUTHOR>
 * @date 2022/3/22 3:58 下午
 */
public enum RecEvalEnum implements EnumDescable {
    /**
     * 进门测
     * 所有点找弱项
     */
    REC_EVAL4IN("进门测"),

    /**
     * 同步习题
     */
    REC_EVAL4SYNC("同步习题"),

    /**
     * 出门测
     */
    REC_EVAL4OUT("出门测"),

    /**
     * 仅灰点测评
     * 继续找弱项
     */
    REC_EVAL4CTN("仅灰点测评"),

    /**
     * 知识簇
     * 同步学-流式测评-所有点找弱项
     */
    KC_REC_EVAL4IN("知识簇-找弱项"),
    /**
     * 知识簇
     * 仅灰点测评
     * 继续找弱项
     */
    KC_REC_EVAL4CTN("知识簇-仅灰点测评"),
    /**
     * 寒暑
     * 知识簇-出门测题包
     */
    KC_REC_EVAL4OUT("知识簇-出门测题包"),
    /**
     * 寒暑
     * 知识簇-同步习题题包
     */
    KC_REC_EVAL4SYNC("知识簇-同步习题题包"),
    ;

    @Getter
    private final String desc;

    RecEvalEnum(String desc) {
        this.desc = desc;
    }

    public static RecEvalEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (RecEvalEnum e : RecEvalEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
