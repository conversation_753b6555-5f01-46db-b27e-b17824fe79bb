package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Map;

/***
 * 扩展功能  返回结果
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class ExtFuncResult extends FuncResult {
    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return super.toJson();
    }


    // private JSONObject value;
    private Map<String, Object> value;

}
