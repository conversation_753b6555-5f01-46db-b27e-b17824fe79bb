package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.StudyLogFuncEnum;
import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 答题行为 请求参数
 * <p>
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class StudyLogParam extends FuncParam {
    //    public static final String FUNC_CODE = "STUDY_LOG";
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return studyLogFuncEnum.toString();
    }


    @JSONField(ordinal = 8)
    private StudyLogFuncEnum studyLogFuncEnum = StudyLogFuncEnum.STUDY_LOG;

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 答题记录列表
     */
    @NotEmpty
    @Size(max = 30)
    @Valid
    @JSONField(ordinal = 20)
    private List<StudyLogRecord> items = new ArrayList<>();
}
