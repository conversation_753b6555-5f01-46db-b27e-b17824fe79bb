package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 推荐功能枚举
 *
 * <AUTHOR>
 * @date 2022/3/22 3:58 下午
 */
public enum RecFuncEnum implements EnumDescable {
    /**
     * 进门测  需要 与 RecEvalEnum 中的 REC_EVAL4IN 保持一致
     */
    REC_EVAL4IN("进门测"),

    /**
     * 出门测 需要 与 RecEvalEnum 中的 REC_EVAL4OUT 保持一致
     */
    REC_EVAL4OUT("出门测"),

    /**
     * 限时测评
     */
    REC_EVAL4LIMIT("限时测评"),

    /**
     * 点排序
     */
    REC_NODE("点排序"),

    /**
     * 点推题 需要 与 RecTopicEnum 中的 REC_TOPIC 保持一致
     */
    REC_TOPIC("点推题"),

    /**
     * 再推一题 需要 与 RecTopicEnum 中的 REC_TOPIC_ONEMORE 保持一致
     */
    REC_TOPIC_ONEMORE("再推一题"),

    /**
     * 点属性诊断
     */
    REC_NODE_PROP("点属性诊断"),

    /**
     * 推题包
     */
    REC_TOPIC_PACK("推题包"),

    REC_UNIT("章排序"),

    /**
     * 阶段测题包
     */
    REC_TOPIC_PACK_EVAL4PHASE("阶段测题包"),


    /**
     * 错题本-变式题-推错题
     */
    REC_ERROR_TOPIC("推错题"),
    /**
     * 错题本-溯源点推题
     */
    REC_TOPIC_TRACE_POINT("溯源点推题"),

    /**
     * 大图谱-点排序
     */
    REC_TRACE_NODE("大图谱-点排序");

    @Getter
    private final String desc;

    RecFuncEnum(String desc) {
        this.desc = desc;
    }

    public static RecFuncEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (RecFuncEnum e : RecFuncEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
