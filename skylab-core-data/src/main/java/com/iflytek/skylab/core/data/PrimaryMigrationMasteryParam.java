package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.domain.PrimaryMigrationMastery;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/11 19:35
 * @description 小学精准学章节掌握度入参
 */

@Getter
@Setter
@Accessors(chain = true)
public class PrimaryMigrationMasteryParam extends FuncParam {

    public static final String FUNC_CODE = "PRIMARY_MIGRATION_MASTERY";
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 用户Id
     */
    @NotBlank
    private String userId;

    /**
     * 学科编码
     */
    @NotBlank
    private String subjectCode;

    /**
     * 学段编码
     */
    @NotBlank
    private String phaseCode;

    /**
     * 教材版本编码
     */
    @NotBlank
    private String bookCode;

    /**
     * 章节目录
     */
    @NotBlank
    private String catalogId;


    /**
     * 小学精准学答章节掌握度列表
     */
    @NotEmpty
    @Size(max = 500)
    @Valid
    @JSONField(ordinal = 20)
    private List<PrimaryMigrationMastery> items = new ArrayList<>();
}
