package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/28 16:17
 */
public enum StudyLogFuncEnum implements EnumDescable {


    /**
     * 日志上报
     */
    STUDY_LOG("日志上报"),
    STUDY_CORRECT_LOG("批改上报"),

    KC_STUDY_CORRECT_LOG("批改上报"),

    /**
     * 知识簇-日志上报
     */
    KC_STUDY_LOG("知识簇-日志上报");


    @Getter
    private final String desc;

    StudyLogFuncEnum(String desc) {
        this.desc = desc;
    }

    public static StudyLogFuncEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (StudyLogFuncEnum e : StudyLogFuncEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }

}
