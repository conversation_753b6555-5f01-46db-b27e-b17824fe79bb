package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.MasterFuncEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/***
 * 画像计算 功能 请求参数
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class MasterDiagnoseParam extends FuncParam {
    //    public static final String FUNC_CODE = "MASTER_DIAGNOSE";
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return masterFuncEnum.toString();
    }

    @JSONField(ordinal = 8)
    private MasterFuncEnum masterFuncEnum = MasterFuncEnum.MASTER_DIAGNOSE;

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 输入点范围 目录
     * 节/章/复习点一级点列表
     * <p>
     * 点类型通过SceneInfo学习场景推断
     * <p>
     */
    @JSONField(ordinal = 10)
    private List<String> catalogIds;

    /**
     * 点标识Id列表
     * 锚点、考点、复习点
     * <p>
     */
    @JSONField(ordinal = 10)
    private List<String> nodeIds;

    /**
     * 是否全量更新
     */
    private Boolean allUpdate = false;
}
