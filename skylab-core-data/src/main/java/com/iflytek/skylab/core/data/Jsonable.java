package com.iflytek.skylab.core.data;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.filter.Filter;

import java.io.Serializable;

/**
 * Jsonable
 *
 * <AUTHOR>
 */
public abstract class Jsonable implements Serializable {

    private static final long serialVersionUID = 1L;

    static {
        JSON.config(JSONWriter.Feature.WriteEnumsUsingName, JSONWriter.Feature.ReferenceDetection);
    }

    public String toJson() {
        return JSON.toJSONString(this);
    }

    /**
     * toJson String
     *
     * @param fast     JSONWriter.Feature.NotWriteDefaultValue
     * @param filters:
     * @return Json String
     */
    public String toJson(boolean fast, Filter... filters) {
        return fast ? JSON.toJSONString(this, filters, JSONWriter.Feature.NotWriteDefaultValue) : JSON.toJSONString(this, filters, JSONWriter.Feature.PrettyFormat);
    }

    @Override
    public String toString() {
        return this.toJson();
    }
}
