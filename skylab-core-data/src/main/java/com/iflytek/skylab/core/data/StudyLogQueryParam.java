package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 查询是否存在答题记录(仅锚点) 请求参数
 * <p>
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class StudyLogQueryParam extends FuncParam {
    public static final String FUNC_CODE = "STUDY_QUERY";
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 点的目录列表
     * 章、节
     * 必须
     */
    @NotBlank
    @JSONField(ordinal = 10)
    private String catalogId;
}
