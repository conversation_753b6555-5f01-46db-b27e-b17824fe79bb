package com.iflytek.skylab.core.constant;

/**
 * 图谱顶点类型
 */
public class GraphVertexType {

    /**
     * 教材版本
     */
    public static final String PRESS = "PRESS";

    /**
     * 教材书本
     */
    public static final String BOOK = "BOOK";

    /**
     * 章
     */
    public static final String UNIT = "UNIT";

    /**
     * 节
     */
    public static final String COURSE = "COURSE";

    /**
     * 考点
     */
    public static final String CHECK_POINT = "CHECK_POINT";

    /**
     * 锚点
     */
    public static final String ANCHOR_POINT = "ANCHOR_POINT";

    /**
     * 知识卡片
     */
    public static final String CARD = "CARD";

    /**
     * 视频
     */
    public static final String VIDEO = "VIDEO";

    /**
     * 题
     */
    public static final String TOPIC = "TOPIC";

    /**
     * 学习路径
     */
    public static final String LEARN_PATH = "LEARN_PATH";

    /**
     * 复习点目录树
     */
    public static final String TREE = "TREE";

    /**
     * 复习点目录
     */
    public static final String REVISE = "REVISE";

    /**
     * 复习点
     */
    public static final String REVIEW_POINT = "REVIEW_POINT";

    /**
     * 复习树二级目录
     */
    public static final String L2_MAP_TREE_NODE = "L2_MAP_TREE_NODE";

    /**
     * 考法点
     */
    public static final String EXAM_POINT = "EXAM_POINT";
}
