package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 考试类型
 *
 * <AUTHOR>
 */
public enum ExamType implements EnumDescable {

    /**
     * 期中备考
     */
    MID_EXAM("期中备考", "01", "mid"),

    /**
     * 期末备考
     */
    FINAL_EXAM("期末备考", "02", "final"),

    /**
     * 单元备考
     */
    UNIT_EXAM("单元备考", "unknown", "unknown"),

    WEEK_EXAM("unknown", "unknown", "week"),

    MONTH_EXAM("unknown", "unknown", "month");

    @Getter
    private final String desc;
    @Getter
    private final String aiExamCode;
    @Getter
    private final String aiReviewCode;

    ExamType(String desc, String aiExamCode, String aiReviewCode) {
        this.desc = desc;
        this.aiExamCode = aiExamCode;
        this.aiReviewCode = aiReviewCode;
    }

    public static ExamType parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (ExamType e : ExamType.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
