package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.MasterFuncEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/***精准学OS-全书地图
 * 全书画像 功能 请求参数
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class MasterFetch5BookParam extends FuncParam {
    public static final String FUNC_CODE = MasterFuncEnum.MASTER_SNAPSHOOT_FETCH.toString();
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }


    /**
     * 书本（带教材版本）：01_08050201-002
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private String bookCode;
    /**
     * 输入点范围 目录
     * 节/章/复习点一级点列表
     * <p>
     * 点类型通过SceneInfo学习场景推断
     * <p>
     */
    @JSONField(ordinal = 10)
    private List<String> catalogIds;

    /**
     * 期望返回点类型： 考点、锚点、复习点
     */
    @JSONField(ordinal = 10)
    private List<NodeTypeEnum> expPointTypes;


}
