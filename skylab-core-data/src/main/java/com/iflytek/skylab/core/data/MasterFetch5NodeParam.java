package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.MasterFuncEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/***精准学OS-全书地图
 * 画像获取 功能 请求参数
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class MasterFetch5NodeParam extends FuncParam {
    //    public static final String FUNC_CODE = MasterFuncEnum.MASTER_FETCH.toString();
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return masterFuncEnum.toString();
    }

    @JSONField(ordinal = 8)
    private MasterFuncEnum masterFuncEnum = MasterFuncEnum.MASTER_FETCH;

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * <p> 点:锚点、考点、复习点
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private List<NodeInfo> nodeInfos;


}
