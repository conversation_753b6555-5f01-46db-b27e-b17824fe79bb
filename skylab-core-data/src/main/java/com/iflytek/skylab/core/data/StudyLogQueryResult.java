package com.iflytek.skylab.core.data;

import com.iflytek.skylab.core.domain.StudyLogRecord;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 答题记录 上报返回结果
 * <p>
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class StudyLogQueryResult extends FuncResult {

    private static final long serialVersionUID = 1L;

    /**
     * 答题记录是否存在
     */
    private boolean existStudentLog;

    /**
     * 点id
     */
    private String nodeId;

    /**
     * 答题记录
     */
    private List<StudyLogRecord> studyLogs;
}
