package com.iflytek.skylab.core.domain;

import com.iflytek.skylab.core.constant.EnumDescable;
import lombok.Getter;

/**
 * 分层目录升级标识
 * SceneInfo.layerVersion
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/6 10:33
 */
public enum LayerVersionEnum implements EnumDescable {

    /**
     * 同步学升级
     */
    SYNC_LEARN_UPDATE("同步学升级"),
    /**
     * 中考复习升级
     */
    SENIOR_EXAM_UPDATE("中考复习升级"),
    /**
     * 缺省
     */
    NONE("缺省"),
    /**
     * 扩展1
     */
    EXT1ST("扩展1"),

    /**
     * 扩展2
     */
    EXT2ND("扩展2"),

    /**
     * 扩展3
     */
    EXT3RD("扩展3"),
    ;


    @Getter
    private final String desc;

    LayerVersionEnum(String desc) {
        this.desc = desc;
    }

    public static LayerVersionEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (LayerVersionEnum e : LayerVersionEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
