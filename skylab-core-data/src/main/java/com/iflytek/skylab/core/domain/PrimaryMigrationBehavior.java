package com.iflytek.skylab.core.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 小学精准学学情数据对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class PrimaryMigrationBehavior implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户Id
     */
    @NotBlank
    private String userId;

    /**
     * 学科编码
     */
    @NotBlank
    private String subjectCode;

    /**
     * 学段编码
     */
    @NotBlank
    private String phaseCode;

    /**
     * 锚点id
     */
    @NotBlank
    private String nodeId;

    /**
     * 题目id
     */
    @NotBlank
    private String resNodeId;

    /**
     * 答题时间
     */
    @NotNull
    private Long createTime;

    /**
     * 得分
     */
    @NotNull
    private Double score;

    /**
     * 标准得分
     */
    @NotNull
    private Double standardScore;
}
