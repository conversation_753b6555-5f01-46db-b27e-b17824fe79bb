package com.iflytek.skylab.core.constant;


import lombok.Getter;

/**
 * 学习场景
 *
 * <AUTHOR>
 * @date 2022-02-12 10:12:11
 */
public enum StudyFuncEnum implements EnumDescable {

    /**
     * 作答日志
     */
    STUDY_LOG("作答日志"),


    /**
     * 批改日志
     */
    STUDY_CORRECT_LOG("批改日志"),
    /**
     * 答题记录查询(仅锚点)
     */
    STUDY_QUERY("作答日志查询");


    @Getter
    private final String desc;

    StudyFuncEnum(String desc) {
        this.desc = desc;
    }

    public static StudyFuncEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (StudyFuncEnum e : StudyFuncEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }

}
