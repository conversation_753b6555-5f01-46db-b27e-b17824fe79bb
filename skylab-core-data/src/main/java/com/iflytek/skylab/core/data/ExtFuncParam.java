package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Map;

/***
 * 扩展功能 请求参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class ExtFuncParam extends FuncParam {
    private static final long serialVersionUID = 1L;

    private String funcCode;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return funcCode;
    }

    @JSONField(ordinal = 10)
    // private JSONObject value;
    private Map<String, Object> value;

    @Override
    public String toString() {
        return super.toString();
    }
}
