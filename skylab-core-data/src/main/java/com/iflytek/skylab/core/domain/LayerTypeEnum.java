package com.iflytek.skylab.core.domain;

import com.iflytek.skylab.core.constant.EnumDescable;
import lombok.Getter;

/**
 * 分层等级升级标识
 * SceneInfo.layerType
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/6 10:33
 */
public enum LayerTypeEnum implements EnumDescable {

    /**
     * 普通
     */
    conventional("普通"),
    /**
     * 进阶
     */
    highScoreAdvanced("进阶"),
    /**
     * 思维拓展
     */
    thinkingExpansion("思维拓展"),
    ;


    @Getter
    private final String desc;

    LayerTypeEnum(String desc) {
        this.desc = desc;
    }

    public static LayerTypeEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (LayerTypeEnum e : LayerTypeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }

}
