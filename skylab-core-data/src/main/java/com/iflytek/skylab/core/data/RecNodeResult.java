package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/***
 * 点排序 功能 返回结果
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecNodeResult extends FuncResult {

    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return super.toJson();
    }

    /**
     * 推荐点
     * <p>
     * （薄弱锚点列表）
     * 或 单个推荐考点、复习点
     * 必须
     */
    private List<String> nodeIds = new ArrayList<>();

    // nodeId to nodeInfo
    private List<NodeInfo> nodeInfos = new ArrayList<>();

    /**
     * 推荐点建议终止
     * <p>
     * 推荐考点建议终止。true：建议终止，此时推荐考点id不建议继续做题 false：不建议终止，建议在推荐考点id下进行做题
     */
    private boolean pointRecommendEnd;

    /**
     * 应学点
     * <p>
     * 兼容老系统 ，返回应学点数据
     */
    //@Deprecated
    //private List<String> learnPoints = new ArrayList<>();
}
