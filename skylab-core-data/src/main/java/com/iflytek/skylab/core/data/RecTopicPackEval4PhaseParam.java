package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 阶段测题包
 *
 * <AUTHOR>
 * @date 2022/9/27 17:44
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecTopicPackEval4PhaseParam extends FuncParam {

    public static final String FUNC_CODE = "REC_TOPIC_PACK_EVAL4PHASE";
    private static final long serialVersionUID = 1L;

    @NotEmpty
    private List<String> examIds;

    @Override
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
