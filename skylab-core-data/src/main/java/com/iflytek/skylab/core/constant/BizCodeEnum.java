package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 业务编码 定义
 *
 * <AUTHOR>
 */
public enum BizCodeEnum implements EnumDescable {

    /**
     * 学习机
     */
    ZSY_XXJ("学习机"),

    /**
     * 知学宝
     */
    ZSY_ZXB("知学宝"),

    /**
     * 中学作业
     */
    ZSY_XKT("中学作业"),

    /**
     * 全场景BYOD
     */
    ZSY_BYOD("全场景BYOD"),

    /**
     * 课后服务
     */
    ZSY_KHSDB("课后服务"),

    /**
     * 内部测试
     */
    ADAPTIVE_TEST("内部测试"),


    //region
    /**
     * 未知
     */
    NONE("未知"),

    /**
     * 扩展1
     */
    EXT1ST("扩展1"),

    /**
     * 扩展2
     */
    EXT2ND("扩展2"),

    /**
     * 扩展3
     */
    EXT3RD("扩展3"),

    //endregion
    ;


    @Getter
    private final String desc;

    BizCodeEnum(String desc) {
        this.desc = desc;
    }

    public static BizCodeEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (BizCodeEnum e : BizCodeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
