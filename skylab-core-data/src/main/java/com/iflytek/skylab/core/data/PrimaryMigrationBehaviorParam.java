package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.domain.PrimaryMigrationBehavior;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/11 19:35
 * @description 小学精准学答题记录入参
 */

@Getter
@Setter
@Accessors(chain = true)
public class PrimaryMigrationBehaviorParam extends FuncParam {
    public static final String FUNC_CODE = "PRIMARY_MIGRATION_BEHAVIOR";
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 小学精准学答题记录列表
     */
    @NotEmpty
    @Size(max = 30)
    @Valid
    @JSONField(ordinal = 20)
    private List<PrimaryMigrationBehavior> items = new ArrayList<>();

}
