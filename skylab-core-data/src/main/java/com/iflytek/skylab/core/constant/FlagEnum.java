package com.iflytek.skylab.core.constant;

/**
 * 标志的枚举。包含 布尔、整型、文字 形式的值
 *
 * <AUTHOR>
 * @date 2022/3/24 16:22
 */
public enum FlagEnum {

    /**
     * 是
     */
    TRUE(true, 1, "是"),

    /**
     * 否
     */
    FALSE(false, 0, "否");


    private final boolean boolValue;
    private final int intValue;
    private final String strValue;

    FlagEnum(boolean boolValue, int intValue, String strValue) {
        this.boolValue = boolValue;
        this.intValue = intValue;
        this.strValue = strValue;
    }

    public boolean boolValue() {
        return boolValue;
    }

    public int intValue() {
        return intValue;
    }

    public String strValue() {
        return strValue;
    }
}
