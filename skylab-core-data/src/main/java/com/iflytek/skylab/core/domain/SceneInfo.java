package com.iflytek.skylab.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.annotation.MetricTagField;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.HisStudyCodeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.Jsonable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/***
 * 场景信息
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SceneInfo extends Jsonable implements Serializable {

    private static final long serialVersionUID = 4717385659232524944L;

    /**
     * 用户Id
     * <p>
     * 必传
     */
    @NotBlank
    @JSONField(ordinal = 5)
    private String userId;

    /**
     * 学习场景，主要用于 方案分流
     * <p>
     * 必传
     */
    @NotNull
    @JSONField(ordinal = 10)
    @MetricTagField
    private StudyCodeEnum studyCode;

    /**
     * 如果StudyCode  =  SYNC_OS
     * hisStudyCode 必传
     * 历史学习场景，
     *
     * <p>
     * 必传
     */
    @JSONField(ordinal = 10)
    @MetricTagField
    private HisStudyCodeEnum hisStudyCode;

    /***
     * 业务功能
     * <p/>
     * 精准找弱项 | 推荐提升 |  针对学 | 互动题 | 变式题 |  反馈测试题 | 摸底测 | 学霸闯关
     */
    @NotNull
    @JSONField(ordinal = 15)
    @MetricTagField
    private BizActionEnum bizAction;

    /**
     * 应用业务方代码
     * <p/>
     * 学习机：ZSY_XXJ
     * 知学宝：ZSY_ZXB
     * 内部测试：ADAPTIVE_TEST
     * 中学作业:ZSY_XKT
     * 全场景BYOD:ZSY_BYOD
     * 课后三点半:ZSY_KHSDB
     * <p>
     * 必传
     */
    @NotNull
    @JSONField(ordinal = 20)
    @MetricTagField
    protected BizCodeEnum bizCode;


    /**
     * 可枚举值[“02"]，学科代码，代表数学 学科
     * <p>
     * 必传
     */
    @NotBlank
    @JSONField(ordinal = 25)
    @MetricTagField
    private String subjectCode;
    /**
     * 学段编码 学  初中：04   高中：05
     * <p>
     * 必传
     */
    @NotBlank
    @JSONField(ordinal = 30)
    @MetricTagField
    private String phaseCode;

    /**
     * 年级：高一 10 高二 11  高三 12
     * <p>
     */
    @JSONField(ordinal = 35)
    @MetricTagField
    private String gradeCode;

    /**
     * 区域编码
     * 6位地址码，省/市
     * 必传（老系统内部兼容，设置默认值）
     */
    // @NotBlank
    @JSONField(ordinal = 40)
    @MetricTagField
    private String areaCode = "000000";

    /**
     * 功能编码
     */
    @JSONField(ordinal = 45)
    @MetricTagField
    private String functionCode;

    /**
     * 图谱版本
     */
    @JSONField(ordinal = 50)
    private String graphVersion;

    /**
     * 学校Id
     * <p>
     * B端有
     */
    @JSONField(ordinal = 55)
    private String schoolId;
    /**
     * 班级Id
     * <p>
     * B端有
     */
    @JSONField(ordinal = 60)
    private String classId;

    /**
     * catalogCode
     * <p>
     * 教材_书_章_节
     * 教材_书_章_节_课时
     */
    @JSONField(ordinal = 65)
    private String catalogCode;

    //region  资源信息
    /**
     * 教材版本编码
     * <p>
     * 人教/苏教
     * <p>
     * 必传
     */
    @JSONField(ordinal = 70)
    private String pressCode;

    /**
     * 教材册别  教材书本 code
     * <p>
     * 人教/册别
     * <p>
     * 不一定有
     */
    @JSONField(ordinal = 75)
    private String bookCode;

    /**
     * 学生需求 分层类型
     * <p>
     * 学习机调用特征接口获得后传入（同步学场景使用）
     * LayerTypeEnum.conventional.name() ：普通
     * LayerTypeEnum.highScoreAdvanced.name() ：进阶
     * LayerTypeEnum.thinkingExpansion.name() ：思维拓展
     */
    @JSONField(ordinal = 80)
    private String layerType;

    /**
     * 分层测评版本
     * <p>
     * LayerVersionEnum.SYNC_LEARN_UPDATE.name() ：同步学新分层测评逻辑
     * LayerVersionEnum.SENIOR_EXAM_UPDATE.name() :中考复习升级
     */
    @JSONField(ordinal = 85)
    private String layerVersion;

    /**
     * true or false 按钮,上游对额外定制功能的触发
     * 使用场景：1、备考画像开启 受锚点画像影响  2、
     */
    @JSONField(ordinal = 90)
    private Boolean tfButton;

    /**
     * 终端版本,如：T10
     */
    private String deviceVersion;

    /**
     * 设备标识
     */
    private String deviceId;

    //region  扩展属性
    /**
     * 扩展属性1
     * MINI_PROGRAM小程序;
     * LNK-SCREEN墨水屏；
     */
    @JSONField(ordinal = 13010)
    private String ext1;
    /**
     * 扩展属性2
     */
    @JSONField(ordinal = 13020)
    private String ext2;

    /**
     * 扩展属性3
     */
    @JSONField(ordinal = 13030)
    private String ext3;

    //endregion


    //region  非业务相关的属性

    /**
     * 是否是测试
     * <p>
     * 目的：区分数据统计
     */
    @JSONField(ordinal = 13000)
    @MetricTagField
    private boolean test;

    /**
     * 策略Code
     */
    @JSONField(ordinal = 1000)
    private String strategyCode;

    /**
     * planVersionId
     */
    @JSONField(ordinal = 13040)
    private String planVersionId;
    //endregion


    @Override
    public String toString() {
        return super.toJson();
    }

}
