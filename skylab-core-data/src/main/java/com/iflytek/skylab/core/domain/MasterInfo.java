package com.iflytek.skylab.core.domain;

import com.iflytek.skylab.core.constant.CatalogTypeEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.data.Jsonable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 知识点体系-画像描述数据Item
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class MasterInfo extends Jsonable implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 父目录
     */
    private String catalogId;

    /**
     * 父目录类型
     */
    private CatalogTypeEnum catalogType;

    /**
     * 点标识
     */
    private String nodeId;

    /**
     * 点类型
     */
    private NodeTypeEnum nodeType;

    /**
     * 是否使用思维拓展点 （仅锚点体系，同步学场景使用）
     */
    private Boolean useThinkingExpansion;

    /**
     * 画像得分
     */
    private Double masteryScore;

    /**
     * 画像类型，用来区分是真实画像还是预测画像
     * <p>
     * 用来区分是真实画像还是预测画像（REAL：真实画像, PREDICT：预测画像）
     */
    private String computeType;

    /**
     * 融合画像值
     */
    private Double fusion;

    /**
     * 真实画像
     */
    private Double real;

    /**
     * 预测画像
     */
    private Double predict;

    /**
     * 是否是应学点
     */
    private Boolean shouldFlag;

    /**
     * 画像更新时间，时间戳
     */
    // private Instant updateTime;
    private Date updateTime;

    /**
     * 最后一题答题时间
     */
    // private Instant lastAnswerTime;
    private Date lastAnswerTime;

    /**
     * 全局畫像值
     */
    private GlobalMasteryInfo globalMastery;
}
