package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 点的目录类型 定义
 *
 * <AUTHOR>
 */
public enum CatalogTypeEnum implements EnumDescable {

    /**
     * 章目录
     */
    UNIT("章目录"),

    /**
     * 节目录
     */
    COURSE("节目录"),

    /**
     * 寒暑专题目录
     */
    OSCOURSE("寒暑专题目录"),

    /**
     * 寒暑专题课时目录
     */
    OSPERIOD("寒暑专题课时目录"),
    /**
     * 课时
     */
    PERIOD("课时"),

    /**
     * 小节目录
     */
    L2COURSE("小节目录"),

    /**
     * 小小节目录
     */
    L3COURSE("小小节目录"),

    /**
     * 复习点目录
     */
    REVISE("复习点目录"),

    /**
     * 复习点树目录
     */
    TREE("复习点树目录"),

    /**
     * AI考试专题，视作目录，挂在书下
     */
    AIEXAM("AI考试专题"),

    /**
     * 中考复习目录
     */
    L2_MAP_TREE_NODE("复习树二级目录"),

    /**
     * 中考复习专题，视作目录
     */
    L3_MAP_TREE_NODE("复习树三级目录");


    @Getter
    private final String desc;

    CatalogTypeEnum(String desc) {
        this.desc = desc;
    }

    public static CatalogTypeEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (CatalogTypeEnum e : CatalogTypeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
