package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 清空画像
 * <p>
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class ClearMasteryResult extends FuncResult {

    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return super.toJson();
    }

    private String userId;
}
