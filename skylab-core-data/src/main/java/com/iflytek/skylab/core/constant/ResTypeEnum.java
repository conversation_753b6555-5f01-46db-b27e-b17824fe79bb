package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 资源扩展属性 资源类型
 *
 * <AUTHOR>
 */
public enum ResTypeEnum implements EnumDescable {

    /**
     * 学习资源
     */
    STUDY_RES("学习资源"),
    /**
     * 练习资源
     */
    PRACTICE_RES("练习资源");


    @Getter
    private final String desc;

    ResTypeEnum(String desc) {
        this.desc = desc;
    }

    public static ResTypeEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (ResTypeEnum e : ResTypeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
