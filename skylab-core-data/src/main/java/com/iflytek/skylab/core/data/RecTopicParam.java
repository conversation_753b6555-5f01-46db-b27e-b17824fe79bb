package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.RecTopicEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 点推题 功能 请求参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecTopicParam extends FuncParam {

    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return recTopicEnum.toString();
    }

    @Override
    public String toString() {
        return super.toString();
    }


    /**
     * 推测类型：推题 | 再推一题
     */
    @JSONField(ordinal = 8)
    private RecTopicEnum recTopicEnum = RecTopicEnum.REC_TOPIC;

    /**
     * 点标识id
     * (锚点/考点/复习点)
     * 必须
     */
    @NotBlank
    private String nodeId;

    /**
     * 任务id（标识该点推荐流程）
     * <p>
     * 必须
     * <p>
     * 调用端生成和传入，多次推荐属于一次任务的，传入相同任务Id
     */
    private String roundId;

    /**
     * 推荐题号,题号不能小于1
     * <p>
     * 必须
     */
    @Min(1)
    private int topicOrderNumber;

    /**
     * 透传扩展
     */
    // private JSONObject recExt;
    private Map<String, Object> recExt;

    /**
     * 禁用点
     */
    private List<String> forbiddenNodes;
}
