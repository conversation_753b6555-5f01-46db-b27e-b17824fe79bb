package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/6 11:14
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecUnitResult extends FuncResult {

    private static final long serialVersionUID = 1L;

    private List<String> catalogIds = new ArrayList<>();

    @Override
    public String toString() {
        return super.toJson();
    }
}
