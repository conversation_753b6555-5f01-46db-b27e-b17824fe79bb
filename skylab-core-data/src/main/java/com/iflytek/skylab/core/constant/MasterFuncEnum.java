package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/28 16:17
 */
public enum MasterFuncEnum implements EnumDescable {
    /**
     * 画像获取
     */
    MASTER_FETCH("画像获取"),


    /**
     * 画像诊断
     */
    MASTER_DIAGNOSE("画像诊断"),
    /**
     * 画像查询（mongo直接查询）
     */
    MASTER_SNAPSHOOT_FETCH("画像快照查询"),


    /**
     * 知识簇-画像获取
     */
    KC_MASTER_FETCH("知识簇-画像获取"),


    /**
     * 知识簇-画像诊断
     */
    KC_MASTER_DIAGNOSE("知识簇-画像诊断"),

    /**
     * 快速画像查询
     */
    FAST_MASTER_FETCH("快速画像查询");


    @Getter
    private final String desc;

    MasterFuncEnum(String desc) {
        this.desc = desc;
    }

    public static MasterFuncEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (MasterFuncEnum e : MasterFuncEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }

}
