package com.iflytek.skylab.core.domain.extra;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/7 14:00
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class AiDiagSceneInfo extends SceneInfo {

    public AiDiagSceneInfo() {
        super();
        super.setStudyCode(StudyCodeEnum.AI_DIAG);
    }

    /**
     * 点所在的目录
     * key: nodeId
     * val: catalogId
     */
    @NotEmpty
    @JSONField(ordinal = 20)
    private Map<String, String> nodeCatalogMap;

}
