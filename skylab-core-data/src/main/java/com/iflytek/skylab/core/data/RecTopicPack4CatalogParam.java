package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 推题包 请求参数
 *
 * <AUTHOR>
 * @date 2022/6/8 10:56
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecTopicPack4CatalogParam extends FuncParam {
    public static final String FUNC_CODE = "REC_TOPIC_PACK";
    private static final long serialVersionUID = 1L;

    /**
     * 目录ID
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private List<String> catalogIds;


    @Override
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
