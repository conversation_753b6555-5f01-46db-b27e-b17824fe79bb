package com.iflytek.skylab.core.constant;


import lombok.Getter;

/**
 * 学习场景
 *
 * <AUTHOR>
 * @date 2022-02-12 10:12:11
 */
public enum StudyCodeEnum implements EnumDescable {

    /**
     * 试题屏
     */
    TOPIC_RUSH("试题屏"),

    /**
     * 精准学OS
     */
    SYNC_OS("精准学OS"),
    /**
     * 同步学
     */
    SYNC_LEARN("同步学"),

    /**
     * AI诊断
     */
    AI_DIAG("AI诊断"),

    /**
     * 大图谱-溯源点
     */
    DTP_TRACE("大图谱溯源点"),

    /**
     * 单元复习
     */
    UNIT_REVIEW("单元复习"),

    /**
     * 期中备考
     */
    MID_EXAM("期中备考"),

    /**
     * 期末备考
     */
    FINAL_EXAM("期末备考"),

    /**
     * 中考一轮
     */
    SENIOR_EXAM_1("中考一轮"),

    /**
     * 中考二轮
     */
    SENIOR_EXAM_2("中考二轮"),

    /**
     * 中考三轮
     */
    SENIOR_EXAM_3("中考三轮"),


    AI_REVIEW("AI复习/理科单品"),

    /**
     * AI考试
     */
    AI_EXAM("AI考试"),

    /**
     * AI试题过滤
     */
    AI_TEST_FILTER("AI试题过滤"),

    /**
     * 大图谱-学习机阶段备考-精品密卷
     */
    MACROGRAPH_QUALITY_TEST_PAPER("学习机阶段备考-精品密卷"),
    /**
     * 大图谱-学习机AI全科错题本
     */
    MACROGRAPH_ERROR_BOOK("学习机AI全科错题本"),
    /**
     * 大图谱-学习机全科学习资源-同步习题
     */
    MACROGRAPH_SYNC_TEST("学习机全科学习资源-同步习题"),

    /**
     * AI伴学-同步指导
     */
    SYNC_TUTORING("同步指导"),
    /**
     * 缺省
     */
    NONE("缺省"),
    /**
     * 扩展1
     */
    EXT1ST("扩展1"),

    /**
     * 扩展2
     */
    EXT2ND("扩展2"),

    /**
     * 扩展3
     */
    EXT3RD("扩展3"),
    ;


    @Getter
    private final String desc;

    StudyCodeEnum(String desc) {
        this.desc = desc;
    }

    public static StudyCodeEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (StudyCodeEnum e : StudyCodeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }

}
