package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/***
 * 画像获取 功能 请求参数
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class MasterFetch4CatalogParam extends FuncParam {
    public static final String FUNC_CODE = "MASTER_FETCH";
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 输入点范围 目录
     * 节/章/复习点一级点列表
     * <p>
     * 点类型通过SceneInfo学习场景推断
     * <p>
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private List<String> catalogIds;

    @JSONField(ordinal = 20)
    public Boolean learnPointUpdate = false;

    /**
     * 透传扩展
     */
    // private JSONObject masterFetchExt;
    private Map<String, Object> masterFetchExt;

}
