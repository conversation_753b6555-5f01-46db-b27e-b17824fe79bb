package com.iflytek.skylab.core.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class GlobalMasteryInfo implements Serializable {
    private static final long serialVersionUID = 3952327042650985577L;

    /**
     * 画像得分
     */
    private Double masteryScore;

    /**
     * 融合画像值
     */
    private Double fusion;

    /**
     * 真实画像
     */
    private Double real;

    /**
     * 预测画像
     */
    private Double predict;
}
