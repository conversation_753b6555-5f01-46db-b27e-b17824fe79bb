package com.iflytek.skylab.core.exception;

/**
 * SdkException
 *
 * <AUTHOR>
 */
public class SdkException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private final int code;

    public SdkException(int code, String message, Object... args) {
        this(code, String.format(message, args));
    }

    public SdkException(int code, String message) {
        super(message);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
