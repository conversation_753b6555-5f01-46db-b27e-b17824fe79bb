package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 推点类型
 */
public enum RecNodeEnum implements EnumDescable {
    /**
     * 薄弱点推荐
     */
    REC_NODE("薄弱点推荐"),


    /**
     * 知识簇-同步学-规划学推点
     */
    KC_REC_NODE_PLAN("规划学点排序"),
    KC_REC_NODE_VARIANT("自主学点排序"),
    KC_REC_NODE("看板点排序");

    @Getter
    private final String desc;

    RecNodeEnum(String desc) {
        this.desc = desc;
    }

    public static RecNodeEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (RecNodeEnum e : RecNodeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
