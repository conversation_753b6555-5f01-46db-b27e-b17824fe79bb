package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 推荐资源类型 定义
 *
 * <AUTHOR>
 */
public enum ResourceTypeEnum implements EnumDescable {

    /**
     * 资源题
     */
    TOPIC("资源题"),
    /**
     * 资源卡片
     */
    CARD("资源卡片"),
    /**
     * 资源视频
     */
    VIDEO("资源视频"),
    ;

    @Getter
    private final String desc;

    ResourceTypeEnum(String desc) {
        this.desc = desc;
    }

    public static ResourceTypeEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (ResourceTypeEnum e : ResourceTypeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
