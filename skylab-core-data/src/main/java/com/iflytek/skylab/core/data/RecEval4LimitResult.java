package com.iflytek.skylab.core.data;

import com.iflytek.skylab.core.domain.EvaluationItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/***
 * 测评推题 功能 返回结果
 *
 * 限时测场景
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecEval4LimitResult extends FuncResult {

    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return super.toJson();
    }

    /**
     * 测评推荐结果列表
     * <p>
     * 1、多轮测评推题 集合仅有1个推荐资源
     * 2、单次测评推题 集合包含全部推荐资源
     */
    List<EvaluationItem> evaluationItems = new ArrayList<>();

    /**
     * 是否可终止本次测评
     * <p>
     * 推荐终止标识
     */
    private boolean terminationFlag;

    /**
     * 本次推荐流程-将推荐试题总题量
     */
    private int recTotalNum;

    /**
     * 推荐时间戳
     */
    // private Instant updateTime = Instant.now();
    private Date updateTime = new Date();

    /**
     * 章节测试总时长-单位秒 (单元限时测使用)
     */
    private long unitTimes;

    /**
     * 建议终止时最后一题是否做标识：true：建议终止时最后一题需要做，false：建议终止时最后一题不需要做。
     */
    private boolean doTerminateTopic;
}
