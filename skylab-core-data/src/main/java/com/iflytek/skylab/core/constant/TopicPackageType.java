package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 课时包类型
 *
 * <AUTHOR>
 */
public enum TopicPackageType implements EnumDescable {

    /**
     * 不用课时包
     */
    NONE("非课时包"),

    /**
     * 概念梳理
     */
    CONCEPT_CARDING("概念梳理"),

    /**
     * 单元备考-基础卷
     */
    UNIT_BASIC("单元备考-基础卷"),
    ;

    @Getter
    private final String desc;

    TopicPackageType(String desc) {
        this.desc = desc;
    }

    public static TopicPackageType parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (TopicPackageType e : TopicPackageType.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
