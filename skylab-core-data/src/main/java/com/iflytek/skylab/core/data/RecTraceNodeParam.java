package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/***
 * 点排序 功能 请求参数
 *
 * 溯源点点搜索场景
 * <AUTHOR>
 * @date 2023-8-14 11:20:17
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecTraceNodeParam extends FuncParam {

    public static final String FUNC_CODE = "REC_TRACE_NODE";

    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 锚点ID集合-(溯源点+目标点)
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private List<String> nodeIds;
    /**
     * （目标点）
     * 点标识id
     * (锚点)
     * 必须
     */
    @NotBlank
    private String targetNodeId;
}
