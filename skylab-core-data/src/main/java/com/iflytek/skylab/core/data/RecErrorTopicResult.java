package com.iflytek.skylab.core.data;

import com.iflytek.skylab.core.domain.EvaluationItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/***
 * 错题推荐
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecErrorTopicResult extends FuncResult {
    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return super.toJson();
    }

    /**
     * 测评推荐结果列表
     * <p>
     * 1、多轮测评推题 集合仅有1个推荐资源
     * 2、单次测评推题 集合包含全部推荐资源
     */
    private List<EvaluationItem> evaluationItems = new ArrayList<>();

    /**
     * 推荐时间戳
     */
    private Date updateTime = new Date();
}
