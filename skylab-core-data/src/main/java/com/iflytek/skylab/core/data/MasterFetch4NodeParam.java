package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/***
 * 画像获取 功能 请求参数
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class MasterFetch4NodeParam extends FuncParam {
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return MasterFetch4CatalogParam.FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }

    /**
     * 点标识Id列表
     * 锚点、考点、复习点
     * <p>
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private List<String> nodeIds;

    @JSONField(ordinal = 20)
    public Boolean learnPointUpdate = false;

}
