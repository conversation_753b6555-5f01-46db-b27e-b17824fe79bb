package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.RecNodeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/***
 * 点排序 功能 请求参数
 *
 * 薄弱点点搜索场景
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecNodeParam extends FuncParam {
    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return recNodeEnum.toString();
    }

    @Override
    public String toString() {
        return super.toString();
    }

    @JSONField(ordinal = 8)
    private RecNodeEnum recNodeEnum = RecNodeEnum.REC_NODE;

    /**
     * 点的目录列表
     * 章、节、一级复习节点
     * <p>
     * 必须
     * 目录类型通过SceneInfo学习场景推断
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private List<String> catalogIds;

    /**
     * 目标掌握度值
     * <p>
     * 可选
     * 中考复习 场景需传入设值。
     */
    @JSONField(ordinal = 15)
    private double targetMastery = 0.8;

    /**
     * 是否使用 思维拓展点（仅锚点体系，同步学场景使用）
     */
    @JSONField(ordinal = 20)
    private boolean useThinkingExpansion = true;

    /**
     * 选簇内中心点
     */
    @JSONField(ordinal = 30)
    private String selectedCenterPoint;

    /**
     * 透传扩展
     */
    // @JSONField(ordinal = 99)
    // private JSONObject recExt;
    private Map<String, Object> recExt;
}
