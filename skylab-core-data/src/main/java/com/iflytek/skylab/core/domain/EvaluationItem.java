package com.iflytek.skylab.core.domain;

import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.ResourceTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 测评推荐的资源Item 对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class EvaluationItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 推荐的资源id，题/视频/卡片
     */
    private String resNodeId;

    /**
     * 资源类型
     */
    private ResourceTypeEnum resNodeType;

    /**
     * 推荐的关联点id
     * <p>
     * 锚点/考点/复习点
     */
    private String nodeId;

    /**
     * 点名称
     * <p>
     * 锚点/考点/复习点
     */
    private String nodeName;

    /**
     * 关联点类型
     */
    private NodeTypeEnum nodeType;

    /**
     * 关联的锚点ID
     */
    private String anchorPointId;

    /**
     * 耗时
     */
    private long costTime;


    /**
     * 试题标签
     */
    private List<StandardTopicLabel> standardTopicLabels;

}
