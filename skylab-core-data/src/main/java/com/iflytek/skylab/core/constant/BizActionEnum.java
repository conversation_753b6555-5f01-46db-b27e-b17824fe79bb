package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 业务功能 Enum
 *
 * <AUTHOR>
 */
public enum BizActionEnum implements EnumDescable {

    /**
     * 同步学-测评（锚点推荐）
     */
    SYNC_EVAL("精准找弱项"),

    /**
     * 同步学-测评继续找弱项
     */
    SYNC_EVAL_CTN("同步学-仅灰点找弱项"),

    /**
     * 同步学-薄弱点搜索（锚点推荐）
     */
    SYNC_SEARCH_WEAK("高亮薄弱锚点"),

    /**
     * 同步学-推荐提升（锚点推荐）
     */
    SYNC_REC("推荐提升"),

    /**
     * 同步学-针对学（锚点推荐）
     */
    SYNC_AIM("针对学"),

    /**
     * 同步学-典例题
     */
    SYNC_TYP("典例题"),

    /**
     * 备考场景-阶段备考--知识回顾
     */
    EXAM_STAGE_REVIEW("知识回顾"),

    /**
     * 备考场景-阶段备考--考前过招
     */
    EXAM_STAGE_DEALING("考前过招"),

    /**
     * 备考场景-阶段备考--高亮薄弱考点
     */
    EXAM_STAGE_SEARCH_WEAK("高亮薄弱考点"),

    EXAM_STAGE_BREAK_THROUGH("考前突破"),
    EXAM_STAGE_INTER("阶段备考-互动题"),

    /**
     * 备考场景-单元复习--基础巩固
     */
    EXAM_UNIT_BASIS("基础巩固"),

    /**
     * 备考场景-单元复习--单元模拟
     */
    EXAM_UNIT_SIMULATION("单元模拟"),

    /**
     * 备考场景-单元复习--高亮薄弱考点
     */
    EXAM_UNIT_SEARCH_WEAK("高亮薄弱考点"),

    /**
     * 老的备考使用，新的精准学os  切换到 SYNC_OS_CHECK_REC("精准学OS-全书地图-考点推题")
     */
    EXAM_UNIT_REC("考点提升"),

    EXAM_UNIT_INTER("单元备考-互动题"),

    /**
     * 备考场景-单元复习--针对学
     */
    @Deprecated
    EXAM_UNIT_AIM("针对学"),

    /**
     * 拍搜画像-拍搜画像入参试题
     */
    PHOTO_INPUT("拍搜画像-拍搜画像入参试题"),

    /**
     * 拍搜画像-BC融合
     */
    BC_INPUT("拍搜画像-BC融合"),

    /**
     * 一轮复习-模块水平测试
     */
    FIRST_REVISE_EVAL("一轮复习-测评"),

    /**
     * 一轮复习-模块复习点排序
     */
    FIRST_REVISE_SORT("一轮复习-模块复习点排序"),

    /**
     * 一轮复习-复习点习题练习-推题
     */
    FIRST_REVISE_REC("一轮复习-推荐"),

    /**
     * 二轮复习-水平测试-测评
     */
    SECOND_REVISE_EVAL("二轮复习-水平测试-测评"),

    /**
     * 一轮复习-薄弱复习点搜索
     */
    SECOND_REVISE_SORT("一轮复习-薄弱复习点搜索"),

    /**
     * 二轮复习-复习点习题练习推荐-典例题
     */
    SECOND_REVISE_TYP("二轮复习-复习点习题练习推荐-典例题"),

    /**
     * 二轮复习-复习点习题练习推荐-变式题
     */
    SECOND_REVISE_VARY(" 二轮复习-复习点习题练习推荐-变式题"),

    /**
     * 拍照测评
     */
    EVALUATION_BY_PHOTO_RESULT("拍照测评"),

    /**
     * 课后三点半-学霸闯关（锚点推荐）
     */
    AFTER_CLASS_UNIT_TEST("课后三点半-学霸闯关（锚点推荐）"),

    /**
     * AI复习-水平测题包
     */
    AI_REVIEW_EVAL("AI复习-水平测题包"),

    /**
     * AI复习-薄弱项题包
     */
    AI_REVIEW_WEAK("AI复习-薄弱项题包"),

    /**
     * AI复习-单题推荐
     */
    AI_REVIEW_REC("AI复习-单题推荐"),

    AI_DIAGNOSIS_VARY("AI诊断举一反三"),
    AI_DIAGNOSIS_SEARCH_WEAK("AI诊断-高亮薄弱点"),
    AI_DIAGNOSIS_REC("AI诊断-推荐提升"),
    AI_DIAGNOSIS_AIM("AI诊断-针对学"),
    AI_DIAGNOSIS_REC_PACK("AI诊断-题包推荐"),

    AI_EXAM_SPECIAL_REVIEW("专题复习"),
    AI_EXAM_HARD_TOPIC("重难点题包"),
    AI_EXAM_NEVER_FORGET("遗忘巩固题包"),

    /**
     * 理科单品 AI复习-重点复习考点
     */
    AI_REVIEW_POINTS("AI复习-重点复习考点"),


    EXAM_STAGE_UNITS("推荐复习顺序"),

    /**
     * 错题本-变式题推荐
     */
    MACROGRAPH_ERROR_BOOK_VARY("错题本-变式题推荐"),
    /**
     * 错题本-溯源点推题
     */
    MACROGRAPH_ERROR_BOOK_TOPIC("错题本-溯源点推题"),

    /**
     * 错题本-订正
     */
    MACROGRAPH_ERROR_BOOK_REVISAL("错题本-订正"),


    /**
     * 溯源点排序-单点
     */
    DTP_SEARCH_WEAK("溯源点-点排序"),
    /**
     * 溯源点排序-全书
     */
    DTP_BOOK_SEARCH_WEAK("溯源点-全书排序"),
    /**
     * 溯源点-推题
     */
    DTP_SYD("溯源点-推题"),
    /**
     * 溯源点-目标点推题
     */
    DTP_TARGET("溯源点-目标点推题"),
    /**
     * 溯源点-测评
     */
    DTP_EVAL("溯源点-测评"),

    /**
     * 小学阶段测
     */
    PRIMARY_STAGE_TEST("小学阶段测"),
    /**
     * 同步习题
     */
    SYNC_TEST_REC("同步习题"),
    /**
     * 精品密卷
     */
    UALITY_TEST_PAPER_REC("精品密卷"),
    /**
     * AI提优突破
     */
    AI_IMPROVE_TEST("AI提优突破"),

    /**
     * AI试题过滤
     */
    AI_TEST_FILTER("AI试题过滤"),


    /**
     * 精准学OS-全书地图-锚点推题（锚点推题）
     */
    SYNC_OS_ANCHOR_REC("精准学OS-全书地图-锚点推题"),
    /**
     * 精准学OS-全书地图-考点推题（考点推题）
     */
    SYNC_OS_CHECK_REC("精准学OS-全书地图-考点推题"),
    /**
     * 精准学OS-全书地图-复习点推题（复习点推题）
     */
    SYNC_OS_REVIEW_REC("精准学OS-全书地图-复习点推题"),

//========================寒暑专题============================
    /**
     * 精准学OS-寒暑专题-测评
     */
    HOLIDAY_OS_EVAL("精准学OS-寒暑专题-测评"),

    /**
     * 精准学OS-寒暑专题-测评-继续找弱项
     */
    HOLIDAY_OS_EVAL_CTN("精准学OS-寒暑专题-测评-继续找弱项"),
    /**
     * 精准学OS-寒暑专题-同步习题入门测
     * 推5题
     */
    HOLIDAY_OS_AIM("精准学OS-寒暑专题-同步习题"),
    /**
     * 精准学OS-寒暑专题-出门测
     * 推10题
     */
    HOLIDAY_OS_REVIEW("精准学OS-寒暑专题-出门测"),
    /**
     * 精准学OS-寒暑专题-点排序
     */
    HOLIDAY_OS_SEARCH_WEAK("精准学OS-寒暑专题-点排序"),
    /**
     * 精准学OS-寒暑专题-点推题
     */
    HOLIDAY_OS_REC("精准学OS-寒暑专题-点推题"),


//========================寒暑专题============================

//========================备考专题============================

    EXAM_OS_MID_EVAL("期中备考-所有点找弱项"),
    EXAM_OS_MID_EVAL_CTN("期中备考-仅灰点找弱项"),
    EXAM_OS_MID_ERROR_LEARN("期中备考-易错题推题"),
    EXAM_OS_MID_STAGE_BREAK_THROUGH("期中备考-点推题"),
    EXAM_OS_MID_STAGE_SEARCH_WEAK("期中备考-点排序"),

    EXAM_OS_FINAL_EVAL("期末备考-所有点找弱项"),
    EXAM_OS_FINAL_EVAL_CTN("期末备考-仅灰点找弱项"),
    EXAM_OS_FINAL_ERROR_LEARN("期末备考-易错题推题"),
    EXAM_OS_FINAL_STAGE_BREAK_THROUGH("期末备考-点推题"),
    EXAM_OS_FINAL_STAGE_SEARCH_WEAK("期末备考-点排序"),

    EXAM_OS_UNIT_EVAL("单元复习-所有点找弱项"),
    EXAM_OS_UNIT_EVAL_CTN("单元复习-仅灰点找弱项"),

    UALITY_TEST_PAPER_OSEXAM("备考-精品密卷"),
    AI_IMPROVE_TEST_OSEXAM("备考-提优突破"),
    ERROR_BOOK_REVISAL_OSEXAM("备考-错题本订正"),
    REAL_TEST_PAPER_OEXAM(" 备考-历年真题"),


//========================备考专题============================

    //========================中考复习============================
    UALITY_TEST_PAPER_REVISE("一轮复习-精品密卷"),
    ERROR_BOOK_REVISAL_REVISE("一轮复习-错题本订正"),
    THRID_REVISE_TOPIC("三轮复习-课时包"),

    FIRST_REVISE_EVAL_CTN("一轮复习-仅灰点找弱项"),
    SECOND_REVISE_EVAL_CTN("一轮复习-仅灰点找弱项"),


//========================中考复习============================

    //========================试题屏============================
    TOPIC_SCREEN_PACK("试题屏-非订正"),
    TOPIC_SCREEN_PACK_COR("试题屏-订正"),
//========================试题屏============================
    /**
     * 错题重练(适用于所有场景)
     */
    ERROR_TOPIC_REPRACTICE("错题重练"),

    //region
    /**
     * 未知
     */
    NONE("未知"),

    /**
     * 扩展1
     */
    EXT1ST("扩展1"),

    /**
     * 扩展2
     */
    EXT2ND("扩展2"),

    /**
     * 扩展3
     */
    EXT3RD("扩展3"),

    KC_SYNC_REC("同步学-推题"),
    KC_SYNC_EVAL("同步学-流式测评-所有点找弱项"),
    KC_SYNC_EVAL_CTN("同步学-流式测评-仅灰点找弱项"),
    KC_SYNC_CLUSTER_REC_PLAN("同步学-簇下练习-AI规划学"),
    KC_SYNC_SEARCH_WEAK_PLAN("同步学-AI规划学-薄弱点推荐"),
    KC_SYNC_AIM("同步学-簇下练习-自主学点推题"),
    KC_SYNC_SEARCH_WEAK_VARIANT("同步学-簇下练习-自主学点排序"),
    KC_SYNC_SEARCH_WEAK("同步学-看板点排序"),
    KC_HOLIDAY_OS_EVAL("寒暑-所有点找弱项"),
    KC_HOLIDAY_OS_EVAL_CTN("寒暑-仅灰点找弱项"),
    KC_HOLIDAY_OS_SEARCH_WEAK("寒暑-看板点排序"),
    KC_HOLIDAY_OS_SEARCH_WEAK_VARIANT("寒暑-自主学点排序"),
    KC_HOLIDAY_OS_REC("寒暑-自主学点推题"),
    KC_HOLIDAY_OS_REVIEW("寒暑-出门测题包"),
    KC_HOLIDAY_OS_SYNC("寒暑-同步习题题包"),
    SYNC_LEARN_SYNC_PACK("同步学-同步练-非知识簇"),
    KC_SYNC_LEARN_SYNC_PACK("同步学-同步练-知识簇")

    //endregion

    ;

    @Getter
    private final String desc;

    BizActionEnum(String desc) {
        this.desc = desc;
    }

    public static BizActionEnum parse(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        for (BizActionEnum e : BizActionEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
