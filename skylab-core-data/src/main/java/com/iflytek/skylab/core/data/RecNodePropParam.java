package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 点属性诊断 请求参数
 *
 * <AUTHOR>
 * @date 2022/6/8 10:45
 */
@Getter
@Setter
@Accessors(chain = true)
@Deprecated
public class RecNodePropParam extends FuncParam {
    public static final String FUNC_CODE = "REC_NODE_PROP";
    private static final long serialVersionUID = 1L;

    /**
     * 锚点ID集合
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    private List<String> nodeIds;


    @Override
    public String getFuncCode() {
        return FUNC_CODE;
    }

    @Override
    public String toString() {
        return super.toString();
    }

}
