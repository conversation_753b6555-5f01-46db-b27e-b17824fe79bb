package com.iflytek.skylab.core.domain;

import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.ResourceTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 学情数据对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class StudyLogRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 书本.不为空时覆盖场景信息中的bookCode
     */
    private String bookCode;

    /**
     * 目录。不为空时覆盖场景信息中的catalogCode
     */
    private String catalogCode;

    /**
     * 推题时的关联 TraceId
     */
    @NotBlank
    private String refTraceId;

    /**
     * 批改TraceId
     */
    private String correctTraceId;

    /**
     * 点标识：考点Id、锚点Id、复习点Id
     */
    @NotBlank
    private String nodeId;

    /**
     * 点类型：考点、锚点、复习点
     */
    @NotNull
    private NodeTypeEnum nodeType;

    /**
     * 资源id ：题Id、视频Id、卡片Id
     */
    @NotBlank
    private String resNodeId;

    /**
     * 资源类型：题、视频、卡片
     */
    @NotNull
    private ResourceTypeEnum resNodeType;

    /**
     * 轮标识，如测评任务
     */
    private String roundId;

    /**
     * 轮序号，一次测评任务中的序号
     */
    private String roundIndex;

    /**
     * 答题耗时(单位：秒)
     */
    @NotNull
    private Integer timeCost;

    /**
     * 得分
     */
    @NotNull
    private Double score;

    /**
     * 标准得分
     */
    @NotNull
    private Double standardScore;

    /**
     * 反馈扩展
     */
    // private JSONObject feedbackExt;
    private Map<String, Object> feedbackExt;

    /**
     * 反馈时间，客户端提交时间
     */
    // private Instant feedbackTime = Instant.now();
    private Date feedbackTime = new Date();

    /**
     * 学情记录来源
     */
    private String from;

    /**
     * 批改方式.(数据埋点使用)
     */
    private String correctType;
}
