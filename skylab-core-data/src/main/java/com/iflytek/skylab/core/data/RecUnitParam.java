package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/6 11:10
 */
@Getter
@Setter
public class RecUnitParam extends FuncParam {
    private static final long serialVersionUID = 1L;

    public static final String FUNC_CODE = "REC_UNIT";

    @NotEmpty
    @JSONField(ordinal = 10)
    private List<String> catalogIds;


    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }
}
