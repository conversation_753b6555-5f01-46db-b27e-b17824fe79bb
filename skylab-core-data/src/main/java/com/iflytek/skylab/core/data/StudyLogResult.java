package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 答题记录 上报返回结果
 * <p>
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class StudyLogResult extends FuncResult {

    private static final long serialVersionUID = 1L;

    /**
     * 答题记录的Id列表
     */
    private List<String> idList;
}
