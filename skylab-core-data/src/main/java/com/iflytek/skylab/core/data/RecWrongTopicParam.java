package com.iflytek.skylab.core.data;


import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.iflytek.skylab.core.constant.RecTopicEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;


/**
 * 易錯題推薦
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecWrongTopicParam extends FuncParam {
    private static final long serialVersionUID = 4832165899083462329L;

    @JsonIgnore
    private RecTopicEnum recTopicEnum = RecTopicEnum.REC_PRONE_WRONG_TOPIC;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return recTopicEnum.toString();
    }

    /**
     * 输入点范围
     * <p>
     * 节/章/复习点一级点列表/
     */
    @NotEmpty
    @JSONField(ordinal = 10)
    List<String> catalogIds;

    /**
     * 测评任务Id
     * 调用端生成和传入，多次测评属于一次任务的，传入相同任务Id
     * <p>
     */
    @JSONField(ordinal = 15)
    private String roundId;

    /**
     * 测评题号,题号不能小于1
     * <p>
     * 必须
     */
    @Min(1)
    @JSONField(ordinal = 20)
    private int topicOrderNumber;


    /**
     * 透传扩展
     */
    private Map<String, Object> recExt;

}
