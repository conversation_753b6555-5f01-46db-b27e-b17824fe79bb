package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.RecTopicEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 错题推荐
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecErrorTopicParam extends FuncParam {

    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return recTopicEnum.toString();
    }

    @Override
    public String toString() {
        return super.toString();
    }


    /**
     * 推测类型：推题 | 再推一题| 推错题
     */
    @JSONField(ordinal = 8)
    private RecTopicEnum recTopicEnum = RecTopicEnum.REC_ERROR_TOPIC;

    /**
     * 点标识id
     * (锚点/考点/复习点)
     * 必须
     */
    @NotBlank
    private String nodeId;


    /**
     * 原始题目id
     * <p>
     * 必须
     */
    @NotBlank
    private String topicId;

    /**
     * 原始题目题型
     * <p>
     * 必须
     */
    @NotBlank
    private String topicSection;

    /**
     * 用户无论是否作答，都支持“换一换
     * <p>
     * 换一换标识
     */
    private Boolean change = false;

}
