package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.skylab.core.constant.StudyLogFuncEnum;

/**
 * <AUTHOR>
 * @date 2023/3/16 15:01
 * @description 批改行为日志
 */

public class StudyCorrectLogParam extends StudyLogParam {

//    public static final String CORRECT_FUNC_CODE = "STUDY_CORRECT_LOG";

    private static final long serialVersionUID = 1L;

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return super.getStudyLogFuncEnum().toString();
    }

    public StudyCorrectLogParam() {
        this.setStudyLogFuncEnum(StudyLogFuncEnum.STUDY_CORRECT_LOG);
    }

}
