package com.iflytek.skylab.core.data;

import com.iflytek.skylab.core.domain.MasterInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/***
 * 画像获取 功能 返回结果
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class MasterFetchResult extends FuncResult {

    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return super.toJson();
    }

    /**
     * 画像掌握度汇总信息
     */
    private List<MasterInfo> masterInfos = new ArrayList<>();

    /**
     * 是否学尖生
     */
    private Boolean isTopStudent = true;
}
