package com.iflytek.skylab.core.data;

import com.iflytek.skylab.core.constant.NodeTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/1 19:22
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TopicInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题ID
     */
    private String topicId;
    // TODO
    private String topicType;
    /**
     * 关联的 点ID
     */
    private String nodeId;
    /**
     * 点类型
     */
    private NodeTypeEnum nodeType;
    /**
     * 关联的 锚点ID
     */
    private String anchorPointId;
}