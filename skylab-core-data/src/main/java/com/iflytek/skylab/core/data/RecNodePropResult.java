package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 点属性诊断 返回结果
 *
 * <AUTHOR>
 * @date 2022/6/8 10:48
 */
@Getter
@Setter
@Accessors(chain = true)
@Deprecated
public class RecNodePropResult extends FuncResult {
    private static final long serialVersionUID = 1L;

    /**
     * 点属性结果集合
     */
    List<NodeInfo> nodeInfos = new ArrayList<>();

    @Override
    public String toString() {
        return super.toJson();
    }
}
