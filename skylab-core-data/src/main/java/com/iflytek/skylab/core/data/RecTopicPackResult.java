package com.iflytek.skylab.core.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 推提包 返回结果
 *
 * <AUTHOR>
 * @date 2022/6/8 10:58
 */
@Getter
@Setter
@Accessors(chain = true)
public class RecTopicPackResult extends FuncResult {
    private static final long serialVersionUID = 1L;

    List<NodeInfo> nodeInfos = new ArrayList<>();

    List<TopicInfo> topicInfos = new ArrayList<>();

    @Override
    public String toString() {
        return super.toJson();
    }
}
