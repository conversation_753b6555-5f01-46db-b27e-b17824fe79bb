package com.iflytek.skylab.core.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 小学精准学章节掌握度对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class PrimaryMigrationMastery implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 点标识，点id，平铺存储数据
     */
    @NotBlank
    private String nodeId;

    /**
     * 真实掌握度
     */
    @NotNull
    private Double real;

    /**
     * 预测掌握度
     */
    @NotNull
    private Double predict;
}
