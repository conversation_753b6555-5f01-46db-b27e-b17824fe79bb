package com.iflytek.skylab.core.data;


import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class BehaviorQueryParam extends FuncParam {
    public static final String FUNC_CODE = "QUERY_BEHAVIOR";

    @Override
    @JSONField(ordinal = 5)
    public String getFuncCode() {
        return FUNC_CODE;
    }


    /**
     * 查询点范围
     */
    @NotNull
    @JSONField(ordinal = 10)
    private List<NodeInfo> nodeInfos;

    /**
     * 查询起始时间
     */
    @JSONField(ordinal = 20)
    private Long startDate;

    /**
     * 查询结束时间
     */
    @JSONField(ordinal = 30)
    private Long endDate;

    /**
     * 点下最大查询作答记录条数
     */
    @JSONField(ordinal = 40)
    private Integer maxNum = 10;

}
