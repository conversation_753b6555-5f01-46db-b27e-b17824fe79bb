package com.iflytek.skylab.core.data;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 公共输出参数对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SkylabResponse<T extends FuncResult> extends Jsonable implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(position = 10, notes = "追踪Id")
    @JSONField(ordinal = 10)
    @JsonProperty(index = 10)
    private String traceId;

    @ApiModelProperty(position = 20, notes = "返回码")
    @JSONField(ordinal = 20)
    @JsonProperty(index = 20)
    private int code = 0;

    @ApiModelProperty(position = 30, notes = "返回信息")
    @JSONField(ordinal = 30)
    @JsonProperty(index = 30)
    private String message = "success";

    /**
     * 引擎输出结果 列表
     */
    @ApiModelProperty(position = 50, notes = "数据体")
    @JSONField(ordinal = 50)
    @JsonProperty(index = 50)
    private T payload;


    /**
     * 推荐上下文,业务不用关心
     */
    @ApiModelProperty(position = 100, notes = "推荐上下文（业务不用关心）")
    @JSONField(ordinal = 100)
    @JsonProperty(index = 100)
    private JSONObject context;

    /**
     * 推荐过程中，异常节点列表，供测试使用，业务侧不用关心
     */
    @ApiModelProperty(position = 120, notes = "调用异常节点列表（业务侧不用关心）")
    @JSONField(ordinal = 120)
    @JsonProperty(index = 120)
    private JSONArray errorNodes;

    @Override
    public String toString() {
        return super.toString();
    }
}
