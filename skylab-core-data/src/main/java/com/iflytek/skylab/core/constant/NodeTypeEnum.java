package com.iflytek.skylab.core.constant;

import lombok.Getter;

/**
 * 图谱节点类型
 *
 * <AUTHOR>
 * @date 2022-02-12 10:12:11
 */
public enum NodeTypeEnum implements EnumDescable {

    /**
     * 图谱锚点
     */
    ANCHOR_POINT("图谱锚点"),

    /**
     * 图谱考点
     */
    CHECK_POINT("图谱考点"),

    /**
     * 图谱知识点
     */
    KNOWLEDGE_POINT("图谱知识点"),

    /**
     * 复习点
     */
    REVIEW_POINT("复习点"),

    /**
     * 扩展1
     */
    EXT1ST("扩展1"),

    /**
     * 扩展2
     */
    EXT2ND("扩展2"),

    /**
     * 扩展3
     */
    EXT3RD("扩展3"),

    /**
     * 扩展4
     */
    EXT4TH("扩展4"),

    ;


    @Getter
    private final String desc;

    NodeTypeEnum(String desc) {
        this.desc = desc;
    }

    public static NodeTypeEnum parse(String name) {
        if (name == null || name.trim().length() == 0) {
            return null;
        }
        for (NodeTypeEnum e : NodeTypeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
