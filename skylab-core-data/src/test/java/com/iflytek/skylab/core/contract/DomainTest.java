package com.iflytek.skylab.core.contract;

import com.iflytek.skylab.core.data.MasterFetch4CatalogParam;
import com.iflytek.skylab.core.data.RecEval4InOutParam;

import java.util.Arrays;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/3/24 5:25 下午
 */
public class DomainTest {

    public static void main(String[] args) {
        MasterFetch4CatalogParam masterFetch4CatalogParam = new MasterFetch4CatalogParam();
        masterFetch4CatalogParam.setCatalogIds(Arrays.asList(UUID.randomUUID().toString(),
                UUID.randomUUID().toString(), UUID.randomUUID().toString()));

        System.out.println(masterFetch4CatalogParam);

        RecEval4InOutParam recEval4InOutParam = new RecEval4InOutParam();
        recEval4InOutParam.setTopicOrderNumber(1);
        recEval4InOutParam.setRoundId(UUID.randomUUID().toString());
        recEval4InOutParam.setCatalogIds(Arrays.asList(UUID.randomUUID().toString(),
                UUID.randomUUID().toString(), UUID.randomUUID().toString()));

        System.out.println(recEval4InOutParam);

    }
}
