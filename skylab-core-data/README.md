## skylab-core-data

平台公共数据结构、常量与领域模型

### 1. 项目概述
- 项目名称：skylab-core-data
- 项目描述：承载 DTO（请求/响应）、领域对象、枚举常量等
- 版本信息：2.0.9-SNAPSHOT（开发中）

### 2. 主要内容
- 常量枚举：StudyCodeEnum、RecEvalEnum、RecTopicEnum、MasterFuncEnum 等
- DTO：SkylabRequest/SkylabResponse、FeatureParam/Result、StudyLogParam/Result、各推荐/诊断参数与结果
- 领域对象：SceneInfo、StudyLogRecord、MasterInfo 等

### 3. 使用说明
- 作为其他模块的编译期与运行期依赖
- 包名：com.iflytek.skylab.core.*

### 4. 开发指南
- 新增协议时遵循包结构：core.data（DTO）、core.domain（领域）、core.constant（枚举）
- 保持向后兼容；对重要字段添加注释与示例

