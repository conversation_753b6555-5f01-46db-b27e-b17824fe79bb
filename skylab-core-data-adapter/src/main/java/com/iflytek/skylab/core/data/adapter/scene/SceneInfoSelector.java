package com.iflytek.skylab.core.data.adapter.scene;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.core.domain.extra.AiExamSceneInfo;
import com.iflytek.skylab.core.domain.extra.AiReviewSceneInfo;
import com.iflytek.skyline.common.api.ApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;

/**
 * <AUTHOR>
 * @date 2022/9/13 13:44
 */
public class SceneInfoSelector {

    private final GraphProperties graphProperties;

    public SceneInfoSelector(GraphProperties graphProperties) {
        this.graphProperties = graphProperties;
    }

    /**
     * 根据学习场景，推断对应的SceneInfo类型
     *
     * @param request
     * @return
     */
    public static SceneInfo select(DispatchApiRequest request) {
        return doSelect(request.getScene());
    }

    public static SceneInfo select(ApiRequest request) {
        return doSelect(request.getScene());
    }

    private static SceneInfo doSelect(JSONObject sceneJson) {
        SceneInfo sceneInfo;
        StudyCodeEnum studyCode = StudyCodeEnum.parse(sceneJson.getString("studyCode"));

        switch (studyCode) {
            case AI_EXAM:
                sceneInfo = sceneJson.to(AiExamSceneInfo.class);
                break;
            case AI_REVIEW:
                sceneInfo = sceneJson.to(AiReviewSceneInfo.class);
                break;
            case AI_DIAG:
                sceneInfo = sceneJson.to(AiDiagSceneInfo.class);
                break;
            case SYNC_OS:
                //精准学os，兼容ai诊断入参
                if (sceneJson.containsKey("nodeCatalogMap")) {
                    sceneInfo = sceneJson.to(AiDiagSceneInfo.class);
                } else {
                    sceneInfo = sceneJson.to(SceneInfo.class);
                }
                break;
            default:
                sceneInfo = sceneJson.to(SceneInfo.class);
        }
        return sceneInfo;
    }
}
