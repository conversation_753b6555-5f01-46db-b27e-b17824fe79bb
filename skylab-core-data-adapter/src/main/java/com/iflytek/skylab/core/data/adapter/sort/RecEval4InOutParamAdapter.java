package com.iflytek.skylab.core.data.adapter.sort;

import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.RecEval4InOutParam;
import com.iflytek.skylab.core.data.RecEval4InOutResult;
import com.iflytek.skylab.core.data.adapter.mapper.RecResponseMapper;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.data.adapter.mapper.SessionInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiReviewSceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/9 4:30 下午
 */
@Slf4j
@Validated
public class RecEval4InOutParamAdapter extends AbstractParamAdapter<RecEval4InOutParam, RecEval4InOutResult> {

    /**
     * 输入参数适配
     *
     * @param traceId   跟踪Id
     * @param sceneInfo 场景信息
     * @param funcParam 功能 请求参数
     * @return MultiLayerGraphRecommendRequest
     */
    @Override
    public MultiLayerGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, RecEval4InOutParam funcParam) {
        MultiLayerGraphRecommendRequest request = new MultiLayerGraphRecommendRequest();

        //转换引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);
        if (sceneInfo instanceof AiReviewSceneInfo) {
            SceneInfoMapper.INSTANCE.appendAiReviewFields((AiReviewSceneInfo) sceneInfo, engineScene);
        }

        //转换引擎会话信息
        //本轮id、本轮已推题量
        SessionInfo sessionInfo = SessionInfoMapper.INSTANCE.recEval4InOutToSession(traceId, funcParam);

        //设值 测评目录列表
        List<String> catalogIds = funcParam.getCatalogIds();

        //根据场景 设值 关联点类型
        List<String> relationNodeType = new ArrayList<>();
        relationNodeType.add(String.valueOf(getREL_NODE_TYPE_MAPPING().get(sceneInfo.getStudyCode())));

        request.setSceneInfo(engineScene);
        request.setSessionInfo(sessionInfo);
        request.setInNodeIdList(catalogIds);
        request.setRelationNodeType(relationNodeType);

        //知识簇
        request.setForbiddenNodes(funcParam.getForbiddenNodes());

        return request;
    }

    /**
     * 输出参数适配
     *
     * @param response 引擎返回
     * @return RecEval4InOutResult
     */
    @Override
    public RecEval4InOutResult adapt(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        if (response.getRecommendInfo() == null) {
            return null;
        }

        // 推荐信息
        RecEval4InOutResult result = RecResponseMapper.INSTANCE.toRecEvaluationResult(response.getRecommendInfo());
        // 测评推荐的资源Item
        result.setEvaluationItems(transferEvaluationItems(request, response));
        return result;
    }
}
