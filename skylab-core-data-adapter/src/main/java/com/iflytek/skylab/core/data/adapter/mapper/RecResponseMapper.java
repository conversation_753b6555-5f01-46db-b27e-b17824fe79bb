package com.iflytek.skylab.core.data.adapter.mapper;

import com.iflytek.hy.rec.sort.interfaces.param.NodeInfo;
import com.iflytek.hy.rec.sort.interfaces.param.RecommendInfo;
import com.iflytek.skylab.core.data.RecEval4InOutResult;
import com.iflytek.skylab.core.data.RecEval4LimitResult;
import com.iflytek.skylab.core.data.RecTopicResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 推荐结果 mapstruct对象转换
 *
 * <AUTHOR>
 */
@Mapper
public interface RecResponseMapper {

    RecResponseMapper INSTANCE = Mappers.getMapper(RecResponseMapper.class);

    /**
     * 推荐信息转换
     * @param recommendInfo 引擎推荐属性
     * @return RecEval4InOutResult
     */
    @Mapping(source = "recommendInfo.isTermination", target = "terminationFlag")
    @Mapping(source = "recommendInfo.roundTotalNum", target = "recTotalNum")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    @Mapping(source = "recommendInfo.isRecommendDoTerminateTopic", target = "doTerminateTopic")
    RecEval4InOutResult toRecEvaluationResult(RecommendInfo recommendInfo);

    /**
     * 推荐信息转换
     * @param recommendInfo 引擎推荐属性
     * @return RecEval4LimitResult
     */
    @Mapping(source = "recommendInfo.isTermination", target = "terminationFlag")
    @Mapping(source = "recommendInfo.roundTotalNum", target = "recTotalNum")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    @Mapping(source = "recommendInfo.costTime", target = "unitTimes")
    @Mapping(source = "recommendInfo.isRecommendDoTerminateTopic", target = "doTerminateTopic")
    RecEval4LimitResult toRecEval4LimitResult(RecommendInfo recommendInfo);

    /**
     * 推荐信息转换
     * @param node 点信息
     * @param recommendInfo 引擎推荐属性
     * @return RecTopicResult
     */
    @Mapping(source = "node.nodeId", target = "topicId")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    @Mapping(source = "recommendInfo.isTermination", target = "terminationFlag")
    @Mapping(source = "recommendInfo.isRecommendDoTerminateTopic", target = "doTerminateTopic")
    @Mapping(source = "recommendInfo.roundTotalNum", target = "recTotalNum")
    @Mapping(source = "recommendInfo.costTime", target = "unitTimes")
    RecTopicResult toRecTopicResult(NodeInfo node, RecommendInfo recommendInfo);

    /**
     * 推荐信息转换
     *
     * @param node          点信息
     * @param recommendInfo 引擎推荐属性
     * @return RecTopicResult
     */
    @Mapping(source = "node.nodeId", target = "topicId")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    @Mapping(source = "recommendInfo.isTermination", target = "terminationFlag")
    @Mapping(source = "recommendInfo.isRecommendDoTerminateTopic", target = "doTerminateTopic")
    @Mapping(source = "recommendInfo.roundTotalNum", target = "recTotalNum")
    @Mapping(source = "recommendInfo.costTime", target = "unitTimes")
    RecTopicResult toRecTopicResult(com.iflytek.hy.rec.errtopsort.interfaces.param.NodeInfo node, com.iflytek.hy.rec.errtopsort.interfaces.param.RecommendInfo recommendInfo);
}
