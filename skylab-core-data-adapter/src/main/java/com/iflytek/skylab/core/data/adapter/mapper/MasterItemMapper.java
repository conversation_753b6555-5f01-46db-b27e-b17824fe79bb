package com.iflytek.skylab.core.data.adapter.mapper;

import com.iflytek.hy.rec.diag.interfaces.param.MasterInfo;
import com.iflytek.skylab.core.dataapi.data.MasterItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * MasterItem mapstruct对象转换
 *
 * <AUTHOR>
 */
@Mapper
public interface MasterItemMapper {

    MasterItemMapper INSTANCE = Mappers.getMapper(MasterItemMapper.class);

    /**
     *转换引擎返回画像数据
     * @param traceId 跟踪ID
     * @param sceneInfo 场景信息
     * @param info 引擎返回用户画像计算
     * @return 用户画像掌握度Item
     */
    @Mapping(source = "sceneInfo.bizCode", target = "bizCode")
    @Mapping(source = "sceneInfo.studyCode", target = "studyCode")
    @Mapping(source = "sceneInfo.bookCode", target = "bookCode")
    @Mapping(source = "info.catalogId", target = "catalogId")
    @Mapping(source = "info.catalogType", target = "catalogType")
    @Mapping(source = "info.nodeId", target = "nodeId")
    @Mapping(source = "info.nodeType", target = "nodeType")
    @Mapping(source = "info.masterScore", target = "masteryScore")
    @Mapping(source = "info.masterType.code", target = "masteryType")
    @Mapping(source = "info.shouldFlag", target = "shouldFlag")
    @Mapping(source = "info.masterDetailInfo.fusionMasterScore", target = "fusion")
    @Mapping(source = "info.masterDetailInfo.realMasterScore", target = "real")
    @Mapping(source = "info.masterDetailInfo.predictMasterScore", target = "predict")
    @Mapping(source = "info.masterDetailInfo.algorithmFusionMasterScore", target = "algoFusion")
    @Mapping(source = "info.masterDetailInfo.algorithmRealMasterScore", target = "algoReal")
    @Mapping(source = "info.masterDetailInfo.algorithmPredictMasterScore", target = "algoPredict")
    MasterItem toMasterItem(String traceId, SceneInfo sceneInfo, MasterInfo info);

    /**
     * 转换引擎返回画像数据
     *
     * @param traceId   跟踪ID
     * @param sceneInfo 场景信息
     * @param info      引擎返回用户画像计算
     * @return 用户画像掌握度Item
     */
    @Mapping(source = "sceneInfo.bizCode", target = "bizCode")
    @Mapping(source = "sceneInfo.studyCode", target = "studyCode")
    @Mapping(source = "sceneInfo.bookCode", target = "bookCode")
    @Mapping(source = "info.catalogId", target = "catalogId")
    @Mapping(source = "info.catalogType", target = "catalogType")
    @Mapping(source = "info.nodeId", target = "nodeId")
    @Mapping(source = "info.nodeType", target = "nodeType")
    @Mapping(source = "info.masterScore", target = "masteryScore")
    @Mapping(source = "info.masterType.code", target = "masteryType")
    @Mapping(source = "info.shouldFlag", target = "shouldFlag")
    @Mapping(source = "info.masterDetailInfo.fusionMasterScore", target = "fusion")
    @Mapping(source = "info.masterDetailInfo.realMasterScore", target = "real")
    @Mapping(source = "info.masterDetailInfo.predictMasterScore", target = "predict")
    @Mapping(source = "info.masterDetailInfo.algorithmFusionMasterScore", target = "algoFusion")
    @Mapping(source = "info.masterDetailInfo.algorithmRealMasterScore", target = "algoReal")
    @Mapping(source = "info.masterDetailInfo.algorithmPredictMasterScore", target = "algoPredict")
    @Mapping(source = "info.globalMasterInfo", target = "globalMastery")
    MasterItem toMasterItem(String traceId, SceneInfo sceneInfo, com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo info);


}
