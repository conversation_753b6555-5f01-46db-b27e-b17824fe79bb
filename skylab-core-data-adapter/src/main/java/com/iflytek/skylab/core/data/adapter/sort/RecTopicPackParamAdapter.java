package com.iflytek.skylab.core.data.adapter.sort;

import cn.hutool.core.map.MapUtil;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.dataapi.exception.SkylabException;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.core.domain.extra.AiReviewSceneInfo;
import com.iflytek.skylab.core.domain.extra.AiExamSceneInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/8 11:10
 */
@Slf4j
public class RecTopicPackParamAdapter extends AbstractParamAdapter<RecTopicPackParam, RecTopicPackResult> {

    @Override
    public MultiLayerGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, RecTopicPackParam funcParam) {
        MultiLayerGraphRecommendRequest request = new MultiLayerGraphRecommendRequest();

        //转换引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);
        if (sceneInfo instanceof AiReviewSceneInfo) {
            SceneInfoMapper.INSTANCE.appendAiReviewFields((AiReviewSceneInfo) sceneInfo, engineScene);
        }
        if (sceneInfo instanceof AiDiagSceneInfo) {
            request.setInNodeChapterMap(((AiDiagSceneInfo) sceneInfo).getNodeCatalogMap());
        }
        if (sceneInfo instanceof AiExamSceneInfo) {
            engineScene.setExamType(((AiExamSceneInfo) sceneInfo).getExamType().getAiExamCode());
            engineScene.setStudentLevel(((AiExamSceneInfo) sceneInfo).getStudentLevel().getCode());
        }
        request.setSceneInfo(engineScene);

        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(traceId);

        //设值 点的目录列表
        request.setInNodeIdList(funcParam.getNodeIds());
        request.setSceneInfo(engineScene);
        request.setSessionInfo(sessionInfo);
        return request;
    }

    @Override
    public RecTopicPackResult adapt(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        RecTopicPackResult result = new RecTopicPackResult();

        List<TopicInfo> topicInfos = new ArrayList<>();
        try {
            Map<String, String> topicLogicType = new HashMap<>();
            if(null != response.getRecommendInfo() && null != response.getRecommendInfo().getTopicLogicType() ){
                topicLogicType = response.getRecommendInfo().getTopicLogicType();
            }

            Map<String, String> finalTopicLogicType = topicLogicType;
            response.getOutNodeInfos().forEach(obj -> {
                String topicId = obj.getNodeId();
                String nodeId = null==obj.getRelationNodes()?null:obj.getRelationNodes().getNodeId();
                String topicType = finalTopicLogicType.get(topicId);

                topicInfos.add(new TopicInfo()
                        .setTopicId(topicId)
                        .setTopicType(topicType)
                        .setNodeId(nodeId)
                );
            });
        } catch (Exception e) {
            log.error("推题包结果转换失败。resp:{}, message:{}", response, e.getMessage());
            throw new SkylabException(-1, "推题包结果转换失败", e);
        }

        List<NodeInfo> nodeInfos = new ArrayList<>();
        try {
            Map<String, String> nodeLogicType = new HashMap<>();

            if(null != response.getRecommendInfo() && null != response.getRecommendInfo().getNodeLogicType() ){
                nodeLogicType = response.getRecommendInfo().getNodeLogicType();
            }
            if (MapUtil.isNotEmpty(nodeLogicType)) {
                nodeLogicType.forEach((nodeId, logicType) -> nodeInfos.add(new NodeInfo().setNodeId(nodeId).setLogicType(logicType)));
            }
        } catch (Exception e) {
            log.error("点属性诊断结果转换失败。resp:{}, message:{}", response, e.getMessage());
            throw new SkylabException(-1, "点属性诊断结果转换失败", e);
        }

        return result.setTopicInfos(topicInfos).setNodeInfos(nodeInfos);
    }
}
