package com.iflytek.skylab.core.data.adapter.diag;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONB;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.hy.rec.diag.interfaces.param.SessionInfo;
import com.iflytek.hy.rec.merdiag.interfaces.param.GraphSingleDiganoseRequest;
import com.iflytek.skylab.core.data.NodeInfo;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/20 14:52
 */
@Slf4j
public class DiagSingleParamAdapter {
    public final GraphSingleDiganoseRequest adapt(String traceId, AiDiagSceneInfo sceneInfo, JSONObject apiPayload) {
        log.debug("traceId={};adapt..", traceId);

        GraphSingleDiganoseRequest request = new GraphSingleDiganoseRequest();
        //设值 引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo scene = SceneInfoMapper.INSTANCE.sceneToDiagScene(sceneInfo);

        //设值 引擎会话信息
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(traceId);

        request.setSceneInfo(scene);
        request.setSessionInfo(sessionInfo);
        //兼容
        if (sceneInfo instanceof AiDiagSceneInfo) {
            AiDiagSceneInfo aiDiagSceneInfo = (AiDiagSceneInfo) sceneInfo;
            request.setInNodeChapterMap(aiDiagSceneInfo.getNodeCatalogMap());
        }
        if (CollUtil.isEmpty(request.getInNodeChapterMap())) {
            log.debug("sceneInfo 未获取到NodeCatalogMap，从nodeInfos中获取");
            List<NodeInfo> nodeInfos = apiPayload.getList("nodeInfos", NodeInfo.class);
            if (CollUtil.isNotEmpty(nodeInfos)) {
                Map<String, String> nodeCatalogMap = nodeInfos.stream().collect(Collectors.toMap(NodeInfo::getNodeId, NodeInfo::getCatalogId));
                request.setInNodeChapterMap(nodeCatalogMap);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("traceId={}; GraphDiagnoseRequest= {}", traceId, request);
            log.debug("traceId={}; GraphDiagnoseRequest:Json= {}", traceId, JSON.toJSONString(request));
        }
        return request;
    }


    /**
     * 输入参数适配
     *
     * @param traceId            跟踪Id
     * @param sceneInfo          场景信息
     * @param dispatchApiPayload 入参数据
     * @return GraphDiagnoseRequest 引擎入参
     */
    public final GraphSingleDiganoseRequest adapt(@Valid @NotBlank String traceId, @NotNull AiDiagSceneInfo sceneInfo, @NotNull DispatchApiPayload dispatchApiPayload) {
        //设值 目录/点列表  暂时采用 取属性的方式，后期可以考虑反射
        //JSONObject masterFetchParamObj = dispatchApiPayload.getData(JSONObject.class);
        JSONObject masterFetchParamObj = null;
        if (dispatchApiPayload.getData().size() != 0 && dispatchApiPayload.getData().get(0).getData() != null) {
            masterFetchParamObj = JSONB.parseObject(dispatchApiPayload.getData().get(0).getData().toJSONBBytes(), JSONObject.class);
        }
        return adapt(traceId, sceneInfo, masterFetchParamObj);
    }


    private List<String> getListObj(JSONObject jsonObject, String key) {
        return jsonObject.containsKey(key) ? jsonObject.getObject(key, new TypeReference<List<String>>() {
        }) : Collections.emptyList();
    }


}
