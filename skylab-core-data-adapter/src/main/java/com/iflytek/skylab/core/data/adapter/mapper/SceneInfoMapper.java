package com.iflytek.skylab.core.data.adapter.mapper;

import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiReviewSceneInfo;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * SceneInfo mapstruct对象转换
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneInfoMapper {

    SceneInfoMapper INSTANCE = Mappers.getMapper(SceneInfoMapper.class);

    /**
     *转换引擎场景信息
     * @param sceneInfo 场景信息
     * @return 引擎场景信息
     */
    @Mapping(source = "bizCode", target = "bizCode")
    @Mapping(source = "subjectCode", target = "subjectCode")
    @Mapping(source = "phaseCode", target = "phaseCode")
    @Mapping(source = "bookCode", target = "bookCode")
    @Mapping(source = "pressCode", target = "bookVersion")
    @Mapping(source = "areaCode", target = "areaCode")
    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "studyCode", target = "studyCode")
    @Mapping(source = "graphVersion", target = "graphVersion")
    @Mapping(source = "functionCode", target = "functionCode")
    @Mapping(source = "layerType", target = "layerType")
    @Mapping(source = "layerVersion", target = "layerVersion")
    com.iflytek.hy.rec.domain.model.valueobj.SceneInfo sceneToSortScene(SceneInfo sceneInfo);

    /**
     *转换引擎场景信息
     * @param sceneInfo 场景信息
     * @return 引擎场景信息
     */
    @Mapping(source = "bizAction", target = "bizAction")
    @Mapping(source = "bizCode", target = "bizCode")
    @Mapping(source = "subjectCode", target = "subjectCode")
    @Mapping(source = "phaseCode", target = "phaseCode")
    @Mapping(source = "bookCode", target = "bookCode")
    @Mapping(source = "pressCode", target = "bookVersion")
    @Mapping(source = "areaCode", target = "areaCode")
    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "studyCode", target = "studyCode")
    @Mapping(source = "graphVersion", target = "graphVersion")
    @Mapping(source = "functionCode", target = "functionCode")
    @Mapping(source = "layerType", target = "layerType")
    @Mapping(source = "layerVersion", target = "layerVersion")
    com.iflytek.hy.rec.domain.model.valueobj.SceneInfo sceneToFeatureScene(SceneInfo sceneInfo);


    /**
     * 设值 引擎场景信息
     * @param sceneInfo 场景信息
     * @return SceneInfo 引擎场景信息
     */
    @Mapping(source = "bizAction", target = "bizAction")
    @Mapping(source = "bizCode", target = "bizCode")
    @Mapping(source = "functionCode", target = "functionCode")
    @Mapping(source = "subjectCode", target = "subjectCode")
    @Mapping(source = "phaseCode", target = "phaseCode")
    @Mapping(source = "bookCode", target = "bookCode")
    @Mapping(source = "pressCode", target = "bookVersion")
    @Mapping(source = "areaCode", target = "areaCode")
    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "studyCode", target = "studyCode")
    @Mapping(source = "graphVersion", target = "graphVersion")
    @Mapping(source = "layerVersion", target = "layerVersion")
    com.iflytek.hy.rec.domain.model.valueobj.SceneInfo sceneToDiagScene(SceneInfo sceneInfo);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(source = "studentLevel.code", target = "studentLevel")
    @Mapping(source = "examType.aiReviewCode", target = "examType")
    @Mapping(source = "planDiff.code", target = "planDiff")
    @Mapping(source = "pointType", target = "pointType")
    void appendAiReviewFields(AiReviewSceneInfo sceneInfo, @MappingTarget com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene);
}
