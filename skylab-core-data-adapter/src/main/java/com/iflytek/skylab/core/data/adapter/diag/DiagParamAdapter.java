package com.iflytek.skylab.core.data.adapter.diag;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONB;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseRequest;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.hy.rec.diag.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.MasterFetch5BookParam;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.data.NodeInfo;
import com.iflytek.skylab.core.data.adapter.mapper.MasterInfoMapper;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.MasterInfo;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiReviewSceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 参数适配器
 *
 * <AUTHOR>
 * @date 2022/3/9 4:22 下午
 */
@Slf4j
public class DiagParamAdapter {

    public final GraphDiagnoseRequest adapt(String traceId, SceneInfo sceneInfo, JSONObject apiPayload) {
        log.debug("traceId={};adapt..", traceId);

        GraphDiagnoseRequest request = new GraphDiagnoseRequest();
        //设值 引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo scene = SceneInfoMapper.INSTANCE.sceneToDiagScene(sceneInfo);
        if (sceneInfo instanceof AiReviewSceneInfo) {
            SceneInfoMapper.INSTANCE.appendAiReviewFields((AiReviewSceneInfo) sceneInfo, scene);
        }

        //设值 引擎会话信息
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(traceId);

        request.setSceneInfo(scene);
        request.setSessionInfo(sessionInfo);
        request.setCatalogIds(getListObj(apiPayload, "catalogIds"));
        request.setNodeIds(getListObj(apiPayload, "nodeIds"));
        if (CollUtil.isEmpty(request.getNodeIds())) {
            List<NodeInfo> nodeInfos = apiPayload.getList("nodeInfos", NodeInfo.class);
            if (CollUtil.isNotEmpty(nodeInfos)) {
                List<String> collect = nodeInfos.stream().map(NodeInfo::getNodeId).collect(Collectors.toList());
                request.setNodeIds(collect);
            }
        }
        request.setAllUpdate(getObj(apiPayload, "allUpdate", boolean.class, false));
        request.setLearnPointUpdate(getObj(apiPayload, "learnPointUpdate", boolean.class, false));

        if (log.isDebugEnabled()) {
            log.debug("traceId={}; GraphDiagnoseRequest= {}", traceId, request);
            log.debug("traceId={}; GraphDiagnoseRequest:Json= {}", traceId, JSON.toJSONString(request));
        }
        return request;
    }


    /**
     * 输入参数适配
     *
     * @param traceId            跟踪Id
     * @param sceneInfo          场景信息
     * @param dispatchApiPayload 入参数据
     * @return GraphDiagnoseRequest 引擎入参
     */
    public final GraphDiagnoseRequest adapt(@Valid @NotBlank String traceId, @NotNull SceneInfo sceneInfo, @NotNull DispatchApiPayload dispatchApiPayload) {
        //设值 目录/点列表  暂时采用 取属性的方式，后期可以考虑反射
        //JSONObject masterFetchParamObj = dispatchApiPayload.getData(JSONObject.class);
        JSONObject masterFetchParamObj = null;
        if (dispatchApiPayload.getData().size() != 0 && dispatchApiPayload.getData().get(0).getData() != null) {
            masterFetchParamObj = JSONB.parseObject(dispatchApiPayload.getData().get(0).getData().toJSONBBytes(), JSONObject.class);
        }
        return adapt(traceId, sceneInfo, masterFetchParamObj);
    }

    /**
     * 输入参数适配
     *
     * @param dispatchApiPayload 入参数据
     * @return GraphDiagnoseRequest 引擎入参
     */
    public final MasterFetch5BookParam adaptFetch5Book(@NotNull DispatchApiPayload dispatchApiPayload) {
        //设值 目录/点列表  暂时采用 取属性的方式，后期可以考虑反射
        //JSONObject masterFetchParamObj = dispatchApiPayload.getData(JSONObject.class);
        JSONObject masterFetchParamObj = null;
        if (dispatchApiPayload.getData().size() != 0 && dispatchApiPayload.getData().get(0).getData() != null) {
            masterFetchParamObj = JSONB.parseObject(dispatchApiPayload.getData().get(0).getData().toJSONBBytes(), JSONObject.class);
        }
        return masterFetchParamObj.to(MasterFetch5BookParam.class);
    }

    /**
     * 输出参数适配
     *
     * @param nodeIds          顶点列表
     * @param diagnoseResponse 引擎返回
     * @return MasterFetchResult 画像获取结果
     */
    public final MasterFetchResult adapt(List<String> nodeIds, GraphDiagnoseResponse diagnoseResponse) {
        MasterFetchResult result = new MasterFetchResult();
        //根据引擎返回数据，过滤仅返回 用户请求的数据
        List<com.iflytek.hy.rec.diag.interfaces.param.MasterInfo> diagMasterInfoList = diagnoseResponse.getMasterInfoList();
        List<MasterInfo> masterInfoList = new ArrayList<>();
        for (com.iflytek.hy.rec.diag.interfaces.param.MasterInfo info : diagMasterInfoList) {
            //过滤掉 非请求的nodeId
            if (!CollectionUtils.isEmpty(nodeIds) && !nodeIds.contains(info.getNodeId())) {
                continue;
            }
            //转换引擎返回画像数据 到用户画像对象
            MasterInfo mastery = MasterInfoMapper.INSTANCE.toMasterInfo(info);

            masterInfoList.add(mastery);
        }
        result.setMasterInfos(masterInfoList);
        if (log.isDebugEnabled()) {
            log.debug("MasterFetchResult= {}", result);
        }
        return result;
    }

    /**
     * 输出参数适配
     *
     * @param nodeIds          顶点列表
     * @param diagnoseResponse 引擎返回
     * @return MasterFetchResult 画像获取结果
     */
    public final MasterFetchResult adapt(List<String> nodeIds, com.iflytek.hy.rec.dtpmapdiag.interfaces.param.GraphDiagnoseResponse diagnoseResponse) {
        MasterFetchResult result = new MasterFetchResult();
        //根据引擎返回数据，过滤仅返回 用户请求的数据
        List<com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo> diagMasterInfoList = diagnoseResponse.getMasterInfoList();
        List<MasterInfo> masterInfoList = new ArrayList<>();
        for (com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo info : diagMasterInfoList) {
            //过滤掉 非请求的nodeId
            if (!CollectionUtils.isEmpty(nodeIds) && !nodeIds.contains(info.getNodeId())) {
                continue;
            }
            //转换引擎返回画像数据 到用户画像对象
            MasterInfo mastery = MasterInfoMapper.INSTANCE.toMasterInfo(info);

            masterInfoList.add(mastery);
        }
        result.setMasterInfos(masterInfoList);
        if (log.isDebugEnabled()) {
            log.debug("MasterFetchResult= {}", result);
        }
        return result;
    }

    private List<String> getListObj(JSONObject jsonObject, String key) {
        return jsonObject.containsKey(key) ? jsonObject.getObject(key, new TypeReference<List<String>>() {
        }) : Collections.emptyList();
    }

    private <T> T getObj(JSONObject jsonObject, String key, Class<T> clazz, T defaultValue) {
        if (jsonObject.containsKey(key)) {
            T jsonObjectObject = jsonObject.getObject(key, clazz);
            if (jsonObjectObject != null) {
                return jsonObjectObject;
            }
            return defaultValue;
        } else {
            return defaultValue;
        }
    }
}
