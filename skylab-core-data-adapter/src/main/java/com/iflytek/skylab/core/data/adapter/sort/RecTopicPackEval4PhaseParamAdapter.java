package com.iflytek.skylab.core.data.adapter.sort;

import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.RecTopicPackEval4PhaseParam;
import com.iflytek.skylab.core.data.RecTopicPackResult;
import com.iflytek.skylab.core.data.TopicInfo;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.dataapi.exception.SkylabException;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiExamSceneInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/29 17:44
 */
@Slf4j
public class RecTopicPackEval4PhaseParamAdapter extends AbstractParamAdapter<RecTopicPackEval4PhaseParam, RecTopicPackResult> {

    @Override
    public MultiLayerGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, RecTopicPackEval4PhaseParam funcParam) {
        if (log.isDebugEnabled()) {
            log.debug("sceneInfo={}, funcParam={}", sceneInfo, funcParam);
        }
        MultiLayerGraphRecommendRequest request = new MultiLayerGraphRecommendRequest();

        //转换引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);
        if (sceneInfo instanceof AiExamSceneInfo) {
            engineScene.setExamType(((AiExamSceneInfo) sceneInfo).getExamType().getAiExamCode());
            engineScene.setStudentLevel(((AiExamSceneInfo) sceneInfo).getStudentLevel().getCode());
        }
        request.setSceneInfo(engineScene);

        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(traceId);
        request.setSessionInfo(sessionInfo);

        //设值 点的目录列表
        request.setInNodeIdList(funcParam.getExamIds());
        if (log.isDebugEnabled()) {
            log.debug("RecTopicPackEval4PhaseParamAdapter result={}", request);
        }
        return request;
    }

    @Override
    public RecTopicPackResult adapt(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        RecTopicPackResult result = new RecTopicPackResult();

        List<TopicInfo> topicInfos = new ArrayList<>();
        try {
            Map<String, String> topicLogicType = response.getRecommendInfo().getTopicLogicType();

            response.getOutNodeInfos().forEach(obj -> {
                String topicId = obj.getNodeId();
                String nodeId = obj.getRelationNodes().getNodeId();
                String topicType = topicLogicType.get(topicId);

                topicInfos.add(new TopicInfo()
                        .setTopicId(topicId)
                        .setTopicType(topicType)
                        .setNodeId(nodeId)
                );
            });
        } catch (Exception e) {
            log.error("阶段测题包结果转换失败。resp:{}, message:{}", response, e.getMessage());
            throw new SkylabException(-1, "阶段测题包结果转换失败", e);
        }
        return result.setTopicInfos(topicInfos);
    }
}
