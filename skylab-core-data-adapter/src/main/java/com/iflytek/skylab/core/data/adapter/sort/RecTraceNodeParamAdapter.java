package com.iflytek.skylab.core.data.adapter.sort;

import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.NodeInfo;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.RecNodeResult;
import com.iflytek.skylab.core.data.RecTraceNodeParam;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.List;

/**
 * 点搜索 参数转换
 *
 * <AUTHOR>
 * @date 2022/3/9 4:30 下午
 */
@Slf4j
public class RecTraceNodeParamAdapter extends AbstractParamAdapter<RecTraceNodeParam, RecNodeResult> implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    /**
     * 点搜索 引擎输入
     *
     * @param traceId   跟踪Id
     * @param sceneInfo 场景信息
     * @param funcParam 功能 请求参数
     * @return MultiLayerGraphRecommendRequest
     */
    @Override
    public MultiLayerGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, RecTraceNodeParam funcParam) {
        MultiLayerGraphRecommendRequest request = new MultiLayerGraphRecommendRequest();

        //转换引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);

        if (sceneInfo instanceof AiDiagSceneInfo) {
            request.setInNodeChapterMap(((AiDiagSceneInfo) sceneInfo).getNodeCatalogMap());
        }
        engineScene.setTargetPoint(funcParam.getTargetNodeId());

        List<String> nodeIds = funcParam.getNodeIds();

        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(traceId);


        request.setSceneInfo(engineScene);
        request.setSessionInfo(sessionInfo);
        request.setInNodeIdList(nodeIds);

        return request;
    }

    /**
     * 点搜索 返回
     *
     * @param request  引擎入参
     * @param response 引擎返回
     * @return RecNodeResult
     */
    @Override
    public RecNodeResult adapt(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        RecNodeResult result = new RecNodeResult();
        //推荐点列表
        List<String> nodeIds = new ArrayList<>();
        List<NodeInfo> outNodeInfos = response.getOutNodeInfos();
        for (NodeInfo node : outNodeInfos) {
            nodeIds.add(node.getNodeId());
        }
        result.setNodeIds(nodeIds);

        //推荐点建议终止
        if (response.getRecommendInfo() != null && response.getRecommendInfo().getIsTermination() != null) {
            result.setPointRecommendEnd(response.getRecommendInfo().getIsTermination());
        }
        // 返回结果
        return result;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
