package com.iflytek.skylab.core.data.adapter.feature;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONB;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.hy.rec.feaacquire.interfaces.param.FeaAcquireRequest;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

/**
 * 参数适配器
 *
 * <AUTHOR>
 * @date 2022/3/9 4:22 下午
 */
@Slf4j
public class AcquireFeatureParamAdapter {

    public final FeaAcquireRequest adapt(String traceId, SceneInfo sceneInfo, JSONObject apiPayload) {
        log.debug("traceId={};adapt..", traceId);

        FeaAcquireRequest request = new FeaAcquireRequest();
        //设值 引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo scene = SceneInfoMapper.INSTANCE.sceneToFeatureScene(sceneInfo);

        //设值 引擎会话信息;
        request.setTraceId(traceId);
        request.setSceneInfo(scene);

        request.setFeaNameList(getListObj(apiPayload, "featureNames"));
        request.setInNodeIdList(getListObj(apiPayload, "params"));

        if (log.isDebugEnabled()) {
            log.debug("traceId={}; FeaAcquireRequest= {}", traceId, request);
            log.debug("traceId={}; FeaAcquireRequest:Json= {}", traceId, JSON.toJSONString(request));
        }
        return request;
    }


    /**
     * 输入参数适配
     *
     * @param traceId            跟踪Id
     * @param sceneInfo          场景信息
     * @param dispatchApiPayload 入参数据
     * @return GraphDiagnoseRequest 引擎入参
     */
    public final FeaAcquireRequest adapt(@Valid @NotBlank String traceId, @NotNull SceneInfo sceneInfo, @NotNull DispatchApiPayload dispatchApiPayload) {
        //设值 目录/点列表  暂时采用 取属性的方式，后期可以考虑反射
        //JSONObject masterFetchParamObj = dispatchApiPayload.getData(JSONObject.class);
        JSONObject feaAcquireParamObj = null;
        if(dispatchApiPayload.getData().size() != 0 && dispatchApiPayload.getData().get(0).getData() != null){
            feaAcquireParamObj = JSONB.parseObject(dispatchApiPayload.getData().get(0).getData().toJSONBBytes(), JSONObject.class);
        }
        return adapt(traceId, sceneInfo, feaAcquireParamObj);
    }

    private List<String> getListObj(JSONObject jsonObject, String key) {
        return jsonObject.containsKey(key) ? jsonObject.getObject(key, new TypeReference<List<String>>() {
        }) : Collections.emptyList();
    }


}
