package com.iflytek.skylab.core.data.adapter.sort.errortopic;

import com.google.common.collect.Maps;
import com.iflytek.hy.rec.errtopsort.interfaces.param.ErrorTopicGraphRecommendRequest;
import com.iflytek.hy.rec.errtopsort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.errtopsort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.RecErrorTopicParam;
import com.iflytek.skylab.core.data.RecErrorTopicResult;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 错题本参数适配器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/10 14:44
 */
@Slf4j
@Validated
public class RecErrorTopicParamAdapter extends ErrorTopicAbstractParamAdapter<RecErrorTopicParam, RecErrorTopicResult> {

    /**
     * 引擎接口输出 转换成接口输出 抽象方法
     *
     * @param request  引擎入参
     * @param response 引擎返回
     * @return 接口输出
     */
    @Override
    public RecErrorTopicResult adapt(ErrorTopicGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {

        if (response.getRecommendInfo() == null) {
            return null;
        }

        // 推荐信息
        RecErrorTopicResult result = new RecErrorTopicResult();
        // 测评推荐的资源Item
        result.setEvaluationItems(transferEvaluationItems(request, response));

        return result;

    }

    /**
     * 入参转换成引擎接口入参 抽象方法
     *
     * @param traceId   跟踪Id
     * @param sceneInfo 场景信息
     * @param funcParam 功能 请求参数
     * @return ErrorTopicGraphRecommendRequest
     */
    @Override
    public ErrorTopicGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, RecErrorTopicParam funcParam) {

        if (log.isDebugEnabled()) {
            log.debug("sceneInfo={}, funcParam={}", sceneInfo, funcParam);
        }
        ErrorTopicGraphRecommendRequest request = new ErrorTopicGraphRecommendRequest();

        //转换引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);

        //换一换
        engineScene.setTfButton(funcParam.getChange());

        request.setSceneInfo(engineScene);

        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(traceId);

        //点标识id
        List<String> inNodeIdList = new ArrayList<>();
        inNodeIdList.add(funcParam.getNodeId());

        request.setSceneInfo(engineScene);
        request.setSessionInfo(sessionInfo);
        request.setInNodeIdList(inNodeIdList);

        Map<String, String> forbiddenTopics = Maps.newHashMap();
        forbiddenTopics.put(funcParam.getTopicId(), funcParam.getTopicSection());
        //题目题型
        request.setForbiddenTopics(forbiddenTopics);

        if (log.isDebugEnabled()) {
            log.debug("RecErrorTopicParamAdapter result={}", request);
        }
        return request;
    }
}
