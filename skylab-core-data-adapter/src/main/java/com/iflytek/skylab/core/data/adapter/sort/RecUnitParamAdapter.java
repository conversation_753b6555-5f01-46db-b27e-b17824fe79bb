package com.iflytek.skylab.core.data.adapter.sort;
import com.google.common.collect.Lists;

import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.NodeInfo;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.RecUnitParam;
import com.iflytek.skylab.core.data.RecUnitResult;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/13 15:44
 */
@Slf4j
public class RecUnitParamAdapter extends AbstractParamAdapter<RecUnitParam, RecUnitResult> {
    @Override
    public MultiLayerGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, RecUnitParam funcParam) {

        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);
        SessionInfo engineSession = new SessionInfo().setTraceId(traceId);

        MultiLayerGraphRecommendRequest request = new MultiLayerGraphRecommendRequest();
        request.setSceneInfo(engineScene);
        request.setSessionInfo(engineSession);
        request.setInNodeIdList(funcParam.getCatalogIds());
        return request;
    }

    @Override
    public RecUnitResult adapt(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        List<String> catalogIds = response.getOutNodeInfos()
                .stream()
                .map(NodeInfo::getNodeId)
                .collect(Collectors.toList());

        return new RecUnitResult().setCatalogIds(catalogIds);
    }
}
