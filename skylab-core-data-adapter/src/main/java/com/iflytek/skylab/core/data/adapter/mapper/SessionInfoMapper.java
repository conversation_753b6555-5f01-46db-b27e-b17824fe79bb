package com.iflytek.skylab.core.data.adapter.mapper;

import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.RecEval4InOutParam;
import com.iflytek.skylab.core.data.RecEval4LimitParam;
import com.iflytek.skylab.core.data.RecTopicParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * SessionInfo mapstruct对象转换
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionInfoMapper {

    SessionInfoMapper INSTANCE = Mappers.getMapper(SessionInfoMapper.class);

    /**
     * 转换引擎会话信息
     * @param traceId 跟踪Id
     * @param funcParam 功能 请求参数
     * @return SessionInfo
     */
    @Mapping(source = "traceId", target = "traceId")
    @Mapping(source = "funcParam.roundId", target = "roundId")
    @Mapping(source = "funcParam.topicOrderNumber", target = "roundRecNum")
    SessionInfo recEval4InOutToSession(String traceId, RecEval4InOutParam funcParam);

    /**
     * 转换引擎会话信息
     * @param traceId 跟踪Id
     * @param funcParam 功能 请求参数
     * @return SessionInfo
     */
    @Mapping(source = "traceId", target = "traceId")
    @Mapping(source = "funcParam.roundId", target = "roundId")
    @Mapping(source = "funcParam.topicOrderNumber", target = "roundRecNum")
    SessionInfo recEval4LimitToSession(String traceId, RecEval4LimitParam funcParam);

    /**
     * 转换引擎会话信息
     *
     * @param traceId   跟踪Id
     * @param funcParam 功能 请求参数
     * @return SessionInfo
     */
    @Mapping(source = "traceId", target = "traceId")
    @Mapping(source = "funcParam.roundId", target = "roundId")
    @Mapping(source = "funcParam.topicOrderNumber", target = "roundRecNum")
    SessionInfo recTopicParamToSession(String traceId, RecTopicParam funcParam);

    /**
     * 转换引擎会话信息
     *
     * @param traceId   跟踪Id
     * @param funcParam 功能 请求参数
     * @return SessionInfo
     */
    @Mapping(source = "traceId", target = "traceId")
    @Mapping(source = "funcParam.roundId", target = "roundId")
    @Mapping(source = "funcParam.topicOrderNumber", target = "roundRecNum")
    com.iflytek.hy.rec.errtopsort.interfaces.param.SessionInfo recErrorTopicParamToSession(String traceId, RecTopicParam funcParam);
}
