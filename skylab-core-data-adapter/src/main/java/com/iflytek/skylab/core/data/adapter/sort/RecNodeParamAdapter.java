package com.iflytek.skylab.core.data.adapter.sort;

import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.NodeInfo;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.RecNodeParam;
import com.iflytek.skylab.core.data.RecNodeResult;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.core.domain.extra.AiReviewSceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 点搜索 参数转换
 *
 * <AUTHOR>
 * @date 2022/3/9 4:30 下午
 */
@Slf4j
public class RecNodeParamAdapter extends AbstractParamAdapter<RecNodeParam, RecNodeResult> implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    /**
     * 点搜索 引擎输入
     *
     * @param traceId   跟踪Id
     * @param sceneInfo 场景信息
     * @param funcParam 功能 请求参数
     * @return MultiLayerGraphRecommendRequest
     */
    @Override
    public MultiLayerGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, RecNodeParam funcParam) {
        MultiLayerGraphRecommendRequest request = new MultiLayerGraphRecommendRequest();

        //转换引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);
        if (sceneInfo instanceof AiReviewSceneInfo) {
            SceneInfoMapper.INSTANCE.appendAiReviewFields((AiReviewSceneInfo) sceneInfo, engineScene);
        }
        if (sceneInfo instanceof AiDiagSceneInfo) {
            request.setInNodeChapterMap(((AiDiagSceneInfo) sceneInfo).getNodeCatalogMap());
        }

        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(traceId);

        //设值 点的目录列表
        List<String> catalogIds = funcParam.getCatalogIds();

        //设值 目标掌握度
        double targetMastery = funcParam.getTargetMastery();

        //是否使用 思维拓展点（仅锚点体系，同步学场景使用）
        if (StudyCodeEnum.SYNC_LEARN == sceneInfo.getStudyCode()) {
            if (!funcParam.isUseThinkingExpansion()) {
                Map<String, String> nodePropertyFilter = new HashMap<>(1);
                nodePropertyFilter.put("anchorPointType", "thinkingExpansion");
                request.setNodePropertFilter(nodePropertyFilter);
            }
        }

        request.setSceneInfo(engineScene);
        request.setSessionInfo(sessionInfo);
        request.setInNodeIdList(catalogIds);
        request.setTargetMastery(targetMastery);

        request.setSelectedCenterPoint(funcParam.getSelectedCenterPoint());

        return request;
    }

    /**
     * 点搜索 返回
     *
     * @param request  引擎入参
     * @param response 引擎返回
     * @return RecNodeResult
     */
    @Override
    public RecNodeResult adapt(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        RecNodeResult result = new RecNodeResult();
        //推荐点列表
        List<String> nodeIds = new ArrayList<>();
        List<NodeInfo> outNodeInfos = response.getOutNodeInfos();
        for (NodeInfo node : outNodeInfos) {
            nodeIds.add(node.getNodeId());
        }
        result.setNodeIds(nodeIds);

        //推荐点建议终止
        if (response.getRecommendInfo() != null && response.getRecommendInfo().getIsTermination() != null) {
            result.setPointRecommendEnd(response.getRecommendInfo().getIsTermination());
        }

//        兼容老系统 ，调用数据句柄 查询应学点数据；和罗俊确认，废弃业务字段
//        try {
//            String traceId = request.getSessionInfo().getTraceId();
//            ShouldLearnPointQuery query = new ShouldLearnPointQuery();
//            query.setUserId(request.getSceneInfo().getUserId());
//            query.setStudyCode(StudyCodeEnum.parse(request.getSceneInfo().getStudyCode()));
//            query.setCatalogIds(request.getInNodeIdList());
//            MasterService masterService = applicationContext.getBean(MasterService.class);
//            List<String> learnPoints = masterService.queryLearnPoints(traceId, query);
//            result.setLearnPoints(learnPoints);
//        } catch (BeansException be) {
//            log.warn("查询应学点失败。找不到 bean: masterService");
//        } catch (Exception e) {
//            log.warn("查询应学点失败。{}", e.getMessage());
//        }

        // 返回结果
        return result;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
