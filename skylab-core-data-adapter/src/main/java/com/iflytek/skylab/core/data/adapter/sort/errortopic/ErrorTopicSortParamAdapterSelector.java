package com.iflytek.skylab.core.data.adapter.sort.errortopic;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.dataapi.exception.SkylabException;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/7 15:45
 */
@Slf4j
public class ErrorTopicSortParamAdapterSelector implements ApplicationContextAware {

    private ApplicationContext context;

    public ErrorTopicAbstractParamAdapter<?, ?> select(SceneInfo sceneInfo, DispatchApiPayload payload) {
        try {
            JSONObject data = payload.getData().get(0).getData();
            return doSelect(sceneInfo, data);
        } catch (Exception e) {
            throw new SkylabException(-1, String.format("Not found the functionCode=%s paramAdapter.", sceneInfo.getFunctionCode()));
        }
    }

    public ErrorTopicAbstractParamAdapter<?, ?> select(SceneInfo sceneInfo, JSONObject data) {
        try {
            return doSelect(sceneInfo, data);
        } catch (Exception e) {
            throw new SkylabException(-1, String.format("Not found the functionCode=%s paramAdapter.", sceneInfo.getFunctionCode()));
        }
    }

    private ErrorTopicAbstractParamAdapter<?, ?> doSelect(SceneInfo sceneInfo, JSONObject data) {
        String funcCode = sceneInfo.getFunctionCode();
        return context.getBean(funcCode, ErrorTopicAbstractParamAdapter.class);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
    }
}
