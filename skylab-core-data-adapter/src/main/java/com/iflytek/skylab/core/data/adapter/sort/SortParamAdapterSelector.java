package com.iflytek.skylab.core.data.adapter.sort;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.RecTopicPackParam;
import com.iflytek.skylab.core.dataapi.exception.SkylabException;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * <AUTHOR>
 * @date 2022/9/30 10:44
 */
@Slf4j
public class SortParamAdapterSelector implements ApplicationContextAware {

    private ApplicationContext context;

    public AbstractParamAdapter<?, ?> select(SceneInfo sceneInfo, DispatchApiPayload payload) {
        try {
            JSONObject data = payload.getData().get(0).getData();
            return doSelect(sceneInfo, data);
        } catch (Exception e) {
            throw new SkylabException(-1, String.format("Not found the functionCode=%s paramAdapter.", sceneInfo.getFunctionCode()));
        }
    }

    public AbstractParamAdapter<?, ?> select(SceneInfo sceneInfo, JSONObject data) {
        try {
            return doSelect(sceneInfo, data);
        } catch (Exception e) {
            throw new SkylabException(-1, String.format("Not found the functionCode=%s paramAdapter.", sceneInfo.getFunctionCode()));
        }
    }

    private AbstractParamAdapter<?, ?> doSelect(SceneInfo sceneInfo, JSONObject data) {
        StudyCodeEnum studyCode = sceneInfo.getStudyCode();
        String funcCode = sceneInfo.getFunctionCode();

        // TODO 同一个funcCode对应俩参数，有点难搞. 这里先特殊处理下
        if (studyCode == StudyCodeEnum.AI_EXAM
                && RecTopicPackParam.FUNC_CODE.equals(funcCode)
                && data.containsKey("catalogIds")) {

            return context.getBean(RecTopicPack4CatalogParamAdapter.class);
        }
        return context.getBean(funcCode, AbstractParamAdapter.class);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
    }
}
