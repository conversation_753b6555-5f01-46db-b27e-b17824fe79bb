package com.iflytek.skylab.core.data.adapter.sort.v2;

import com.alibaba.fastjson2.JSONB;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.core.domain.extra.AiReviewSceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.util.Assert;

import java.lang.reflect.ParameterizedType;

/**
 * <AUTHOR>
 * @date 2022/9/14 14:29
 */
@Slf4j
public abstract class SortParamAdapter<I extends FuncParam, O extends FuncResult> {

    private Class<I> funcParamClazz;

    /**
     * 转换得到引擎请求
     *
     * @param traceId
     * @param sceneInfo
     * @param dispatchApiPayload
     * @return
     */
    public final MultiLayerGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, DispatchApiPayload dispatchApiPayload) {
        // 功能参数
        //I funcParam = dispatchApiPayload.getData(deduceFuncParamClass());
        I funcParam = null;
        if (dispatchApiPayload.getData().size() != 0 && dispatchApiPayload.getData().get(0).getData() != null) {
            funcParam = JSONB.parseObject(dispatchApiPayload.getData().get(0).getData().toJSONBBytes(), deduceFuncParamClass());
        }
        // 场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = getEngineScene(traceId, sceneInfo, funcParam);
        // 会话信息
        SessionInfo engineSession = getEngineSession(traceId, sceneInfo, funcParam);
        // 请求
        MultiLayerGraphRecommendRequest engineRequest = getEngineRequest(traceId, sceneInfo, funcParam);
        // 补充engineRequest
        engineRequest.setSceneInfo(engineScene);
        engineRequest.setSessionInfo(engineSession);
        return injectFuncParam(engineRequest, funcParam);
    }

    /**
     * 转换得到返回的结果
     *
     * @param request
     * @param response
     * @return
     */
    public final O adapt(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        return getFuncResult(request, response);
    }

    /**
     * 解析并返回引擎请session
     *
     * @param traceId
     * @param sceneInfo
     * @param funcParam
     * @return
     */
    protected com.iflytek.hy.rec.domain.model.valueobj.SceneInfo getEngineScene(String traceId, SceneInfo sceneInfo, I funcParam) {
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);
        if (sceneInfo instanceof AiReviewSceneInfo) {
            SceneInfoMapper.INSTANCE.appendAiReviewFields((AiReviewSceneInfo) sceneInfo, engineScene);
        }
        return engineScene;
    }

    /**
     * 解析并返回引擎请session
     *
     * @param traceId
     * @param sceneInfo
     * @param funcParam
     * @return
     */
    protected SessionInfo getEngineSession(String traceId, SceneInfo sceneInfo, I funcParam) {
        return new SessionInfo().setTraceId(traceId);
    }

    protected MultiLayerGraphRecommendRequest getEngineRequest(String traceId, SceneInfo sceneInfo, I funcParam) {
        MultiLayerGraphRecommendRequest engineRequest = new MultiLayerGraphRecommendRequest();
        if (sceneInfo instanceof AiDiagSceneInfo) {
            engineRequest.setInNodeChapterMap(((AiDiagSceneInfo) sceneInfo).getNodeCatalogMap());
        }
        return engineRequest;
    }

    /**
     * 子类实现。按不同功能注入不同的字段值
     *
     * @param engineRequest
     * @param funcParam
     * @return
     */
    protected abstract MultiLayerGraphRecommendRequest injectFuncParam(MultiLayerGraphRecommendRequest engineRequest, I funcParam);

    /**
     * 子类实现。构建返回的结果对象
     *
     * @param request
     * @param response
     * @return
     */
    protected abstract O getFuncResult(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response);

    @SuppressWarnings("unchecked")
    private Class<I> deduceFuncParamClass() {
        if (this.funcParamClazz == null) {
            Object targetObject = AopProxyUtils.getSingletonTarget(this);
            if (targetObject == null) {
                targetObject = this;
            }
            ParameterizedType parameterizedType = (ParameterizedType) targetObject.getClass().getGenericSuperclass();
            this.funcParamClazz = (Class<I>) parameterizedType.getActualTypeArguments()[0];
            if (log.isDebugEnabled()) {
                log.debug("funcParamClazz={}", funcParamClazz);
            }
            Assert.notNull(this.funcParamClazz, String.format("[%s]funcParamClazz is null.", this.getClass()));
        }
        return this.funcParamClazz;
    }

}
