package com.iflytek.skylab.core.data.adapter.diag;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONB;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.hy.rec.diag.interfaces.param.GraphDiagnoseResponse;
import com.iflytek.hy.rec.diag.interfaces.param.SessionInfo;
import com.iflytek.hy.rec.merdiag.interfaces.param.GraphMergeDiagnoseRequest;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.data.NodeInfo;
import com.iflytek.skylab.core.data.adapter.mapper.MasterInfoMapper;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.MasterInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/20 14:52
 */
@Slf4j
public class DiagMergeParamAdapter {
    public final GraphMergeDiagnoseRequest adapt(String traceId, AiDiagSceneInfo sceneInfo) {
        log.debug("traceId={};adapt..", traceId);

        GraphMergeDiagnoseRequest request = new GraphMergeDiagnoseRequest();
        //设值 引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo scene = SceneInfoMapper.INSTANCE.sceneToDiagScene(sceneInfo);

        //设值 引擎会话信息
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(traceId);

        request.setSceneInfo(scene);
        request.setSessionInfo(sessionInfo);
        if (log.isDebugEnabled()) {
            log.debug("traceId={}; GraphDiagnoseRequest= {}", traceId, request);
            log.debug("traceId={}; GraphDiagnoseRequest:Json= {}", traceId, JSON.toJSONString(request));
        }
        return request;
    }


    public final List<String> adapt(@NotNull DispatchApiPayload dispatchApiPayload) {
        JSONObject masterFetchParamObj = adaptParamObj(dispatchApiPayload);
        List<String> nodeIds = getListObj(masterFetchParamObj, "nodeIds");
        if (CollUtil.isEmpty(nodeIds)) {
            List<NodeInfo> nodeInfos = masterFetchParamObj.getList("nodeInfos", NodeInfo.class);
            if (CollUtil.isNotEmpty(nodeInfos)) {
                return nodeInfos.stream().map(NodeInfo::getNodeId).collect(Collectors.toList());
            }
        }
        return nodeIds;
    }

    public final JSONObject adaptParamObj(@NotNull DispatchApiPayload dispatchApiPayload) {
        //设值 目录/点列表  暂时采用 取属性的方式，后期可以考虑反射
        //JSONObject masterFetchParamObj = dispatchApiPayload.getData(JSONObject.class);
        JSONObject masterFetchParamObj = null;
        if (dispatchApiPayload.getData().size() != 0 && dispatchApiPayload.getData().get(0).getData() != null) {
            masterFetchParamObj = JSONB.parseObject(dispatchApiPayload.getData().get(0).getData().toJSONBBytes(), JSONObject.class);
        }
        return masterFetchParamObj;
    }

    /**
     * 输出参数适配
     *
     * @param nodeIds          顶点列表
     * @param diagnoseResponse 引擎返回
     * @return MasterFetchResult 画像获取结果
     */
    public final MasterFetchResult adapt(List<String> nodeIds, GraphDiagnoseResponse
            diagnoseResponse) {
        MasterFetchResult result = new MasterFetchResult();
        //根据引擎返回数据，过滤仅返回 用户请求的数据
        List<com.iflytek.hy.rec.diag.interfaces.param.MasterInfo> diagMasterInfoList = diagnoseResponse.getMasterInfoList();
        List<MasterInfo> masterInfoList = new ArrayList<>();
        for (com.iflytek.hy.rec.diag.interfaces.param.MasterInfo info : diagMasterInfoList) {
            //过滤掉 非请求的nodeId
            if (!CollectionUtils.isEmpty(nodeIds) && !nodeIds.contains(info.getNodeId())) {
                continue;
            }
            //转换引擎返回画像数据 到用户画像对象
            MasterInfo mastery = MasterInfoMapper.INSTANCE.toMasterInfo(info);

            masterInfoList.add(mastery);
        }
        result.setMasterInfos(masterInfoList);
        if (log.isDebugEnabled()) {
            log.debug("MasterFetchResult= {}", result);
        }
        return result;
    }

    private List<String> getListObj(JSONObject jsonObject, String key) {
        return jsonObject.containsKey(key) ? jsonObject.getObject(key, new TypeReference<List<String>>() {
        }) : Collections.emptyList();
    }


}
