package com.iflytek.skylab.core.data.adapter.sort;

import cn.hutool.core.map.MapUtil;

import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendRequest;
import com.iflytek.hy.rec.sort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.sort.interfaces.param.SessionInfo;
import com.iflytek.skylab.core.data.NodeInfo;
import com.iflytek.skylab.core.data.RecNodePropParam;
import com.iflytek.skylab.core.data.RecNodePropResult;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/8 11:03
 */
@Slf4j
public class RecNodePropParamAdapter extends AbstractParamAdapter<RecNodePropParam, RecNodePropResult> {


    @Override
    public MultiLayerGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, RecNodePropParam funcParam) {
        MultiLayerGraphRecommendRequest request = new MultiLayerGraphRecommendRequest();

        //转换引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);
        request.setSceneInfo(engineScene);

        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTraceId(traceId);
        request.setSessionInfo(sessionInfo);

        //设值 点的目录列表
        request.setInNodeIdList(funcParam.getNodeIds());
        return request;
    }


    @Override
    public RecNodePropResult adapt(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        RecNodePropResult result = new RecNodePropResult();

        List<NodeInfo> nodeInfos = new ArrayList<>();
        try {
            Map<String, String> nodeLogicType = response.getRecommendInfo().getNodeLogicType();
            if (MapUtil.isNotEmpty(nodeLogicType)) {
                nodeLogicType.forEach((nodeId, logicType) -> nodeInfos.add(new NodeInfo().setNodeId(nodeId).setLogicType(logicType)));
            }
        } catch (Exception e) {
            log.error("点属性诊断结果转换失败。resp:{}, message:{}", response, e.getMessage());
        }
        return result.setNodeInfos(nodeInfos);
    }
}
