package com.iflytek.skylab.core.data.adapter.config;

import com.iflytek.skylab.core.data.*;
import com.iflytek.skylab.core.data.adapter.sort.*;
import com.iflytek.skylab.core.data.adapter.sort.errortopic.ErrorTopicSortParamAdapterSelector;
import com.iflytek.skylab.core.data.adapter.sort.errortopic.RecErrorTopicParamAdapter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * adapter bean注册到spring容器。
 *
 * <AUTHOR>
 * @date 2022/5/9 11:48
 */
@Configuration(proxyBeanMethods = false)
public class AdapterConfiguration {

    @Bean({"REC_TOPIC", "REC_TOPIC_ONEMORE","KC_REC_TOPIC_PLAN","KC_REC_TOPIC","KC_REC_TOPIC_ONEMORE"})
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecTopicParamAdapter recTopicParamAdapter() {
        return new RecTopicParamAdapter();
    }

    @Bean({"REC_NODE","KC_REC_NODE_PLAN","KC_REC_NODE_VARIANT","KC_REC_NODE"})
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecNodeParamAdapter recNodeParamAdapter() {
        return new RecNodeParamAdapter();
    }

    @Bean(RecTraceNodeParam.FUNC_CODE)
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecTraceNodeParamAdapter recTraceNodeParamAdapter() {
        return new RecTraceNodeParamAdapter();
    }

    @Bean(RecUnitParam.FUNC_CODE)
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecUnitParamAdapter recUnitParamAdapter() {
        return new RecUnitParamAdapter();
    }

    @Bean({"REC_EVAL4IN", "REC_EVAL4OUT", "REC_EVAL4SYNC","REC_EVAL4CTN","KC_REC_EVAL4IN","KC_REC_EVAL4CTN","KC_REC_EVAL4OUT","KC_REC_EVAL4SYNC"})
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecEval4InOutParamAdapter recEvaluationParamAdapter() {
        return new RecEval4InOutParamAdapter();
    }

    @Bean(RecEval4LimitParam.FUNC_CODE)
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecEval4LimitParamAdapter recEval4LimitParamAdapter() {
        return new RecEval4LimitParamAdapter();
    }

    @Bean(RecNodePropParam.FUNC_CODE)
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecNodePropParamAdapter recNodePropParamAdapter() {
        return new RecNodePropParamAdapter();
    }

    @Bean(RecTopicPackParam.FUNC_CODE)
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecTopicPackParamAdapter recTopicPackParamAdapter() {
        return new RecTopicPackParamAdapter();
    }

    @Bean(RecTopicPackEval4PhaseParam.FUNC_CODE)
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecTopicPackEval4PhaseParamAdapter recTopicPackEval4PhaseParamAdapter() {
        return new RecTopicPackEval4PhaseParamAdapter();
    }

    @Bean
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecTopicPack4CatalogParamAdapter recTopicPack4CatalogParamAdapter() {
        return new RecTopicPack4CatalogParamAdapter();
    }

    @Bean
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public SortParamAdapterSelector sortParamAdapterSelector() {
        return new SortParamAdapterSelector();
    }


    @Bean
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.errtopsort.ErrorTopicGraphRecommend"})
    public ErrorTopicSortParamAdapterSelector errorTopicSortParamAdapterSelector() {
        return new ErrorTopicSortParamAdapterSelector();
    }

    @Bean("REC_ERROR_TOPIC")
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.errtopsort.ErrorTopicGraphRecommend"})
    public RecErrorTopicParamAdapter recErrorTopicParamAdapter() {
        return new RecErrorTopicParamAdapter();
    }

    @Bean("REC_TOPIC_TRACE_POINT")
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.errtopsort.ErrorTopicGraphRecommend"})
    public com.iflytek.skylab.core.data.adapter.sort.errortopic.RecTopicParamAdapter recTopicWithErrorTopicParamAdapter() {
        return new com.iflytek.skylab.core.data.adapter.sort.errortopic.RecTopicParamAdapter();
    }

    @Bean("REC_PRONE_WRONG_TOPIC")
    @ConditionalOnClass(name = {"com.iflytek.hy.rec.sort.MultiLayerGraphRecommend"})
    public RecPronWrongTopicParamAdapter recPronWrongTopicParamAdapter() {
        return new RecPronWrongTopicParamAdapter();
    }
}
