package com.iflytek.skylab.core.data.adapter.mapper;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.domain.GlobalMasteryInfo;
import com.iflytek.skylab.core.domain.MasterInfo;
import jline.internal.Log;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * MasterInfo mapstruct对象转换
 *
 * <AUTHOR>
 */
@Mapper
public interface MasterInfoMapper {

    MasterInfoMapper INSTANCE = Mappers.getMapper(MasterInfoMapper.class);

    /**
     * 转换引擎返回画像数据 到用户画像对象
     * @param info 引擎返回用户画像计算
     * @return MasterInfo 画像描述数据Item
     */
    @Mapping(source = "info.catalogId", target = "catalogId")
    @Mapping(source = "info.catalogType", target = "catalogType")
    @Mapping(source = "info.nodeId", target = "nodeId")
    @Mapping(source = "info.nodeType", target = "nodeType")
    @Mapping(source = "info.masterScore", target = "masteryScore")
    @Mapping(source = "info.masterType", target = "computeType")
    @Mapping(source = "info.shouldFlag", target = "shouldFlag")
    @Mapping(source = "info.masterDetailInfo.fusionMasterScore", target = "fusion")
    @Mapping(source = "info.masterDetailInfo.realMasterScore", target = "real")
    @Mapping(source = "info.masterDetailInfo.predictMasterScore", target = "predict")
    @Mapping(source = "info.lastAnswerTime", target = "lastAnswerTime")
    @Mapping(source = "info.nodeDetail.useThinkingExpansion", target = "useThinkingExpansion")
    MasterInfo toMasterInfo(com.iflytek.hy.rec.diag.interfaces.param.MasterInfo info);

    /**
     * 转换引擎返回画像数据 到用户画像对象
     *
     * @param info 引擎返回用户画像计算
     * @return MasterInfo 画像描述数据Item
     */

    @Mapping(source = "info.catalogId", target = "catalogId")
    @Mapping(source = "info.catalogType", target = "catalogType")
    @Mapping(source = "info.nodeId", target = "nodeId")
    @Mapping(source = "info.nodeType", target = "nodeType")
    @Mapping(source = "info.masterScore", target = "masteryScore")
    @Mapping(source = "info.masterType", target = "computeType")
    @Mapping(source = "info.shouldFlag", target = "shouldFlag")
    @Mapping(source = "info.masterDetailInfo.fusionMasterScore", target = "fusion")
    @Mapping(source = "info.masterDetailInfo.realMasterScore", target = "real")
    @Mapping(source = "info.masterDetailInfo.predictMasterScore", target = "predict")
    @Mapping(source = "info.lastAnswerTime", target = "lastAnswerTime")
    @Mapping(source = "info.nodeDetail.useThinkingExpansion", target = "useThinkingExpansion")
    @Mapping(expression = "java(toGlobalMasteryInfo(info.getGlobalMasterInfo()))", target = "globalMastery")
    MasterInfo toMasterInfo(com.iflytek.hy.rec.dtpmapdiag.interfaces.param.MasterInfo info);


    default GlobalMasteryInfo toGlobalMasteryInfo(JSONObject globalMasterInfo) {
        try {
            GlobalMasteryInfo globalMasteryInfo = new GlobalMasteryInfo();
            globalMasteryInfo.setMasteryScore(globalMasterInfo.getDouble("masterScore"));
            globalMasteryInfo.setReal(globalMasterInfo.getDouble("real"));
            globalMasteryInfo.setPredict(globalMasterInfo.getDouble("predict"));
            globalMasteryInfo.setFusion(globalMasterInfo.getDouble("fusion"));
            return globalMasteryInfo;
        } catch (Exception e) {
            Log.error("toGlobalMasteryInfo error,globalMasterInfo is: {} ", globalMasterInfo.toJSONString(), e);
        }
        return null;
    }
}
