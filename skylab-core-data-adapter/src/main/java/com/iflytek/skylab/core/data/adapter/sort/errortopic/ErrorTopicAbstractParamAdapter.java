package com.iflytek.skylab.core.data.adapter.sort.errortopic;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONB;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.hy.rec.errtopsort.interfaces.param.ErrorTopicGraphRecommendRequest;
import com.iflytek.hy.rec.errtopsort.interfaces.param.MultiLayerGraphRecommendResponse;
import com.iflytek.hy.rec.errtopsort.interfaces.param.NodeInfo;
import com.iflytek.hy.rec.errtopsort.interfaces.param.RelationNodeInfo;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.ResourceTypeEnum;
import com.iflytek.skylab.core.data.FuncParam;
import com.iflytek.skylab.core.data.FuncResult;
import com.iflytek.skylab.core.domain.EvaluationItem;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.util.Assert;
import skynet.boot.logging.LoggingCost;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;

/**
 * 参数适配器
 *
 * <AUTHOR>
 * @date 2022/3/9 4:22 下午
 */
@Slf4j
public abstract class ErrorTopicAbstractParamAdapter<I extends FuncParam, O extends FuncResult> {

    private Class<I> currentFuncParamClass;


    /**
     * http入参转换成引擎接口入参
     *
     * @param traceId            跟踪Id
     * @param sceneInfo          场景信息
     * @param dispatchApiPayload 接口参数
     * @return ErrorTopicGraphRecommendRequest
     */
    @LoggingCost
    public final ErrorTopicGraphRecommendRequest adapt(@Valid @NotBlank String traceId, @NotNull SceneInfo sceneInfo, @NotNull DispatchApiPayload dispatchApiPayload) {
        log.debug("traceId={};adapt..", traceId);
        //I funcParam = dispatchApiPayload.getData(getCurrentFuncParamClass());
        I funcParam = null;
        if (dispatchApiPayload.getData().size() != 0 && dispatchApiPayload.getData().get(0).getData() != null) {
            funcParam = JSONB.parseObject(dispatchApiPayload.getData().get(0).getData().toJSONBBytes(), getCurrentFuncParamClass());
        }
        return this.adapt(traceId, sceneInfo, funcParam);
    }

    @LoggingCost
    public final ErrorTopicGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, JSONObject payload) {
        I funcParam = JSON.parseObject(payload.toJSONString(), getCurrentFuncParamClass());
        return this.adapt(traceId, sceneInfo, funcParam);
    }

    /**
     * 引擎接口输出 转换成接口输出 抽象方法
     *
     * @param request  引擎入参
     * @param response 引擎返回
     * @return 接口输出
     */
    public abstract O adapt(ErrorTopicGraphRecommendRequest request, MultiLayerGraphRecommendResponse response);

    /**
     * 入参转换成引擎接口入参 抽象方法
     *
     * @param traceId   跟踪Id
     * @param sceneInfo 场景信息
     * @param funcParam 功能 请求参数
     * @return ErrorTopicGraphRecommendRequest
     */
    public abstract ErrorTopicGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, I funcParam);

    private Class<I> getCurrentFuncParamClass() {

        if (this.currentFuncParamClass == null) {
            Object targetObject = AopProxyUtils.getSingletonTarget(this);
            if (targetObject == null) {
                targetObject = this;
            }
            ParameterizedType parameterizedType = (ParameterizedType) targetObject.getClass().getGenericSuperclass();
            this.currentFuncParamClass = (Class<I>) parameterizedType.getActualTypeArguments()[0];
            if (log.isDebugEnabled()) {
                log.debug("CurrentFuncParamClass={}", currentFuncParamClass);
            }
            Assert.notNull(this.currentFuncParamClass, String.format("[%s]currentFuncParamClass is null.", this.getClass()));
        }
        return this.currentFuncParamClass;
    }


    /**
     * 转换 测评输出Item对象
     *
     * @param request  引擎入参
     * @param response 引擎返回
     * @return List<EvaluationItem>
     */
    public List<EvaluationItem> transferEvaluationItems(ErrorTopicGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        List<EvaluationItem> evaluationItems = new ArrayList<>();
        //设值 测评推荐结果
        for (NodeInfo node : response.getOutNodeInfos()) {
            EvaluationItem item = new EvaluationItem();
            //设值 资源
            item.setResNodeId(node.getNodeId());
            item.setResNodeType(ResourceTypeEnum.TOPIC);

            //设值关联点
            if (CollectionUtils.isNotEmpty(request.getRelationNodeType())
                    && node.getRelationNodes() != null) {

                RelationNodeInfo relationNodeInfo = node.getRelationNodes();

                item.setNodeId(relationNodeInfo.getNodeId());
                //关联点类型
                item.setNodeType(NodeTypeEnum.parse(relationNodeInfo.getNodeType()));
                item.setNodeName(relationNodeInfo.getNodeName());
            }
            evaluationItems.add(item);
        }
        return evaluationItems;
    }
}
