package com.iflytek.skylab.core.data.adapter.sort;

import com.iflytek.hy.rec.sort.interfaces.param.*;
import com.iflytek.skylab.core.data.RecTopicParam;
import com.iflytek.skylab.core.data.RecTopicResult;
import com.iflytek.skylab.core.data.adapter.mapper.RecResponseMapper;
import com.iflytek.skylab.core.data.adapter.mapper.SceneInfoMapper;
import com.iflytek.skylab.core.data.adapter.mapper.SessionInfoMapper;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skylab.core.domain.extra.AiDiagSceneInfo;
import com.iflytek.skylab.core.domain.extra.AiExamSceneInfo;
import com.iflytek.skylab.core.domain.extra.AiReviewSceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 点推题 参数转换
 *
 * <AUTHOR>
 * @date 2022/3/9 4:30 下午
 */
@Slf4j
public class RecTopicParamAdapter extends AbstractParamAdapter<RecTopicParam, RecTopicResult> {

    /**
     * 点推题 转换引擎入参
     *
     * @param traceId   跟踪Id
     * @param sceneInfo 场景信息
     * @param funcParam 功能 请求参数
     * @return MultiLayerGraphRecommendRequest
     */
    @Override
    public MultiLayerGraphRecommendRequest adapt(String traceId, SceneInfo sceneInfo, RecTopicParam funcParam) {
        if (log.isDebugEnabled()) {
            log.debug("sceneInfo={}, funcParam={}", sceneInfo, funcParam);
        }
        MultiLayerGraphRecommendRequest request = new MultiLayerGraphRecommendRequest();

        //转换引擎场景信息
        com.iflytek.hy.rec.domain.model.valueobj.SceneInfo engineScene = SceneInfoMapper.INSTANCE.sceneToSortScene(sceneInfo);
        if (sceneInfo instanceof AiReviewSceneInfo) {
            SceneInfoMapper.INSTANCE.appendAiReviewFields((AiReviewSceneInfo) sceneInfo, engineScene);
        }
        if (sceneInfo instanceof AiDiagSceneInfo) {
            request.setInNodeChapterMap(((AiDiagSceneInfo) sceneInfo).getNodeCatalogMap());
        }
        if (sceneInfo instanceof AiExamSceneInfo) {
            engineScene.setExamType(((AiExamSceneInfo) sceneInfo).getExamType().getAiExamCode());
            engineScene.setStudentLevel(((AiExamSceneInfo) sceneInfo).getStudentLevel().getCode());
        }
        request.setSceneInfo(engineScene);

        //本轮id、本轮已推题量
        SessionInfo sessionInfo = SessionInfoMapper.INSTANCE.recTopicParamToSession(traceId, funcParam);

        //点标识id
        List<String> inNodeIdList = new ArrayList<>();
        inNodeIdList.add(funcParam.getNodeId());

        request.setSceneInfo(engineScene);
        request.setSessionInfo(sessionInfo);
        request.setInNodeIdList(inNodeIdList);

        //禁止的节点
        request.setForbiddenNodes(funcParam.getForbiddenNodes());

        if (log.isDebugEnabled()) {
            log.debug("RecTopicParamAdapter result={}", request);
        }
        return request;
    }

    /**
     * 点推题 返回
     *
     * @param request  引擎入参
     * @param response 引擎返回
     * @return RecTopicResult
     */
    @Override
    public RecTopicResult adapt(MultiLayerGraphRecommendRequest request, MultiLayerGraphRecommendResponse response) {
        RecTopicResult result = null;
        //校验是否 有引擎推题
        List<NodeInfo> outNodeInfos = response.getOutNodeInfos();
        if (CollectionUtils.isNotEmpty(outNodeInfos)) {
            NodeInfo node = outNodeInfos.get(0);
            result = RecResponseMapper.INSTANCE.toRecTopicResult(node, response.getRecommendInfo());
            result.setNodeId(request.getInNodeIdList().get(0));
            return result;
        }


        log.error("{}功能无推荐结果，traceId={}", request.getSceneInfo().getFunctionCode(), request.getSessionInfo().getTraceId());
        Optional<RecommendInfo> recommend = Optional.ofNullable(response.getRecommendInfo());
        // 无推题结果
        result = new RecTopicResult();
        result.setUpdateTime(new Date());
        result.setNodeId(request.getInNodeIdList().get(0));
        result.setTerminationFlag(recommend.map(RecommendInfo::getIsTermination).orElse(result.isTerminationFlag()));
        result.setDoTerminateTopic(recommend.map(RecommendInfo::getIsRecommendDoTerminateTopic).orElse(result.isDoTerminateTopic()));
        result.setRecTotalNum(recommend.map(RecommendInfo::getRoundTotalNum).orElse(result.getRecTotalNum()));
        result.setUnitTimes(recommend.map(RecommendInfo::getCostTime).orElse(result.getUnitTimes()));
        return result;
    }
}
