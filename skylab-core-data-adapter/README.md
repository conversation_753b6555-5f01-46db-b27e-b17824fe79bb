## skylab-core-data-adapter

参数与结果适配层（隔离平台协议与引擎协议）

### 1. 项目概述
- 项目名称：skylab-core-data-adapter
- 项目描述：将 DispatchApiRequest/SceneInfo 等平台协议适配为引擎请求；并将引擎响应适配为平台输出
- 版本信息：2.0.9-SNAPSHOT（开发中）

### 2. 主要组件
- 场景解析：SceneInfoSelector
- 排序适配：AbstractParamAdapter、SortParamAdapterSelector 及各 Rec*ParamAdapter
- 诊断适配：DiagParamAdapter、DiagSyncOsParamAdapter、DiagMergeParamAdapter
- 特征适配：AcquireFeatureParamAdapter

### 3. 使用说明
- 服务模块注入选择器，按 SceneInfo.functionCode 选择具体适配器

### 4. 开发指南
- 新增能力时，新增对应 ParamAdapter 并在 Selector 中注册
- 编写转换单元测试，确保字段映射正确

