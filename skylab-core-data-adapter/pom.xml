<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skylab</groupId>
        <artifactId>skylab-platform</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>skylab-core-data-adapter</artifactId>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-data</artifactId>
        </dependency>


        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-sort</artifactId>
            <version>${recommend-engine.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-errortopic-sort</artifactId>
            <version>${recommend-engine.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-diag</artifactId>
            <version>${recommend-engine.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-fea-process</artifactId>
            <version>${recommend-engine.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-merge-diag</artifactId>
            <version>${recommend-engine.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.iflytek.hy.pl.engine</groupId>
            <artifactId>recommend-engine-dtpmap-diag</artifactId>
            <version>${recommend-engine.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source> <!-- depending on your project -->
                    <target>1.8</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <!-- additional annotation processor required as of Lombok 1.18.16 -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>
                            -Amapstruct.suppressGeneratorTimestamp=true
                        </arg>
                        <arg>
                            -Amapstruct.suppressGeneratorVersionInfoComment=true
                        </arg>
                        <arg>
                            -Amapstruct.verbose=true
                        </arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
