<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skylab</groupId>
        <artifactId>skylab-platform</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>skylab-core-dataapi</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
        <maven.source.skip>false</maven.source.skip>
        <skipTests>true</skipTests>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-mongo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.skyline</groupId>
            <artifactId>skyline-context</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-data</artifactId>
        </dependency>

        <!-- Nebula Graph client -->
        <dependency>
            <groupId>com.vesoft</groupId>
            <artifactId>client</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.iflytek.cog2</groupId>
            <artifactId>feaflow-sdk</artifactId>
        </dependency>

        <!-- Redis 6.0 依赖，推荐Lettuce客户端 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
    </dependencies>
</project>