#-----------------------------------------------------------------
skyline.data.api.sdk.app-key=app-mih20vnu
skyline.data.api.sdk.app-secret=8f94fc5721b5d4edce2bf00abb723b26179b7e4d
skyline.data.api.sdk.url=http://172.31.161.86:30888/api/v1/execute
skyline.data.api.sdk.keep-alive-duration-second=5
skyline.data.api.sdk.max-idle-conn=5
skyline.data.api.sdk.timeout-ms=5000
skyline.data.api.sdk.timeoutMs=5000
skyline.data.api.sdk.maxIdleConn=10
skyline.data.api.sdk.keepAliveDurationSecond=5
skyline.data.api.sdk.queryLabelDataApiId=api-me0vg2e4
skyline.data.api.sdk.queryApiDataApiId=api-2rl8e4vh
#-----------------------------------------------------------------
skylab.zion.dict-table-name=zion_dict_info_test_v2
skylab.zion.dict-family=c
skylab.zion.dict-qualifier=info
skylab.zion.dict-default-row-key=001
skylab.zion.dict-refresh-period-seconds=3600
skylab.zion.dict-data-api-item.dataApiId=api-78wnc6h9
skylab.zion.dict-data-api-item.version=1
#-----------------------------------------------------------------
skyline.spring.cloud.zookeeper.discovery.enabled=false
skyline.spring.cloud.zookeeper.discovery.register=false
skyline.spring.cloud.zookeeper.enabled=false
skynet.action-point=${spring.application.name}@skyline
#-----------------------------------------------------------------
IP=***********
server.port=88888
#-----------------------------------------------------------------
spring.application.name=skylab-dataapi
spring.cloud.zookeeper.connect-string=${IP}:2181
spring.cloud.zookeeper.discovery.root=/skynet/discovery/skylab
spring.cloud.zookeeper.discovery.enabled=true
#-----------------------------------------------------------------
#mongodb
management.metrics.export.prometheus.enabled=true
spring.profiles.active=test
#skylab.data.api.graph.hosts=***********:9669
#skylab.data.api.graph.nodeProps.ANCHOR_POINT=anchorPointType,difficulty,evaluatePoint,evaluations,examPoint,phaseCode,realLastLevelRelationCatas,subjectCode,tracePoints
#skylab.data.api.graph.nodeProps.LEARN_PATH=pathType
skylab.data.api.graph.nodeProps.LEARN_PATH_V2=pathType2




skylab.data.api.graph.hosts=***********:9669
skylab.data.api.graph.username=root
skylab.data.api.graph.password=root
skylab.data.api.graph.nodeProps.ANCHOR_POINT=anchorPointType,difficulty,coreAttainment,examPoint,phaseCode,realLastLevelRelationCatas,subjectCode,falliblePoint,name,classStandardFeatures,difficultPoint,importantPoint
skylab.data.api.graph.nodeProps.CHECK_POINT=checkPointName
skylab.data.api.graph.nodeProps.LEARN_PATH=
skylab.data.api.graph.nodeProps.BOOK=name,phase,subject
skylab.data.api.graph.nodeProps.UNIT=name
skylab.data.api.graph.nodeProps.COURSE=name
skylab.data.api.graph.nodeProps.PERIOD=name
skylab.data.api.graph.nodeProps.L2COURSE=name
skylab.data.api.graph.nodeProps.L3COURSE=name

skylab.data.api.graph.nodeProps.REVIEW_POINT=examFrequency,difficulty
skylab.data.api.graph.nodeProps.TOPIC=topic_difficulty,topic_type


skylab.data.api.graph.localCacheEnabled=true
skylab.data.api.graph.cacheVersion=20250723_001
#skylab.data.api.graph.path=D:/Date/graph/nebulaData_20250723_001.zip
#skylab.data.api.middle.math.path=D:/Date/graph/newGraphData.zip
skylab.data.api.graph.path=/Users/<USER>/Downloads/nebulaData_20250723_001.zip
skylab.data.api.middle.math.path=/Users/<USER>/Downloads/newGraphData.zip
skylab.data.api.graph.cacheFiles=TAG_PRESS.info,TAG_BOOK.info,TAG_UNIT.info,TAG_COURSE.info,TAG_PERIOD.info,TAG_L2COURSE.info,TAG_CHECK_POINT.info,TAG_ANCHOR_POINT.info,TAG_TOPIC.info,EDGE_PRESS_BOOK.info,EDGE_BOOK_UNIT.info,EDGE_UNIT_COURSE.info,EDGE_UNIT_PERIOD.info,EDGE_COURSE_PERIOD.info,EDGE_COURSE_L2COURSE.info,EDGE_COURSE_CHECK_POINT.info,EDGE_COURSE_ANCHOR_POINT.info,EDGE_UNIT_CHECK_POINT.info,EDGE_UNIT_ANCHOR_POINT.info,EDGE_PERIOD_ANCHOR_POINT.info,EDGE_PERIOD_CHECK_POINT.info,EDGE_L2COURSE_ANCHOR_POINT.info,EDGE_L2COURSE_CHECK_POINT.info,EDGE_CHECK_POINT_ANCHOR_POINT.info,EDGE_ANCHOR_POINT_TOPIC.info,EDGE_EXAM_POINT_TOPIC.info,EDGE_ANCHOR_POINT_EXAM_POINT.info
#es




#-----dataApi\u5207ES   start----------
#------------zion common--------------
zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.dict-family=u
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.dict-refresh-period-seconds=10
#------------zion es--------------
zion.query-data-base=es
zion.es-host=***********:9200,***********:9200,***********:9200
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
#------------zion dataapi--------------
#zion.query-data-base=hbase_data_api
#zion.dict-table-name=dim_xxj_dic_model
#zion.dict-data-api-item.dataApiId=api-vimqibeu
#zion.feature-data-api-item.dataApiId=api-x4znzm0l
#zion.app-key=app-69v3u6vj
#zion.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
#zion.url=http://**************:30890/api/v1/execute
#-----dataApi\u5207ES  end--------------

skylab.feature.redis.enabled=false