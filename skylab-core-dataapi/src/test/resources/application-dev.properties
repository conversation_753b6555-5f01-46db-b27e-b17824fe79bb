IP=***********

server.port=1106

skyline.data.api.sdk.timeoutMs=5000
skyline.data.api.sdk.url=http://**************:30888/api/v1/execute
skyline.data.api.sdk.appKey=app-wpjtr32h
skyline.data.api.sdk.appSecret=2ffbd8857dbea6eeea76d1ae243f3ad6c5d764df
skyline.data.api.sdk.maxIdleConn=10
skyline.data.api.sdk.keepAliveDurationSecond=5
skyline.data.api.sdk.queryLabelDataApiId=api-me0vg2e4
skyline.data.api.sdk.queryApiDataApiId=api-2rl8e4vh


skyline.spring.cloud.zookeeper.discovery.enabled=false
skyline.spring.cloud.zookeeper.discovery.register=false
skyline.spring.cloud.zookeeper.enabled=false

skynet.action-point=${spring.application.name}@skyline

skynet.zookeeper.cluster_name=skynet
skynet.zookeeper.connection-timeout=30000
skynet.zookeeper.connection_timeout=30000
skynet.zookeeper.enabled=true
skynet.zookeeper.server_list=${IP}:2181
skynet.zookeeper.session_timeout=20000

management.metrics.export.prometheus.enabled=true



##########################redis-start##########################
spring.redis.host=***********
spring.redis.port=6379
# Redis\u54E8\u5175\u6A21\u5F0F\u914D\u7F6E
# \u4E3B\u8282\u70B9\u540D\u79F0\uFF0C\u9700\u4E0Esentinel.conf\u4E2D\u7684\u4E00\u81F4
#spring.redis.sentinel.master=mymaster
## \u54E8\u5175\u8282\u70B9\u5730\u5740\uFF0C\u9017\u53F7\u5206\u9694
#spring.redis.sentinel.nodes=***********:6380,***********:6380,***********:6380
## # Redis\u5BC6\u7801\uFF08\u5982\u6709\uFF09
#spring.redis.password=Y_zm9cHOfeYa_2cfAsQv
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u5355\u4F4D\u6BEB\u79D2\uFF09
spring.redis.timeout=500
# Lettuce\u8FDE\u63A5\u6C60\u914D\u7F6E
spring.redis.lettuce.pool.max-active=64
spring.redis.lettuce.pool.max-idle=32
spring.redis.lettuce.pool.min-idle=10
spring.redis.lettuce.pool.max-wait=500
# \u8FDE\u63A5\u6570\u636E\u5E93\uFF08\u5982\u6709\u5206\u5E93\u9700\u6C42\uFF0C\u9ED8\u8BA40\uFF09
spring.redis.database=0
#\u5F00\u542Fredis\u7F13\u5B58
skylab.feature.redis.enabled=true
#\u8FC7\u671F\u65F6\u95F4\u914D\u7F6E
skylab.feature.redis.expire.default-expire-second=3600
# \u5404\u7279\u5F81\u81EA\u5B9A\u4E49\u8FC7\u671F\u65F6\u95F4\uFF08\u79D2\uFF09
skylab.feature.redis.expire.feature-expire-second.anchorpoint_graph_layer=3600
skylab.feature.redis.expire.feature-expire-second.anchorpoint_issubjective=3600
skylab.feature.redis.expire.feature-expire-second.anchorpoint_mastery=3600
skylab.feature.redis.expire.feature-expire-second.anchorpoint_single_frequency=3600
skylab.feature.redis.expire.feature-expire-second.anchorpoint_max_recommond_topic=3600
skylab.feature.redis.expire.feature-expire-second.anchorpoint_score=3600
skylab.feature.redis.expire.feature-expire-second.anchorpoint_single_score_frequency=3600
skylab.feature.redis.expire.feature-expire-second.anchorpoint_test_frequency_nationwide=3600
skylab.feature.redis.expire.feature-expire-second.can_correct=3600
skylab.feature.redis.expire.feature-expire-second.catalog_answer_time=3600
skylab.feature.redis.expire.feature-expire-second.catalog_max_topic_num=3600
skylab.feature.redis.expire.feature-expire-second.checkpoint_exam_layer_freq=3600
skylab.feature.redis.expire.feature-expire-second.checkpoint_frequency=3600
skylab.feature.redis.expire.feature-expire-second.checkpoint_graph_layer=3600
skylab.feature.redis.expire.feature-expire-second.checkpoint_prepare_graph_layer=3600
skylab.feature.redis.expire.feature-expire-second.checkpoint_single_frequency=3600
skylab.feature.redis.expire.feature-expire-second.checkpoint_single_score_frequency=3600
skylab.feature.redis.expire.feature-expire-second.exam_type_freq=3600
skylab.feature.redis.expire.feature-expire-second.has_video=3600
skylab.feature.redis.expire.feature-expire-second.is_forbidden_topic=3600
skylab.feature.redis.expire.feature-expire-second.paper_type_codes=3600
skylab.feature.redis.expire.feature-expire-second.revise_avg_mastery=3600
skylab.feature.redis.expire.feature-expire-second.revise_graph_layer=3600
skylab.feature.redis.expire.feature-expire-second.revise_virtual_node=3600
skylab.feature.redis.expire.feature-expire-second.revisepoint_test_frequency=3600
skylab.feature.redis.expire.feature-expire-second.topic_areas=3600
skylab.feature.redis.expire.feature-expire-second.topic_avg_cost=3600
skylab.feature.redis.expire.feature-expire-second.topic_avg_diff_cost=3600
skylab.feature.redis.expire.feature-expire-second.topic_difficulty=3600
skylab.feature.redis.expire.feature-expire-second.topic_type=3600
skylab.feature.redis.expire.feature-expire-second.topic_year=3600

skylab.feature.redis.clear-feature-name=anchorpoint_area_frequency,anchorpoint_exam_frequency,anchorpoint_graph_layer,anchorpoint_test_frequency_nationwide,checkpoint_exam_layer_freq,checkpoint_frequency,checkpoint_graph_layer,checkpoint_prepare_graph_layer,revise_graph_layer,revise_virtual_node
# \u6279\u91CF\u64CD\u4F5C\u914D\u7F6E
skylab.feature.redis.batch.size=500
# \u5185\u5B58\u76D1\u63A7\u914D\u7F6E
skylab.feature.redis.memory.monitor-enabled=true
skylab.feature.redis.memory.threshold=0.6
skylab.feature.redis.memory.check-interval=60000
##########################redis-end##########################