package com.iflytek.skylab.core.dataapi.service.impl;

import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.data.MasterData;
import com.iflytek.skylab.core.dataapi.data.MasterItem;
import com.iflytek.skylab.core.dataapi.data.MasterQuery;
import com.iflytek.skylab.core.dataapi.mongo.dao.UserMasteryRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.dataapi.service.MasterService;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import com.iflytek.skylab.core.dataapi.util.SubCollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
@EnableDataHub
@Slf4j
class MasterServiceImplV2Test {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MasterService masterService;

    @Autowired
    private UserMasteryRecordRepository userMasteryRecordRepository;

    private String userId = "dhpei";

    /**
     * 用户画像单元测试模拟数据
     */
    //@Test
    public void test_insert() {
        // 用户Id

        String collectionName = SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId);
        // 模拟有一个用户在同一个nodeId下有5条数据
        // 1001-01
        UserMasteryRecord record1 = new UserMasteryRecord();
        record1.setUserId(userId);
        record1.setNodeId("1001");
        record1.setUpdateTime(Instant.now());
        record1.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        // 1001-02
        UserMasteryRecord record2 = new UserMasteryRecord();
        record2.setUserId(userId);
        record2.setNodeId("1001");
        record2.setUpdateTime(Instant.now().minus(Duration.ofDays(1)));
        record2.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        // 1001-03
        UserMasteryRecord record3 = new UserMasteryRecord();
        record3.setUserId(userId);
        record3.setNodeId("1001");
        record3.setUpdateTime(Instant.now().minus(Duration.ofDays(2)));
        record3.setNodeType(NodeTypeEnum.ANCHOR_POINT);

        // 1002-01
        UserMasteryRecord record4 = new UserMasteryRecord();
        record4.setUserId(userId);
        record4.setNodeId("1002");
        record4.setUpdateTime(Instant.now().minus(Duration.ofDays(3)));
        record4.setNodeType(NodeTypeEnum.ANCHOR_POINT);

        // 1003-01
        UserMasteryRecord record5 = new UserMasteryRecord();
        record5.setUserId(userId);
        record5.setNodeId("1003");
        record5.setUpdateTime(Instant.now().minus(Duration.ofDays(4)));
        record5.setNodeType(NodeTypeEnum.ANCHOR_POINT);

        // 1004-01
        UserMasteryRecord record6 = new UserMasteryRecord();
        record6.setUserId(userId);
        record6.setNodeId("1004");
        record6.setUpdateTime(Instant.now().minus(Duration.ofDays(5)));
        record6.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        // 1004-02
        UserMasteryRecord record7 = new UserMasteryRecord();
        record7.setUserId(userId);
        record7.setNodeId("1004");
        record7.setUpdateTime(Instant.now().minus(Duration.ofDays(6)));
        record7.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        mongoTemplate.insert(Lists.newArrayList(record1, record2, record3,record4,record5,record6,record7),collectionName);
    }

    @Test
    public void test_queryMasterData(){
        // 查询数据
        MasterData masterData = masterService.queryMasterData("t100",
                MasterQuery.builder().userId(userId).build()
        );
        Assert.assertEquals(masterData.getItems().size(),4);
    }

    @Test
    public void test_queryMasterDataBatch(){
        // 查询数据
        List<UserMasteryRecord> userMasteryRecords = masterService.queryMasterDataBatch("t100",
                MasterQuery.builder().userId(userId).build()
        );
        Assert.assertEquals(userMasteryRecords.size(),4);
    }

    @Test
    public void test_savePrimaryMigrationUserMasteryRecordAndGet(){
        // 查询数据
        UserMasteryRecord r1 = new UserMasteryRecord();
        UserMasteryRecord r2 = new UserMasteryRecord();

        r1.setUserId(userId);
        r1.setUpdateTime(Instant.now());
        r1.setNodeId("1001");

        r1.setUserId(userId);
        r1.setUpdateTime(Instant.now());
        r1.setNodeId("1008");

        List<String> insertIdList = masterService.savePrimaryMigrationUserMasteryRecordAndGet(userId,
                Lists.newArrayList(r1,r2)
        );
        Assert.assertEquals(insertIdList.size(),1);
    }

    @Test
    public void test_upsertMasterDataBatch(){
        // 查询数据
        masterService.upsertMasterDataBatch("t100",
                MasterData.builder().userId(userId).items(
                        Lists.newArrayList(
                                MasterItem.builder().nodeId("1001").updateTime(Instant.now()).nodeType(NodeTypeEnum.ANCHOR_POINT).algoFusion(5.0).build(),
                                MasterItem.builder().nodeId("1002").updateTime(Instant.now()).nodeType(NodeTypeEnum.ANCHOR_POINT).algoFusion(5.0).build(),
                                MasterItem.builder().nodeId("1015").updateTime(Instant.now()).nodeType(NodeTypeEnum.ANCHOR_POINT).algoFusion(6.0).build()
                        )
                ).build()
        );
    }


    @Test
    public void test_updateMasterDataAndGet(){
        List<String> resList = masterService.updateMasterData("t100",
                MasterData.builder().userId("dhpei").items(
                        Lists.newArrayList(
                                MasterItem.builder().nodeId("1001").updateTime(Instant.now()).nodeType(NodeTypeEnum.ANCHOR_POINT).algoFusion(1.0).build(),
                                MasterItem.builder().nodeId("1002").updateTime(Instant.now()).nodeType(NodeTypeEnum.ANCHOR_POINT).algoFusion(1.0).build(),
                                MasterItem.builder().nodeId("1010").updateTime(Instant.now()).nodeType(NodeTypeEnum.ANCHOR_POINT).algoFusion(1.0).build()
                        )
                ).build()
        );
        Assert.assertEquals(resList.size(),3);
    }

}
