package com.iflytek.skylab.core.dataapi.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.service.SkylineConfigService;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = DataHubTestBoot.class
)
@EnableDataHub
class SkylineConfigServiceImplTest {

    @Resource
    private SkylineConfigService skylineConfigService;

    @Test
    public void queryAllConfigsTest() {
        // List<JSONObject> list = skylineConfigService.queryAllConfigs("skylab-sort@skylab-platform");
        // System.out.println(list);
        List<JSONObject> list2 = skylineConfigService.queryAllConfigs("skylab-sort");
        System.out.println(list2);
    }
}