package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyActionEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyTraceRecord;
import com.iflytek.skylab.core.dataapi.service.StudyTraceService;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.Instant;

@RunWith(SpringRunner.class)
@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = DataHubTestBoot.class
)
@EnableDataHub
class StudyTraceServiceImplTest {

    @Resource
    private StudyTraceService studyTraceService;

    @Test
    void saveStudyTraceRecord() {
        System.out.println(studyTraceService.saveStudyTraceRecord(generateStudyTraceData()));
    }

    @Test
    void saveStudyTraceRecordAsync() {
        studyTraceService.saveStudyTraceRecordAsync(generateStudyTraceData());
        System.out.println("main-" + System.currentTimeMillis());
        try {
            Thread.sleep(5000);
        } catch (InterruptedException ignored) {
        }
        System.out.println("main2-" + System.currentTimeMillis());
    }


    private StudyTraceRecord generateStudyTraceData() {
        StudyTraceRecord record = new StudyTraceRecord();
        // record.setId("623005b075b0a704ae9a3461");
        record.setUuid(IdUtil.randomUUID());
        record.setUserId("setUserId");
        record.setBizCode(BizCodeEnum.ADAPTIVE_TEST);
        record.setStudyCode(StudyCodeEnum.FINAL_EXAM);
        record.setStudyAction(StudyActionEnum.WEAK);
        record.setGraphVersion("setGraphVersion");
        record.setSubjectCode("setSubjectCode");
        record.setPhaseCode("setPhaseCode");
        record.setBookCode("setBookCode");
        record.setActionCode("setActionCode");
        record.setTraceContext(new JSONObject());
        record.setTraceId("setTraceId");
        record.setCreateTime(Instant.now());
        return record;
    }
}