package com.iflytek.skylab.core.dataapi.service.impl;

import com.iflytek.cog2.feaflow.sdk.data.ZionDictModel;
import com.iflytek.skylab.core.dataapi.annotation.EnableFeatureAPI;
import com.iflytek.skylab.core.dataapi.data.FeatureData;
import com.iflytek.skylab.core.dataapi.data.FeatureQuery;
import com.iflytek.skylab.core.dataapi.data.FeatureQueryItem;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
@EnableFeatureAPI
class FeatureServiceTest {

    @Autowired
    private FeatureService featureService;

    @Test
    public void query() {
        String traceId = UUID.randomUUID().toString();
//        DataApiItem dataApiItem = DataApiItem.builder().dataApiId("api-zqijjerf").version("1").build();

        //采用默认
        String schemaRowKey = "";

        FeatureQueryItem featureParamItem = new FeatureQueryItem();

        featureParamItem.setGraphVersion("v20");
        featureParamItem.setFeatureVersion(1);
        featureParamItem.setFeatureName("user_level");


        Map<String, String> params = new HashMap<>();
        params.put("user_id", "hhji2_synlearn_evel3_allright11");
        params.put("biz_code", "zsy_xxj");
        params.put("subject_code", "02");
        params.put("phase_code", "04");
        featureParamItem.setParams(Arrays.asList(params));


        FeatureQuery featureQuery = new FeatureQuery();
        featureQuery.setDictRowKey(schemaRowKey);
//        featureQuery.setDataApiItem(dataApiItem);
        featureQuery.setItems(Arrays.asList(featureParamItem));

        FeatureData featureData = featureService.query(traceId, featureQuery);
        log.info("featureData={}", featureData);
    }

    @Test
    public void querySchema() {
        String traceId = UUID.randomUUID().toString();
        String dictRowKey = "";

        //缺省 dictRowKey
        ZionDictModel zionDictModel = featureService.querySchema(traceId, dictRowKey);
        log.info("zionDictModel={}", zionDictModel);

        traceId = UUID.randomUUID().toString();
        dictRowKey = "001";
        zionDictModel = featureService.querySchema(traceId, dictRowKey);

        log.info("zionDictModel={}", zionDictModel);
    }

}