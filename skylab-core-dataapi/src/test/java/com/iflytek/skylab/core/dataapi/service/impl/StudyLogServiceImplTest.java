package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.mongo.dao.StudyLogRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.dataapi.service.StudyLogService;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.iflytek.skylab.core.constant.RecEvalEnum.REC_EVAL4IN;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DataHubTestBoot.class)
class StudyLogServiceImplTest {


    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    private StudyLogService studyLogService;
    @Resource
    private StudyLogRecordRepository studyLogRecordRepository;

    @Test
    public void saveRecLog() {
        RecLog recLog = new RecLog();
        recLog.setRecProps(new StudyLogRecordEntity.RecProps());
        recLog.setUserId("dingyufan");
        recLog.setPhaseCode("phase-code");
        recLog.setSchoolId("school-id");
        recLog.setGradeCode("grade-code");
        recLog.setClassId("class-id");
        recLog.setSubjectCode("subject-code");
        recLog.setBookCode("book-code");
        recLog.setGraphVersion("ver");
        recLog.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        recLog.setBizCode(BizCodeEnum.ZSY_XXJ);
        recLog.setNodeId("node-id");
        recLog.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        recLog.setResNodeId("res-node-id");
        recLog.setResNodeType(ResourceTypeEnum.TOPIC);
        recLog.setRoundId("mission-id");
        recLog.setRoundIndex(0);
        recLog.setFuncCode("func-code");
        recLog.setFrom("from");
        recLog.setCloneFlag(0);

        String savedId = studyLogService.saveRecLog(IdUtil.fastUUID(), recLog);
        System.out.println(savedId);
    }

    @Test
    public void query() {
        StudyLogQuery query = new StudyLogQuery();
        query.setUserId("2432a6a0-466d-4a0a-9357-bdf3db3de0e8");
        query.setSubjectCode("02");
        query.setPhaseCode("04");
        query.setNodeIdList(Lists.newArrayList("197bb2c7-ecf5-4b93-ae9e-9403d8432bd6"));
        // query.setResNodeIdList(Lists.newArrayList("a0e24b39-3334-403e-afe3-7403479b8830"));
        query.setBizCodeList(Lists.newArrayList(BizCodeEnum.ZSY_XXJ));

        List<StudyLogData> list = studyLogService.queryFeedbackLogs(IdUtil.fastUUID(), query, null);
        list.forEach(System.out::println);
    }


    @Test
    public void saveFeedbackLog() throws InterruptedException {
        FeedbackLog feedbackLog = new FeedbackLog();
        feedbackLog.setRefTraceId("ebb5f3df-8ff2-4386-b200-d35da1a573df");
        feedbackLog.setTimeCost(999);
        feedbackLog.setScoreRatio(0.7);
        feedbackLog.setFeedbackExt(new JSONObject());
        feedbackLog.setFeedbackTime(Instant.now());
        Thread.sleep(100L);

        feedbackLog.setUserId("xinagzhang182");
        feedbackLog.setPhaseCode("phase-code");
        feedbackLog.setGradeCode("grade-code");
        feedbackLog.setSubjectCode("subject-code");
        feedbackLog.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        feedbackLog.setBizCode(BizCodeEnum.ZSY_XXJ);
        feedbackLog.setNodeId("node-id");
        feedbackLog.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        feedbackLog.setResNodeId("res-node-id");
        feedbackLog.setResNodeType(ResourceTypeEnum.TOPIC);

        feedbackLog.setScore(1D);
        feedbackLog.setStandardScore(5D);
        feedbackLog.setTimeCost(999);
        feedbackLog.setScoreRatio(0.7);
        feedbackLog.setClassId(null);
        feedbackLog.setBookCode("null98798");

        String id = studyLogService.saveFeedbackLog(IdUtil.fastUUID(), feedbackLog);
        System.out.println(id);
    }


    @Test
    public void saveFeedbackLogWithoutRefTraceId() {
        FeedbackLog feedbackLog = new FeedbackLog();
        feedbackLog.setTimeCost(1000);
        feedbackLog.setScoreRatio(0.1);
        feedbackLog.setFeedbackExt(new JSONObject());
        feedbackLog.setFeedbackTime(Instant.now());
        feedbackLog.setUserId("asdf");
        feedbackLog.setPhaseCode("asdf");
        feedbackLog.setSchoolId("adf");
        feedbackLog.setGradeCode("adf");
        feedbackLog.setClassId("asdf");
        feedbackLog.setSubjectCode("sdf");
        feedbackLog.setBookCode("sdf");
        feedbackLog.setGraphVersion("dsf");
        feedbackLog.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        feedbackLog.setBizCode(BizCodeEnum.ZSY_XXJ);
        feedbackLog.setBizAction(BizActionEnum.EXAM_STAGE_REVIEW);
        feedbackLog.setNodeId("ddsf");
        feedbackLog.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        feedbackLog.setResNodeId("res");
        feedbackLog.setResNodeType(ResourceTypeEnum.TOPIC);
        feedbackLog.setRoundId("sad");
        feedbackLog.setRoundIndex(0);
        feedbackLog.setFuncCode("dfd");
        feedbackLog.setFrom("sdf");
        feedbackLog.setCloneFlag(0);
        String id = studyLogService.saveFeedbackLog(IdUtil.fastUUID(), feedbackLog);
        System.out.println(id);
    }


    @Test
    public void delete() {
        studyLogService.deleteStudyLog(IdUtil.fastUUID(), Lists.newArrayList("62394b2a6ccd295513379d48"));
    }

    @Test
    public void deleteStudyLogByUserId() {

        long before = studyLogRecordRepository.count();
        System.out.println("before = " + before);

        long aLong = studyLogService.deleteStudyLog(IdUtil.fastUUID(), "10597");
        System.out.println("aLong = " + aLong);

        long after = studyLogRecordRepository.count();
        System.out.println("after = " + after);
        long delete = before - after;
        System.out.println("delete = " + delete);
        Assert.assertEquals(delete, aLong);
    }

    @Test
    void queryRecommendRecordCache() {
        JSONObject jsonObject = studyLogService.queryRecommendRecordCache(IdUtil.fastUUID(), "abcd");
        System.out.println(jsonObject);
    }

    @Test
    void saveRecommendRecordCache() {
        studyLogService.saveRecommendRecordCache(IdUtil.fastUUID(), "abcd", new JSONObject(MapUtil.of("key", "val")));
    }

    public static final Map<String, List<String>> nodeTopicMap = Maps.newHashMap();

    {
        nodeTopicMap.put("afaa1100-947b-4379-b7b8-9535989e4330", Lists.newArrayList("2879dba8-efb9-476d-b153-da33d7e853df", "716a5363-5824-4a9d-9673-408c067e86bf", "d9989943-dbd3-427f-8a58-2278d249b918", "aff1607a-3436-4411-995e-24eeeebce37b", "0da8e005-6a29-4e34-b68d-b7c3a259e2f9"));
        nodeTopicMap.put("71ad24bc-2f43-4539-8bf7-645a4f798602", Lists.newArrayList("63679fb7-7586-4320-a672-65c167f11555", "15d67c04-3369-444a-a56c-b5ce4dbf00a6", "4e972bd6-cff1-4d89-961a-8e884acc3a92"));
        nodeTopicMap.put("61c83b37-9f42-49e5-b64f-31736c930f5c", Lists.newArrayList("65ccaae0-6670-4f9d-a639-95b266e3eb6a", "ca401ac1-0bfa-4472-b2a6-91070442f42f", "73af0ec0-548e-49d3-afc0-c2f10e8cb3a1"));
        nodeTopicMap.put("e0d632b4-2eea-4d5e-b1aa-cf43a05e907a", Lists.newArrayList("b6be8b36-2415-4cb8-a804-be078ab75803", "6e1e3e86-4071-4e11-a99c-a83d88866523", "127345bd-bd3b-4141-959d-1a8245734a70", "8ea3a26c-e267-4e5d-ae24-a99d25ec52b0", "e7665391-9539-4570-8e6a-805d3649d4d1"));
    }

    @Test
    void unitReview() {
        nodeTopicMap.forEach((nodeId, topicIds) -> {
            topicIds.forEach(topicId -> {
                // FeedbackLog feedbackLog = createFeedbackLog(StudyCodeEnum.UNIT_REVIEW, nodeId, topicId);
                // FeedbackLog feedbackLog = createFeedbackLog(StudyCodeEnum.MID_EXAM, nodeId, topicId);
                FeedbackLog feedbackLog = createFeedbackLog(StudyCodeEnum.FINAL_EXAM, nodeId, topicId);
                String id = studyLogService.saveFeedbackLog(IdUtil.fastUUID(), feedbackLog);
                System.out.println(id);
            });
        });

    }

    public static FeedbackLog createFeedbackLog(StudyCodeEnum studyCode, String nodeId, String topicId) {
        FeedbackLog feedbackLog = new FeedbackLog();
        feedbackLog.setRefTraceId(IdUtil.fastUUID());
        feedbackLog.setTimeCost(RandomUtil.randomInt(10, 100));
        feedbackLog.setScore(RandomUtil.randomDouble(10.0, 20.0));
        feedbackLog.setStandardScore(RandomUtil.randomDouble(10.0, 20.0));
        feedbackLog.setScoreRatio(feedbackLog.getScore() / feedbackLog.getStandardScore());
        feedbackLog.setFeedbackExt(null);
        feedbackLog.setFeedbackTime(Instant.now());
        feedbackLog.setEvalEnd(true);
        feedbackLog.setUserId("68eeb310-2475-4724-97db-ce278cbabe45");
        feedbackLog.setPhaseCode("04");
        // feedbackLog.setSchoolId();
        feedbackLog.setGradeCode("04");
        // feedbackLog.setClassId();
        feedbackLog.setSubjectCode("05");
        feedbackLog.setBookCode("01");
        feedbackLog.setGraphVersion("v2");
        feedbackLog.setStudyCode(studyCode);
        feedbackLog.setBizCode(BizCodeEnum.ZSY_XXJ);
        feedbackLog.setBizAction(BizActionEnum.NONE);
        feedbackLog.setNodeId(nodeId);
        feedbackLog.setNodeType(NodeTypeEnum.CHECK_POINT);
        feedbackLog.setResNodeId(topicId);
        feedbackLog.setResNodeType(ResourceTypeEnum.TOPIC);
        feedbackLog.setRoundId(IdUtil.fastUUID());
        feedbackLog.setRoundIndex(1);
        feedbackLog.setFuncCode(REC_EVAL4IN.name());
        feedbackLog.setFrom("unknown");
        feedbackLog.setCloneFlag(0);
        return feedbackLog;
    }

    @Test
    void test() {

    }

    @Test
    public void queryUserRecentlyAnswerRecord() {
        String traceId = IdUtil.fastSimpleUUID();
        UserRecentlyStudyLogQuery query = new UserRecentlyStudyLogQuery();
        query.setUserId("9900000100102339999");
        query.setSubjectCode("02");
        query.setPhaseCode("05");
        query.setBizCode(BizCodeEnum.ZSY_XXJ);
        query.setLimit(10);

        List<StudyLogData> list = studyLogService.queryUserRecentlyAnswerRecord(traceId, query);
        list.forEach(System.out::println);
    }

    @Test
    public void queryFeedbackLogsToday() {
        String traceId = IdUtil.fastSimpleUUID();

        StudyLogQuery query = new StudyLogQuery();
        query.setUserId("xiangzhang182_test");
        query.setSubjectCode("02");
        query.setPhaseCode("04");
        query.setNodeIdList(Lists.newArrayList("dfa490d8-ee53-4e52-bcdc-340bad9352f3", "e444848d-eacc-4977-8a17-4225c27e75ea"));
        query.setBizCodeList(Arrays.asList(BizCodeEnum.ZSY_XXJ));
        List<StudyLogData> list = studyLogService.queryFeedbackLogsToday(IdUtil.fastUUID(), query, null);

        list.forEach(System.out::println);
    }
}
