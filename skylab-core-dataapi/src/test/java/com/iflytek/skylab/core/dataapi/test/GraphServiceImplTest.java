package com.iflytek.skylab.core.dataapi.test;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.GraphEdge;
import com.iflytek.skylab.core.dataapi.data.GraphLabelQuery;
import com.iflytek.skylab.core.dataapi.data.GraphQuery;
import com.iflytek.skylab.core.dataapi.data.GraphVertex;
import com.iflytek.skylab.core.dataapi.data.GraphVertexQuery;
import com.iflytek.skylab.core.dataapi.data.GraphVertexesQuery;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.data.UpperLayerQuery;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.service.cache.LocalFileGraphCache;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class GraphServiceImplTest {



    @Autowired
    @Qualifier("localGraphService")
    private GraphService localGraphService;

    @Autowired
    LocalFileGraphCache diagGraphCache;

    @Test
    public void querySubGraphNeedProps() {

        long a = 0;
        for (int i = 0; i < 1; i++) {
            long currentTimeMillis = System.currentTimeMillis();
            SubGraphQuery subGraphQuery = new SubGraphQuery();
            subGraphQuery.setTraceId(UUID.randomUUID().toString());
            subGraphQuery.setGraphVersion("20250415_001");
            subGraphQuery.setRootVertexLabel("ANCHOR_POINT");
            subGraphQuery.setRootVertexIdList(Arrays.asList("ffeaa8ae-0e93-4628-8670-901eae305bb3"));
            List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("COURSE").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("PERIOD").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("PERIOD").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("L2COURSE").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("L3COURSE").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L3COURSE").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L3COURSE").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("LEARN_PATH").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
            subGraphQuery.setEdgeLabels(edgeLabels);
//            Map<String, List<String>> nodeProps = new HashMap<>();
//            nodeProps.put("ANCHOR_POINT", Arrays.asList("anchorPointType", "difficulty", "evaluatePoint", "evaluations", "examPoint", "phaseCode", "realLastLevelRelationCatas", "subjectCode", "tracePoints"));
//            nodeProps.put("LEARN_PATH", Arrays.asList("pathType"));
            subGraphQuery.setNodeProps(null);


//            subGraphQuery.setTraceId(UUID.randomUUID().toString());
//            subGraphQuery.setGraphVersion("v2");
//            subGraphQuery.setRootVertexLabel("COURSE");
//            subGraphQuery.setRootVertexIdList(Arrays.asList("01_07020101-001_02_001"));
//            List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
//            subGraphQuery.setEdgeLabels(edgeLabels);
            GraphData graphData = localGraphService.querySubGraph(subGraphQuery);
//            System.out.println(graphData);
            Assert.assertTrue(graphData != null);
            Assert.assertTrue(!graphData.getEdges().isEmpty());
            Assert.assertTrue(!graphData.getVertices().isEmpty());
            long end = System.currentTimeMillis() - currentTimeMillis;
            a = a + end;
            System.err.println("耗时：" + end + " 图谱长度" + JSON.toJSONString(graphData).length());
        }
        System.err.println("平均耗时：" + a / 20);
    }


}
