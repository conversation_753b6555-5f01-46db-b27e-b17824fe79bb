package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.dataapi.configuration.StudyLogProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.mongo.dao.StudyLogRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.dao.StudyMacrographRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyMacrographLogRecordEntity;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skylab.core.dataapi.service.StudyMacrographLogService;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import com.iflytek.skylab.core.dataapi.util.UserErrorAnswerDelegate;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DataHubTestBoot.class)
class UserErrorAnswerTest {
    @Resource
    private StudyLogProperties studyLogProperties;
    @Resource
    private FeatureService featureService;

    @Resource
    private StudyMacrographLogService studyMacrographLogService;

    @Resource
    private  StudyMacrographRecordRepository macrographRecordRepository;

    @Resource
    private  StudyLogRecordRepository studyLogRecordRepository;

    @Test
    public void saveRecLog() {
        ErrorLogQuery studyLogQuery = new ErrorLogQuery();
        studyLogQuery.setUserId("1500000100247860383");
        studyLogQuery.setPhaseCode("05");
        studyLogQuery.setSubjectCode("05");
        FeatureQuery featureQuery = UserErrorAnswerDelegate.generateFeatureQuery(studyLogQuery, studyLogProperties);
        FeatureData featureData = featureService.query("Asdfasfd", featureQuery);

        List<UserErrorAnswerLog> list = new ArrayList<>();
        if (featureData == null || CollectionUtil.isEmpty(featureData.getItems())) {

        }

        List<Map<String, String>> values = featureData.getItems().get(0).getValues();
        if (CollectionUtil.isEmpty(values)) {

        }
        // 遍历查询结果
        for (Map<String, String> valueMap : values) {
            String json = valueMap.get("user_error_answers");
            if (StrUtil.isBlank(json)) {
                continue;
            }

            // json为对象
            List<UserErrorData> errorDataList = JSON.parseArray(json, UserErrorData.class);
            errorDataList.forEach(errorData -> {
                UserErrorAnswerLog userErrorAnswerLog = new UserErrorAnswerLog();
                userErrorAnswerLog.setResNodeId(errorData.getTopicId());
                userErrorAnswerLog.setNodeId(errorData.getAnchorId());
                userErrorAnswerLog.setUpdateTime(Instant.ofEpochMilli(errorData.getFeedbackTime().getTime()));
                list.add(userErrorAnswerLog);
            });

        }
        log.info("featureData:{}", featureData);
    }

    @Test
    public void Test() {
        ErrorLogQuery studyLogQuery = new ErrorLogQuery();
        studyLogQuery.setUserId("f387c550-d704-458f-9de7-37395165f99e");
        studyLogQuery.setPhaseCode("03");
        studyLogQuery.setSubjectCode("02");
        List<UserErrorAnswerLog> list = studyMacrographLogService.queryWrongLogs("123", studyLogQuery, 100);
        log.info("list:{}", list);
    }
    @Test
    public void Test1() {
        ErrorLogQuery studyLogQuery = new ErrorLogQuery();
        studyLogQuery.setUserId("4d2f3226-783e-4447-9f0e-bf0bf4fcb1c5");
        studyLogQuery.setPhaseCode("03");
        studyLogQuery.setSubjectCode("02");
        //LocalDateTime dateTime = LocalDateTime.of(2024, 6, 19, 0, 30, 45);
        //Criteria criteria = studyLogQuery.buildCriteria()
        //        .and("feedback_time").gte(dateTime)
        //        .and("time_cost").ne(null);
        //List<StudyLogRecordEntity> search = studyLogRecordRepository.search(criteria);
        //List<StudyMacrographLogRecordEntity> search1 = macrographRecordRepository.search(criteria);
        List<UserErrorAnswerLog> list = studyMacrographLogService.queryWrongLogs("123", studyLogQuery, 100);
        log.info("list:{}", JSON.toJSONString(list));
    }
}
