package com.iflytek.skylab.core.dataapi.service;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyForbiddenRecordEntity;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
@EnableDataHub
@Slf4j
class StudyForbiddenRecordServiceTest {

    @Test
    public void testSave() {
        String userId = "user123";
        String catalogId = "catalog456";
        List<String> nodeIds = Arrays.asList("node789", "node101");
        log.info("开始保存第一批数据，userId: {}, catalogId: {}, nodeIds数量: {}", userId, catalogId, nodeIds.size());
        DataHub.getForbiddenRecordService().save(userId, catalogId, nodeIds);

        // 查询保存结果
        List<StudyForbiddenRecordEntity> firstResult = DataHub.getForbiddenRecordService().findByUserIdAndCatalogId(userId, catalogId);
        log.info("第一批数据保存后查询结果数量: {}", firstResult.size());

        List<String> nodeIds2 = Arrays.asList("node789", "node6666666","324232131node");
        log.info("开始保存第二批数据，userId: {}, catalogId: {}, nodeIds数量: {}", userId, catalogId, nodeIds2.size());
        DataHub.getForbiddenRecordService().save(userId, catalogId, nodeIds2);

        // 查询最终结果
        List<StudyForbiddenRecordEntity> finalResult = DataHub.getForbiddenRecordService().findByUserIdAndCatalogId(userId, catalogId);
        log.info("最终数据查询结果数量: {}", finalResult.size());
    }

    @Test
    public void testFindByUserIdAndCatalogId() {
        String userId = "user123";
        String catalogId = "catalog456";

        List<StudyForbiddenRecordEntity> result = DataHub.getForbiddenRecordService().findByUserIdAndCatalogId(userId, catalogId);
        log.info("查询结果数量: {}, 数据详情: {}", result.size(), JSON.toJSONString(result));
    }

} 