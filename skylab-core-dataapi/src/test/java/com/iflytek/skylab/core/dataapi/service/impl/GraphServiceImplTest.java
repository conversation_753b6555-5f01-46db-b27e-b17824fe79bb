package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class GraphServiceImplTest {

    @Autowired
    private GraphService graphService;

    /**
     * 根据 考点 查询子图 考点 -> 锚点 -> 题
     * |             ^
     * |             |
     * +-------------+
     * 05-02 21:31:05.736 DEBUG [     main] c.i.s.c.d.service.impl.GraphServiceImpl [676][]: traceId=6512f21d-4744-476e-9ff5-772153f1cc8e;Exec nGQL=
     * USE xxj_20240424_001;GET SUBGRAPH WITH PROP 6 STEPS FROM '01_09020101-002_002'
     * OUT UNIT_PERIOD,UNIT_LEARN_PATH,COURSE_PERIOD,PERIOD_LEARN_PATH,PERIOD_ANCHOR_POINT,
     * L2COURSE_L3COURSE,UNIT_COURSE,UNIT_ANCHOR_POINT,COURSE_ANCHOR_POINT,COURSE_LEARN_PATH,
     * COURSE_L2COURSE,L2COURSE_ANCHOR_POINT,L2COURSE_LEARN_PATH,L3COURSE_ANCHOR_POINT,
     * L3COURSE_LEARN_PATH,LEARN_PATH_ANCHOR_POINT,ANCHOR_POINT_ANCHOR_POINT YIELD
     * VERTICES AS nodes, EDGES AS relationships;
     */
    @Test
    public void testQuerySubGraph() {
        long a = 0;
        for (int i = 0; i < 1; i++) {
            long currentTimeMillis = System.currentTimeMillis();
            SubGraphQuery subGraphQuery = new SubGraphQuery();
            subGraphQuery.setTraceId(UUID.randomUUID().toString());
            subGraphQuery.setGraphVersion("20240424_001");
            subGraphQuery.setRootVertexLabel("UNIT");
            subGraphQuery.setRootVertexIdList(Arrays.asList("01_09020101-002_002"));
            List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("COURSE").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("PERIOD").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("PERIOD").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("L2COURSE").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("L3COURSE").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L3COURSE").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L3COURSE").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("LEARN_PATH").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("ANCHOR_POINT").build());
//        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
            // edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
            subGraphQuery.setEdgeLabels(edgeLabels);
            GraphData graphData = graphService.querySubGraph(subGraphQuery);
//        System.out.println(graphData);
            Assert.assertTrue(graphData != null);
            Assert.assertTrue(!graphData.getEdges().isEmpty());
            Assert.assertTrue(!graphData.getVertices().isEmpty());
            long end = System.currentTimeMillis() - currentTimeMillis;
            a = a + end;
            System.err.println("耗时：" + end + " 图谱长度" + JSON.toJSONString(graphData).length());
        }
        System.err.println("平均耗时：" + a / 20);
        System.err.println("########################");
        querySubGraphNeedProps();
    }

    @Test
    public void querySubGraphNeedProps() {
        long a = 0;
        for (int i = 0; i < 1; i++) {
            long currentTimeMillis = System.currentTimeMillis();
            SubGraphQuery subGraphQuery = new SubGraphQuery();
            subGraphQuery.setTraceId(UUID.randomUUID().toString());
            subGraphQuery.setGraphVersion("20240801_002");
            subGraphQuery.setRootVertexLabel("UNIT");
            subGraphQuery.setRootVertexIdList(Arrays.asList("01_09020101-002_002"));
            List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("COURSE").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("PERIOD").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("PERIOD").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("L2COURSE").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("L3COURSE").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L3COURSE").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L3COURSE").target("LEARN_PATH").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("LEARN_PATH").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("ANCHOR_POINT").build());
            subGraphQuery.setEdgeLabels(edgeLabels);
//            Map<String, List<String>> nodeProps = new HashMap<>();
//            nodeProps.put("ANCHOR_POINT", Arrays.asList("anchorPointType", "difficulty", "evaluatePoint", "evaluations", "examPoint", "phaseCode", "realLastLevelRelationCatas", "subjectCode", "tracePoints"));
//            nodeProps.put("LEARN_PATH", Arrays.asList("pathType"));
            subGraphQuery.setNodeProps(null);
            GraphData graphData = graphService.querySubGraphNeedProps(subGraphQuery);
//            System.out.println(graphData);
            Assert.assertTrue(graphData != null);
            Assert.assertTrue(!graphData.getEdges().isEmpty());
            Assert.assertTrue(!graphData.getVertices().isEmpty());
            long end = System.currentTimeMillis() - currentTimeMillis;
            a = a + end;
            System.err.println("耗时：" + end + " 图谱长度" + JSON.toJSONString(graphData).length());
        }
        System.err.println("平均耗时：" + a / 20);
    }

    /**
     * {
     * "ANCHOR_POINT.anchorPointType": "conventional",
     * "ANCHOR_POINT.difficulty": "1",
     * "ANCHOR_POINT.evaluatePoint": "0",
     * "ANCHOR_POINT.evaluations": "ks,xj",
     * "ANCHOR_POINT.examPoint": "0",
     * <p>
     * "ANCHOR_POINT.phaseCode": "04",
     * <p>
     * "ANCHOR_POINT.realLastLevelRelationCatas": "01_09020101-002_002_000_period10",
     * "ANCHOR_POINT.subjectCode": "02",
     * <p>
     * "ANCHOR_POINT.tracePoints": "[{\"anchorPointId\":\"7572f4d8-44b0-4856-8740-9048a11251b0\",\"name\":\"二次函数的定义\",\"oneWayRelation\":{\"code\":\"reverse\",\"name\":\"反向\",\"primaryKey\":\"reverse\"},\"primaryKey\":\"7572f4d8-44b0-4856-8740-9048a11251b0\",\"realLastLevelRelationCatas\":\"01_09020101-002_002_000_period10\",\"relation\":{\"code\":\"tracePointRelation\",\"name\":\"溯源关系\",\"primaryKey\":\"tracePointRelation\"},\"weight\":1.0}]",
     * }
     */
    @Test
    public void queryVertexByIdsWithProps() {
        GraphVertexesQuery query = new GraphVertexesQuery();
        query.setIds(Arrays.asList("25264d4d-1659-4c7e-8018-fa07911046e3",
                "003e46b3-1da2-4a16-8697-33705df3d15a",
                "13897fa3-f837-4ea5-b140-a7e5ae19523c",
                "5679f291-80a3-464f-9a3a-8ef108598684", "01_08020101-002_06", "01_08020101-002_06_003"));
        Map<String, List<String>> nodeProps = new HashMap<>();
        nodeProps.put("ANCHOR_POINT", Arrays.asList("anchorPointType", "difficulty", "evaluatePoint", "evaluations", "examPoint", "phaseCode", "realLastLevelRelationCatas", "subjectCode", "tracePoints"));
        nodeProps.put("CHECK_POINT", Arrays.asList("checkPointName"));
        query.setNodeProps(nodeProps);
        query.setGraphVersion("20240424_001");
        query.setTraceId(IdUtil.fastSimpleUUID());
        List<GraphData.GraphVertex> graphData = graphService.queryVertexByIdsWithPropsCache(query);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.isEmpty());


        GraphVertexesQuery query2 = new GraphVertexesQuery();
        query2.setIds(Arrays.asList("25264d4d-1659-4c7e-8018-fa07911046e3",
                "003e46b3-1da2-4a16-8697-33705df3d15a", "6722d497-b93e-4908-91ca-dcaa0f7fd8e9", "01_09020101-002_002_000_period10"
        ));

        query2.setNodeProps(nodeProps);
        query2.setGraphVersion("20240424_001");
        query2.setTraceId(IdUtil.fastSimpleUUID());
        List<GraphData.GraphVertex> graphData2 = graphService.queryVertexByIdsWithPropsCache(query2);
        System.out.println(graphData2);

    }

    /**
     * 根据 考点 查询子图 书 -> 章 -> 考点 -> 锚点 -> 题
     * |             ^
     * |             |
     * +-------------+
     */
    @Test
    public void testQuerySubGraph2() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("CHECK_POINT");
        subGraphQuery.setRootVertexIdList(Arrays.asList("00434b08-eb94-46d3-9768-dd02690a23b0"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("CHECK_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("BOOK").target("UNIT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("TOPIC").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("ANCHOR_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 查询 节 下面的 锚点
     */
    @Test
    public void testQuerySubGraph3() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("COURSE");
        subGraphQuery.setRootVertexIdList(Arrays.asList("01_07020101-001_02_001"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 查询 锚点 下面的 题
     */
    @Test
    public void testQuerySubGraph4() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v9");
        subGraphQuery.setRootVertexLabel("ANCHOR_POINT");
        subGraphQuery.setRootVertexIdList(Arrays.asList("25264d4d-1659-4c7e-8018-fa07911046e3"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = DataHub.getGraphService().querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 根据锚点反查 节 -> 锚点 子图
     */
    @Test
    public void testQuerySubGraph5() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("ANCHOR_POINT");
        subGraphQuery.setRootVertexIdList(Arrays.asList("1873903a-4090-4371-8a1e-a0a8d7c97c8d"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 查询 章 下面的 题包
     */
    @Test
    public void testQuerySubGraph6() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("UNIT");
        subGraphQuery.setRootVertexIdList(Arrays.asList("72_09020254-002_004"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("TOPIC").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 根据路径 教材版本 -> 书本 -> 章 查询 教材版本 下面的所有 章
     */
    @Test
    public void testQueryVertices() {
        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion("v2");
        graphQuery.setRootVertexLabel("PRESS");
        graphQuery.setRootVertexIdList(Arrays.asList("63"));
        List<GraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("BOOK").setTarget("UNIT"));
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("PRESS").setTarget("BOOK"));
        graphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.queryVertices(graphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    @Test
    public void testQueryVertices2() {
        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion("v2");
        graphQuery.setRootVertexLabel("COURSE");
        graphQuery.setRootVertexIdList(Arrays.asList("01_07020101-001_02_001"));
        List<GraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
//        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("COURSE").target("LEARN_PATH").filter("").build());
//        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("COURSE").target("LEARN_PATH").filter("$.indexNum==0").build());
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("COURSE").setTarget("LEARN_PATH"));
//        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("LEARN_PATH").target("ANCHOR_POINT").filter("$.indexNum==6").build());
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("LEARN_PATH").setTarget("ANCHOR_POINT"));
//        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("LEARN_PATH").target("ANCHOR_POINT").filter("").build());
        graphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.queryVertices(graphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 查询锚点的后继锚点
     */
    @Test
    public void testQueryVertices3() {
        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion("v2");
        graphQuery.setRootVertexLabel("ANCHOR_POINT");
        graphQuery.setRootVertexIdList(Arrays.asList("df7ef736-b812-40d2-9733-db47840f2a92"));
        List<GraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("ANCHOR_POINT").setTarget("ANCHOR_POINT"));
        graphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.queryVertices(graphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    @Test
    public void testQueryVertices4() {
        List<GraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("COURSE").setTarget("LEARN_PATH"));
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("LEARN_PATH").setTarget("ANCHOR_POINT"));
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("ANCHOR_POINT").setTarget("TOPIC"));
        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion("v11");
        graphQuery.setRootVertexLabel("COURSE");
        graphQuery.setRootVertexIdList(Arrays.asList("01_08020101-002_02_002"));
        graphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.queryVertices(graphQuery);
        System.out.println(graphData);
    }

    /**
     * 根据路径 教材版本 -> 书本 -> 章 查询 章 所属的 教材版本
     */
    @Test
    public void testQueryVerticesReverse() {
        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion("v2");
        graphQuery.setRootVertexLabel("UNIT");
        graphQuery.setRootVertexIdList(Arrays.asList("63_08020245-002_004"));
        List<GraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("BOOK").setTarget("UNIT"));
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("PRESS").setTarget("BOOK"));
        graphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.queryVerticesReverse(graphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 根据路径 考点 -> 题 反查 题 所属的 考点
     */
    @Test
    public void testQueryVerticesReverse2() {
        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion("v2");
        graphQuery.setRootVertexLabel("TOPIC");
        graphQuery.setRootVertexIdList(Arrays.asList("b004f3dc-6955-419f-8633-23278c742266"));
        List<GraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("CHECK_POINT").setTarget("TOPIC"));
        graphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.queryVerticesReverse(graphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 查询 节点 详情
     */
    @Test
    public void testQueryVertex() {
        GraphVertexQuery graphVertexQuery = new GraphVertexQuery();
        graphVertexQuery.setTraceId(UUID.randomUUID().toString());
        graphVertexQuery.setId("00434b08-eb94-46d3-9768-dd02690a23b0");
        graphVertexQuery.setGraphVersion("v2");
        GraphData graphData = graphService.queryVertex(graphVertexQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 新增点 -> 编辑点 -> 删除点
     */
    @Test
    public void testSaveAndDeleteVertex() {

        // 插入点
        GraphVertex graphVertex = new GraphVertex().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("BOOK").setId("BOOK001").setProperties(new JSONObject().fluentPut("name", "001"));
        graphService.saveVertex(graphVertex);

        // 查询
        GraphVertexQuery graphVertexQuery = new GraphVertexQuery().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setId("BOOK001");
        GraphData graphData = graphService.queryVertex(graphVertexQuery);
        System.out.println(graphData);

        // 修改名称
        graphVertex = new GraphVertex().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("BOOK").setId("BOOK001").setProperties(new JSONObject().fluentPut("name", "002"));
        graphService.saveVertex(graphVertex);

        // 查询
        graphVertexQuery = new GraphVertexQuery().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setId("BOOK001");
        graphData = graphService.queryVertex(graphVertexQuery);
        System.out.println(graphData);

        // 删除点
        graphVertex = new GraphVertex().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("BOOK").setId("BOOK001");
        graphService.deleteVertex(graphVertex);

        // 查询
        graphVertexQuery = new GraphVertexQuery().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setId("BOOK001");
        graphData = graphService.queryVertex(graphVertexQuery);
        System.out.println(graphData);
    }

    /**
     * 插入点 -> 插入边 -> 删除边 -> 删除点
     */
    @Test
    public void testSaveEdge() {

        // 插入第一个点
        GraphVertex bookVertex = new GraphVertex().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("BOOK").setId("BOOK001").setProperties(new JSONObject().fluentPut("name", "001"));
        graphService.saveVertex(bookVertex);

        // 插入第二个点
        GraphVertex unitVertex = new GraphVertex().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("UNIT").setId("UNIT001").setProperties(new JSONObject().fluentPut("name", "001"));
        graphService.saveVertex(unitVertex);

        // 插入边
        GraphEdge bookUnitEdge = new GraphEdge().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("BOOK_UNIT").setSource("BOOK001").setTarget("UNIT001");
        graphService.saveEdge(bookUnitEdge);

        // 查询 BOOK -> UNIT 子图
        SubGraphQuery subGraphQuery = new SubGraphQuery().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setRootVertexLabel("BOOK").setRootVertexIdList(Arrays.asList("BOOK001")).setEdgeLabels(Arrays.asList(SubGraphQuery.EdgeLabel.builder().source("BOOK").target("UNIT").build()));
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);

        // 删除边
        bookUnitEdge = new GraphEdge().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("BOOK_UNIT").setSource("BOOK001").setTarget("UNIT001");
        graphService.deleteEdge(bookUnitEdge);

        // 查询 BOOK -> UNIT 子图
        subGraphQuery = new SubGraphQuery().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setRootVertexLabel("BOOK").setRootVertexIdList(Arrays.asList("BOOK001")).setEdgeLabels(Arrays.asList(SubGraphQuery.EdgeLabel.builder().source("BOOK").target("UNIT").build()));
        graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);

        // 删除第一个点
        GraphVertex graphVertex = new GraphVertex().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("BOOK").setId("BOOK001");
        graphService.deleteVertex(graphVertex);

        // 删除第二个点
        graphVertex = new GraphVertex().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("UNIT").setId("UNIT001");
        graphService.deleteVertex(graphVertex);
    }

    /**
     * 路径查询
     */
    @Test
    public void testQueryPath() {
        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion("vtrytry");
        graphQuery.setRootVertexLabel("PRESS");
        graphQuery.setRootVertexIdList(Arrays.asList("01"));
        graphQuery.setTailVertexLabel("ANCHOR_POINT");
        graphQuery.setTailVertexIdList(Arrays.asList("c555b1ad-b512-4b2c-b38a-af918629b6ed"));
        List<GraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("PRESS").setTarget("BOOK"));
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("BOOK").setTarget("UNIT"));
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("UNIT").setTarget("ANCHOR_POINT"));
        // edgeLabels.add(new GraphQuery.EdgeLabel().setSource("COURSE").setTarget("ANCHOR_POINT"));
        graphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.queryPath(graphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 根据 label 查询点列表
     */
    @Test
    public void testLookup() {
        GraphLabelQuery graphLabelQuery = new GraphLabelQuery().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v2").setLabel("CHECK_POINT");
        GraphData graphData = graphService.lookup(graphLabelQuery);
        System.out.println(graphData);
    }

    @Test
    public void testLookup2() {
        GraphLabelQuery graphLabelQuery = new GraphLabelQuery().setTraceId(UUID.randomUUID().toString()).setGraphVersion("v10_aiexam").setLabel("CHECK_POINT");
        JSONObject props = new JSONObject().fluentPut("level", 3).fluentPut("phaseCode", "04").fluentPut("subjectCode", "02").fluentPut("difficulty", "4");
        // .fluentPut("checkPointName", "最短路径问题（勾股定理）");

        GraphData graphData = graphService.lookup(graphLabelQuery, props);
        System.out.println(graphData);
    }

    @Test
    public void testLookup3() {
        GraphLabelQuery graphLabelQuery = new GraphLabelQuery().setTraceId(UUID.randomUUID().toString()).setGraphVersion("20250723_001").setLabel("UNIT");
        JSONObject props = new JSONObject().fluentPut("phase", "03").fluentPut("subject", "02");
        GraphData graphData = graphService.lookup(graphLabelQuery, props);
        System.out.println(graphData);
    }


    @Test
    public void testQueryVertexByIds() {
        GraphVertexesQuery query = new GraphVertexesQuery();
        query.setTraceId(UUID.randomUUID().toString());
        query.setIds(Lists.newArrayList("01_08020101-002", "01"));
        query.setGraphVersion("v10_aiexam");

        GraphData graphData = graphService.queryVertexByIds(query);
        System.out.println(graphData);
    }

    @Test
    public void testQueryVerticesWithEdgeProps() {
        GraphQuery.EdgeLabel edge1 = new GraphQuery.EdgeLabel().setSource("AIEXAM").setTarget("CHECK_POINT");
        GraphQuery.EdgeLabel edge2 = new GraphQuery.EdgeLabel().setSource("CHECK_POINT").setTarget("TOPIC").setProperties(new JSONObject().fluentPut("topicType", "10"));

        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion("v10_aiexam");
        graphQuery.setRootVertexLabel("AIEXAM");
        graphQuery.setRootVertexIdList(Arrays.asList("37aa5f65-9524-444e-942f-8d5c57cb7494"));
        graphQuery.setEdgeLabels(Lists.newArrayList(edge1, edge2));

        GraphData graphData = graphService.queryVertices(graphQuery);
        System.out.println(graphData);
    }

    public static void main(String[] args) {
        List<String> input = new ArrayList<>();
        input.add("01_06020101-003_06_002_005_007");
        input.add("01_06020101-003_06_002");
        input.add("01_06020101-003_07");
        input.add("01_06020101-003_07_002");
        input.add("01_06020101-003_07_002_ppp");
        input.add("01_06020101-003_06_002_005");
        input.add("01_06020101-004_06");
        input.add("01_06020101-004_06_cca");
        input.add("01_04020101-003_02_001_period10");
        input.add("01_04020101-003_02");
        input.add("01_04020101-003_02_001");

        List<String> output = parseCollection(input);
        System.out.println(output);
    }

    public static List<String> parseCollection(List<String> input) {
        Set<String> parsedStrings = new HashSet<>(input); // 用于存储解析后的字符串，使用Set来去重
        for (String str : input) {
            Iterator<String> iterator = parsedStrings.iterator();
            while (iterator.hasNext()) {
                String ps = iterator.next();
                if (!ps.equals(str)) {
                    if (str.contains(ps)) {
                        iterator.remove();
                    }
                }
            }
        }
        // 存储最终结果的列表
        return new ArrayList<>(parsedStrings);
    }


    @Test
    public void findAnchorPointUpperLayer2() {
//        锚点，章节s
        String s2 = FileUtil.readUtf8String("D:\\文档\\推荐服务\\2023-08\\小学3456\\1.txt");
//        所有书本下锚点 list
        Set list = JSONUtil.parseObj(s2).keySet();
        Map<String, List<String>> anchorMaps = extracted(list);

//      输出  anchorMaps
        System.err.println(JSONUtil.toJsonStr(anchorMaps));
        String s = FileUtil.readUtf8String("D:\\文档\\推荐服务\\2023-08\\小学3456\\小数人教版1-6上锚点和最末级目录对应关系-20230730.json");
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(s);


        Set<String> set = anchorMaps.keySet();
        Set<String> fileSet = jsonObject.keySet();
        List<String> strings = CollUtil.subtractToList(set, fileSet);
        System.err.println(JSONUtil.toJsonStr(strings));
        List<String> strings2 = CollUtil.subtractToList(fileSet, set);
        System.err.println(JSONUtil.toJsonStr(strings2));
        Assert.assertTrue(strings.size() == 0);
        Assert.assertTrue(strings2.size() == 0);

        for (Map.Entry<String, List<String>> entry : anchorMaps.entrySet()) {
            List<String> value = entry.getValue();
            List<String> beanList = jsonObject.getBeanList(entry.getKey(), String.class);
            if (!(CollUtil.containsAll(value, beanList) && CollUtil.containsAll(beanList, value))) {
                System.err.println("value=" + value + ";beanList=" + beanList + ";anchor=" + entry.getKey());
            }
        }
    }

    private Map<String, List<String>> extracted(Set list) {

        Map<String, List<String>> anchorMaps = new HashMap<>();
        for (Object o : list) {
            List<String> cata = new ArrayList<>();
            UpperLayerQuery query = new UpperLayerQuery().setTraceId(IdUtil.fastSimpleUUID()).setGraphVersion("20230731_001").setTargetIds(Lists.newArrayList(o.toString())).setEdgeLabels(Lists.newArrayList(new UpperLayerQuery.EdgeLabel().setSource("UNIT").setTarget("ANCHOR_POINT"), new UpperLayerQuery.EdgeLabel().setSource("COURSE").setTarget("ANCHOR_POINT"), new UpperLayerQuery.EdgeLabel().setSource("L2COURSE").setTarget("ANCHOR_POINT"), new UpperLayerQuery.EdgeLabel().setSource("PERIOD").setTarget("ANCHOR_POINT")));

            GraphData graphData = graphService.findUpperLayer(query);
            for (GraphData.GraphVertex vertex : graphData.getVertices()) {
                if (!"ANCHOR_POINT".equals(vertex.getLabel())) {
                    cata.add(vertex.getId());
                }
            }
            List<String> newCata = new ArrayList<>(parseCollection(cata));
            System.out.println("anchor=" + o + ";original=" + cata + ";newCata= " + newCata);
            if (!anchorMaps.containsKey(o.toString())) {
                anchorMaps.put(o.toString(), new ArrayList<>());
            }
            anchorMaps.get(o.toString()).addAll(newCata);
        }
        return anchorMaps;
    }

    @Test
    public void findAnchorPointUpperLayer() {
        List<String> cata = new ArrayList<>();
        UpperLayerQuery query = new UpperLayerQuery().setTraceId(IdUtil.fastSimpleUUID()).setGraphVersion("20230731_001").setTargetIds(Lists.newArrayList("60659235-e0ab-4502-9472-b56f6b8ddf97"))
//                .setResultLimitId("44")
                .setEdgeLabels(Lists.newArrayList(new UpperLayerQuery.EdgeLabel().setSource("UNIT").setTarget("ANCHOR_POINT"), new UpperLayerQuery.EdgeLabel().setSource("COURSE").setTarget("ANCHOR_POINT"), new UpperLayerQuery.EdgeLabel().setSource("L2COURSE").setTarget("ANCHOR_POINT"), new UpperLayerQuery.EdgeLabel().setSource("PERIOD").setTarget("ANCHOR_POINT")));

        GraphData graphData = graphService.findUpperLayer(query);
        for (GraphData.GraphVertex vertex : graphData.getVertices()) {
            if (!"ANCHOR_POINT".equals(vertex.getLabel())) {
                cata.add(vertex.getId());
            }
        }
        CollectionUtil.sort(cata, (o1, o2) -> 0);

        System.out.println(cata);


    }

//    public static void main(String[] args) {
//        String s = FileUtil.readUtf8String("D:\\文档\\推荐服务\\2023-08\\小学3456\\小数人教版1-6上锚点和最末级目录对应关系-20230730.json");
//        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(s);
//        Set<String> allKeys = jsonObject.keySet();
//
//        String s2 = FileUtil.readUtf8String("D:\\文档\\推荐服务\\2023-08\\小学3456\\1.txt");
//        List list = JSONUtil.parseArray(s2).toBean(List.class);
//
//
//        List<String> strings = CollUtil.subtractToList(allKeys, list);
//        System.err.println(JSONUtil.toJsonStr(strings));
//        List<String> strings2 = CollUtil.subtractToList(list, allKeys);
//        System.err.println(JSONUtil.toJsonStr(strings2));
//
//    }

    @Test
    public void findBookAllAnchorPointUpperLayer() {

        String s = FileUtil.readUtf8String("D:\\文档\\推荐服务\\2023-08\\小学3456\\小数人教版1-6上锚点和最末级目录对应关系-20230730.json");
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(s);
        Set<String> allKeys = jsonObject.keySet();

        Set<String> anchorPoints = new HashSet<>();

        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion("20230731_001");
        graphQuery.setRootVertexLabel("BOOK");
        graphQuery.setRootVertexIdList(Arrays.asList("01_01020101-001", "01_02020101-003", "01_03020101-003", "01_04020101-003", "01_05020101-003", "01_06020101-003"));
        List<GraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("BOOK").setTarget("UNIT"));
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("UNIT").setTarget("ANCHOR_POINT"));
        graphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.queryPath(graphQuery);
        System.out.println(graphData);
        for (GraphData.GraphVertex vertex : graphData.getVertices()) {
            if ("ANCHOR_POINT".equals(vertex.getLabel())) {
                anchorPoints.add(vertex.getId());
            }
        }
        System.err.println();
//1648


        List<String> strings = CollUtil.subtractToList(allKeys, anchorPoints);
        System.err.println(strings);
        List<String> strings2 = CollUtil.subtractToList(anchorPoints, allKeys);
        System.err.println(strings2);


    }


    @Test
    public void querySubGraphWithCata() {

        String traceId = "aaaa";
        String graphVersion = "20240112_006";
        List<String> catas = Lists.newArrayList("01_06020101-003_06_002");

        GraphVertexesQuery query = new GraphVertexesQuery();
        query.setTraceId(traceId);
        query.setIds(catas);
        query.setGraphVersion(graphVersion);
        GraphData graphData = graphService.queryVertexByIds(query);

        List<GraphData.GraphVertex> vertices = graphData.getVertices();

        if (CollectionUtil.isNotEmpty(vertices)) {

            for (GraphData.GraphVertex vertex : vertices) {
                String label = vertex.getLabel();

                SubGraphQuery subGraphQuery = new SubGraphQuery();
                subGraphQuery.setTraceId(traceId);
                subGraphQuery.setGraphVersion(graphVersion);
                subGraphQuery.setRootVertexLabel(label);
                subGraphQuery.setRootVertexIdList(catas);
                List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
                edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(label).target("ANCHOR_POINT").build());
                subGraphQuery.setEdgeLabels(edgeLabels);
                if (log.isDebugEnabled()) {
                    log.debug("GraphQuery={}", subGraphQuery);
                }
                GraphData subGraph = graphService.querySubGraph(subGraphQuery);
                if (log.isDebugEnabled()) {
                    log.debug("GraphData={}", subGraph);
                }
                List<GraphData.GraphEdge> edges = subGraph.getEdges();
                if (CollUtil.isNotEmpty(edges)) {
                    List<String> anchors = edges.stream().map(GraphData.GraphEdge::getTarget).collect(Collectors.toList());
                    log.debug("anchors={}", anchors.size());
                }
                if (subGraph == null) {
                    return;
                }
            }

        }


    }

    /**
     * 查询所有复习点与章关系
     */
    @Test
    public void queryAllReviewPointToUnit() {
        ConcurrentHashMap<String, List<String>> reviewPointToUnitCache = new ConcurrentHashMap<>();

        String sourceLabel = "UNIT";
        String targetLabel = "REVIEW_POINT";
        String version = "20240401_002";
        String traceId = IdUtil.fastSimpleUUID();
        //查询所有复习点
        GraphData graphData = graphService.lookup(new GraphLabelQuery().setGraphVersion(version).setTraceId(traceId).setLabel(targetLabel));

        List<String> points = graphData.getVertices().stream().map(GraphData.GraphVertex::getId).collect(Collectors.toList());
        int total = graphData.getVertices().size();
        List<List<String>> split = CollUtil.split(points, 100);
        log.info(String.format("Loading %s 0/%s", targetLabel, total));
        int i = 0;
        for (List<String> list : split) {
            i = i + list.size();
            log.info(String.format("Loading %s %s/%s", sourceLabel + "_" + targetLabel, i, total));

            GraphQuery graphQuery = new GraphQuery();
            graphQuery.setTraceId(traceId);
            graphQuery.setGraphVersion(version);
            graphQuery.setRootVertexLabel(targetLabel);
            graphQuery.setRootVertexIdList(list);
            GraphQuery.EdgeLabel edgeLabel = new GraphQuery.EdgeLabel().setSource(sourceLabel).setTarget(targetLabel);
            graphQuery.setEdgeLabels(Collections.singletonList(edgeLabel));
            GraphData edge = graphService.queryPathReverse(graphQuery);
            if (CollUtil.isNotEmpty(edge.getEdges())) {
                Map<String, List<String>> groupMapping = edge.getEdges().stream().collect(Collectors.groupingBy(GraphData.GraphEdge::getTarget, Collectors.mapping(GraphData.GraphEdge::getSource, Collectors.toList())));
                reviewPointToUnitCache.putAll(groupMapping);
            }
        }
        log.info("缓存到复习点章关系={}", reviewPointToUnitCache.keys());
        reviewPointToUnitCache.forEach((k, v) -> System.err.println(k + " = " + v));
    }

    @Autowired
    GraphProperties graphProperties;

    @Test
    public void cache() {

        List<GraphData.GraphVertex> all = new ArrayList<>();
        String string = FileUtil.readUtf8String("锚点ids.txt");
        JSONArray jsonArray = JSON.parseArray(string);
        List<String> ids = new ArrayList<>();
        for (Object o : jsonArray) {
            ids.add(o.toString());
        }

        List<List<String>> split = CollUtil.split(ids, 200);
        for (List<String> list : split) {
            GraphVertexesQuery query = new GraphVertexesQuery();
            query.setTraceId(UUID.randomUUID().toString());
            query.setGraphVersion("20240424_001");
            query.setIds(list);
            query.setNodeProps(graphProperties.getNodeProps());
            List<GraphData.GraphVertex> vertices = graphService.queryVertexByIdsWithPropsCache(query);
            all.addAll(vertices);
            log.info("size()={}", all.size());
        }

        System.err.println(all.size());

    }


    @Test
    public void cache2() {

        List<File> files = FileUtil.loopFiles("D:\\11931-1\\nebulaData_20240507_003\\records");
        List<String> ids = new ArrayList<>();
        for (File file : files) {
            if (file.getName().startsWith("TAG_")) {
                List<String> list = FileUtil.readUtf8Lines(file);
                for (String s : list) {
                    String id = JSON.parseObject(s).getString("id");
                    ids.add(id);
                }

            }

        }

        List<GraphData.GraphVertex> all = new ArrayList<>();
//        String string = FileUtil.readUtf8String("锚点ids.txt");
//        JSONArray jsonArray = JSON.parseArray(string);
//        List<String> ids = new ArrayList<>();
//        for (Object o : jsonArray) {
//            ids.add(o.toString());
//        }

        List<List<String>> split = CollUtil.split(ids, 200);
        for (List<String> list : split) {
            GraphVertexesQuery query = new GraphVertexesQuery();
            query.setTraceId(UUID.randomUUID().toString());
            query.setGraphVersion("20240424_001");
            query.setIds(list);
            query.setNodeProps(graphProperties.getNodeProps());
            List<GraphData.GraphVertex> vertices = graphService.queryVertexByIdsWithPropsCache(query);
            all.addAll(vertices);
            log.info("size()={}", all.size());
        }

        System.err.println(all.size());

        ThreadUtil.safeSleep(Integer.MAX_VALUE);

    }
}
