package com.iflytek.skylab.core.dataapi.utils;

import com.iflytek.skylab.core.dataapi.data.GraphQuery;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.util.GraphUtils;
import org.junit.Test;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @program: skylab-platform
 * @description: todo
 * @author: xuYao2
 * @create: 2022-03-15 11:05
 **/
@ContextConfiguration(loader = AnnotationConfigContextLoader.class,
        classes = {GraphUtils.class})
public class GraphUtilsTest {

    @Test
    public void isLineTest() {
        // 测试节点是否都在一条线上
        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setGraphVersion("v1");
        graphQuery.setRootVertexLabel("press");
        graphQuery.setRootVertexIdList(Arrays.asList("63", "64"));
        List<GraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("press").setTarget("book"));
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("book").setTarget("unit"));
        edgeLabels.add(new GraphQuery.EdgeLabel().setSource("book").setTarget("topic"));
        graphQuery.setEdgeLabels(edgeLabels);
        System.out.println(GraphUtils.isLine(graphQuery));

    }

    @Test
    public void getDPHLongestPathTest() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setGraphVersion("v1");
        subGraphQuery.setRootVertexLabel("check_point");
        subGraphQuery.setRootVertexIdList(Arrays.asList("c2d388ca-9ea5-4ba0-a675-ac538d00e919", "c2d388ca-9ea5-4ba0-a675-ac538d00e920"));
        List<SubGraphQuery.EdgeLabel> subgraphEdgeLabels = new ArrayList<>();
        subgraphEdgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("anchor_point").target("topic").build());
        subgraphEdgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("check_point").target("anchor_point").build());
//		edgeLabelss.add(SubGraphQuery.EdgeLabel.builder().source("topic").target("check_point").build());
        subgraphEdgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("check_point").target("topic").build());
        subGraphQuery.setEdgeLabels(subgraphEdgeLabels);
        System.out.println(GraphUtils.getDPHLongestPath(subgraphEdgeLabels));
    }

    @Test
    public void graphLabelOrderTest() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("ANCHOR_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("CHECK_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("BOOK").target("UNIT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("TOPIC").build());

        subGraphQuery.setEdgeLabels(edgeLabels);
        List<SubGraphQuery.EdgeLabel> edgeLabels1 = GraphUtils.sortGraphEdge(subGraphQuery.getEdgeLabels());
        System.out.println(edgeLabels1);
    }

}
