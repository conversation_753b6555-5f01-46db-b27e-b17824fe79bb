package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.ResourceTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.data.FeedbackLog;
import com.iflytek.skylab.core.dataapi.data.StudyLogData;
import com.iflytek.skylab.core.dataapi.data.StudyMacrographLogQuery;
import com.iflytek.skylab.core.dataapi.service.StudyMacrographLogService;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/5 14:44
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DataHubTestBoot.class)
public class StudyMacrographLogServiceImplTest {

    @Resource
    private StudyMacrographLogService studyMacrographLogService;

    @Test
    public void saveFeedbackLogAndGet() {
        String fastSimpleUUID = IdUtil.fastSimpleUUID();

        FeedbackLog feedbackLog = new FeedbackLog();
        feedbackLog.setRefTraceId(fastSimpleUUID);
        feedbackLog.setScore(1D);
        feedbackLog.setStandardScore(5D);
        feedbackLog.setTimeCost(999);
        feedbackLog.setScoreRatio(0.7);
        feedbackLog.setFeedbackExt(new JSONObject());
        feedbackLog.setFeedbackTime(Instant.now());

        feedbackLog.setUserId("xinagzhang182");
        feedbackLog.setPhaseCode("phase-code");
        feedbackLog.setGradeCode("grade-code");
        feedbackLog.setSubjectCode("subject-code");
        feedbackLog.setStudyCode(StudyCodeEnum.MACROGRAPH_QUALITY_TEST_PAPER);
        feedbackLog.setBizCode(BizCodeEnum.ZSY_XXJ);
        feedbackLog.setNodeId("node-id");
        feedbackLog.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        feedbackLog.setResNodeId("res-node-id");
        feedbackLog.setResNodeType(ResourceTypeEnum.TOPIC);

        feedbackLog.setClassId(null);
        feedbackLog.setBookCode("null98798");

        FeedbackLog feedbackLog2 = new FeedbackLog();

        BeanUtil.copyProperties(feedbackLog, feedbackLog2);

        feedbackLog2.setStudyCode(StudyCodeEnum.MACROGRAPH_ERROR_BOOK);


        FeedbackLog feedbackLog3 = new FeedbackLog();

        BeanUtil.copyProperties(feedbackLog, feedbackLog3);

        feedbackLog3.setStudyCode(StudyCodeEnum.MACROGRAPH_SYNC_TEST);

        List<FeedbackLog> feedbackLogs = studyMacrographLogService.saveFeedbackLogListAndGet(fastSimpleUUID, Lists.newArrayList(feedbackLog, feedbackLog2, feedbackLog3));
        System.err.println(feedbackLogs);
    }

    @Test
    public void queryFeedbackLogs() {
        StudyMacrographLogQuery studyMacrographLogQuery = new StudyMacrographLogQuery();

        studyMacrographLogQuery.setUserId("55095d83-3116-49ff-9492-382332e66b31")
                .setBizCodeList(Arrays.asList(BizCodeEnum.ZSY_XXJ))
                .setSubjectCode("02")
                .setPhaseCode("04");
        studyMacrographLogQuery.setNodeIdList(Arrays.asList("959fd5b3-174a-42a7-b790-fcbe45c695db"));

        studyMacrographLogQuery.setChange(true);

        List<StudyLogData> studyLogData = studyMacrographLogService.queryFeedbackLogs("************", studyMacrographLogQuery, 100);
        System.err.println(studyLogData);


//        studyMacrographLogQuery.setStudyCodes(Arrays.asList(StudyCodeEnum.MACROGRAPH_ERROR_BOOK, StudyCodeEnum.SYNC_LEARN));
//        List<StudyLogData> studyLogData2 = studyMacrographLogService.queryFeedbackLogs("123", studyMacrographLogQuery, 10);
//        System.err.println(studyLogData2);
    }
}
