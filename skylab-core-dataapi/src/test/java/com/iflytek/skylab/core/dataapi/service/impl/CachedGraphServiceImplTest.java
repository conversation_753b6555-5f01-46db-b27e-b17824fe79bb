package com.iflytek.skylab.core.dataapi.service.impl;

import com.iflytek.skylab.core.dataapi.configuration.GraphAutoConfiguration;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.service.cache.GraphCacheImpl;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@RunWith(SpringRunner.class)
@ContextConfiguration(loader = AnnotationConfigContextLoader.class,
        classes = {GraphAutoConfiguration.class, GraphServiceImpl.class})
public class CachedGraphServiceImplTest {

    @Autowired
    private GraphService graphService;
    @Autowired
    private GraphCacheImpl graphCache;

    @Before
    public void setup() throws Exception {
        if (!graphCache.isInitialized()) {
            graphCache.startInitializeThread();
        }
        while(!graphCache.isInitialized()) {
            Thread.sleep(1000);
        }
    }

    /**
     * 根据 考点 查询子图 考点 -> 锚点 -> 题
     *                    |             ^
     *                    |             |
     *                    +-------------+
     */
    @Test
    public void testQuerySubGraph() throws Exception {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("CHECK_POINT");
        subGraphQuery.setRootVertexIdList(Arrays.asList("00434b08-eb94-46d3-9768-dd02690a23b0"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("ANCHOR_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("TOPIC").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 根据 考点 查询子图 书 -> 章 -> 考点 -> 锚点 -> 题
     *                                |             ^
     *                                |             |
     *                                +-------------+
     */
    @Test
    public void testQuerySubGraph2() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("CHECK_POINT");
        subGraphQuery.setRootVertexIdList(Arrays.asList("00434b08-eb94-46d3-9768-dd02690a23b0"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("CHECK_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("BOOK").target("UNIT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("TOPIC").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("ANCHOR_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 查询 节 下面的 锚点
     */
    @Test
    public void testQuerySubGraph3() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("COURSE");
        subGraphQuery.setRootVertexIdList(Arrays.asList("01_07020101-001_02_001"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 查询 锚点 下面的 题
     */
    @Test
    public void testQuerySubGraph4() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("ANCHOR_POINT");
        subGraphQuery.setRootVertexIdList(Arrays.asList("25264d4d-1659-4c7e-8018-fa07911046e3"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 根据锚点反查 节 -> 锚点 子图
     */
    @Test
    public void testQuerySubGraph5() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("ANCHOR_POINT");
        subGraphQuery.setRootVertexIdList(Arrays.asList("1873903a-4090-4371-8a1e-a0a8d7c97c8d"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }

    /**
     * 查询 章 下面的 题包
     */
    @Test
    public void testQuerySubGraph6() {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("UNIT");
        subGraphQuery.setRootVertexIdList(Arrays.asList("72_09020254-002_004"));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("TOPIC").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        System.out.println(graphData);
        Assert.assertTrue(graphData != null);
        Assert.assertTrue(!graphData.getEdges().isEmpty());
        Assert.assertTrue(!graphData.getVertices().isEmpty());
    }
}
