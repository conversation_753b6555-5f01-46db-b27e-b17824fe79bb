package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.CatalogTypeEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.data.MasterData;
import com.iflytek.skylab.core.dataapi.data.MasterItem;
import com.iflytek.skylab.core.dataapi.data.MasterQuery;
import com.iflytek.skylab.core.dataapi.mongo.dao.UserMasteryRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.dataapi.service.MasterService;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
@EnableDataHub
@Slf4j
class MasterServiceImplTest {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MasterService masterService;
    @Autowired
    private UserMasteryRecordRepository userMasteryRecordRepository;

    /**
     * 用户画像单元测试模拟数据
     */
    // @Before
    public void prepareData() {
        UserMasteryRecord record = new UserMasteryRecord();
        record.setId("6229cf48cf32ba3fa10ce0aa");
        record.setUserId("dingyufan");
        record.setCatalogId("debugCatalogId");
        record.setCatalogType(CatalogTypeEnum.UNIT);
        record.setNodeId("debugNodeId");
        record.setNodeType(NodeTypeEnum.ANCHOR_POINT);

        UserMasteryRecord record2 = new UserMasteryRecord();
        record2.setId("6229cf48cf32ba3fa10ce0ab");
        record2.setUserId("dingyufan");
        record2.setCatalogId("debugCatalogId2");
        record2.setCatalogType(CatalogTypeEnum.UNIT);
        record2.setNodeId("debugNodeId2");
        record2.setNodeType(NodeTypeEnum.ANCHOR_POINT);

        mongoTemplate.insert(Lists.newArrayList(record, record2), "user_mastery_record");
    }


    /**
     * 测试 用户画像 查询
     */
    @Test
    public void queryMasterData() {
        // 用户锚点数据查询
        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId("0038f51b-4812-45cc-b6a9-575c08fe8d68");
        boolean exists = masterService.exists(IdUtil.fastUUID(), masterQuery);
        System.out.println(exists);
        //masterQuery.setNodeType(NodeTypeEnum.CHECK_POINT);
        //// 用户锚点数据查询
        //List<UserMasteryRecord> search = masterService.queryMasterDataBatch(IdUtil.fastUUID(),masterQuery);
        //
        //UserMasteryRecord lastUserMasteryRecord = getLastUserMasteryRecord(search);
        //System.out.println(lastUserMasteryRecord.toString());
    }

    public UserMasteryRecord userMasteryRecordHandle(UserMasteryRecord userMasteryRecord) {
        String catalogId = userMasteryRecord.getCatalogId();
        String[] s = catalogId.split("_");
        String bookCode = s[0] + "_" + s[1];
        userMasteryRecord.setBookCode(bookCode);
        userMasteryRecord.setStudyCode(StudyCodeEnum.SYNC_OS);
        return userMasteryRecord;
    }

    public UserMasteryRecord getLastUserMasteryRecord(List<UserMasteryRecord> userMasteryRecord) {
        //otherCollect.stream().sorted(Comparator.comparing(UserMasteryRecord::getLastAnswerTime).reversed()).collect(Collectors.toList());
        List<UserMasteryRecord> sortedList = userMasteryRecord.stream().sorted(Comparator.comparing(record -> {
            if (record.getLastAnswerTime() != null) {
                return record.getLastAnswerTime();
            } else if (record.getUpdateTime() != null) {
                return record.getUpdateTime();
            } else {
                return record.getCreateTime();
            }
        })).collect(Collectors.toList());
        // 数据治理
        UserMasteryRecord resUserMasteryRecord = sortedList.get(sortedList.size() - 1);
        UserMasteryRecord newUserMasteryRecord = userMasteryRecordHandle(resUserMasteryRecord);

        String catalogId = newUserMasteryRecord.getCatalogId();
        String[] s = catalogId.split("_");
        String bookCode = s[0] + "_" + s[1];
        newUserMasteryRecord.setBookCode(bookCode);
        newUserMasteryRecord.setStudyCode(StudyCodeEnum.SYNC_OS);
        return newUserMasteryRecord;
    }


    /**
     * 测试 用户画像 更新
     */
    @Test
    public void updateMasterData() {
        MasterItem item1 = new MasterItem();
        item1.setBizCode(BizCodeEnum.ZSY_XXJ);
        item1.setStudyCode(StudyCodeEnum.SYNC_LEARN);
        item1.setCatalogId("asdfasdfasdf");
        item1.setCatalogType(CatalogTypeEnum.UNIT);
        item1.setNodeId("aaa");
        item1.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        // item1.setFusion(1.0D);
        // item1.setReal(2.0D);
        // item1.setPredict(3.0D);
        // item1.setShouldFlag(false);
        // item1.setAlgoFusion(1.1D);
        // item1.setAlgoReal(2.2D);
        // item1.setAlgoPredict(3.3D);
        // item1.setMasteryScore(8.0D);
        item1.setMasteryType("ddd-fff");
        item1.setUpdateTime(Instant.now());


        MasterData masterData = new MasterData();
        masterData.setUserId("dingyufan");
        masterData.setItems(Lists.newArrayList(item1));

        List<String> savedIds = masterService.updateMasterData("debugTraceId", masterData);
        savedIds.forEach(System.out::println);
    }


    /**
     * 测试 用户画像 删除
     */
    @Test
    public void deleteMasterDataByUserId() {
        long before = userMasteryRecordRepository.count();
        System.out.println("before = " + before);
        long deleteMasterDataByUserId = masterService.deleteMasterDataByUserId("deleteMasterDataByUserId", "a21ffc27-01f6-4ffb-9953-567e2bea1e05");
        System.out.println("deleteMasterDataByUserId = " + deleteMasterDataByUserId);
        long after = userMasteryRecordRepository.count();
        System.out.println("after = " + after);
        long delete = before - after;
        System.out.println("delete = " + delete);
        Assert.assertEquals(delete, deleteMasterDataByUserId);
    }


    @Test
    public void toMasterItem() {
        UserMasteryRecord record = new UserMasteryRecord();
        record.setId(new ObjectId().toHexString());
        record.setUserId("");
        record.setBizCode(BizCodeEnum.ZSY_XKT);
        record.setStudyCode(StudyCodeEnum.EXT3RD);
        record.setGraphVersion("");
        record.setSubjectCode("");
        record.setPhaseCode("");
        record.setBookCode("");
        record.setCatalogId("");
        record.setCatalogType(CatalogTypeEnum.UNIT);
        record.setNodeId("");
        record.setNodeType(NodeTypeEnum.ANCHOR_POINT);
        record.setShouldFlag(false);
        record.setFusion(0.0D);
        record.setReal(0.0D);
        record.setPredict(0.0D);
        record.setAlgoFusion(0.0D);
        record.setAlgoReal(0.0D);
        record.setAlgoPredict(0.0D);
        record.setCreateTime(Instant.now());
        record.setUpdateTime(Instant.now());
        record.setMasteryScore(0.0D);
        record.setMasteryType("xx");

        System.out.println(record.toMasterItem());
    }

    @Test
    void testUpdateMasterDataTryUpsert() {
        // userId - studyCode - nodeId - catalogId
        MasterItem item = new MasterItem();
        item.setStudyCode(StudyCodeEnum.AI_DIAG);
        item.setNodeId("node-id");
        item.setCatalogId("catalog-id");
        item.setAlgoFusion(8.1);
        item.setAlgoReal(8.2);
        item.setAlgoPredict(8.3);

        String id = masterService.updateMasterData(IdUtil.fastUUID(), "dingyufan", item);
        System.out.println(id);
    }

    @Test
    void testUpdateMasterDataTryUpdate() {
        MasterItem item = new MasterItem();
        item.setStudyCode(StudyCodeEnum.AI_DIAG);
        item.setNodeId("node-id");
        item.setCatalogId("catalog-id");
        item.setAlgoFusion(123.456);
        item.setGraphVersion("dsafsadfsadf");

        String id = masterService.updateMasterData(IdUtil.fastUUID(), "dingyufan", item);
        System.out.println(id);
    }

    @Test
    void testqueryMasterDataBatch() {
        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId("xiangzhang182_test");
        List<UserMasteryRecord> userMasteryRecords = masterService.queryMasterDataBatch(IdUtil.fastUUID(), masterQuery);
        System.err.println(userMasteryRecords.size());
        System.err.println(JSONUtil.toJsonStr(userMasteryRecords.get(0)));

    }

    static ExecutorService executorService = Executors.newFixedThreadPool(1);

    @Test
    void testUpsert2() {
        MasterQuery masterQuery = new MasterQuery();
        masterQuery.setUserId("3d20f514-b1c9-46a3-ad5b-f177563445e0");
        masterQuery.setNodeIdList(Arrays.asList("ff7f6c92-b320-4bf2-842a-87b47c1eed26"));
        List<UserMasteryRecord> userMasteryRecords = masterService.queryMasterDataBatch(IdUtil.fastUUID(), masterQuery);
        MasterItem item1 = userMasteryRecords.get(0).toMasterItem();
        item1.setId(null);
        item1.setMasteryScore(88888D);

        MasterData masterData = new MasterData().setUserId("3d20f514-b1c9-46a3-ad5b-f177563445e0").setItems(Arrays.asList(item1));

        masterService.updateMasterData(IdUtil.fastSimpleUUID(), masterData);

    }

    @Test
    void testUpsert() {
        for (int j = 0; j < 10000; j++) {

            int finalJ = j;
            executorService.submit(() -> {
                MasterQuery masterQuery = new MasterQuery();
                masterQuery.setUserId("xiangzhang182_test");
                List<UserMasteryRecord> userMasteryRecords = masterService.queryMasterDataBatch(IdUtil.fastUUID(), masterQuery);

                long start = System.currentTimeMillis();
                List<MasterItem> items = new ArrayList<>();
                for (int i = 0; i < userMasteryRecords.size(); i++) {
                    UserMasteryRecord record = userMasteryRecords.get(i);
                    MasterItem masterItem = record.toMasterItem();
                    masterItem.setCatalogId("01_08020101-002" + "ABC" + i + finalJ);
                    masterItem.setMasteryScore(Math.random());
                    masterItem.setFusion(Math.random());
                    masterItem.setPredict(Math.random());
                    masterItem.setId(null);
                    items.add(masterItem);
                }
                MasterData masterData = new MasterData();
                masterData.setUserId("xiangzhang182_test");
                masterData.setItems(items);
                masterService.upsertMasterDataBatch(IdUtil.fastSimpleUUID(), masterData);
                System.err.println("更新耗时：" + (System.currentTimeMillis() - start));
            });

        }


        ThreadUtil.safeSleep(100000);
    }
}
