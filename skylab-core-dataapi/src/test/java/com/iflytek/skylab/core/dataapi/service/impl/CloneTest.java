package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.data.FeedbackLog;
import com.iflytek.skyline.brave.data.TraceRecord;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/4/15 10:50
 */
@Slf4j
public class CloneTest {


    public static void main(String[] args) {
        List<FeedbackLog> feedbackLogList = new ArrayList<>();

        Map<String, List<String>> nodeTopicMap = Maps.newHashMap();

        nodeTopicMap.put("afaa1100-947b-4379-b7b8-9535989e4330", Lists.newArrayList("2879dba8-efb9-476d-b153-da33d7e853df", "716a5363-5824-4a9d-9673-408c067e86bf", "d9989943-dbd3-427f-8a58-2278d249b918", "aff1607a-3436-4411-995e-24eeeebce37b", "0da8e005-6a29-4e34-b68d-b7c3a259e2f9"));
        nodeTopicMap.put("71ad24bc-2f43-4539-8bf7-645a4f798602", Lists.newArrayList("63679fb7-7586-4320-a672-65c167f11555", "15d67c04-3369-444a-a56c-b5ce4dbf00a6", "4e972bd6-cff1-4d89-961a-8e884acc3a92"));
        nodeTopicMap.put("61c83b37-9f42-49e5-b64f-31736c930f5c", Lists.newArrayList("65ccaae0-6670-4f9d-a639-95b266e3eb6a", "ca401ac1-0bfa-4472-b2a6-91070442f42f", "73af0ec0-548e-49d3-afc0-c2f10e8cb3a1"));
        nodeTopicMap.put("e0d632b4-2eea-4d5e-b1aa-cf43a05e907a", Lists.newArrayList("b6be8b36-2415-4cb8-a804-be078ab75803", "6e1e3e86-4071-4e11-a99c-a83d88866523", "127345bd-bd3b-4141-959d-1a8245734a70", "8ea3a26c-e267-4e5d-ae24-a99d25ec52b0", "e7665391-9539-4570-8e6a-805d3649d4d1"));

        List<TraceRecord> traceRecordList = new ArrayList<>();

        nodeTopicMap.forEach((nodeId, topicIds) -> {
            List<FeedbackLog> feedbackLogListIn = new ArrayList<>();
            topicIds.forEach(topicId -> {
                FeedbackLog feedbackLog = StudyLogServiceImplTest.createFeedbackLog(StudyCodeEnum.FINAL_EXAM, nodeId, topicId);
                feedbackLogListIn.add(feedbackLog);
            });
            TraceRecord traceRecord = new TraceRecord();
            traceRecord.setAction("action");
            traceRecord.setData(feedbackLogListIn);
            traceRecordList.add(traceRecord);
        });


        MyData myData = new MyData();
        myData.setAge(20).setName("lyhu").setSex("m");
        TraceRecord traceRecord = new TraceRecord();
        traceRecord.setAction("action");
        traceRecord.setData(myData);

        TraceRecord clone1 = ObjectUtil.cloneByStream(traceRecord);
        log.info("{}", clone1);

        TraceRecord clone = ObjectUtil.cloneByStream(traceRecordList.get(0));
        log.info("{}", clone);


        // clone = ObjectUtil.cloneByStream(traceRecordList.get(1));


        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        ObjectUtil.cloneByStream(traceRecordList.get(0));
        log.info("----------------------------");
        log.info("cloneByStream {};\t {}ms", stopWatch, stopWatch.getTime());
        log.info("----------------------------");


        stopWatch.reset();
        stopWatch.start();

        int loop = 250;
        for (int i = 0; i < loop; i++) {
            traceRecordList.forEach(x -> ObjectUtil.cloneByStream(x));
        }
        stopWatch.stop();
        log.info("cloneByStream {};\t{}\t {}ms", stopWatch, loop * traceRecordList.size(), stopWatch.getTime() / (loop * traceRecordList.size() * 1.0));
        log.info("----------------------------");

        stopWatch.reset();
        stopWatch.start();

        for (int i = 0; i < loop; i++) {
            traceRecordList.forEach(x -> ObjectUtil.cloneByStream(x));
        }
        stopWatch.stop();
        log.info("cloneByStream {};\t{}\t {}ms", stopWatch, loop * traceRecordList.size(), stopWatch.getTime() / (loop * traceRecordList.size() * 1.0));

        log.info("----------------------------");
        JSON.toJSONString(traceRecordList.get(0));

        stopWatch.reset();
        stopWatch.start();

        for (int i = 0; i < loop; i++) {
            traceRecordList.forEach(x -> x.toString());
        }
        stopWatch.stop();
        log.info("toJson {};\t{}\t {}ms", stopWatch, loop * traceRecordList.size(), stopWatch.getTime() / (loop * traceRecordList.size() * 1.0));
    }


    @Getter
@Setter
    @Accessors(chain = true)
    static class MyData implements Serializable {
        String name;
        String sex;
        long age;
        Address address = new Address();
    }

    @Getter
@Setter
    @Accessors(chain = true)
    static class Address implements Serializable {
        String add = UUID.randomUUID().toString();
    }
}
