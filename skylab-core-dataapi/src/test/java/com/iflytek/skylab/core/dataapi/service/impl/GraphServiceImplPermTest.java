package com.iflytek.skylab.core.dataapi.service.impl;

import com.iflytek.skylab.core.dataapi.configuration.GraphAutoConfiguration;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.service.cache.GraphCacheImpl;

import org.apache.commons.lang3.time.StopWatch;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@RunWith(SpringRunner.class)
@ContextConfiguration(loader = AnnotationConfigContextLoader.class,
        classes = {GraphAutoConfiguration.class})
public class GraphServiceImplPermTest {

    @Autowired
    private GraphService graphService;
    @Autowired
    private GraphCacheImpl graphCache;

    @Test
    public void testQuerySubGraph() throws Exception {

        GraphLabelQuery graphLabelQuery = new GraphLabelQuery()
            .setTraceId(UUID.randomUUID().toString())
            .setGraphVersion("v2")
            .setLabel("CHECK_POINT");
        GraphData graphData = graphService.lookup(graphLabelQuery);
        
        StopWatch total = StopWatch.createStarted();
        graphData.getVertices().forEach(vertex -> {
            StopWatch sw = StopWatch.createStarted();
            testQuerySubGraph(vertex.getId());
            sw.stop();
            log.debug("Query subgraph with CHECK_POINT={}, {}ms cost.", vertex.getId(), sw.getTime(TimeUnit.MILLISECONDS));
        });
        total.stop();
        log.info("Query subgraph without cache, total {}ms cost.", total.getTime(TimeUnit.MILLISECONDS));

        graphCache.startInitializeThread();
        while(!graphCache.isInitialized()) {
            Thread.sleep(1000);
        }

        total.reset();
        total.start();
        graphData.getVertices().forEach(vertex -> {
            StopWatch sw = StopWatch.createStarted();
            testQuerySubGraph(vertex.getId());
            sw.stop();
            log.debug("Query subgraph with CHECK_POINT={}, {}ms cost.", vertex.getId(), sw.getTime(TimeUnit.MILLISECONDS));
        });
        total.stop();
        log.info("Query subgraph with cache, total {}ms cost.", total.getTime(TimeUnit.MILLISECONDS));
        
    }

    private void testQuerySubGraph(String checkPointId) {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion("v2");
        subGraphQuery.setRootVertexLabel("CHECK_POINT");
        subGraphQuery.setRootVertexIdList(Arrays.asList(checkPointId));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("ANCHOR_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("TOPIC").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        Assert.assertTrue(graphData != null);
        // Assert.assertTrue(!graphData.getEdges().isEmpty());
        // Assert.assertTrue(!graphData.getVertices().isEmpty());
    }
}
