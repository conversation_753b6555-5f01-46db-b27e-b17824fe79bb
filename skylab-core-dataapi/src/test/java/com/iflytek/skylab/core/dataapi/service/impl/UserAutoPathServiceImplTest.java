package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.util.IdUtil;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.data.TopicInfo;
import com.iflytek.skylab.core.dataapi.data.UserAutoPathData;
import com.iflytek.skylab.core.dataapi.data.UserAutoPathQuery;
import com.iflytek.skylab.core.dataapi.test.DataHubTestBoot;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/16 16:29
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class UserAutoPathServiceImplTest {

    @Test
    public void queryUserAutoPath() {
        UserAutoPathQuery userAutoPathQuery = new UserAutoPathQuery();
        userAutoPathQuery.setUserId("b6bad0c2-cd3f-4a02-9f77-4bbbb338a37d");
        userAutoPathQuery.setSubjectCode("02");
        userAutoPathQuery.setPhaseCode("04");
        UserAutoPathData data = DataHub.getUserAutoPathService().queryUserAutoPath("xiangzhang182_" + IdUtil.fastUUID(), userAutoPathQuery);
        log.info("data:{}", data.toString());
    }

    @Test
    public void queryTopicTh() {
        List<String> topics = Arrays.asList("0137e2dc-15cf-4f93-9202-5d1a678c35c4","0605b133-f181-47dd-afd3-032f5ea67301","0c147fec-7288-4fe6-82fa-c8b31e5d87ec");
        Map<String, TopicInfo> data = DataHub.getUserAutoPathService().queryTopicInfo("xiangzhang182_" + IdUtil.fastUUID(), topics);
        log.info("data:{}", data.toString());
    }
}
