package com.iflytek.skylab.core.dataapi.test;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.service.cache.LocalFileGraphCache;
import org.assertj.core.util.Lists;
import org.junit.Test;

import java.util.*;


public class LocalZipFileTest {

    @Test
    public void test() throws Exception {
        GraphProperties properties = new GraphProperties();
        properties.setLocalCacheEnabled(true);
        properties.setPath("D://nebulaDataLite_20241230_001.zip");
        Map<String,List<String>> nodeProps = new HashMap<>();
        nodeProps.put("CHECK_POINT",Lists.newArrayList("checkPointName","checkPointType","difficulty","evaluatePoint","phaseCode","subjectCode","versionSupport"));
        properties.setNodeProps(nodeProps);
        LocalFileGraphCache graphCache = new LocalFileGraphCache(properties);


        List<String> from = Lists.newArrayList("01_08020101-002_04");
        Set<String> tagTypeOrEdgeType = new HashSet<>();
        tagTypeOrEdgeType.add("UNIT");
        tagTypeOrEdgeType.add("CHECK_POINT");
        tagTypeOrEdgeType.add("TOPIC");
        tagTypeOrEdgeType.add("UNIT_CHECK_POINT");
        tagTypeOrEdgeType.add("CHECK_POINT_TOPIC");

        List<GraphData.GraphVertex> vertices = new ArrayList<>();
        List<GraphData.GraphEdge> edges = new ArrayList<>();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        graphCache.bfs(from,tagTypeOrEdgeType,vertices, edges, Integer.MAX_VALUE);
        stopWatch.stop();
        System.out.println("耗时 "+stopWatch.getLastTaskTimeMillis());

//        System.gc();

//        for(GraphData.GraphVertex vertex:vertices){
//            System.out.println("tag " +vertex.getId());
//        }
//
//        for(GraphData.GraphEdge edge:edges){
//            System.out.println("edge " +edge.getSource()+"->"+edge.getTarget());
//        }
    }

    @Test
    public void testQueryVertex()throws Exception{
        GraphProperties properties = new GraphProperties();
        properties.setLocalCacheEnabled(true);
        properties.setPath("D://nebulaDataLite_20241230_001.zip");
        Map<String,List<String>> nodeProps = new HashMap<>();
        nodeProps.put("CHECK_POINT",Lists.newArrayList("checkPointName","checkPointType","difficulty","evaluatePoint","phaseCode","subjectCode","versionSupport"));
        properties.setNodeProps(nodeProps);
        LocalFileGraphCache graphCache = new LocalFileGraphCache(properties);

        System.out.println(graphCache.queryVertexes(Lists.newArrayList("19_08020207-002_02")));
    }


    @Test
    public void testQuerySubGraph()throws Exception{
        GraphProperties properties = new GraphProperties();
        properties.setLocalCacheEnabled(true);
        properties.setPath("D://nebulaDataLite_20241230_001.zip");
        Map<String,List<String>> nodeProps = new HashMap<>();
        nodeProps.put("CHECK_POINT",Lists.newArrayList("checkPointName","checkPointType","difficulty","evaluatePoint","phaseCode","subjectCode","versionSupport"));
        properties.setNodeProps(nodeProps);
        LocalFileGraphCache graphCache = new LocalFileGraphCache(properties);
        SubGraphQuery subGraphQuery = new SubGraphQuery();

        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("CHECK_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("TOPIC").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("CHECK_POINT").target("CHECK_POINT").build());


        subGraphQuery.setRootVertexIdList(Lists.newArrayList("19_08020207-002_02"));
        subGraphQuery.setRootVertexLabel("UNIT");
        subGraphQuery.setGraphVersion("20241230_001");
        subGraphQuery.setEdgeLabels(edgeLabels);


        Set<String> tagTypeOrEdgeType = new HashSet<>();

        if (null != subGraphQuery.getRootVertexLabel()){
            tagTypeOrEdgeType.add(subGraphQuery.getRootVertexLabel());
        }

        if (null != subGraphQuery.getEdgeLabels()){
            subGraphQuery.getEdgeLabels().stream()
                    .forEach(e ->
                    {
                        tagTypeOrEdgeType.add(e.getSource());
                        tagTypeOrEdgeType.add(e.getTarget());
                        tagTypeOrEdgeType.add(e.getSource()+"_"+e.getTarget());
                    });
        }

        List<GraphData.GraphVertex> vertices = new ArrayList<>();
        List<GraphData.GraphEdge> edges = new ArrayList<>();
        graphCache.bfs(subGraphQuery.getRootVertexIdList(),tagTypeOrEdgeType,vertices, edges, Integer.MAX_VALUE);

        GraphData result = new GraphData();
        result.setVertices(vertices);
        result.setEdges(edges);

        for (GraphData.GraphVertex vertex: vertices){
            System.out.println(JSON.toJSONString(vertex));
        }

        for (GraphData.GraphEdge edge: edges){
            System.out.println(JSON.toJSONString(edge));
        }
    }
}
