package com.iflytek.skylab.core.dataapi.data;


import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 特征查询返回的数据结构<br>
 *
 * <AUTHOR>
 * @date 2022/5/25 10:02
 */
@Getter
@Setter
public class UserAnswerTopic {

    @JSONField(name = "topic_id")
    private String topicId;
    @JSONField(name = "score_ratio")
    private Double scoreRatio;
    @JSONField(name = "feedback_time")
    private Date feedbackTime;

}
