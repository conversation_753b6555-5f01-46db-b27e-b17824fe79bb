package com.iflytek.skylab.core.dataapi.mongo.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Getter
@Setter
@Document(collection = "skyline_plan")
public class SkylinePlan {
    
    @Id
    private String id;

    /**
     * 名称
     */
    @Field(name = "name")
    private String name;

    /**
     * 状态
     */
    @Field(name = "status")
    private Integer status;
}
