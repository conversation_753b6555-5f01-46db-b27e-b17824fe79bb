package com.iflytek.skylab.core.dataapi.util;

import com.iflytek.skylab.core.dataapi.data.GraphQuery;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: basic
 * @description: Nebula 工具类
 * @author: xuYao2
 * @create: 2022-03-11 10:30
 **/
public class GraphUtils {

    private static HashMap<String, Integer> EDGE_LABEL_MAP = new HashMap<>();

    private static final String[] EDGES = {
            "PRESS_BOOK",
            "BOOK_UNIT",
            "UNIT_COURSE",
            "UNIT_OSCOURSE",
            "UNIT_PERIOD",

            "UNIT_CHECK_POINT", "UNIT_ANCHOR_POINT", "UNIT_TOPIC",
            "UNIT_REVIEW_POINT",

            "TREE_REVISE",

            "TREE_L2_MAP_TREE_NODE", "TREE_L3_MAP_TREE_NODE", "TREE_REVIEW_POINT", "REVISE_L2_MAP_TREE_NODE", "REVISE_REVIEW_POINT",
            "L2_MAP_TREE_NODE_L3_MAP_TREE_NODE", "L3_MAP_TREE_NODE_REVIEW_POINT", "L2_MAP_TREE_NODE_REVIEW_POINT",

            "REVIEW_POINT_TOPIC",
            "REVIEW_POINT_CHECK_POINT",

            "CHECK_POINT_CHECK_POINT", "CHECK_POINT_ANCHOR_POINT", "CHECK_POINT_CARD", "CHECK_POINT_VIDEO", "CHECK_POINT_TOPIC",

            "COURSE_PERIOD",

            "COURSE_ANCHOR_POINT", "COURSE_LEARN_PATH", "COURSE_L2COURSE", "COURSE_TOPIC",

            "PERIOD_CHECK_POINT", "PERIOD_ANCHOR_POINT", "PERIOD_LEARN_PATH",

            "OSCOURSE_ANCHOR_POINT", "OSCOURSE_OSPERIOD", "OSPERIOD_LEARN_PATH", "OSPERIOD_ANCHOR_POINT",

            "L2COURSE_CHECK_POINT", "L2COURSE_ANCHOR_POINT", "L2COURSE_LEARN_PATH",

            "L3COURSE_CHECK_POINT", "L3COURSE_ANCHOR_POINT", "L3COURSE_LEARN_PATH",

            "LEARN_PATH_ANCHOR_POINT",

            "ANCHOR_POINT_ANCHOR_POINT", "ANCHOR_POINT_CARD", "ANCHOR_POINT_VIDEO", "ANCHOR_POINT_TOPIC",
    };

    static {
        for (int i = 0; i < EDGES.length; i++) {
            EDGE_LABEL_MAP.put(EDGES[i], i + 1);
        }

        // EDGE_LABEL_MAP.put("PRESS_BOOK", 1);
        // EDGE_LABEL_MAP.put("BOOK_UNIT", 2);
        // EDGE_LABEL_MAP.put("UNIT_COURSE", 3);
        // EDGE_LABEL_MAP.put("UNIT_CHECK_POINT", 4);
        // EDGE_LABEL_MAP.put("CHECK_POINT_CHECK_POINT", 5);
        // EDGE_LABEL_MAP.put("CHECK_POINT_ANCHOR_POINT", 6);
        // EDGE_LABEL_MAP.put("CHECK_POINT_CARD", 7);
        // EDGE_LABEL_MAP.put("CHECK_POINT_VIDEO", 8);
        // EDGE_LABEL_MAP.put("CHECK_POINT_TOPIC", 9);
        // EDGE_LABEL_MAP.put("COURSE_ANCHOR_POINT", 10);
        // EDGE_LABEL_MAP.put("COURSE_LEARN_PATH", 11);
        // EDGE_LABEL_MAP.put("LEARN_PATH_ANCHOR_POINT", 13);
        // EDGE_LABEL_MAP.put("ANCHOR_POINT_ANCHOR_POINT", 12);
        // EDGE_LABEL_MAP.put("ANCHOR_POINT_CARD", 14);
        // EDGE_LABEL_MAP.put("ANCHOR_POINT_VIDEO", 15);
        // EDGE_LABEL_MAP.put("ANCHOR_POINT_TOPIC", 16);
        // EDGE_LABEL_MAP.put("UNIT_TOPIC", 17);
        // EDGE_LABEL_MAP.put("COURSE_TOPIC", 18);
    }

    /**
     * @Description: 获取有向图的最长路径
     * @Param:
     * @return:
     * @Author: xuYao2
     * @Date: 2022/3/11
     */
    public static Integer getDPHLongestPath(List<SubGraphQuery.EdgeLabel> edgeLabels) {

        ArrayList<String> list = getVertexList(edgeLabels);
        int size = list.size();

        Graph graph = new Graph(size);

        HashMap<String, Integer> map = new HashMap<>();
        // 将节点以 (label, integer) 键值对的形式放在一个map里
        for (int i = 0; i < size; i++) {
            map.put(list.get(i), i + 1);
        }

        // 设置边
        for (SubGraphQuery.EdgeLabel edgeLabel : edgeLabels) {
            graph.addEdge(map.get(edgeLabel.getSource()), map.get(edgeLabel.getTarget()));
        }

        return graph.findLongestPath(size);

    }

    /**
     * 或许边中对应的所有节点
     *
     * @param edgeLabels
     * @return
     */
    private static ArrayList<String> getVertexList(List<SubGraphQuery.EdgeLabel> edgeLabels) {
        HashSet<String> hashSet = new HashSet<>();

        for (SubGraphQuery.EdgeLabel edgeLabel : edgeLabels) {
            hashSet.add(edgeLabel.getSource());
            hashSet.add(edgeLabel.getTarget());
        }

        return new ArrayList<>(hashSet);

    }


    /**
     * @Description: 判断各个节点是否在一条线上
     * @Param:
     * @return:
     * @Author: xuYao2
     * @Date: 2022/3/11
     */
    public static boolean isLine(GraphQuery graphQuery) {
        if (graphQuery.getEdgeLabels().size() == 1) {
            return true;
        }
        List<String> sources = graphQuery.getEdgeLabels().stream()
                .map(GraphQuery.EdgeLabel::getSource)
                .collect(Collectors.toList());
        List<String> targets = graphQuery.getEdgeLabels().stream()
                .map(GraphQuery.EdgeLabel::getTarget)
                .collect(Collectors.toList());
        boolean sourceUnique = sources.stream().distinct().count() == sources.size();
        boolean targetUnique = targets.stream().distinct().count() == targets.size();
        long sourceDiff = sources.stream().filter(item -> !targets.contains(item)).count();
        long targetDiff = targets.stream().filter(item -> !sources.contains(item)).count();
        return sourceUnique && targetUnique && sourceDiff == 1 && targetDiff == 1;
    }

    /**
     * 将给定图的边进行排序
     *
     * @param edgeLabels
     * @return
     */
    public static List<SubGraphQuery.EdgeLabel> sortGraphEdge(List<SubGraphQuery.EdgeLabel> edgeLabels) {
        return edgeLabels.stream()
                .sorted(Comparator.comparingInt(GraphUtils::getGraphLabelOrder))
                .collect(Collectors.toList());
    }

    private static int getGraphLabelOrder(SubGraphQuery.EdgeLabel edgeLabel) {
        String label = edgeLabel.getSource() + "_" + edgeLabel.getTarget();
        return EDGE_LABEL_MAP.getOrDefault(label, -1);
    }

    /**
     * 将给定线的边进行排序
     *
     * @param edgeLabels
     * @return
     */
    public static List<GraphQuery.EdgeLabel> sortVertexEdge(List<GraphQuery.EdgeLabel> edgeLabels) {
        return edgeLabels.stream()
                .sorted(Comparator.comparingInt(GraphUtils::getVertexLabelOrder))
                .collect(Collectors.toList());
    }

    private static int getVertexLabelOrder(GraphQuery.EdgeLabel edgeLabel) {
        String label = edgeLabel.getSource() + "_" + edgeLabel.getTarget();
        return EDGE_LABEL_MAP.getOrDefault(label, -1);
    }

}