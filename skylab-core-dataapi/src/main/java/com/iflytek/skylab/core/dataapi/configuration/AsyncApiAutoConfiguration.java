package com.iflytek.skylab.core.dataapi.configuration;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.iflytek.skylab.core.dataapi.annotation.EnableBizDataAPI;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;


/**
 * Async注解使用的线程池配置。
 */
@Configuration
@EnableAsync
@ConditionalOnBean(annotation = EnableBizDataAPI.class)
public class AsyncApiAutoConfiguration {

    @Bean
    @ConfigurationProperties("skylab.data.api.async")
    public AsyncProperties asyncProperties() {
        return new AsyncProperties();
    }

    @Bean
    public Executor asyncExecutor(AsyncProperties asyncProperties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(asyncProperties.getCorePoolSize());
        executor.setMaxPoolSize(asyncProperties.getMaximumPoolSize());
        executor.setKeepAliveSeconds((int) (asyncProperties.getKeepAliveMilliseconds() / 1000));
        executor.setQueueCapacity(asyncProperties.getQueueCapacity());
        executor.setThreadFactory(new ThreadFactoryBuilder().setNameFormat("async-pool-%d").build());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return executor;
    }
}
