package com.iflytek.skylab.core.dataapi.mongo.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.iflytek.skylab.core.dataapi.data.FeedbackLog;
import com.iflytek.skylab.core.dataapi.data.RecLog;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2023年6月30日14:58:02
 * @description 大图谱外来学情-不影响精准学，做物理隔离
 */

@Getter
@Setter
@Document(collection = "xxj_study_macrograph_log_record")
@NoArgsConstructor
public class StudyMacrographLogRecordEntity extends StudyLogRecordEntity {

    private static CopyOptions copyOptions = CopyOptions.create();


    /**
     * 根据 traceId， FeedbackLog 构造StudyLogRecordEntity
     *
     * @param traceId
     * @param log
     */
    public StudyMacrographLogRecordEntity(String traceId, FeedbackLog log) {
        setTraceId(traceId);
        BeanUtil.copyProperties(log, this, copyOptions);
    }

    /**
     * 根据 FeedbackLog StudyMacrographLogRecordEntity
     *
     * @param log
     */
    public StudyMacrographLogRecordEntity(FeedbackLog log) {
        BeanUtil.copyProperties(log, this, copyOptions);
    }

    public StudyMacrographLogRecordEntity(String traceId, RecLog log) {
        setTraceId(traceId);
        BeanUtil.copyProperties(log, this, copyOptions);
    }
}


