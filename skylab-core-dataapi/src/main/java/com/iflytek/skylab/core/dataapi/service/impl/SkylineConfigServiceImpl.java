package com.iflytek.skylab.core.dataapi.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.dataapi.mongo.dao.SkylinePlanRepository;
import com.iflytek.skylab.core.dataapi.mongo.dao.SkylinePlanVersionRepository;
import com.iflytek.skylab.core.dataapi.mongo.dao.SkylineStrategyFunctionRepository;
import com.iflytek.skylab.core.dataapi.mongo.dao.SkylineStrategyRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.SkylinePlan;
import com.iflytek.skylab.core.dataapi.mongo.entity.SkylinePlanVersion;
import com.iflytek.skylab.core.dataapi.mongo.entity.SkylineStrategy;
import com.iflytek.skylab.core.dataapi.mongo.entity.SkylineStrategyFunction;
import com.iflytek.skylab.core.dataapi.service.SkylineConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class SkylineConfigServiceImpl implements SkylineConfigService {

    private final SkylinePlanRepository skylinePlanRepository;
    private final SkylinePlanVersionRepository skylinePlanVersionRepository;
    private final SkylineStrategyRepository skylineStrategyRepository;
    private final SkylineStrategyFunctionRepository skylineStrategyFunctionRepository;

    public SkylineConfigServiceImpl(
            SkylinePlanRepository skylinePlanRepository, SkylinePlanVersionRepository skylinePlanVersionRepository,
            SkylineStrategyRepository skylineStrategyRepository, SkylineStrategyFunctionRepository skylineStrategyFunctionRepository) {
        this.skylinePlanRepository = skylinePlanRepository;
        this.skylinePlanVersionRepository = skylinePlanVersionRepository;
        this.skylineStrategyRepository = skylineStrategyRepository;
        this.skylineStrategyFunctionRepository = skylineStrategyFunctionRepository;
    }

    /*
     * 获取某个服务的所有配置参数
     */
    @Override
    public List<JSONObject> queryAllConfigs(String actionPoint) {
        if (log.isDebugEnabled()) {
            log.debug("actionPoint={}", actionPoint);
        }
        List<JSONObject> parameters = new ArrayList<>();
        List<SkylinePlanVersion> onlinePlanVersions = getOnlinePlanVersions();
        List<SkylineStrategy> strategies = getStrategies(onlinePlanVersions);
        for (SkylineStrategy strategy : strategies) {
            List<SkylineStrategyFunction> strategyFunctions = getStrategyFunctions(strategy);
            for (SkylineStrategyFunction strategyFunction : strategyFunctions) {
                // log.debug("Strategy function: {}", strategyFunction);
                JSONObject parameter = getParameterByActionPoint(strategy, strategyFunction, actionPoint);
                if (parameter != null && !parameter.isEmpty()) {
                    parameters.add(parameter);
                }
            }
        }
        return parameters;
    }

    /**
     * 获取已发布的方案版本
     */
    private List<SkylinePlanVersion> getOnlinePlanVersions() {
        List<SkylinePlanVersion> onlinePlanVersions = new ArrayList<>();
        List<SkylinePlan> onlinePlans = skylinePlanRepository.findAllByStatus(1);
        for (SkylinePlan plan : onlinePlans) {
            onlinePlanVersions.addAll(skylinePlanVersionRepository.findAllByPlanIdAndStatus(plan.getId(), 2));
        }

        if (log.isDebugEnabled()) {
            onlinePlans.forEach(obj -> log.debug("SkylinePlan, id={}, name={}, status={}", obj.getId(), obj.getName(), obj.getStatus()));
            onlinePlanVersions.forEach(obj -> log.debug("SkylinePlanVersion,id={}, planId={}, name={}", obj.getId(), obj.getPlanId(), obj.getName()));
        }
        return onlinePlanVersions;
    }

    /**
     * 读取方案拓扑，获取关联的策略
     */
    private List<SkylineStrategy> getStrategies(List<SkylinePlanVersion> planVersions) {
        List<SkylineStrategy> results = new ArrayList<>();
        for (SkylinePlanVersion planVersion : planVersions) {
            if (planVersion.getDesignData() == null || planVersion.getDesignData().getJSONArray("nodeList") == null) {
                continue;
            }
            JSONArray nodeList = planVersion.getDesignData().getJSONArray("nodeList");
            for (int i = 0; i < nodeList.size(); i++) {
                JSONObject node = nodeList.getJSONObject(i);
                JSONObject parameter = node.getJSONObject("parameter");
                if (parameter != null && "strategy".equals(parameter.getString("nodeType"))) {
                    String strategyId = parameter.getString("strategyId");
                    if (StringUtils.isNotBlank(strategyId)) {
                        skylineStrategyRepository.findById(strategyId).ifPresent(strategy -> {
                            log.debug("SkylineStrategy,id={}, name={}, version={}", strategy.getId(), strategy.getName(), strategy.getVersionNumber());
                            results.add(strategy);
                        });
                    }
                }
            }
        }
        return results;
    }

    /**
     * 获取策略功能
     */
    private List<SkylineStrategyFunction> getStrategyFunctions(SkylineStrategy strategy) {
        List<SkylineStrategyFunction> results = skylineStrategyFunctionRepository.findAllByStrategyId(strategy.getId());
        if (log.isDebugEnabled()) {
            results.forEach(obj -> log.debug("SkylineStrategyFunction, id={}, strategyId={}, name={}, code={}", obj.getId(), obj.getStrategyId(), obj.getName(), obj.getCode()));
        }
        return results;
    }

    /**
     * 从功能配置中获取参数，注意合并策略级的公共参数
     */
    private JSONObject getParameterByActionPoint(SkylineStrategy strategy, SkylineStrategyFunction strategyFunction, String actionPoint) {
        if (strategyFunction == null || strategyFunction.getFunctionConfig() == null) {
            return null;
        }
        JSONObject service = strategyFunction.getFunctionConfig().getJSONObject("service");
        if (service == null || !matchesActionPoint(actionPoint, service.getString("actionPoint"))) {
            return null;
        }
        return mergeParameters(strategy.getParameter(), service.getJSONObject("parameter"));
    }

    /**
     * 判断两个 actionPoint 是否一致
     */
    private boolean matchesActionPoint(String actionPoint1, String actionPoint2) {
        if (actionPoint1 == null || actionPoint2 == null) {
            return false;
        }
        String serviceName1 = getServiceName(actionPoint1);
        String serviceName2 = getServiceName(actionPoint2);
        return actionPoint1.equals(actionPoint2) || serviceName1.equals(serviceName2);
    }

    /**
     * 从 actionPoint 中截取服务名，actionPoint 的格式为：serviceName@namespace
     */
    private String getServiceName(String actionPoint) {
        if (actionPoint.contains("@")) {
            return actionPoint.substring(0, actionPoint.indexOf("@"));
        }
        return actionPoint;
    }

    /*
     * 合并请求参数
     */
    private JSONObject mergeParameters(JSONObject... parameters) {
        JSONObject result = new JSONObject();
        for (JSONObject parameter : parameters) {
            if (parameter != null) {
                parameter.forEach((k, v) -> {
                    if (v instanceof Map) {
                        JSONObject json = (JSONObject) JSON.toJSON(v);
                        if ("json".equals(json.getString("language"))) {
                            result.put(k, JSON.parse(json.getString("value")));
                        } else {
                            result.put(k, v);
                        }
                    } else {
                        result.put(k, v);
                    }
                });
            }
        }
        return result;
    }
}
