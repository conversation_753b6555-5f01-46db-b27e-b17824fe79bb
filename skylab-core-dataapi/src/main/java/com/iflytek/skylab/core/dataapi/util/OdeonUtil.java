package com.iflytek.skylab.core.dataapi.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.dataapi.data.FeedbackLog;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/5/24 09:59
 */
@Slf4j
public class OdeonUtil {


    public static JSONObject wrappedOrNull(FeedbackLog feedbackLog) {
        if (feedbackLog == null
                || feedbackLog.getNodeType() == null
                || feedbackLog.getResNodeType() == null
                || feedbackLog.getStudyCode() == null
                || feedbackLog.getScoreRatio() == null
                || feedbackLog.getScore() == null
                || feedbackLog.getStandardScore() == null
                || feedbackLog.getFeedbackTime() == null
                || feedbackLog.getBizCode() == null
                || feedbackLog.getBizAction() == null
                || isBlank(feedbackLog.getUserId())
                || isBlank(feedbackLog.getPhaseCode())
                || isBlank(feedbackLog.getGradeCode())
                || isBlank(feedbackLog.getSubjectCode())
                || isBlank(feedbackLog.getNodeId())
                || isBlank(feedbackLog.getResNodeId())
                || isBlank(feedbackLog.getGradeCode())) {

            log.warn("odeon illegal feedbackLog:{}", feedbackLog == null ? "null" : feedbackLog.toJson());
            return null;
        }


        try {
            JSONObject feedbackLogJson = JSON.parseObject(feedbackLog.toJson());
            if (log.isDebugEnabled()) {
                log.debug("wrapped for Odeon:{}", feedbackLogJson);
            }
            return feedbackLogJson;
        } catch (Exception e) {
            log.error("OdeonUtil#wrappedOrNull error", e);
        }
        return null;
    }


    private static boolean isBlank(String str) {
        return StrUtil.isBlank(str) || "bg-1".equals(str);
    }
}
