package com.iflytek.skylab.core.dataapi.mongo.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;

@Getter
@Setter
@Document(collection = "xxj_study_forbidden_record")
//@CompoundIndex(name = "idx_user_catalog", def = "{'user_id': 1, 'catalog_id': 1}", expireAfterSeconds = 7 * 24 * 60 * 60)
public class StudyForbiddenRecordEntity {
    @Id
    private String id;

    /**
     * 用户Id
     */
    @Field(name = "user_id")
    private String userId;

    /**
     * 父目录
     */
    @Field(name = "catalog_id")
    private String catalogId;

    /**
     * 关联点id
     */
    @Field(name = "node_id")
    private String nodeId;

    /**
     * 创建时间
     */
    @CreatedDate
    @Field(name = "create_time")
    private Instant createTime;

} 