package com.iflytek.skylab.core.dataapi.mongo.entity;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.StudyActionEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.Jsonable;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;

/**
 * 学习行为跟踪记录
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Document(collection = "xxj_study_trace_record")
public class StudyTraceRecord extends Jsonable {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 记录ID， uuid
     */
    @Field(name = "uuid")
    private String uuid;
    /**
     * 用户id
     */
    @Field(name = "user_id")
    private String userId;
    /**
     * 业务编码
     */
    @Field(name = "biz_code")
    private BizCodeEnum bizCode;
    /**
     * 学习场景
     */
    @Field(name = "study_code")
    private StudyCodeEnum studyCode;
    /**
     * 学习能力
     */
    @Field(name = "study_action")
    private StudyActionEnum studyAction;
    /**
     * 图谱版本
     */
    @Field(name = "graph_version")
    private String graphVersion;
    /**
     * 学科
     */
    @Field(name = "subject_code")
    private String subjectCode;
    /**
     * 学段
     */
    @Field(name = "phase_code")
    private String phaseCode;
    /**
     * 教材版本编码
     */
    @Field(name = "book_code")
    private String bookCode;
    /**
     * 行为编码
     */
    @Field(name = "action_code")
    private String actionCode;
    /**
     * 行为上下文
     */
    @Field(name = "trace_context")
    private JSONObject traceContext;
    /**
     * 关联的追踪ID
     */
    @Field(name = "trace_id")
    private String traceId;
    /**
     * 创建时间
     */
    @Field(name = "create_time")
    private Instant createTime;

    @Override
    public String toString() {
        return super.toString();
    }
}
