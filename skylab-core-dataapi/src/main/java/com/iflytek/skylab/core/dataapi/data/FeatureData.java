package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.data.Jsonable;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;


/**
 * 特征数据对象
 * <p>
 * [{
 * "featureName": "fc_score",
 * "values": [{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"a", "fc_score":"0.5"},{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"b", "fc_score":"0.8"}],
 * },{
 * "featureName": "dev_score",
 * "ids": [{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"a", "dev_score":"0.1"},{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"b", "dev_score":"0.9"}],
 * }
 * ]
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class FeatureData extends Jsonable {

//    /**
//     * 图谱版本
//     */
//    private String graphVersion;
//    /**
//     * 用户标识
//     */
//    private String userId;

    /**
     * 特征数据列表
     */
    private List<FeatureDataItem> items = new ArrayList<>();

    @Override
    public String toString() {
        return super.toString();
    }
}
