package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.data.Jsonable;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/3/22 10:11
 */
@Getter
@Setter
public class StudyLogBase extends Jsonable {

    /**
     * 主键
     */
    private String id;

    /**
     * 用户Id
     */
    @NotBlank
    private String userId;

    /**
     * 学段编码
     */
    @NotBlank
    private String phaseCode;

    /**
     * 学校Id
     */
    private String schoolId;

    /**
     * 年级编码
     */
    private String gradeCode;

    /**
     * 班级id
     */
    private String classId;

    /**
     * 学科编码
     */
    @NotBlank
    private String subjectCode;

    /**
     * 教材版本编码
     */
    private String bookCode;

    /**
     * 图谱版本
     */
    private String graphVersion;

    /**
     * 学习场景
     */
    @NotNull
    private StudyCodeEnum studyCode;

    /**
     * 业务编码
     */
    @NotNull
    private BizCodeEnum bizCode;

    /**
     * 业务功能枚举
     */
    private BizActionEnum bizAction;

    /**
     * catalogCode
     * <p>
     * 教材_书_章_节
     */
    private String catalogCode;

    /**
     * 关联点id
     */
    private String nodeId;

    /**
     * 关联点类型 （锚点、考点、复习点）
     */
    private NodeTypeEnum nodeType;

    /**
     * 资源id （题目id 、其他资源id）
     */
    // @NotBlank  点排序 无资源
    private String resNodeId;

    /**
     * 资源类型 （题、视频、卡片）
     */
    // @NotNull
    private ResourceTypeEnum resNodeType;

    /**
     * 轮标识，如测评任务、推荐任务
     */
    private String roundId;

    /**
     * 轮序号，一次测评任务中的序号
     */
    private Integer roundIndex;

    /**
     * 功能编码
     */
    private String funcCode;

    /**
     * 学情记录来源
     */
    private String from;

    /**
     * 是否克隆
     */
    private Integer cloneFlag;

    // @Override
    // public String toString() {
    //     return super.toString();
    // }
}
