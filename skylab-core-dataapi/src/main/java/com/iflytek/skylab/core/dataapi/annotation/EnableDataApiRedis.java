package com.iflytek.skylab.core.dataapi.annotation;

import com.iflytek.skylab.core.dataapi.controller.GraphController;
import org.springframework.context.annotation.Import;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 开启 特征访问Service 功能
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Import({GraphController.class})
public @interface EnableDataApiRedis {
}
