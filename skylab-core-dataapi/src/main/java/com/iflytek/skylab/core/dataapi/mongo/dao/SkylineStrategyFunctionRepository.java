package com.iflytek.skylab.core.dataapi.mongo.dao;

import com.iflytek.skylab.core.dataapi.mongo.DataApiMongoRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.SkylineStrategyFunction;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SkylineStrategyFunctionRepository extends DataApiMongoRepository<SkylineStrategyFunction, String> {

    List<SkylineStrategyFunction> findAllByStrategyId(String strategyId);
}
