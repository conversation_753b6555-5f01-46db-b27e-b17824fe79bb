package com.iflytek.skylab.core.dataapi.service.cache;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.util.OffHeapObjUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 本地文件图谱缓存实现类
 * <p>
 * 功能：
 * 1. 从本地ZIP压缩包中加载图谱数据（顶点和边）到堆外内存缓存
 * 2. 支持顶点和边的高效查询操作
 * 3. 提供图谱遍历功能（BFS广度优先搜索）
 * 4. 支持按文件类型过滤缓存加载
 * 5. 提供缓存统计和监控功能
 * <p>
 * 处理逻辑思路：
 * 1. 初始化阶段：
 * - 读取ZIP压缩包中的图谱文件
 * - 解析TAG_*文件（顶点数据）和EDGE_*文件（边数据）
 * - 将数据序列化到堆外内存以减少JVM堆内存占用
 * - 支持并发多线程加载以提高初始化速度
 * - 提供加载进度监控和异常恢复机制
 * <p>
 * 2. 数据存储结构：
 * - offHeapVertexMap: 顶点ID到顶点对象的堆外内存映射
 * - offHeapEdgeMap: 边标签到源点ID到边列表的堆外内存映射
 * - 使用堆外内存避免大对象对GC的影响
 * - 支持内存使用统计和监控
 * <p>
 * 3. 查询处理：
 * - 支持顶点批量查询
 * - 支持关联顶点和边的联合查询
 * - 提供特定类型顶点（考点、锚点、复习点）的查询接口
 * - 优化查询性能，减少重复对象创建
 * <p>
 * 4. 图谱遍历：
 * - 实现BFS广度优先遍历算法
 * - 支持深度限制和节点类型过滤
 * - 防止循环访问的重复节点检测
 * - 优化遍历性能，减少内存分配
 * <p>
 * 5. 性能优化：
 * - 使用堆外内存减少GC压力
 * - 支持并发加载提高初始化速度
 * - 批量操作减少IO开销
 * - 定期日志输出监控加载进度
 * - 优化数据结构和算法，提升查询效率
 * - 支持缓存预热和懒加载策略
 * <p>
 * 6. 资源管理：
 * - 提供缓存清理和资源释放功能
 * - 支持缓存重新加载和热更新
 * - 异常处理和恢复机制
 * <p>
 * 使用场景：
 * - 适用于需要频繁访问的大型图谱数据
 * - 适合内存敏感但对性能要求高的场景
 * - 支持教育领域知识点图谱的高效查询和遍历
 * - 适用于需要高并发访问的图谱服务
 *
 * <AUTHOR>
 * @version 2.0
 * @since 1.0
 */
@Slf4j
public class LocalFileGraphCache implements GraphCache {

    // 常量定义
    private static final int DEFAULT_VERTEX_MAP_SIZE = 10240;
    private static final int DEFAULT_EDGE_MAP_SIZE = 10240;
    private static final int LOG_INTERVAL = 50000;
    private static final String RECORDS_PREFIX = "records/";
    private static final String TAG_PREFIX = "TAG_";
    private static final String EDGE_PREFIX = "EDGE_";

    @Getter
    @Setter
    private ConcurrentHashMap<String, ByteBuffer> offHeapVertexMap = new ConcurrentHashMap<>(DEFAULT_VERTEX_MAP_SIZE);

    @Getter
    @Setter
    private ConcurrentHashMap<String, Map<String, ByteBuffer>> offHeapEdgeMap = new ConcurrentHashMap<>(DEFAULT_EDGE_MAP_SIZE);

    private final Map<String, List<String>> nodeProps;
    private final GraphProperties graphProperties;
    private volatile boolean initialized = false;
    @Getter
    private volatile long vertexCount = 0;
    @Getter
    private volatile long edgeCount = 0;
    @Getter
    private volatile long loadTimeMillis = 0;

    public LocalFileGraphCache(GraphProperties localGraphProperties) throws Exception {
        this.nodeProps = localGraphProperties.getNodeProps();
        this.graphProperties = localGraphProperties;
        if (!localGraphProperties.isLocalCacheEnabled()) {
            log.info("缓存加载被禁止");
            return;
        }
        validateGraphProperties(localGraphProperties);
        loadGraphCache(localGraphProperties);
    }

    private void validateGraphProperties(GraphProperties properties) throws Exception {
        String graphZipFilePath = properties.getPath();
        if (StringUtils.isBlank(graphZipFilePath)) {
            throw new IllegalArgumentException("图谱文件路径为空");
        }
        File localGraphFile = new File(graphZipFilePath);
        if (!localGraphFile.exists() || !localGraphFile.canRead()) {
            throw new IllegalArgumentException("图谱文件不存在或无法读取: " + graphZipFilePath);
        }
    }

    private void loadGraphCache(GraphProperties properties) throws Exception {
        this.offHeapVertexMap.clear();
        this.offHeapEdgeMap.clear();


        StopWatch stopWatch = StopWatch.createStarted();
        String graphZipFilePath = properties.getPath();
        List<String> cacheFiles = properties.getCacheFiles();
        log.info("开始加载图谱文件 {}", graphZipFilePath);

        try (ZipFile localGraphZip = new ZipFile(new File(graphZipFilePath))) {
            List<ZipEntry> processableEntries = collectProcessableEntries(localGraphZip, cacheFiles);
            log.info("发现 {} 个需要处理的图谱文件", processableEntries.size());
            if (processableEntries.isEmpty()) {
                log.warn("未找到可处理的图谱文件");
                return;
            }
            processEntriesInParallel(localGraphZip, processableEntries, this.offHeapVertexMap, this.offHeapEdgeMap);
            this.vertexCount = offHeapVertexMap.size();
            this.edgeCount = offHeapEdgeMap.values().stream().mapToLong(Map::size).sum();
            this.loadTimeMillis = stopWatch.getTime(TimeUnit.MILLISECONDS);
            this.initialized = true;
            log.info("图谱缓存加载完成 文件 = {} - 顶点数 = {}, 边数 = {}, 耗时 = {}", graphZipFilePath, vertexCount, edgeCount, stopWatch);
        } catch (Exception e) {
            log.error("读取图谱文件失败: {}", graphZipFilePath, e);
            throw new Exception("读取图谱文件失败", e);
        }
    }

    private List<ZipEntry> collectProcessableEntries(ZipFile zipFile, List<String> cacheFiles) {
        List<ZipEntry> processableEntries = new ArrayList<>();
        Enumeration<? extends ZipEntry> entries = zipFile.entries();
        while (entries.hasMoreElements()) {
            ZipEntry entry = entries.nextElement();
            String fileName = extractFileNameFromPath(entry.getName());
            if (fileName != null && (CollectionUtils.isEmpty(cacheFiles) || cacheFiles.contains(fileName))) {
                processableEntries.add(entry);
            }
        }
        return processableEntries;
    }

    private void processEntriesInParallel(ZipFile zipFile, List<ZipEntry> entries,
                                          ConcurrentHashMap<String, ByteBuffer> tempVertex,
                                          ConcurrentHashMap<String, Map<String, ByteBuffer>> tempEdge) {
        entries.parallelStream().forEach(entry -> {
            try {
                CacheProcessingResult result = processZipEntry(zipFile, entry);
                tempVertex.putAll(result.getVertexData());
                result.getEdgeData().forEach((key, value) ->
                        tempEdge.merge(key, value, (existing, incoming) -> {
                            existing.putAll(incoming);
                            return existing;
                        }));
            } catch (Exception e) {
                String fileName = extractFileNameFromPath(entry.getName());
                log.error("并行处理图谱文件失败: {}", fileName, e);
                throw new RuntimeException("并行处理图谱文件失败: " + fileName, e);
            }
        });
    }

    private void offHeapCacheTags(InputStream inputStream, Map<String, ByteBuffer> cacheMap) throws Exception {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            int count = 0;
            String currentLabel = null;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isBlank(line)) continue;
                try {
                    GraphData.GraphVertex vertex = JSON.parseObject(line, GraphData.GraphVertex.class);
                    if (vertex == null || StringUtils.isBlank(vertex.getId())) {
                        log.warn("跳过无效顶点数据: {}", line);
                        continue;
                    }
                    JSONObject filteredProps = filterVertexProperties(vertex);
                    vertex.setProperties(filteredProps);
                    cacheMap.put(vertex.getId(), OffHeapObjUtils.writeObjectToOffHeap(vertex));
                    count++;
                    currentLabel = vertex.getLabel();
                    if (count % LOG_INTERVAL == 0) {
                        log.info("顶点 {} 缓存进度: {}", currentLabel, count);
                    }
                } catch (Exception e) {
                    log.error("解析顶点数据失败，跳过该行: {}", line, e);
                }
            }
            log.info("顶点缓存完成 - 类型: {}, 数量: {}", currentLabel, count);
        }
    }

    private Map<String, Map<String, ByteBuffer>> offHeapCacheEdges(InputStream inputStream) throws Exception {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            String edgeLabel = null;
            Map<String, List<GraphData.GraphEdge>> sourceToEdgesMap = new HashMap<>();
            int count = 0;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isBlank(line)) continue;
                try {
                    GraphData.GraphEdge edge = JSON.parseObject(line, GraphData.GraphEdge.class);
                    if (edge == null || StringUtils.isBlank(edge.getSource()) || StringUtils.isBlank(edge.getTarget())) {
                        log.warn("跳过无效边数据: {}", line);
                        continue;
                    }
                    String sourceId = edge.getSource();
                    edgeLabel = edge.getLabel();
                    sourceToEdgesMap.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(edge);
                    count++;
                    if (count % LOG_INTERVAL == 0) {
                        log.info("边 {} 缓存进度: {}", edgeLabel, count);
                    }
                } catch (Exception e) {
                    log.error("解析边数据失败，跳过该行: {}", line, e);
                }
            }
            log.info("边缓存完成 - 类型: {}, 数量: {}", edgeLabel, count);
//            Map<String, ByteBuffer> sourceToOffHeapEdgesMap = Maps.newHashMapWithExpectedSize(sourceToEdgesMap.size());
//            for (Map.Entry<String, List<GraphData.GraphEdge>> entry : sourceToEdgesMap.entrySet()) {
//                sourceToOffHeapEdgesMap.put(entry.getKey(), OffHeapObjUtils.writeObjectToOffHeap(entry.getValue()));
//            }
            // 使用并发处理 sourceToEdgesMap
            Map<String, ByteBuffer> sourceToOffHeapEdgesMap =
                    sourceToEdgesMap.entrySet()
                            .parallelStream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey,
                                    entry -> {
                                        try {
                                            return OffHeapObjUtils.writeObjectToOffHeap(entry.getValue());
                                        } catch (IOException e) {
                                            throw new RuntimeException(e);
                                        }
                                    },
                                    (v1, v2) -> v1, // 合并函数（理论上不会有重复 key）
                                    () -> Maps.newHashMapWithExpectedSize(sourceToEdgesMap.size())
                            ));

            Map<String, Map<String, ByteBuffer>> result = Maps.newHashMapWithExpectedSize(1);
            result.put(edgeLabel, sourceToOffHeapEdgesMap);

            log.info("EdgeLabel = {} writeObjectToOffHeap.size = {} OK.", edgeLabel, sourceToOffHeapEdgesMap.size());

//: 边缓存完成 - 类型: ANCHOR_POINT_TOPIC, 数量: 3238505
//: 完成处理缓存文件: EDGE_ANCHOR_POINT_TOPIC.info
//: 图谱缓存加载完成 - 顶点数: 744748, 边数: 217390, 耗时: 25.38 s
            return result;
        }
    }

    private JSONObject filterVertexProperties(GraphData.GraphVertex vertex) {
        JSONObject filteredProps = new JSONObject();
        if (vertex.getProperties() != null && nodeProps.containsKey(vertex.getLabel())) {
            JSONObject originProps = vertex.getProperties();
            List<String> allowedProps = nodeProps.get(vertex.getLabel());
            for (String propKey : allowedProps) {
                Object propValue = originProps.get(propKey);
                if (propValue != null) {
                    filteredProps.put(propKey, propValue);
                }
            }
        }
        return filteredProps;
    }

    /**
     * 批量查询顶点
     *
     * @param vertexIdList 顶点ID列表
     * @return 图谱数据
     */
    public GraphData queryVertexes(List<String> vertexIdList) {
        GraphData result = new GraphData();
        if (!initialized) {
            log.warn("图谱缓存未初始化");
            return result;
        }
        if (CollectionUtils.isEmpty(vertexIdList)) {
            return result;
        }

        // 提取公共逻辑为 Function
        Function<String, GraphData.GraphVertex> fetchVertexFunc = vertexId -> {
            if (StringUtils.isBlank(vertexId)) {
                return null;
            }
            ByteBuffer vertexBuffer = offHeapVertexMap.get(vertexId);
            if (vertexBuffer != null) {
                try {
                    return OffHeapObjUtils.readObjectFromOffHeap(vertexBuffer);
                } catch (Exception e) {
                    log.error("读取顶点堆外内存失败, vertexId: {}", vertexId, e);
                }
            }
            return null;
        };

        List<GraphData.GraphVertex> vertices;
        if (vertexIdList.size() > 100) {
            // 并行处理
            vertices = vertexIdList.parallelStream()
                    .map(fetchVertexFunc)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            // 串行处理
            vertices = new ArrayList<>(vertexIdList.size());
            for (String vertexId : vertexIdList) {
                GraphData.GraphVertex vertex = fetchVertexFunc.apply(vertexId);
                if (vertex != null) {
                    vertices.add(vertex);
                }
            }
        }

        result.setVertices(vertices);
        return result;
    }

    @Override
    public boolean isInitialized() {
        return initialized;
    }

    /**
     * 查询考点
     */
    @Override
    public GraphData queryCheckPoint(List<String> checkPointIds) {
        return queryVertexAndRelatedEdge(checkPointIds);
    }

    /**
     * 查询锚点
     */
    @Override
    public GraphData queryAnchorPoint(List<String> anchorPointIds) {
        return queryVertexAndRelatedEdge(anchorPointIds);
    }

    /**
     * 查询复习点
     */
    @Override
    public GraphData queryReviewPoint(List<String> reviewPointIds) {
        return queryVertexAndRelatedEdge(reviewPointIds);
    }


    /**
     * 查询顶点及其关联的边和目标顶点
     *
     * @param ids 顶点ID列表
     * @return 图谱数据
     */
    private GraphData queryVertexAndRelatedEdge(List<String> ids) {
        if (!initialized) {
            log.warn("图谱缓存未初始化");
            return new GraphData();
        }

        if (CollectionUtils.isEmpty(ids)) {
            return new GraphData();
        }

        Set<GraphData.GraphVertex> vertexSet = new HashSet<>();
        List<GraphData.GraphEdge> edges = new ArrayList<>();

        for (String id : ids) {
            if (StringUtils.isBlank(id)) {
                continue;
            }
            // 查询源顶点
            addVertexToSet(id, vertexSet);

            // 查询所有标签的边
            for (Map<String, ByteBuffer> labelEdges : offHeapEdgeMap.values()) {
                ByteBuffer edgeBuffer = labelEdges.get(id);
                if (edgeBuffer != null) {
                    try {
                        List<GraphData.GraphEdge> edgeList = OffHeapObjUtils.readObjectFromOffHeap(edgeBuffer);
                        if (edgeList != null) {
                            edges.addAll(edgeList);

                            // 添加目标顶点
                            for (GraphData.GraphEdge edge : edgeList) {
                                addVertexToSet(edge.getTarget(), vertexSet);
                            }
                        }
                    } catch (Exception e) {
                        log.error("读取边堆外内存失败, sourceId: {}", id, e);
                    }
                }
            }
        }

        GraphData result = new GraphData();
        result.setVertices(new ArrayList<>(vertexSet));
        result.setEdges(edges);
        return result;
    }

    /**
     * 添加顶点到集合中
     */
    private void addVertexToSet(String vertexId, Set<GraphData.GraphVertex> vertexSet) {
        if (StringUtils.isBlank(vertexId)) {
            return;
        }
        ByteBuffer vertexBuffer = offHeapVertexMap.get(vertexId);
        if (vertexBuffer != null) {
            try {
                GraphData.GraphVertex vertex = OffHeapObjUtils.readObjectFromOffHeap(vertexBuffer);
                if (vertex != null) {
                    vertexSet.add(vertex);
                }
            } catch (Exception e) {
                log.error("读取顶点堆外内存失败, vertexId: {}", vertexId, e);
            }
        }
    }

    /**
     * 广度优先搜索遍历图谱
     *
     * @param from              起始顶点ID列表
     * @param tagTypeOrEdgeType 允许的顶点类型或边类型集合
     * @param vertices          结果顶点列表
     * @param edges             结果边列表
     * @param maxDepth          最大遍历深度
     */
    public void bfs(List<String> from, Set<String> tagTypeOrEdgeType,
                    List<GraphData.GraphVertex> vertices, List<GraphData.GraphEdge> edges, int maxDepth) {
        if (!initialized) {
            log.warn("图谱缓存未初始化，无法执行BFS遍历");
            return;
        }

        if (CollectionUtils.isEmpty(from) || CollectionUtils.isEmpty(tagTypeOrEdgeType) || maxDepth < 0) {
            log.debug("BFS遍历参数无效");
            return;
        }

        Set<String> visitedVertices = new HashSet<>();
        Queue<String> currentLevelQueue = new LinkedList<>(from);
        int currentDepth = 0;

        if (log.isDebugEnabled()) {
            log.debug("开始BFS遍历 - 起点: [{}], 类型: [{}], 最大深度: {}",
                    String.join(",", from), String.join(",", tagTypeOrEdgeType), maxDepth);
        }

        while (!currentLevelQueue.isEmpty() && currentDepth <= maxDepth) {
            Queue<String> nextLevelQueue = new LinkedList<>();
            int currentLevelSize = currentLevelQueue.size();

            log.debug("BFS遍历 - 深度: {}, 当前层顶点数: {}", currentDepth, currentLevelSize);

            while (!currentLevelQueue.isEmpty()) {
                String vertexId = currentLevelQueue.poll();

                // 防止重复访问
                if (visitedVertices.contains(vertexId)) {
                    continue;
                }

                // 处理当前顶点
                if (processVertexInBFS(vertexId, tagTypeOrEdgeType, vertices, visitedVertices)) {
                    // 如果未达到最大深度，添加邻接顶点到下一层
                    if (currentDepth < maxDepth) {
                        addAdjacentVertices(vertexId, tagTypeOrEdgeType, edges, nextLevelQueue, visitedVertices);
                    }
                }
            }

            currentLevelQueue = nextLevelQueue;
            currentDepth++;
        }

        log.debug("BFS遍历完成 - 访问顶点数: {}, 边数: {}", vertices.size(), edges.size());
    }

    /**
     * 在BFS中处理单个顶点
     */
    private boolean processVertexInBFS(String vertexId, Set<String> allowedTypes,
                                       List<GraphData.GraphVertex> vertices, Set<String> visitedVertices) {
        if (StringUtils.isBlank(vertexId)) {
            return false;
        }

        ByteBuffer vertexBuffer = offHeapVertexMap.get(vertexId);
        if (vertexBuffer == null) {
            return false;
        }

        try {
            GraphData.GraphVertex vertex = OffHeapObjUtils.readObjectFromOffHeap(vertexBuffer);
            if (vertex != null && allowedTypes.contains(vertex.getLabel())) {
                vertices.add(vertex);
                visitedVertices.add(vertexId);
                return true;
            }
        } catch (Exception e) {
            log.error("BFS处理顶点失败, vertexId: {}", vertexId, e);
        }

        return false;
    }

    /**
     * 添加邻接顶点到下一层队列
     */
    private void addAdjacentVertices(String sourceId, Set<String> allowedEdgeTypes,
                                     List<GraphData.GraphEdge> edges, Queue<String> nextLevelQueue,
                                     Set<String> visitedVertices) {
        for (String edgeLabel : allowedEdgeTypes) {
            Map<String, ByteBuffer> labelEdgeMap = offHeapEdgeMap.get(edgeLabel);
            if (labelEdgeMap == null) {
                continue;
            }

            ByteBuffer edgeBuffer = labelEdgeMap.get(sourceId);
            if (edgeBuffer == null) {
                continue;
            }

            try {
                List<GraphData.GraphEdge> edgeList = OffHeapObjUtils.readObjectFromOffHeap(edgeBuffer);
                if (edgeList != null) {
                    edges.addAll(edgeList);

                    // 添加目标顶点到下一层
                    for (GraphData.GraphEdge edge : edgeList) {
                        String targetId = edge.getTarget();
                        if (StringUtils.isNotBlank(targetId) && !visitedVertices.contains(targetId)) {
                            nextLevelQueue.offer(targetId);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("BFS处理边失败, sourceId: {}, edgeLabel: {}", sourceId, edgeLabel, e);
            }
        }
    }


    /**
     * 从文件路径中提取文件名
     *
     * @param filePath 文件路径，如 "records/TAG_*.json" 或 "records/EDGE_*.json"
     * @return 提取的文件名，如果路径不匹配则返回null
     */
    private String extractFileNameFromPath(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return null;
        }

        // 检查是否在records目录下且文件名以TAG_或EDGE_开头
        if (filePath.startsWith(RECORDS_PREFIX) && filePath.length() > RECORDS_PREFIX.length()) {
            String fileName = filePath.substring(RECORDS_PREFIX.length());
            if (fileName.startsWith(TAG_PREFIX) || fileName.startsWith(EDGE_PREFIX)) {
                return fileName;
            }
        }

        return null;
    }

    /**
     * 处理ZIP条目的方法，支持并发调用
     *
     * @param zipFile ZIP文件对象
     * @param entry   ZIP条目
     * @return 缓存处理结果
     */
    private CacheProcessingResult processZipEntry(ZipFile zipFile, ZipEntry entry) {
        String fileName = extractFileNameFromPath(entry.getName());
        log.info("开始处理缓存文件: {}", fileName);

        Map<String, ByteBuffer> vertexResult = new HashMap<>();
        Map<String, Map<String, ByteBuffer>> edgeResult = new HashMap<>();

        try (InputStream inputStream = zipFile.getInputStream(entry)) {
            assert fileName != null;
            if (fileName.startsWith(TAG_PREFIX)) {
                offHeapCacheTags(inputStream, vertexResult);
            } else if (fileName.startsWith(EDGE_PREFIX)) {
                edgeResult.putAll(offHeapCacheEdges(inputStream));
            } else {
                log.warn("未识别的图谱文件类型: {}", fileName);
            }
            log.info("完成处理缓存文件: {}", fileName);
        } catch (Exception e) {
            log.error("处理缓存文件失败: {}", fileName, e);
            throw new RuntimeException("处理缓存文件失败: " + fileName, e);
        }

        return new CacheProcessingResult(vertexResult, edgeResult);
    }

    /**
     * 清理缓存资源
     */
    public void clearCache() {
        log.info("开始清理图谱缓存");
        this.offHeapVertexMap.clear();
        this.offHeapEdgeMap.clear();
        initialized = false;
        vertexCount = 0;
        edgeCount = 0;
        loadTimeMillis = 0;

        log.info("图谱缓存清理完成");
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return String.format("图谱缓存统计 - 初始化状态: %s, 顶点数: %d, 边数: %d, 加载耗时: %dms",
                initialized, vertexCount, edgeCount, loadTimeMillis);
    }

    /**
     * 重新加载缓存
     */
    public void reloadCache() throws Exception {
        log.info("开始重新加载图谱缓存");
        clearCache();
        loadGraphCache(graphProperties);
        log.info("图谱缓存重新加载完成");
    }

    /**
     * 缓存处理结果容器类，用于并发处理结果收集
     */
    @Getter
    private static class CacheProcessingResult {
        private final Map<String, ByteBuffer> vertexData;
        private final Map<String, Map<String, ByteBuffer>> edgeData;

        public CacheProcessingResult(Map<String, ByteBuffer> vertexData, Map<String, Map<String, ByteBuffer>> edgeData) {
            this.vertexData = vertexData;
            this.edgeData = edgeData;
        }
    }
}
