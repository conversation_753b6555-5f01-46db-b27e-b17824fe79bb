package com.iflytek.skylab.core.dataapi.data;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/***
 *
 *
 * 特征数据项
 *
 * [{
 *     "featureName": "fc_score",
 *     "values": [{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"a", "fc_score":"0.5"},{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"b", "fc_score":"0.8"}],
 *   },{
 *     "featureName": "dev_score",
 *     "ids": [{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"a", "dev_score":"0.1"},{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"b", "dev_score":"0.9"}],
 *   }
 * ]
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class FeatureDataItem {

    public FeatureDataItem() {
    }

    public FeatureDataItem(String featureName, List<Map<String, String>> values) {
        this.featureName = featureName;
        this.values = values;
    }


    private String featureName;

    private List<Map<String, String>> values = new ArrayList<>();
}
