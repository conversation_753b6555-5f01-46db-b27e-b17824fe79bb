package com.iflytek.skylab.core.dataapi.redis;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.iflytek.skylab.core.dataapi.configuration.FeatureRedisCacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;

/**
 * 特征缓存服务
 * 实现高性能Redis二级缓存，支持批量、分批、异步写入、内存监控、穿透防护、手动清空等功能。
 * 适配JDK8和Spring Data Redis 1.x/2.x，生产可用。
 * <p>
 * 优化方案：
 * 1. 二进制存储：使用RedisTemplate<String, byte[]>减少序列化开销
 * 2. 压缩存储：对大数据进行压缩减少内存占用
 * 3. 批量优化：优化批量操作减少网络往返
 * </p>
 * <p>
 * 主要功能：
 * - 批量缓存读写，分批处理防止阻塞
 * - 异步写入，提升写入性能
 * - 内存监控，防止Redis写满
 * - 缓存穿透防护，空对象标记
 * - 缓存命中率统计与重置
 * - 支持按特征名前缀批量清理缓存
 * </p>
 */
@Slf4j
public class FeatureRedisCacheService {
    /**
     * 空对象标记，用于缓存穿透防护
     */
    public static final String EMPTY_MARK = "_E_";
    public static final String SPLIT_COLON = ":";

    /**
     * Redis二进制模板，减少序列化开销
     */
    private final RedisTemplate<String, byte[]> binaryRedisTemplate;
    /**
     * Redis内存监控服务，防止写满
     */
    private final RedisMemoryMonitorService memoryMonitor;
    /**
     * 缓存配置属性
     */
    private final FeatureRedisCacheProperties cacheProperties;
    /**
     * 异步写入线程池
     */
    private final ExecutorService executorService;

    /**
     * 构造方法，初始化线程池和依赖
     *
     * @param binaryRedisTemplate Redis二进制模板
     * @param memoryMonitor       Redis内存监控
     * @param cacheProperties     缓存配置
     */
    public FeatureRedisCacheService(RedisTemplate<String, byte[]> binaryRedisTemplate, RedisMemoryMonitorService memoryMonitor, FeatureRedisCacheProperties cacheProperties) {
        this.binaryRedisTemplate = binaryRedisTemplate;
        this.memoryMonitor = memoryMonitor;
        this.cacheProperties = cacheProperties;
        FeatureRedisCacheProperties.ThreadPool threadPool = cacheProperties.getThreadPool();
        // 初始化线程池，支持自定义参数
        this.executorService = new ThreadPoolExecutor(threadPool.getCorePoolSize(),
                threadPool.getMaxPoolSize(),
                threadPool.getKeepAliveTime(),
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(threadPool.getQueueCapacity()),
                new ThreadFactoryBuilder().setNameFormat(threadPool.getThreadNamePrefix() + "%d").build(), new ThreadPoolExecutor.AbortPolicy());

    }

    /**
     * 构建缓存key，格式为 feature:特征表名:rowkey
     *
     * @param featureName 特征表名
     * @param rowkey      参数拼接后的唯一标识
     * @return redis缓存key
     */
    public String buildCacheKey(String featureName, String rowkey) {
        return featureName + SPLIT_COLON + rowkey;
    }


    /**
     * 使用二进制方式批量获取缓存，减少网络往返，分批处理防止单次pipeline过大
     *
     * @param rowkeys rowkey集合
     * @return 命中的rowkey与缓存内容映射
     */
    public Map<String, String> batchGetBinary(List<String> rowkeys) {
        if (rowkeys == null || rowkeys.isEmpty()) {
            return Collections.emptyMap();
        }
        int batchSize = cacheProperties.getBatch().getSize();
        Map<String, String> result = new HashMap<>(rowkeys.size());
        for (int i = 0; i < rowkeys.size(); i += batchSize) {
            List<String> batch = rowkeys.subList(i, Math.min(i + batchSize, rowkeys.size()));
            List<Object> batchResults = binaryRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (String rowkey : batch) {
                    connection.get(rowkey.getBytes(StandardCharsets.UTF_8));
                }
                return null;
            });
            if (batchResults != null) {
                int minSize = Math.min(batch.size(), batchResults.size());
                for (int j = 0; j < minSize; j++) {
                    Object value = batchResults.get(j);
                    if (value instanceof byte[] && ((byte[]) value).length > 0) {
                        result.put(batch.get(j), new String((byte[]) value, StandardCharsets.UTF_8));
                    }
                }
            }
        }
        return result;
    }

    /**
     * 批量异步写入缓存，分批MSET，支持自定义过期时间。
     * 写入前判断Redis内存占用，超阈值则跳过写入。
     * 空对象用EMPTY_MARK，正常值用实际内容。
     * 支持二进制存储和压缩存储
     *
     * @param featureName 特征表名
     * @param data        rowkey与内容映射，空对象用EMPTY_MARK
     * @param traceId     链路追踪ID
     */
    public void asyncBatchSet(String featureName, Map<String, String> data, String traceId) {
        if (data == null || data.isEmpty()) {
            return;
        }
        // 内存超阈值不写入
        if (!memoryMonitor.canWrite()) {
            log.warn("[asyncBatchSet] Redis内存超阈值，跳过写入，featureName={}, dataSize={}, traceId={}", featureName, data.size(), traceId);
            return;
        }
        long maxExecutionTime = cacheProperties.getThreadPool().getMaxExecutionTime();
        // 提交异步写入任务
        Future<?> future = executorService.submit(() -> {
            try {
                syncBatchSetBinary(featureName, data);
            } catch (Exception e) {
                log.error("异步批量写入缓存失败: featureName={}, dataSize={}, traceId={}", featureName, data.size(), traceId, e);
            }
        });
        // 超时控制，防止异步任务卡死
        executorService.submit(() -> {
            try {
                future.get(maxExecutionTime, TimeUnit.MILLISECONDS);
            } catch (TimeoutException e) {
                future.cancel(true); // 尝试中断
                log.warn("异步批量写入超时被中断: featureName={}, dataSize={}, maxExecutionTime={}ms, traceId={}", featureName, data.size(), maxExecutionTime, traceId);
            } catch (Exception e) {
                log.error("异步批量写入缓存执行异常: featureName={}, dataSize={}, traceId={}", featureName, data.size(), traceId, e);
            }
        });
    }

    /**
     * 使用二进制方式批量写入缓存，支持自定义过期时间和value大小校验，分批处理防止单次pipeline过大
     *
     * @param featureName 特征表名
     * @param data        rowkey与内容映射
     */
    private void syncBatchSetBinary(String featureName, Map<String, String> data) {
        Long baseExpireSeconds = cacheProperties.getExpire(featureName);
        if (baseExpireSeconds == null) {
            log.warn("featureName={}的过期时间未配置，请检查FeatureRedisCacheProperties", featureName);
            return;
        }
        int batchSize = cacheProperties.getBatch().getSize();
        Random random = ThreadLocalRandom.current();
        List<Map.Entry<String, String>> entries = new ArrayList<>(data.entrySet());
        for (int i = 0; i < entries.size(); i += batchSize) {
            int end = Math.min(i + batchSize, entries.size());
            List<Map.Entry<String, String>> batch = entries.subList(i, end);
            binaryRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (Map.Entry<String, String> entry : batch) {
                    String cacheKey = entry.getKey();
                    String value = entry.getValue();
                    // value过大不缓存
                    if (value != null && value.getBytes(StandardCharsets.UTF_8).length > 1024) {
                        if (log.isDebugEnabled()) {
                            log.debug("value超出1k字节，不进行缓存，featureName={}, key={}", featureName, cacheKey);
                        }
                        continue;
                    }
                    if (value == null) {
                        value = EMPTY_MARK;
                    }
                    byte[] keyBytes = cacheKey.getBytes(StandardCharsets.UTF_8);
                    byte[] valueBytes = value.getBytes(StandardCharsets.UTF_8);
                    int randomExpire = random.nextInt(3600);
                    long expireSeconds = baseExpireSeconds + randomExpire;
                    connection.set(keyBytes, valueBytes);
                    connection.expire(keyBytes, expireSeconds);
                }
                return null;
            });
        }
    }

    /**
     * 判断缓存内容是否为空对象标记
     *
     * @param value 缓存内容
     * @return 是否为空对象
     */
    public boolean isEmptyMark(String value) {
        return EMPTY_MARK.equals(value);
    }

    /**
     * 按featureName前缀批量清理缓存
     *
     * @param featureNamePrefix 特征名前缀
     */
    public long clearByFeaturePrefix(String featureNamePrefix) {
        if (featureNamePrefix == null || featureNamePrefix.isEmpty() || featureNamePrefix.contains("*")) {
            return 0L;
        }
        long start = System.currentTimeMillis();
        String pattern = featureNamePrefix + ":*";
        Set<String> keys = new HashSet<>();
        try {
            // 扫描所有匹配的key
            binaryRedisTemplate.execute((RedisCallback<Object>) (connection) -> {
                ScanOptions scanOptions = ScanOptions.scanOptions().match(pattern).count(1000).build();
                try (Cursor<byte[]> cursor = connection.scan(scanOptions)) {
                    while (cursor.hasNext()) {
                        byte[] keyBytes = cursor.next();
                        String key = new String(keyBytes);
                        keys.add(key);
                    }
                }
                return null;
            });
            int scanned = keys.size();
            // 分批删除，batchSize 取配置
            int batchSize = cacheProperties.getBatch().getSize();
            int deleted = 0;
            List<byte[]> keyBytes = new ArrayList<>();
            for (String key : keys) {
                keyBytes.add(key.getBytes());
            }
            List<byte[]> batch = new ArrayList<>(batchSize);
            for (int i = 0; i < keyBytes.size(); i++) {
                batch.add(keyBytes.get(i));
                if (batch.size() == batchSize || i == keyBytes.size() - 1) {
                    final List<byte[]> toDelete = new ArrayList<>(batch);
                    binaryRedisTemplate.execute((RedisCallback<Object>) connection -> {
                        return connection.unlink(toDelete.toArray(new byte[0][]));
                    });
                    deleted += batch.size();
                    batch.clear();
                }
            }
            long cost = System.currentTimeMillis() - start;
            log.info("[clearByFeaturePrefix] 清理特征前缀缓存, prefix={}, 扫描key数={}, 删除key数={}, 耗时={}ms", featureNamePrefix, scanned, deleted, cost);
            return deleted;
        } catch (Exception e) {
            log.warn("[clearByFeaturePrefix] 清理特征前缀缓存异常, prefix={}, 已扫描key数={}, err={}", featureNamePrefix, keys.size(), e.getMessage(), e);
            return 0L;
        }
    }


    public ExecutorService getExecutorService() {
        return executorService;
    }
} 