package com.iflytek.skylab.core.dataapi.data;

import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserErrorAnswerLog
 * @description TODO
 * @date 2024/6/17 16:25
 */
@Getter
@Setter
public class UserErrorAnswerLog {

    /**
     * 资源id （题目id 、其他资源id）
     */
    private String resNodeId;

    /**
     * 锚点Id
     */
    private String nodeId;

    /**
     * 作答时间
     */
    private Instant updateTime;
}
