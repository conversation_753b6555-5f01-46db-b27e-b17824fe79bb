package com.iflytek.skylab.core.dataapi.data;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 图的点
 */
@Getter
@Setter
@Accessors(chain = true)
public class GraphVertex {
    
    @NotBlank
    private String traceId;

    /**
     * 图谱版本
     */
    @NotBlank
    private String graphVersion;

    /**
     * 点的类型
     */
    @NotBlank
    private String label;
    
    /**
     * 点的主键
     */
    @NotBlank
    private String id;

    /**
     * 点的属性
     */
    private JSONObject properties;
}
