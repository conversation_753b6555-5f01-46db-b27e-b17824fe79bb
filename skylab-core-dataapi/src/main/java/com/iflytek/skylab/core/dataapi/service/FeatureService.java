package com.iflytek.skylab.core.dataapi.service;

import com.iflytek.skylab.core.dataapi.data.FeatureData;
import com.iflytek.skylab.core.dataapi.data.FeatureQuery;
import com.iflytek.cog2.feaflow.sdk.data.ZionDictModel;


import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 特征服务
 */
public interface FeatureService {


    /**
     * 查询特征数据
     *
     * @param traceId
     * @param featureQuery
     * @return
     */
    FeatureData query(@NotBlank String traceId, @NotNull @Valid FeatureQuery featureQuery);


    /**
     * 查询特征数据字典
     *
     * @param traceId
     * @param dictRowKey 特征字典版本
     * @return
     */
    ZionDictModel querySchema(@NotBlank String traceId, String dictRowKey);


    boolean needPostProcess(String traceId, String featureName);

    Long clearRedisCache();
}
