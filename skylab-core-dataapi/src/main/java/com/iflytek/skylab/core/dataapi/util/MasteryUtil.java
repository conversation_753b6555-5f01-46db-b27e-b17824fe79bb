package com.iflytek.skylab.core.dataapi.util;

import com.iflytek.skylab.core.dataapi.data.MasterItem;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;

public class MasteryUtil {

    // 容差值，按实际需求调整
    private static final double EPSILON = 1e-10;

    // 判断是否相等
    public static boolean equals(double a, double b) {
        return Math.abs(a - b) < EPSILON;
    }

    // 判断 a 是否大于 b
    public static boolean greaterThan(double a, double b) {
        return a - b > EPSILON;
    }

    // 判断 a 是否小于 b
    public static boolean lessThan(double a, double b) {
        return b - a > EPSILON;
    }

    // 判断 a 是否大于等于 b
    public static boolean greaterThanOrEqual(double a, double b) {
        return greaterThan(a, b) || equals(a, b);
    }

    // 判断 a 是否小于等于 b
    public static boolean lessThanOrEqual(double a, double b) {
        return lessThan(a, b) || equals(a, b);
    }

    private static boolean validValue(Double value){
        return value != null && !MasteryUtil.equals(-1d, value);
    }


    public static boolean validMastery(MasterItem masterItem){
        if(masterItem.getGlobalMastery() != null){
            if(!validValue(masterItem.getFusion())
                    && !validValue(masterItem.getMasteryScore())
                    && !validValue(masterItem.getGlobalMastery().getDoubleValue("fusion"))
                    && !validValue(masterItem.getGlobalMastery().getDoubleValue("masterScore"))
            ){
                return false;
            }
        } else {
            if(!validValue(masterItem.getFusion())
                    && !validValue(masterItem.getMasteryScore())
            ){
                return false;
            }
        }
        return true;
    }

    public static boolean validMastery(UserMasteryRecord masterItem){
        if(masterItem.getGlobalMastery() != null){
            if(!validValue(masterItem.getFusion())
                    && !validValue(masterItem.getMasteryScore())
                    && !validValue(masterItem.getGlobalMastery().getFusion())
                    && !validValue(masterItem.getGlobalMastery().getMasterScore())
            ){
                return false;
            }
        } else {
            if(!validValue(masterItem.getFusion())
                    && !validValue(masterItem.getMasteryScore())
            ){
                return false;
            }
        }
        return true;
    }
}
