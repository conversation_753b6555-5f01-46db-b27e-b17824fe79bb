package com.iflytek.skylab.core.dataapi.mongo.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Getter
@Setter
@Document(collection = "skyline_strategy_function")
public class SkylineStrategyFunction {
    
    @Id
    private String id;

    /**
     * 策略ID
     */
    @Field(name = "strategy_id")
    private String strategyId;
    
    /**
     * 名称
     */
    @Field(name = "name")
    private String name;

    /**
     * 编码
     */
    @Field(name = "code")
    private String code;

    /**
     * 功能配置
     */
    @Field(name = "function_config")
    private JSONObject functionConfig;
}
