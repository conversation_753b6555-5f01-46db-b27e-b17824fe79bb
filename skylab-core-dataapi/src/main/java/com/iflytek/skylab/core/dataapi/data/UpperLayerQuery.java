package com.iflytek.skylab.core.dataapi.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/23 11:04
 */
@Getter
@Setter
@Accessors(chain = true)
public class UpperLayerQuery {
    @NotBlank
    private String traceId;

    @NotBlank
    private String graphVersion;

    @NotNull
    private List<String> targetIds;

    @NotEmpty
    private List<EdgeLabel> edgeLabels;

    /**
     * 限定结果集在一个节点（一本书/一个教材版本）下
     */
    private String resultLimitId;


    @Getter
@Setter
    @Accessors(chain = true)
    public static class EdgeLabel {

        /**
         * 源节点类型
         */
        @NotBlank
        private String source;

        /**
         * 目标节点类型，所有目标节点类型必须一致
         */
        @NotBlank
        private String target;
    }
}
