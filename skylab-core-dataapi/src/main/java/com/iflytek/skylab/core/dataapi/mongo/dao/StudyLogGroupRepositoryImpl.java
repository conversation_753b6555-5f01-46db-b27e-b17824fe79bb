package com.iflytek.skylab.core.dataapi.mongo.dao;


import com.iflytek.skylab.core.dataapi.mongo.entity.StudyCorrectLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyMacrographLogRecordEntity;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Service
public class StudyLogGroupRepositoryImpl implements StudyLogGroupRepository {

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 按node_id分组查询作答记录
     * @return
     */
    @Override
    public List<StudyLogRecordEntity> findStudyLogGroupByNodeId(Criteria criteria, int maxNum){
        // 匹配操作
        MatchOperation match = Aggregation.match(criteria);
        // 分组操作，每个node_id最多10条记录
        GroupOperation group = Aggregation.group("node_id")
                .push("$$ROOT").as("logs");

        // 使用$project操作来限制每个node_id的记录数量
        ProjectionOperation project = Aggregation.project()
                .and("logs").slice(maxNum).as("logs");

        // 构建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(
                match,
                group,
                project
        );

        List<StudyLogRecordResult> queryResults = mongoTemplate.aggregate(aggregation, "xxj_study_log_record", StudyLogRecordResult.class).getMappedResults();
        List<StudyLogRecordEntity> results = new ArrayList<>();
        for (StudyLogRecordResult queryResult : queryResults) {
            results.addAll(queryResult.logs);
        }
        return results;
    }

    @Override
    public List<StudyCorrectLogRecordEntity> findStudyCorrectLogGroupByNodeId(Criteria criteria, int maxNum) {
        // 匹配操作
        MatchOperation match = Aggregation.match(criteria);
        // 分组操作，每个node_id最多10条记录
        GroupOperation group = Aggregation.group("node_id")
                .push("$$ROOT").as("logs");

        // 使用$project操作来限制每个node_id的记录数量
        ProjectionOperation project = Aggregation.project()
                .and("logs").slice(maxNum).as("logs");

        // 构建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(
                match,
                group,
                project
        );

        List<StudyCorrectLogRecordResult> queryResults = mongoTemplate.aggregate(aggregation, "xxj_study_correct_log_record", StudyCorrectLogRecordResult.class).getMappedResults();
        List<StudyCorrectLogRecordEntity> results = new ArrayList<>();
        for (StudyCorrectLogRecordResult queryResult : queryResults) {
            results.addAll(queryResult.logs);
        }
        return results;
    }

    @Override
    public List<StudyMacrographLogRecordEntity> findMacroGraphStudyLogGroupByNodeId(Criteria criteria, int maxNum) {
        // 匹配操作
        MatchOperation match = Aggregation.match(criteria);
        // 分组操作，每个node_id最多10条记录
        GroupOperation group = Aggregation.group("node_id")
                .push("$$ROOT").as("logs");

        // 使用$project操作来限制每个node_id的记录数量
        ProjectionOperation project = Aggregation.project()
                .and("logs").slice(maxNum).as("logs");

        // 构建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(
                match,
                group,
                project
        );

        List<StudyMacrographLogRecordResult> queryResults = mongoTemplate.aggregate(aggregation, "xxj_study_macrograph_log_record", StudyMacrographLogRecordResult.class).getMappedResults();
        List<StudyMacrographLogRecordEntity> results = new ArrayList<>();
        for (StudyMacrographLogRecordResult queryResult : queryResults) {
            results.addAll(queryResult.logs);
        }
        return results;
    }


    class StudyMacrographLogRecordResult {
        public String nodeId;
        public List<StudyMacrographLogRecordEntity> logs;
    }

    class StudyLogRecordResult {
        public String nodeId;
        public List<StudyLogRecordEntity> logs;
    }

    class StudyCorrectLogRecordResult {
        public String nodeId;
        public List<StudyCorrectLogRecordEntity> logs;
    }

}
