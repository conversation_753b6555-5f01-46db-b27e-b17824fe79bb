package com.iflytek.skylab.core.dataapi.configuration;

import com.iflytek.cog2.feaflow.sdk.ZionClient;
import com.iflytek.cog2.feaflow.sdk.configuration.ZionAutoConfiguration;
import com.iflytek.cog2.feaflow.sdk.properties.ZionProperties;
import com.iflytek.skylab.core.dataapi.annotation.EnableFeatureAPI;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skylab.core.dataapi.service.impl.FeatureServiceImpl;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 特征API自动配置
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableFeatureAPI.class)
@AutoConfigureAfter(ZionAutoConfiguration.class)
public class FeatureAutoConfiguration {

    @Bean
    public FeatureRedisCacheProperties featureCacheProperties() {
        return new FeatureRedisCacheProperties();
    }

    @Bean
    public FeatureService featureService(ZionClient zionClient, ZionProperties zionProperties
            , FeatureRedisCacheProperties featureRedisCacheProperties
    ) {
        return new FeatureServiceImpl(zionClient, zionProperties, featureRedisCacheProperties);
    }

    @Bean
    @ConfigurationProperties("skylab.data.api.study-log")
    public StudyLogProperties studyLogProperties() {
        return new StudyLogProperties();
    }
}
