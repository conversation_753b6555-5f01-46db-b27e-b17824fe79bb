package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.data.Jsonable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 图顶点查询参数
 */
@Getter
@Setter
@Accessors(chain = true)
public class GraphVertexesQuery extends Jsonable {

    @NotBlank
    private String traceId;

    /**
     * 顶点Id
     */
    @NotEmpty
    private List<String> ids;
    /**
     * 点指定的属性
     * nodeProps.put("ANCHOR_POINT", Arrays.asList("anchorPointType", "difficulty", "evaluatePoint", "evaluations", "examPoint", "phaseCode", "realLastLevelRelationCatas", "subjectCode", "tracePoints"));
     */
    private Map<String, List<String>> nodeProps;

    /**
     * 图谱版本
     */
    @NotBlank
    private String graphVersion;


    @Override
    public String toString() {
        return super.toString();
    }
}
