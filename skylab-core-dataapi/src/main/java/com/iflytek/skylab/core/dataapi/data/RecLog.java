package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 推荐记录对象
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RecLog extends StudyLogBase {

    /**
     * 推荐记录对象
     */
    private StudyLogRecordEntity.RecProps recProps;

    // @Override
    // public String toString() {
    //     return super.toString();
    // }
}
