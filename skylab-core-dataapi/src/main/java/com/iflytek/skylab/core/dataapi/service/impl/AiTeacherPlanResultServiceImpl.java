package com.iflytek.skylab.core.dataapi.service.impl;

import com.google.common.base.Stopwatch;
import com.iflytek.skylab.core.dataapi.mongo.dao.AiTeacherPlanResultRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.PlanResultEntity;
import com.iflytek.skylab.core.dataapi.service.AiTeacherPlanResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.query.Criteria;

import javax.validation.constraints.NotEmpty;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/28 10:11
 */
@Slf4j
public class AiTeacherPlanResultServiceImpl implements AiTeacherPlanResultService {

    private final AiTeacherPlanResultRepository aiTeacherPlanResultRepository;

    public AiTeacherPlanResultServiceImpl(AiTeacherPlanResultRepository planResultRepository) {
        this.aiTeacherPlanResultRepository = planResultRepository;
    }

    /**
     * 规划结果表查询接口
     *
     * @param userId
     * @param catalogId
     * @return
     */
@Override
public List<PlanResultEntity> query(@NotEmpty String userId, @NotEmpty String catalogId) {
    Stopwatch stopwatch = Stopwatch.createStarted();

    // 创建查询条件
    Criteria criteria = new Criteria();
    criteria.and("userId").is(userId);
    criteria.and("catalogId").is(catalogId);
    //0存在，1删除
    criteria.and("isDelete").is(0);

    // 执行聚合查询
    List<PlanResultEntity> search = aiTeacherPlanResultRepository.search(criteria);

    // 判空处理
    if (search == null) {
        search = Collections.emptyList();
    }
    // 按照 order 字段升序排序
    List<PlanResultEntity> sortedResult = search.stream()
            .filter(Objects::nonNull)
            .sorted(Comparator.comparingInt(PlanResultEntity::getOrder))
            .collect(Collectors.toList());

    long cost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
    log.info("【dataapi-cost】规划结果表mongo耗时:{} 数量:{}", cost, sortedResult.size());
    return sortedResult;
}

}
