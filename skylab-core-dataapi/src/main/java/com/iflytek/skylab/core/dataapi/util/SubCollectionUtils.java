package com.iflytek.skylab.core.dataapi.util;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.digest.DigestUtil;

/**
 * <AUTHOR>
 * @date 2023-03-06
 * 分表工具
 */
public class SubCollectionUtils {

    /**
     * c端默认画像表名
     */
    public static final String XXJ_USER_MASTERY_RECORD = "xxj_user_mastery_record";

    public static final int XXJ_USER_MASTERY_RECORD_SUB_COLLECTION_SIZE = 32;

    public static String getSubUserMasteryRecordCollectionName(String userId) {
        String index = "";
        // 生成32位小写md5值
        String encode = DigestUtil.md5Hex(userId);
        // 截取取后两位
        String subEncode = encode.substring(encode.length() - 2);
        // 分成32张表，转换成int后对31进行与操作
        index = String.valueOf(HexUtil.hexToInt(subEncode) & (XXJ_USER_MASTERY_RECORD_SUB_COLLECTION_SIZE - 1));
        return XXJ_USER_MASTERY_RECORD + index;
    }


    public static void main(String args[]){
        System.out.println(getSubUserMasteryRecordCollectionName("8a0cbc3f-0d7a-4009-a8fb-a3817ce15d33"));
    }
}
