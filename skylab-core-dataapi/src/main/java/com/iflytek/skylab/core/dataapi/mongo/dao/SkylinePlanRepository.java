package com.iflytek.skylab.core.dataapi.mongo.dao;

import com.iflytek.skylab.core.dataapi.mongo.DataApiMongoRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.SkylinePlan;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SkylinePlanRepository extends DataApiMongoRepository<SkylinePlan, String> {
    
    List<SkylinePlan> findAllByStatus(Integer status);
}
