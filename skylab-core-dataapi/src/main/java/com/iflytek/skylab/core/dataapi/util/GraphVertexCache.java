package com.iflytek.skylab.core.dataapi.util;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.skylab.core.dataapi.data.GraphData;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 图谱点缓存对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/17 14:23
 */
public class GraphVertexCache {

    /**
     * 图谱版本 ， <  点 ，点详情 >
     */
    private static final ConcurrentHashMap<String, ConcurrentHashMap<String, GraphData.GraphVertex>> MULTI_GRAPH_VERTEX_CACHE = new ConcurrentHashMap();

    /**
     * 获取点缓存
     *
     * @param graphVersion 图谱版本号
     * @param ids          点id
     * @return
     */
    public static List<GraphData.GraphVertex> get(String graphVersion, List<String> ids) {
        List<GraphData.GraphVertex> graphVertices = new ArrayList<>();
        ConcurrentHashMap<String, GraphData.GraphVertex> map = MULTI_GRAPH_VERTEX_CACHE.get(graphVersion);
        if (map != null && CollUtil.isNotEmpty(ids)) {
            for (String id : ids) {
                GraphData.GraphVertex graphVertex = map.get(id);
                if (graphVertex != null) {
                    graphVertices.add(graphVertex);
                }
            }
        }
        return graphVertices;
    }

    /**
     * 添加缓存
     *
     * @param graphVersion
     * @param vertices
     */
    public static void put(String graphVersion, List<GraphData.GraphVertex> vertices) {
        if (vertices == null) {
            return;
        }

        if (!MULTI_GRAPH_VERTEX_CACHE.containsKey(graphVersion)) {
            MULTI_GRAPH_VERTEX_CACHE.put(graphVersion, new ConcurrentHashMap<>());
        }
        ConcurrentHashMap<String, GraphData.GraphVertex> vertexConcurrentHashMap = MULTI_GRAPH_VERTEX_CACHE.get(graphVersion);
        for (GraphData.GraphVertex vertex : vertices) {
            if (!vertexConcurrentHashMap.containsKey(vertex)) {
                vertexConcurrentHashMap.put(vertex.getId(), vertex);
            }
        }
    }

}
