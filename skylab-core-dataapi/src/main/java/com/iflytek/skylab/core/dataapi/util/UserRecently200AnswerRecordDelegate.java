package com.iflytek.skylab.core.dataapi.util;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.ResourceTypeEnum;
import com.iflytek.skylab.core.dataapi.configuration.StudyLogProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/24 17:14
 */
@Deprecated
@Slf4j
public class UserRecently200AnswerRecordDelegate {
    // user_id#subject_code#phase_code#biz_code
    private static final String FEATURE_NAME = "user_recently_200_answer_record";
    private static final CopyOptions copyOptionsIgnoreError = CopyOptions.create().ignoreNullValue().ignoreError();

    public static FeatureQuery generateFeatureQuery(UserRecentlyStudyLogQuery query, StudyLogProperties studyLogProperties) {
        Map<String, String> param = MapUtil.builder("user_id", query.getUserId())
                .put("subject_code", query.getSubjectCode())
                .put("phase_code", query.getPhaseCode())
                .put("biz_code", query.getBizCode().name())
                .build();

        FeatureQueryItem queryItem = new FeatureQueryItem();
        queryItem.setFeatureName(FEATURE_NAME);
        queryItem.setFeatureVersion(studyLogProperties.getFeatureVersion());
        queryItem.setGraphVersion(studyLogProperties.getGraphVersion());
        queryItem.setParams(Lists.newArrayList(param));

        FeatureQuery featureQuery = new FeatureQuery();
        featureQuery.setItems(Lists.newArrayList(queryItem));

        return featureQuery;
    }

    public static List<StudyLogData> parseFeatureData(FeatureData featureData) {
        List<StudyLogData> resultList = new ArrayList<>();
        if (featureData == null || CollectionUtil.isEmpty(featureData.getItems())) {
            return resultList;
        }

        Date yesterday = DateUtil.yesterday().toJdkDate();
        Instant yesterdayZeroClock = DateUtil.beginOfDay(yesterday).toInstant();

        List<Map<String, String>> values = featureData.getItems().get(0).getValues();
        for (Map<String, String> valueMap : values) {
            String json = StrUtil.cleanBlank(valueMap.get(FEATURE_NAME));
            if (StrUtil.isBlank(json)) {
                continue;
            }
            // json转对象
            UserRecently200AnswerRecord record = JSON.parseObject(json, UserRecently200AnswerRecord.class);
            List<UserRecently200AnswerRecord.AnchorTopicInfo> recentTopicInfos = record.getRecentTopicInfos();
            if (CollectionUtil.isEmpty(recentTopicInfos)) {
                return resultList;
            }

            List<StudyLogData> list = recentTopicInfos.stream()
                    .map(UserRecently200AnswerRecord.AnchorTopicInfo::getTopicInfos)
                    .map(topicInfo -> topicInfo.toStudyLogData(record.getBizCode()))
                    .filter(data -> data.getFeedbackTime() != null && data.getFeedbackTime().isBefore(yesterdayZeroClock))
                    .collect(Collectors.toList());

            resultList.addAll(list);
        }

        // 按updateTime倒序
        resultList.sort((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()));
        return resultList;
    }
}
