package com.iflytek.skylab.core.dataapi.mongo.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;

/**
 * 记录缓存
 *
 * <AUTHOR>
 * @date 2022/3/30 17:23
 */
@Getter
@Setter
@Document(collection = "xxj_data_cache_record")
@NoArgsConstructor
public class DataCacheRecord {

    @Id
    private String id;

    /**
     * 唯一确定一个缓存数据
     */
    @Field(name = "cache_key")
    private String cacheKey;

    /**
     * 具体内容
     */
    @Field(name = "data")
    private JSONObject data;


    @CreatedDate
    @Field(name = "create_time")
    private Instant createTime;

    public DataCacheRecord(String cacheKey, JSONObject data) {
        this.cacheKey = cacheKey;
        this.data = data;
    }
}
