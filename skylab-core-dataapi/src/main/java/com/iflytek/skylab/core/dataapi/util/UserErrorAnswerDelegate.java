package com.iflytek.skylab.core.dataapi.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.dataapi.configuration.StudyLogProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserErrorAnswerDelegate
 * @description TODO
 * @date 2024/6/17 15:02
 */
@Slf4j
public class UserErrorAnswerDelegate {

    private static final String FEATURE_NAME = "user_error_answers";

    public static FeatureQuery generateFeatureQuery(ErrorLogQuery query, StudyLogProperties studyLogProperties) {
        List<Map<String, String>> params = Lists.newArrayList();
        // user_id#subject_code#phase_code
        Map<String, String> featureParam = new HashMap<>();
        featureParam.put("user_id", query.getUserId());
        featureParam.put("subject_code", query.getSubjectCode());
        featureParam.put("phase_code", query.getPhaseCode());
        params.add(featureParam);
        FeatureQueryItem queryItem = new FeatureQueryItem();
        queryItem.setFeatureName(FEATURE_NAME);
        queryItem.setFeatureVersion(studyLogProperties.getFeatureVersion());
        queryItem.setGraphVersion(studyLogProperties.getGraphVersion());
        queryItem.setParams(params);

        FeatureQuery featureQuery = new FeatureQuery();
        featureQuery.setItems(Lists.newArrayList(queryItem));
        return featureQuery;
    }

    public static List<UserErrorAnswerLog> studyLogToUserErrorAnswerLog(List<StudyLogRecordEntity> studyLogs) {
        if (studyLogs == null || studyLogs.isEmpty()) {
            return Lists.newArrayList();
        }
        List<UserErrorAnswerLog> userErrorAnswerLogs = Lists.newArrayList();
        studyLogs.forEach(studyLog -> {
            UserErrorAnswerLog userErrorAnswerLog = new UserErrorAnswerLog();
            userErrorAnswerLog.setResNodeId(studyLog.getResNodeId());
            userErrorAnswerLog.setNodeId(studyLog.getNodeId());
            userErrorAnswerLog.setUpdateTime(studyLog.getUpdateTime());
            userErrorAnswerLogs.add(userErrorAnswerLog);
        });
        return userErrorAnswerLogs;
    }

    public static List<UserErrorAnswerLog> parseFeatureData(FeatureData featureData) {
        if (featureData == null || CollectionUtil.isEmpty(featureData.getItems())) {
            return null;
        }
        List<Map<String, String>> values = featureData.getItems().get(0).getValues();
        if (CollectionUtil.isEmpty(values)) {
            return null;
        }
        List<UserErrorAnswerLog> list = new ArrayList<>();
        // 遍历查询结果
        for (Map<String, String> valueMap : values) {
            String json = valueMap.get("user_error_answers");
            if (StrUtil.isBlank(json)) {
                continue;
            }

            // json为对象
            List<UserErrorData> errorDataList = JSON.parseArray(json, UserErrorData.class);
            errorDataList.forEach(errorData -> {
                if (Objects.nonNull(errorData.getFeedbackTime())) {
                    UserErrorAnswerLog userErrorAnswerLog = new UserErrorAnswerLog();
                    userErrorAnswerLog.setResNodeId(errorData.getTopicId());
                    userErrorAnswerLog.setNodeId(errorData.getAnchorId());
                    userErrorAnswerLog.setUpdateTime(Instant.ofEpochMilli(errorData.getFeedbackTime().getTime()));
                    list.add(userErrorAnswerLog);
                }
            });

        }
        return list;
    }
}
