package com.iflytek.skylab.core.dataapi.data;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class GlobalMasteryItem {

    /**
     * 画像得分
     */
    @Field(name = "mastery_score")
    private Double masterScore = -1.0D;

    /**
     * 融合画像值
     */
    @Field(name = "fusion")
    private Double fusion = -1.0D;

    /**
     * 真实画像
     */
    @Field(name = "real")
    private Double real = -1.0D;

    /**
     * 预测画像
     */
    @Field(name = "predict")
    private Double predict = -1.0D;

    @Field(name = "associative_point")
    private Map<String,Object> associativePoint = new HashMap<>(0);
}
