package com.iflytek.skylab.core.dataapi.service.cache;

import com.iflytek.skylab.core.dataapi.data.GraphData;

import java.util.List;

public interface GraphCache {

    /**
     * 判断缓存是否已完成初始化
     */
    boolean isInitialized();

    /**
     * 查询考点
     */
    GraphData queryCheckPoint(List<String> checkPointIds);

    /**
     * 查询锚点
     */
    GraphData queryAnchorPoint(List<String> anchorPointIds);

    /**
     * 查询复习点
     */
    GraphData queryReviewPoint(List<String> reviewPointIds);
}
