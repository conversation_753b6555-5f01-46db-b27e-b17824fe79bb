package com.iflytek.skylab.core.dataapi.data;


import com.iflytek.skylab.core.constant.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 特征查询返回的数据结构<br>
 * 与 user_knowledge_point_archive.proto 格式一致，方便json解析
 * <AUTHOR>
 * @date 2022/5/25 10:02
 */
@Getter
@Setter
public class UserAnchorExamRecentlyAnswerRecord {
    private String userId;
    private BizCodeEnum bizCode;
    private String subjectCode;
    private String phaseCode;
    private String nodeId;
    private NodeTypeEnum nodeType;
    private Integer nodeDoneCount;
    List<TopicInfo> topicDetails;
    private String gradeCode;
    private String schoolId;
    private String classId;

    @Getter
@Setter
    public static class TopicInfo {
        private String resNodeId;
        private Double score;
        private Double standardScore;
        private Double scoreRatio;
        private Long feedbackTime;
        private String funcCode;
        private StudyCodeEnum studyCode;
        private BizActionEnum bizAction;
        private String graphVersion;
        private ResourceTypeEnum resNodeType;
        private String bookCode;
        private String catalogCode;
        private String roundId;
        private Integer roundIndex;
        private Integer cloneFlag;
        private Integer timeCost;
        private String evalEnd;
    }
}
