package com.iflytek.skylab.core.dataapi.util;

import com.iflytek.skylab.core.dataapi.data.GraphData;

import java.io.*;
import java.nio.ByteBuffer;

public class OffHeapObjUtils {

    // 序列化对象为字节数组
    private static byte[] serialize(Object obj) throws IOException {
        try(ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(bos)) {
            oos.writeObject(obj);
            oos.flush();
            return bos.toByteArray();
        }
    }

    // 反序列化字节数组为对象
    private static <T> T deserialize(byte[] bytes) throws IOException, ClassNotFoundException {
        try(ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
        ObjectInputStream ois = new ObjectInputStream(bis)){
            return (T) ois.readObject();
        }
    }


    // 将对象写入堆外内存
    public static ByteBuffer writeObjectToOffHeap(Object obj) throws IOException {
        if (null == obj){
            return null;
        }
        byte[] bytes = serialize(obj);
        ByteBuffer buffer = ByteBuffer.allocateDirect(bytes.length);
        buffer.put(bytes);
        buffer.flip();
        return buffer;
    }

    // 从堆外内存读取对象
    public static <T> T readObjectFromOffHeap(ByteBuffer buffer) throws IOException, ClassNotFoundException {
        if (null == buffer){
            return null;
        }
        byte[] bytes = new byte[buffer.remaining()];
        synchronized (buffer) {
            buffer.get(bytes);
            buffer.rewind();
        }
        return deserialize(bytes);
    }
}
