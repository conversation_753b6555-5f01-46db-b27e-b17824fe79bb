package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.data.Jsonable;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/***
 *
 * 特征请求数据
 *
 * {"traceId": "asdgagasdfdsafd", "featureQuery":[{
 *     "featureName": "fc_score",
 *     "params": [{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"a"},{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"b"}],
 *     "featureVersion": 2,
 *     "graphVersion": "v2022-01"
 *
 *   },{
 *     "featureName": "dev_score",
 *     "params": [{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"a"},{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"b"}],
 *     "featureVersion": 2,
 *     "graphVersion": "v2022-01"
 *   }]
 * }
 *
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class FeatureQuery extends Jsonable {

    /**
     * 查询参数
     */
    @NotEmpty
    private List<FeatureQueryItem> items = new ArrayList<>();

    /**
     * 数据源版本 -> 数仓数据版本
     */
    // @NotNull
//    private DataApiItem dataApiItem;


    /**
     * 特征字典版本， 如果为空，将采用系统配置默认的
     */
    private String dictRowKey;


    @Override
    public String toString() {
        return super.toString();
    }

}
