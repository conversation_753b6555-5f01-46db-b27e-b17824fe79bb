package com.iflytek.skylab.core.dataapi.configuration;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class GraphProperties {
    /**
     * 图数据库地址，多个地址使用逗号分隔：************:9669,************:9669
     */
    private String hosts = "*************:9669";

    /**
     * 最大连接数
     */
    private Integer maxConnSize = 10;

    /**
     * 最小连接数
     */
    private Integer minConnSize = 0;

    /**
     * 连接超时时间（单位：毫秒）
     */
    private Integer timeout = 0;

    /**
     * 连接空闲时间（单位：毫秒），超过空闲时间的连接将被删除，0 表示不删除空闲连接
     */
    private int idleTime = 0;

    /**
     * 空闲连接检测间隔（单位：毫秒），-1 表示不检测
     */
    private int intervalIdle = -1;

    /**
     * 从池中获取可用连接的等待时间（单位：毫秒）
     */
    private int waitTime = 0;

    /**
     * 用户名
     */
    private String username = "root";

    /**
     * 密码
     */
    private String password;

    /**
     * 默认名字空间
     */
    private String namespace = "xxj";

    /**
     * 是否开启图谱缓存
     */
    private boolean cacheEnabled = false;

    /**
     * 是否开启本地图谱缓存
     */
    private boolean localCacheEnabled = false;

    /**
     * 图谱缓存版本
     */
    private String cacheVersion = "v2";

    /**
     * 默认图谱版本
     */
    private String defaultVersion = "";


    /**
     * 点指定的属性
     */
    private Map<String, List<String>> nodeProps;


    /**
     * 本地文件缓存-文件路径
     */
    private String path;

    /**
     * 需要缓存的文件
     */
    private List<String> cacheFiles;
}
