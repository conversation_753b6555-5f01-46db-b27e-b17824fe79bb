package com.iflytek.skylab.core.dataapi;

import com.iflytek.skylab.core.dataapi.service.*;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * 数据访问句柄 统一工具入口
 *
 * <AUTHOR>
 * @date 2022/2/28 8:48 下午
 */
public class DataHub implements ApplicationContextAware {
    /**
     * spring上下文
     */
    private static ApplicationContext applicationContext;

    /**
     * 特征数据接口
     */
    public static final FeatureService getFeatureService() {
        return applicationContext.getBean(FeatureService.class);
    }

    /**
     * 图谱数据接口
     *
     * @return
     */
    public static final GraphService getGraphService() {
        return applicationContext.getBean(GraphService.class);
    }

    public static final GraphService getLocalGraphService() {
        return applicationContext.getBean("localGraphService", GraphService.class);
    }

    /**
     * 用户画像数据接口
     */
    public static final MasterService getMasterService() {
        return applicationContext.getBean(MasterService.class);
    }

    /**
     * 学情日志数据接口
     */
    public static final StudyLogService getStudyLogService() {
        return applicationContext.getBean(StudyLogService.class);
    }

    /**
     * 学情日志数据接口
     */
    public static final StudyMacrographLogService getStudyMacrographLogService() {
        return applicationContext.getBean(StudyMacrographLogService.class);
    }

    /**
     * 规划学列表
     */
    public static final AiTeacherPlanResultService getAiTeacherPlanResultService() {
        return applicationContext.getBean(AiTeacherPlanResultService.class);
    }

    /**
     * 获取用户禁用记录
     */
    public static final StudyForbiddenRecordService getForbiddenRecordService() {
        return applicationContext.getBean(StudyForbiddenRecordService.class);
    }

    /**
     * 用户自动化路径数据接口
     * 已下线-废弃
     */
    @Deprecated
    public static final UserAutoPathService getUserAutoPathService() {
        return applicationContext.getBean(UserAutoPathService.class);
    }

    /**
     * 获取平台方案测评配置
     */
    public static final SkylineConfigService getSkylineConfigService() {
        return applicationContext.getBean(SkylineConfigService.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
