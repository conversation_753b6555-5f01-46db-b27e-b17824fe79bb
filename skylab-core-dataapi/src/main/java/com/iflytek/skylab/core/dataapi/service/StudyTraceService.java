package com.iflytek.skylab.core.dataapi.service;

import com.iflytek.skylab.core.dataapi.mongo.entity.StudyTraceRecord;

/**
 * 学习行为追踪服务
 *
 * <AUTHOR>
 */
public interface StudyTraceService {

    /**
     * 保存 学习行为追踪记录
     *
     * @param studyTraceRecord
     */
    String saveStudyTraceRecord(StudyTraceRecord studyTraceRecord);

    /**
     * 异步保存 学习行为追踪记录
     *
     * @param studyTraceRecord
     */
    void saveStudyTraceRecordAsync(StudyTraceRecord studyTraceRecord);

}
