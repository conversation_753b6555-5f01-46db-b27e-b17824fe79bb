package com.iflytek.skylab.core.dataapi.exception;

import com.iflytek.skylab.core.dataapi.annotation.EnableApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import skynet.boot.annotation.EnableSkynetException;

/**
 * 异常格式化
 *
 * <AUTHOR>
 * @date 2022/6/14 14:31
 */
@Slf4j
@EnableSkynetException
@ConditionalOnBean(annotation = EnableApiException.class)
public class ApiExceptionAutoConfiguration {

    @Bean
    public ApiExceptionMessageFormatter apiExceptionMessageFormatter() {
        log.info("Build ApiExceptionMessageFormatter.");
        return new ApiExceptionMessageFormatter();
    }
}
