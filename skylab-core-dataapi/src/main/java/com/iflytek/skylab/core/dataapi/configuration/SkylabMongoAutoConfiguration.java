package com.iflytek.skylab.core.dataapi.configuration;

import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.dataapi.annotation.EnableBizDataAPI;
import com.iflytek.skylab.core.dataapi.annotation.EnableFeatureAPI;
import com.iflytek.skylab.core.dataapi.mongo.DataApiMongoRepositoryImpl;
import com.iflytek.skylab.core.dataapi.mongo.dao.*;
import com.iflytek.skylab.core.dataapi.service.*;
import com.iflytek.skylab.core.dataapi.service.impl.*;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import skynet.boot.annotation.EnableSkynetMongo;
import skynet.boot.mongo.SkynetMongoAutoConfiguration;


/**
 * mongo业务数据库 配置
 */
@Configuration(proxyBeanMethods = false)
@ComponentScan(basePackages = {"com.iflytek.skylab.core.dataapi.mongo"})
@EnableMongoRepositories(basePackages = "com.iflytek.skylab.core.dataapi.mongo.dao", repositoryBaseClass = DataApiMongoRepositoryImpl.class)
@EnableFeatureAPI
@EnableSkynetMongo
@ConditionalOnBean(annotation = EnableBizDataAPI.class)
@AutoConfigureBefore(SkynetMongoAutoConfiguration.class)
public class SkylabMongoAutoConfiguration {


    @Bean
    @ConfigurationProperties("skylab.data.api.mastery")
    public MasteryProperties MasteryProperties() {
        return new MasteryProperties();
    }
    /**
     * 用户画像数据工具配置
     *
     * @return {@link MasterService}
     */
    @Bean
    public MasterService masterService(UserMasteryRecordRepository userMasteryRecordRepository,MasteryProperties masteryProperties) {
        return new MasterServiceImpl(userMasteryRecordRepository,masteryProperties);
    }
    @Bean
    public AiTeacherPlanResultService aiTeacherPlanResultService(AiTeacherPlanResultRepository planResultRepository) {
        return new AiTeacherPlanResultServiceImpl(planResultRepository );
    }

    /**
     * 学情数据工具配置
     *
     * @return {@link StudyLogService}
     */
    @Bean
    @ConditionalOnBean(FeatureService.class)
    public StudyLogService studyLogService(StudyLogProperties studyLogProperties, FeatureService featureService, StudyLogRecordRepository studyLogRecordRepository, StudyCorrectLogRecordRepository studyCorrectLogRecordRepository, DataCacheRecordRepository dataCacheRecordRepository, StudyMacrographRecordRepository macrographRecordRepository) {
        return new StudyLogServiceImpl(studyLogProperties, featureService, studyLogRecordRepository, studyCorrectLogRecordRepository, dataCacheRecordRepository, macrographRecordRepository);
    }

    @Bean
    @ConditionalOnBean({FeatureService.class,StudyLogGroupRepository.class})
    public StudyLogGroupService studyLogGroupService(StudyLogProperties studyLogProperties, FeatureService featureService, StudyLogGroupRepository studyLogGroupRepository){
        return new StudyLogGroupServiceImpl(studyLogProperties, featureService, studyLogGroupRepository);
    }

    /**
     * 学情数据工具配置
     *
     * @return {@link StudyLogService}
     */
    @Bean
    @ConditionalOnBean(FeatureService.class)
    public StudyMacrographLogService studyMacrographLogService(StudyLogProperties studyLogProperties, FeatureService featureService, StudyMacrographRecordRepository macrographRecordRepository, StudyLogRecordRepository studyLogRecordRepository) {
        return new StudyMacrographLogServiceImpl(studyLogProperties, featureService, macrographRecordRepository, studyLogRecordRepository);
    }

    /**
     * 学习行为跟踪记录服务
     *
     * @param studyTraceRecordRepository
     * @return
     */
    @Bean
    public StudyTraceService studyTraceService(StudyTraceRecordRepository studyTraceRecordRepository) {
        return new StudyTraceServiceImpl(studyTraceRecordRepository);
    }
    @Bean
    public StudyForbiddenRecordService studyForbiddenRecordService(StudyForbiddenRecordRepository forbiddenRecordRepository) {
        return new StudyForbiddenRecordServiceImpl(forbiddenRecordRepository);
    }

    @Bean
    public SkylineConfigService skylineConfigService(
            SkylinePlanRepository skylinePlanRepository, SkylinePlanVersionRepository skylinePlanVersionRepository,
            SkylineStrategyRepository skylineStrategyRepository, SkylineStrategyFunctionRepository skylineStrategyFunctionRepository) {
        return new SkylineConfigServiceImpl(
                skylinePlanRepository, skylinePlanVersionRepository,
                skylineStrategyRepository, skylineStrategyFunctionRepository);
    }

    //region 自定义 BigDecimal 转换器

    @Bean
    public BizActionStringConverter bizActionStringConverter() {
        return new SkylabMongoAutoConfiguration.BizActionStringConverter();
    }

    @Bean
    public StringBizActionConverter stringBizActionConverter() {
        return new SkylabMongoAutoConfiguration.StringBizActionConverter();
    }

    @Bean
    public BizCodeStringConverter bizCodeStringConverter() {
        return new SkylabMongoAutoConfiguration.BizCodeStringConverter();
    }

    @Bean
    public StringBizCodeConverter stringBizCodeConverter() {
        return new SkylabMongoAutoConfiguration.StringBizCodeConverter();
    }

    @Bean
    public CatalogTypeStringConverter catalogTypeStringConverter() {
        return new SkylabMongoAutoConfiguration.CatalogTypeStringConverter();
    }

    @Bean
    public StringCatalogTypeConverter stringCatalogTypeConverter() {
        return new SkylabMongoAutoConfiguration.StringCatalogTypeConverter();
    }

    @Bean
    public NodeTypeStringConverter nodeTypeStringConverter() {
        return new SkylabMongoAutoConfiguration.NodeTypeStringConverter();
    }

    @Bean
    public StringNodeTypeConverter stringNodeTypeConverter() {
        return new SkylabMongoAutoConfiguration.StringNodeTypeConverter();
    }

    @Bean
    public ResourceTypeStringConverter resourceTypeStringConverter() {
        return new SkylabMongoAutoConfiguration.ResourceTypeStringConverter();
    }

    @Bean
    public StringResourceTypeConverter stringResourceTypeConverter() {
        return new SkylabMongoAutoConfiguration.StringResourceTypeConverter();
    }

    @Bean
    public ResTypeStringConverter resTypeStringConverter() {
        return new SkylabMongoAutoConfiguration.ResTypeStringConverter();
    }

    @Bean
    public StringResTypeConverter stringResTypeConverter() {
        return new SkylabMongoAutoConfiguration.StringResTypeConverter();
    }

    @Bean
    public StudyActionStringConverter studyActionStringConverter() {
        return new SkylabMongoAutoConfiguration.StudyActionStringConverter();
    }

    @Bean
    public StringStudyActionConverter stringStudyActionConverter() {
        return new SkylabMongoAutoConfiguration.StringStudyActionConverter();
    }

    @Bean
    public StudyCodeStringConverter studyCodeStringConverter() {
        return new SkylabMongoAutoConfiguration.StudyCodeStringConverter();
    }

    @Bean
    public StringStudyCodeConverter stringStudyCodeConverter() {
        return new SkylabMongoAutoConfiguration.StringStudyCodeConverter();
    }
    //endregion 自定义 BigDecimal 转换器

    // region mongodb的枚举类自动转换器

    @WritingConverter
    private static class BizActionStringConverter implements Converter<BizActionEnum, String> {
        @Override
        public String convert(BizActionEnum source) {
            return source.name();
        }
    }

    @ReadingConverter
    private static class StringBizActionConverter implements Converter<String, BizActionEnum> {
        @Override
        public BizActionEnum convert(String source) {
            return BizActionEnum.parse(source);
        }
    }


    @WritingConverter
    private static class BizCodeStringConverter implements Converter<BizCodeEnum, String> {
        @Override
        public String convert(BizCodeEnum source) {
            return source.name();
        }
    }

    @ReadingConverter
    private static class StringBizCodeConverter implements Converter<String, BizCodeEnum> {
        @Override
        public BizCodeEnum convert(String source) {
            return BizCodeEnum.parse(source);
        }
    }

    @WritingConverter
    private static class CatalogTypeStringConverter implements Converter<CatalogTypeEnum, String> {
        @Override
        public String convert(CatalogTypeEnum source) {
            return source.name();
        }
    }

    @ReadingConverter
    private static class StringCatalogTypeConverter implements Converter<String, CatalogTypeEnum> {
        @Override
        public CatalogTypeEnum convert(String source) {
            return CatalogTypeEnum.parse(source);
        }
    }


    @WritingConverter
    private static class NodeTypeStringConverter implements Converter<NodeTypeEnum, String> {
        @Override
        public String convert(NodeTypeEnum source) {
            return source.name();
        }
    }

    @ReadingConverter
    private static class StringNodeTypeConverter implements Converter<String, NodeTypeEnum> {
        @Override
        public NodeTypeEnum convert(String source) {
            return NodeTypeEnum.parse(source);
        }
    }

    @WritingConverter
    private static class ResourceTypeStringConverter implements Converter<ResourceTypeEnum, String> {
        @Override
        public String convert(ResourceTypeEnum source) {
            return source.name();
        }
    }

    @ReadingConverter
    private static class StringResourceTypeConverter implements Converter<String, ResourceTypeEnum> {
        @Override
        public ResourceTypeEnum convert(String source) {
            return ResourceTypeEnum.parse(source);
        }
    }

    @WritingConverter
    private static class ResTypeStringConverter implements Converter<ResTypeEnum, String> {
        @Override
        public String convert(ResTypeEnum source) {
            return source.name();
        }
    }

    @ReadingConverter
    private static class StringResTypeConverter implements Converter<String, ResTypeEnum> {
        @Override
        public ResTypeEnum convert(String source) {
            return ResTypeEnum.parse(source);
        }
    }

    @WritingConverter
    private static class StudyActionStringConverter implements Converter<StudyActionEnum, String> {
        @Override
        public String convert(StudyActionEnum source) {
            return source.name();
        }
    }

    @ReadingConverter
    private static class StringStudyActionConverter implements Converter<String, StudyActionEnum> {
        @Override
        public StudyActionEnum convert(String source) {
            return StudyActionEnum.parse(source);
        }
    }

    @WritingConverter
    private static class StudyCodeStringConverter implements Converter<StudyCodeEnum, String> {
        @Override
        public String convert(StudyCodeEnum source) {
            return source.name();
        }
    }

    @ReadingConverter
    private static class StringStudyCodeConverter implements Converter<String, StudyCodeEnum> {
        @Override
        public StudyCodeEnum convert(String source) {
            return StudyCodeEnum.parse(source);
        }
    }

    // endregion
}
