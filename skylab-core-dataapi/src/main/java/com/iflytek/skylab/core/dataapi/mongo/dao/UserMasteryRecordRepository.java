package com.iflytek.skylab.core.dataapi.mongo.dao;

import com.iflytek.skylab.core.dataapi.mongo.DataApiMongoRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.query.UpdateDefinition;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * UserMasteryRecord 用户画像 mongodb仓库
 */
@Repository
public interface UserMasteryRecordRepository extends DataApiMongoRepository<UserMasteryRecord, String> {


    List<UserMasteryRecord> search(Criteria criteria, String userId);

    boolean exists(Criteria criteria, String userId);

    List<UserMasteryRecord> findAllByCursor(Criteria criteria, String userId);

    long searchCount(Criteria criteria, String userId);

    UserMasteryRecord findAndModify(Query query, UpdateDefinition update, FindAndModifyOptions options, String userId);

    void upsertMasterDataBatch(List<Pair<Query, Update>> updates, String userId);

    UserMasteryRecord save(UserMasteryRecord userMasteryRecord, String userId);

    void saveBatch(List<UserMasteryRecord> userMasteryRecordList, String userId);


}
