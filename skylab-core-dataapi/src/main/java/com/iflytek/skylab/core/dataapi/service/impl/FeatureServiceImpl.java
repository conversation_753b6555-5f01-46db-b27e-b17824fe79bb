package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.lang.Assert;
import com.iflytek.cog2.feaflow.sdk.ZionClient;
import com.iflytek.cog2.feaflow.sdk.data.ZionDictModel;
import com.iflytek.cog2.feaflow.sdk.data.ZionRequest;
import com.iflytek.cog2.feaflow.sdk.data.ZionRequestItem;
import com.iflytek.cog2.feaflow.sdk.data.ZionResponse;
import com.iflytek.cog2.feaflow.sdk.properties.ZionProperties;
import com.iflytek.skylab.core.dataapi.configuration.FeatureRedisCacheProperties;
import com.iflytek.skylab.core.dataapi.data.FeatureData;
import com.iflytek.skylab.core.dataapi.data.FeatureDataItem;
import com.iflytek.skylab.core.dataapi.data.FeatureQuery;
import com.iflytek.skylab.core.dataapi.data.FeatureQueryItem;
import com.iflytek.skylab.core.dataapi.exception.FeatureQueryException;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.redis.FeatureRedisCacheService;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/***
 * 特征服务实现
 *
 * TODO: 增加缓存
 *
 * <AUTHOR> 2022年03月01日09:52:52
 */
@Slf4j
public class FeatureServiceImpl implements FeatureService {

    public static final String GRAPH_VERSION = "graph_version";
    private final ZionClient zionClient;

    private final ZionProperties zionProperties;


    private final FeatureRedisCacheProperties featureRedisCacheProperties;
    private final Set<String> cacheFeatureNames;

    @Autowired(required = false)
    private FeatureRedisCacheService cacheService;

    /**
     * 构造方法，初始化特征服务实现。
     *
     * @param zionClient                  Zion特征查询客户端
     * @param zionProperties              Zion配置
     * @param featureRedisCacheProperties         Redis缓存服务
     * @param featureRedisCacheProperties 缓存相关配置
     */
    public FeatureServiceImpl(ZionClient zionClient, ZionProperties zionProperties,

                              FeatureRedisCacheProperties featureRedisCacheProperties) {
        this.zionClient = zionClient;
        this.zionProperties = zionProperties;

        this.featureRedisCacheProperties = featureRedisCacheProperties;
        Map<String, Long> featureExpire = featureRedisCacheProperties.getExpire().getFeatureExpireSecond();
        if (featureExpire != null) {
            this.cacheFeatureNames = featureExpire.keySet();
        } else {
            this.cacheFeatureNames = Collections.emptySet();
        }
    }

    // 参数校验抽取为私有方法，减少重复

    /**
     * 校验特征查询参数的合法性。
     *
     * @param traceId      调用链追踪ID
     * @param featureQuery 特征查询对象
     */
    private void validateQueryParams(String traceId, FeatureQuery featureQuery) {
        Assert.notBlank(traceId, "traceId must not Blank");
        Assert.notNull(featureQuery, "featureQuery must not Blank");
        Assert.notEmpty(featureQuery.getItems(), "featureQuery.Items is empty");
    }

    // 日志输出工具，减少重复

    /**
     * 便捷的debug日志输出方法。
     *
     * @param format 日志格式
     * @param args   参数
     */
    private void debugLog(String format, Object... args) {
        if (log.isDebugEnabled()) {
            log.debug(format, args);
        }
    }

    /**
     * 直接通过ES（Zion）查询特征数据，不走缓存。
     *
     * @param traceId      调用链追踪ID
     * @param featureQuery 特征查询对象
     * @return 查询结果
     * @throws FeatureQueryException 查询异常
     */
    public FeatureData queryES(String traceId, FeatureQuery featureQuery) {
        debugLog("traceId= {};feaQuery= {}", traceId, featureQuery);
        validateQueryParams(traceId, featureQuery);

        List<ZionRequestItem> items = new ArrayList<>();
        for (FeatureQueryItem feaQueryItem : featureQuery.getItems()) {
            // 为每个参数添加graph_version，使特征查询支持图谱多版本
            List<Map<String, String>> params = feaQueryItem.getParams();
            params.forEach(map -> map.put(GRAPH_VERSION, feaQueryItem.getGraphVersion()));

            ZionRequestItem zionRequestItem = ZionRequestItem.builder().featureName(feaQueryItem.getFeatureName())
                    .featureVersion(feaQueryItem.getFeatureVersion())
                    .graphVersion(feaQueryItem.getGraphVersion())
                    .params(params).build();
            items.add(zionRequestItem);
        }

        // TODO 这里暂时丢弃 传入的 DataApiItem、DictRowKey，过滤引擎传入的写死的值，依赖于zion的默认配置
        // TODO 后期最好引擎也对应改下，改成按需传入，这里就不需要丢弃了
        ZionRequest zionRequest = ZionRequest.builder().traceId(traceId)
                // .dataApiItem(featureQuery.getDataApiItem())
                // .dictRowKey(featureQuery.getDictRowKey())
                .items(items).build();

        ZionResponse zionResponse = zionClient.queryFeature(zionRequest);

        if (zionResponse.getCode() != 0) {
            throw new FeatureQueryException(zionResponse.getCode(), zionResponse.getMessage());
        }

        FeatureData featureData = new FeatureData();
        zionResponse.getItems().forEach(x -> featureData.getItems().add(new FeatureDataItem(x.getFeatureName(), x.getResults())));

        log.debug("FeatureData= {}", featureData);
        return featureData;
    }

    /**
     * <p>
     * 查询特征数据，优先走缓存，未命中再查ES。
     *
     * @param traceId      调用链追踪ID
     * @param featureQuery 特征查询对象
     * @return 查询结果
     */
    @Override
    @SkylineMetric(value = "DataHub-2.查询特征数据", meterProviders = SkylabDataApiMeterProvider.class)
    public FeatureData query(String traceId, FeatureQuery featureQuery) {
        long startTime = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug("【FeatureQuery-入口】featureQuery={}", featureQuery);
        }

        // 总开关关闭，全部走queryES
        if (!featureRedisCacheProperties.isEnabled()) {
            log.info("【FeatureQuery-分流】redis cache closed ");
            FeatureData featureData = queryES(traceId, featureQuery);
            log.info("【FeatureQuery-完成】queryES cost={}ms", System.currentTimeMillis() - startTime);
            return featureData;
        }

        // 优化：使用Stream API分流items，预分配容量
        long splitStart = System.currentTimeMillis();
        List<FeatureQueryItem> items = featureQuery.getItems();
        int totalSize = items.size();

        // 预分配容量，避免动态扩容
        List<FeatureQueryItem> cacheItems = new ArrayList<>(totalSize);
        List<FeatureQueryItem> noCacheItems = new ArrayList<>(totalSize);

        // 使用Stream API优化分流逻辑
        items.forEach(item -> {
            if (cacheFeatureNames.contains(item.getFeatureName())) {
                cacheItems.add(item);
            } else {
                noCacheItems.add(item);
            }
        });

        long splitCost = System.currentTimeMillis() - splitStart;
        log.info("【FeatureQuery-分流】cacheItemsCount={}, noCacheItemsCount={}, splitCost={}ms",
                cacheItems.size(), noCacheItems.size(), splitCost);

        // 合并结果（并发处理）
        FeatureData result = new FeatureData();
        long cacheQueryCost = 0, esQueryCost = 0;
        ExecutorService executor = cacheService.getExecutorService();
        Future<FeatureData> cacheFuture = null;
        Future<FeatureData> esFuture = null;
        try {
            if (!cacheItems.isEmpty()) {
                FeatureQuery cacheQuery = new FeatureQuery();
                cacheQuery.setItems(cacheItems);
                cacheFuture = executor.submit(() -> queryWithCache(traceId, cacheQuery));
            }
            if (!noCacheItems.isEmpty()) {
                FeatureQuery noCacheQuery = new FeatureQuery();
                noCacheQuery.setItems(noCacheItems);
                esFuture = executor.submit(() -> queryES(traceId, noCacheQuery));
            }
            if (cacheFuture != null) {
                long cacheQueryStart = System.currentTimeMillis();
                FeatureData cacheResult = cacheFuture.get();
                cacheQueryCost = System.currentTimeMillis() - cacheQueryStart;
                if (cacheResult.getItems() != null) {
                    result.getItems().addAll(cacheResult.getItems());
                }
                log.info("【FeatureQuery-缓存查询】cacheItemsCount={}, cacheQueryCost={}ms", cacheItems.size(), cacheQueryCost);
            }
            if (esFuture != null) {
                long esQueryStart = System.currentTimeMillis();
                FeatureData noCacheResult = esFuture.get();
                esQueryCost = System.currentTimeMillis() - esQueryStart;
                if (noCacheResult != null && noCacheResult.getItems() != null) {
                    result.getItems().addAll(noCacheResult.getItems());
                }
                log.info("【FeatureQuery-ES查询】noCacheItemsCount={}, esQueryCost={}ms", noCacheItems.size(), esQueryCost);
            }
        } catch (Exception e) {
            long totalCost = System.currentTimeMillis() - startTime;
            log.error("【FeatureQuery-并发查询异常】totalCost={}ms, error={}", totalCost, e.getMessage(), e);
            throw new FeatureQueryException(10001, "特征查询并发异常: " + e.getMessage());
        }
        long totalCost = System.currentTimeMillis() - startTime;
        log.info("【FeatureQuery-完成】totalCost={}ms, cacheQueryCost={}ms, esQueryCost={}ms, resultCount={}",
                totalCost, cacheQueryCost, esQueryCost, result.getItems().size());
        debugLog("traceId= {};FeatureData= {}", traceId, result);
        return result;
    }

    // 拆分原有缓存逻辑为私有方法，便于复用

    /**
     * 查询特征数据，优先查缓存，未命中再查ES并回写缓存。
     *
     * @param traceId      调用链追踪ID
     * @param featureQuery 特征查询对象
     * @return 查询结果
     */
    private FeatureData queryWithCache(String traceId, FeatureQuery featureQuery) {
        long startTime = System.currentTimeMillis();
        debugLog("【FeatureQuery】开始查询特征数据, featureCount={}, totalParams={}",
                featureQuery.getItems().size(),
                featureQuery.getItems().stream().mapToInt(item -> item.getParams().size()).sum());
        validateQueryParams(traceId, featureQuery);
        try {
            // 1. 获取字典
            ZionDictModel zionDictModel = getDictModel();
            // 2. 生成rowkey及映射
            RowkeyMapping mapping = buildRowkeysAndMappingV3(featureQuery.getItems(), zionDictModel);
            // 3. 批量查缓存
            Map<String, String> cacheResult = batchGetCache(mapping.rowkeys);
            if (log.isDebugEnabled()) {
                log.debug("【FeatureQuery】redis cacheResult={}", cacheResult);
            }
            List<String> missRowkeys = getMissRowkeysOptimized(mapping.rowkeys, cacheResult);
            // 4. 查ES（zionClient），组装结果
            Map<String, String> esResultMap;
            if (missRowkeys.isEmpty()) {
                esResultMap = new HashMap<>();
            } else {
                esResultMap = queryAndCacheMissFromES(traceId, featureQuery.getItems(), zionDictModel, missRowkeys);
            }
            // 5. 合并所有特征的结果
            FeatureData featureData = mergeFeatureDataOptimized(mapping.rowkeys, cacheResult, esResultMap, mapping.rowkey2Feature, mapping.rowkey2Params);
            logQueryPerformance(startTime, mapping, cacheResult, missRowkeys, esResultMap, featureData);
            return featureData;
        } catch (FeatureQueryException e) {
            throw e;
        } catch (Exception e) {
            long totalCost = System.currentTimeMillis() - startTime;
            log.error("【FeatureQuery】查询异常,  totalCost={}ms, error={}", totalCost, e.getMessage(), e);
            //常量
            throw new FeatureQueryException(10000, "特征查询异常: " + e.getMessage());
        }
    }

    // 拆分获取字典方法

    /**
     * 获取特征字典模型。
     * //     * @param traceId 调用链追踪ID
     *
     * @return 字典模型
     */
    private ZionDictModel getDictModel() {
        long dictStartTime = System.currentTimeMillis();
        ZionDictModel zionDictModel = zionClient.queryZionDictModel(zionProperties.getDictDefaultRowKey());
        debugLog("【FeatureQuery】获取特征字典完成, dictCount={}, cost={}ms",
                zionDictModel.getDictInfoMap().size(), System.currentTimeMillis() - dictStartTime);
        return zionDictModel;
    }

    // 封装rowkey映射结构体

    /**
     * rowkey映射结构体，包含rowkey列表、rowkey到特征名、参数的映射。
     */
    private static class RowkeyMapping {
        List<String> rowkeys;
        Map<String, String> rowkey2Feature;
        Map<String, Map<String, String>> rowkey2Params;

        RowkeyMapping(List<String> rowkeys, Map<String, String> rowkey2Feature, Map<String, Map<String, String>> rowkey2Params) {
            this.rowkeys = rowkeys;
            this.rowkey2Feature = rowkey2Feature;
            this.rowkey2Params = rowkey2Params;
        }
    }

    // 优化rowkey生成方法，使用Stream API和预分配容量

    /**
     * 根据特征查询项和字典，批量生成rowkey及其映射。
     * 优化版本：使用Stream API和预分配容量提升性能
     *
     * @param queryItems 查询项列表
     * @param dictModel  字典模型
     * @return rowkey映射结构体
     */
    private RowkeyMapping buildRowkeysAndMappingV3(List<FeatureQueryItem> queryItems, ZionDictModel dictModel) {
        // 预计算总容量，避免动态扩容
        int totalParams = queryItems.stream()
                .mapToInt(item -> item.getParams().size())
                .sum();

        List<String> rowkeys = new ArrayList<>(totalParams);
        Map<String, String> rowkey2Feature = new HashMap<>(totalParams);
        Map<String, Map<String, String>> rowkey2Params = new HashMap<>(totalParams);

        // 使用Stream API优化嵌套循环
        queryItems.forEach(feaQueryItem -> {
            String featureName = feaQueryItem.getFeatureName();
            List<Map<String, String>> params = feaQueryItem.getParams();

            params.forEach(param -> {
                // 直接修改原参数，避免创建新对象
                param.put(GRAPH_VERSION, feaQueryItem.getGraphVersion());
                String rowkey = buildRedisFeatureRowkey(featureName, param, dictModel);
                rowkeys.add(rowkey);
                rowkey2Feature.put(rowkey, featureName);
                // 使用HashMap构造函数避免重复创建
                rowkey2Params.put(rowkey, new HashMap<>(param));
            });
        });

        return new RowkeyMapping(rowkeys, rowkey2Feature, rowkey2Params);
    }

    // 拆分性能日志

    /**
     * 查询性能日志输出。
     *
     * @param startTime   查询起始时间
     * @param mapping     rowkey映射
     * @param cacheResult 缓存命中结果
     * @param missRowkeys 未命中rowkey
     * @param esResultMap ES查询结果
     * @param featureData 合并后的特征数据
     */
    private void logQueryPerformance(long startTime, RowkeyMapping mapping, Map<String, String> cacheResult, List<String> missRowkeys, Map<String, String> esResultMap, FeatureData featureData) {
        long totalCost = System.currentTimeMillis() - startTime;
        log.info("【FeatureQuery】查询完成, totalCost={}ms, rowkeyCount={}, cacheHit={}, missCount={}, esResultCount={}, resultCount={}",
                totalCost, mapping.rowkeys.size(), cacheResult.size(), missRowkeys.size(), esResultMap.size(), featureData.getItems().size());
    }

    /**
     * 批量查询缓存。
     *
     * @param rowkeys rowkey列表
     * @return 缓存命中结果
     */
    private Map<String, String> batchGetCache(List<String> rowkeys) {
        Map<String, String> cacheResult = new HashMap<>();
        try {
            if (rowkeys == null || rowkeys.isEmpty()) {
                return cacheResult;
            }

            // 只查一次redis，统计分特征命中率
            cacheResult = cacheService.batchGetBinary(rowkeys);
        } catch (Exception e) {
            log.error("【CacheQuery-优化】Redis缓存批量查询异常, errMsg={}", e.getMessage(), e);
        }
        return cacheResult;
    }

    /**
     * 获取未命中缓存的rowkey列表 - 优化版本。
     * 使用Stream API替代for循环，提升性能
     *
     * @param rowkeys     rowkey列表
     * @param cacheResult 缓存结果
     * @return 未命中rowkey列表
     */
    private List<String> getMissRowkeysOptimized(List<String> rowkeys, Map<String, String> cacheResult) {
        return rowkeys.stream()
                .filter(rowkey -> !cacheResult.containsKey(rowkey))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 查询ES并异步写缓存（含空对象）。
     *
     * @param traceId     调用链追踪ID
     * @param queryItems  查询项列表
     * @param dictModel   字典模型
     * @param missRowkeys 未命中rowkey列表
     * @return ES查询结果
     */
    private Map<String, String> queryAndCacheMissFromES(String traceId, List<FeatureQueryItem> queryItems, ZionDictModel dictModel, List<String> missRowkeys) {
        Map<String, String> esResultMap = new HashMap<>();
        if (missRowkeys == null || missRowkeys.isEmpty()) {
            return esResultMap;
        }
        // 1. 提升missRowkeys查找效率
        Set<String> missRowkeySet = new HashSet<>(missRowkeys);

        // 2. 预先构建rowkey到featureName和param的映射
        Map<String, String> rowkey2Feature = new HashMap<>();
        Map<String, Map<String, String>> rowkey2Param = new HashMap<>();
        queryItems.forEach(item -> {
            String featureName = item.getFeatureName();
            item.getParams().forEach(param -> {
                String rowkey = buildRedisFeatureRowkey(featureName, param, dictModel);
                if (missRowkeySet.contains(rowkey)) {
                    rowkey2Feature.put(rowkey, featureName);
                    rowkey2Param.put(rowkey, param);
                }
            });
        });

        // 3. 按特征名分组miss params
        Map<String, List<Map<String, String>>> feature2MissParams = new HashMap<>();
        missRowkeySet.forEach(rowkey -> {
            String featureName = rowkey2Feature.get(rowkey);
            Map<String, String> param = rowkey2Param.get(rowkey);
            if (featureName != null && param != null) {
                feature2MissParams.computeIfAbsent(featureName, k -> new ArrayList<>()).add(param);
            }
        });

        // 4. 构建missItems
        List<ZionRequestItem> missItems = new ArrayList<>();
        queryItems.forEach(item -> {
            List<Map<String, String>> missParams = feature2MissParams.get(item.getFeatureName());
            if (missParams != null && !missParams.isEmpty()) {
                missItems.add(ZionRequestItem.builder()
                        .featureName(item.getFeatureName())
                        .featureVersion(item.getFeatureVersion())
                        .graphVersion(item.getGraphVersion())
                        .params(missParams)
                        .build());
            }
        });
        if (missItems.isEmpty()) {
            return esResultMap;
        }

        // 5. 查询ES
        ZionRequest missRequest = ZionRequest.builder().traceId(traceId).items(missItems).build();
        ZionResponse missResponse = zionClient.queryFeature(missRequest);

        if (missResponse.getCode() != 0) {
            log.error("【ESQuery】Zion查询失败, code={}, message={}", missResponse.getCode(), missResponse.getMessage());
            throw new FeatureQueryException(missResponse.getCode(), missResponse.getMessage());
        }

        // 6. 组装ES结果并序列化
        missResponse.getItems().stream()
                .filter(zionResponseItem -> zionResponseItem.getResults() != null)
                .forEach(zionResponseItem -> {
                    String featureName = zionResponseItem.getFeatureName();
                    zionResponseItem.getResults().forEach(param -> {
                        String rowkey = buildRedisFeatureRowkey(featureName, param, dictModel);
                        esResultMap.put(rowkey, param.get(featureName));
                    });
                });

        // 7. 异步写入缓存（含空对象）
        feature2MissParams.forEach((featureName, value) -> {
            Map<String, String> toCache = new HashMap<>();
            for (Map<String, String> param : value) {
                String rowkey = buildRedisFeatureRowkey(featureName, param, dictModel);
                String result = esResultMap.get(rowkey);
                toCache.put(rowkey, result != null ? result : FeatureRedisCacheService.EMPTY_MARK);
            }
            cacheService.asyncBatchSet(featureName, toCache, traceId);
        });

        return esResultMap;
    }

    /**
     * 合并所有特征的结果，组装为FeatureData - 优化版本。
     * 使用Stream API和预分配容量提升性能
     *
     * @param rowkeys        rowkey列表
     * @param cacheResult    缓存结果
     * @param esResultMap    ES结果
     * @param rowkey2Feature rowkey到特征名映射
     * @param rowkey2Params  rowkey到参数映射
     * @return 合并后的特征数据
     */
    private FeatureData mergeFeatureDataOptimized(List<String> rowkeys, Map<String, String> cacheResult, Map<String, String> esResultMap,
                                                  Map<String, String> rowkey2Feature, Map<String, Map<String, String>> rowkey2Params) {
        FeatureData featureData = new FeatureData();

        // 使用Stream API按特征分组处理数据
        Map<String, List<Map<String, String>>> feature2Values = rowkeys.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                        rowkey2Feature::get,
                        java.util.stream.Collectors.mapping(
                                rowkey -> {
                                    String value = cacheResult.get(rowkey);
                                    if (value == null && esResultMap.containsKey(rowkey)) {
                                        value = esResultMap.get(rowkey);
                                    }

                                    Map<String, String> originalParams = rowkey2Params.get(rowkey);
                                    if (originalParams != null) {
                                        Map<String, String> valueMap = new HashMap<>(originalParams);
                                        String featureName = rowkey2Feature.get(rowkey);
                                        valueMap.put(featureName, null);

                                        if (value != null && !cacheService.isEmptyMark(value)) {
                                            valueMap.put(featureName, value);
                                        }

                                        return valueMap;
                                    }
                                    return null;
                                },
                                java.util.stream.Collectors.toList()
                        )
                ));

        // 按特征名创建FeatureDataItem，确保所有特征都有结果
        feature2Values.forEach((featureName, values) -> {
            // 过滤掉null值
            values = values.stream()
                    .filter(Objects::nonNull)
                    .collect(java.util.stream.Collectors.toList());
            FeatureDataItem item = new FeatureDataItem(featureName, values);
            featureData.getItems().add(item);
        });

        return featureData;
    }


    /**
     * 查询特征数据字典。
     *
     * @param traceId    调用链追踪ID
     * @param dictRowKey 字典版本
     * @return 字典模型
     */
    @Override
    @SkylineMetric(value = "DataHub-2.查询特征数据字典", meterProviders = SkylabDataApiMeterProvider.class)
    public ZionDictModel querySchema(String traceId, String dictRowKey) {
        long startTime = System.currentTimeMillis();

        if (log.isDebugEnabled()) {
            log.debug("【SchemaQuery】开始查询特征字典, dictRowKey={}", dictRowKey);
        }

        try {
            ZionDictModel result = zionClient.queryZionDictModel(dictRowKey);
            long totalCost = System.currentTimeMillis() - startTime;

            if (log.isDebugEnabled()) {
                log.debug("【SchemaQuery】特征字典查询完成, dictRowKey={}, dictItemCount={}, cost={}ms",
                        dictRowKey, result != null ? result.getDictInfoMap().size() : 0, totalCost);
            }

            return result;
        } catch (Exception e) {
            long totalCost = System.currentTimeMillis() - startTime;
            log.error("【SchemaQuery】特征字典查询异常, dictRowKey={}, cost={}ms, error={}",
                    dictRowKey, totalCost, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 判断特征是否需要后处理。
     *
     * @param traceId     调用链追踪ID
     * @param featureName 特征名
     * @return 是否需要后处理
     */
    @Override
    public boolean needPostProcess(String traceId, String featureName) {
        return zionProperties.getPostProcessFeaNames().contains(featureName);
    }

    /**
     * 清理所有以指定featureName前缀开头的缓存。
     *
     * @return 是否清理成功
     */
    @Override
    public Long clearRedisCache() {
        List<String> featureNamePrefixs = featureRedisCacheProperties.getClearFeatureName();
        log.info("[clearRedisCache] 开始清理Redis缓存, 待清理特征名前缀: {}", featureNamePrefixs);
        long totalDeleted = 0L;
        for (String featureNamePrefix : featureNamePrefixs) {
            log.info("[clearRedisCache] 正在清理特征名前缀: {}", featureNamePrefix);
            totalDeleted += cacheService.clearByFeaturePrefix(featureNamePrefix);
        }
        log.info("[clearRedisCache] Redis缓存清理完成, 已清理特征名前缀总数: {}, 删除key总数: {}", featureNamePrefixs.size(), totalDeleted);
        return totalDeleted;
    }


    /**
     * 根据特征查询项和参数，结合特征字典，生成rowkey。
     *
     * @param featureName 特征名
     * @param param       参数
     * @param dictModel   字典模型
     * @return rowkey
     */
    private String buildRedisFeatureRowkey(String featureName, Map<String, String> param, com.iflytek.cog2.feaflow.sdk.data.ZionDictModel dictModel) {
        com.iflytek.cog2.feaflow.sdk.data.ZionDictItem dictItem = dictModel.getDictInfoMap().get(featureName);
        if (dictItem == null) {
            throw new IllegalArgumentException("未找到特征 " + featureName + " 的字典配置");
        }
        String buildQueryRowKey = dictItem.buildQueryRowKey(param);
        return cacheService.buildCacheKey(featureName, buildQueryRowKey);
    }

}


