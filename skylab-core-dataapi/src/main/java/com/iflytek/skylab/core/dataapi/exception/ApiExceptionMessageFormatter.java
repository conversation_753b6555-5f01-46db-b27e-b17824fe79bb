package com.iflytek.skylab.core.dataapi.exception;

import com.iflytek.skyline.common.api.ApiResponseHeader;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import skynet.boot.exception.SkynetException;
import skynet.boot.exception.message.DefaultExceptionMessageFormatter;
import skynet.boot.exception.message.ErrorMessage;

/**
 * 异常格式化
 *
 * <AUTHOR>
 * @date 2022/6/14 14:31
 */
public class ApiExceptionMessageFormatter extends DefaultExceptionMessageFormatter {

    @Override
    public ErrorMessage format(Throwable e, int code) {
        ErrorMessage errorMessage = super.format(e, code);
        ApiErrorResponseResponse response = new ApiErrorResponseResponse();
        ApiResponseHeader apiResponseHeader = new ApiResponseHeader(
                errorMessage.getTraceId(), errorMessage.getCode(), errorMessage.getMessage());
        response.setHeader(apiResponseHeader);
        return response;
    }

    /**
     * 转换 Skynet 异常
     */
    @Override
    public ErrorMessage format(SkynetException e) {
        return this.format(e, e.getCode());
    }

    public static class ApiErrorResponseResponse extends DispatchApiResponse implements ErrorMessage {

        /**
         * 返回错误码
         *
         * @return
         */
        @Override
        public int getCode() {
            return super.getHeader().getCode();
        }

        /**
         * 返回错误信息
         *
         * @return
         */
        @Override
        public String getMessage() {
            return super.getHeader().getMessage();
        }
    }
}
