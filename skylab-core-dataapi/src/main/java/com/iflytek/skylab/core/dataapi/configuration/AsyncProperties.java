package com.iflytek.skylab.core.dataapi.configuration;

import lombok.Getter;
import lombok.Setter;

/**
 * Async注解 相关线程池属性
 *
 * <AUTHOR>
 * @date 2022/3/21 19:15
 */
@Getter
@Setter
public class AsyncProperties {
    /**
     * 核心线程数
     */
    private Integer corePoolSize = 32;

    /**
     * 最大线程数
     */
    private Integer maximumPoolSize = 128;

    /**
     * 空闲非核心线程存活时间(ms)
     */
    private Long keepAliveMilliseconds = 0L;

    /**
     * 阻塞队列容量
     */
    private Integer queueCapacity = 1024;

}
