package com.iflytek.skylab.core.dataapi.mongo.entity;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;


/**
 * 规划结果数据
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
@Document(collection = "xxj_ai_teacher_plan_result")
public class PlanResultEntity  {

    @Id
    private String id;

    /**
     * 业务方功能（服务层定义）
     */
    @Field(name = "biz_action")
    private String bizAction;

    /**
     * 学习场景（服务层定义）,默认=SYNC_TUTORING
     */
    @Field(name = "study_code")
    private String studyCode;

    /**
     * 用户id
     */
    @Field(name = "user_id")
    private String userId;

    /**
     * 更新时间
     */
    @Field(name = "update_time")
    private long updateTime;

    /**
     * 规划排序 1 2 3 4优先级
     */
    @Field(name = "order")
    private int order;

    /**
     * 点ID
     */
    @Field(name = "node_id")
    private String nodeId;

    /**
     * 点类型
     */
    @Field(name = "node_type")
    private String  nodeType ;

    /**
     * 点属性 中心点 or 延展点  枚举值:
     * CENTRAL_POINT("中心点","CENTRAL_POINT"),
     * EXTENSION_POINT("延展点","EXTENSION_POINT"),
     * OTHERS("其他点","OTHERS");
     */
    @Field(name = "node_attribute")
    private String nodeAttribute;

    /**
     * 学习行为，枚举值：
     * LEARN("学","LEARN"),
     * PRACTICE("练","PRACTICE"),
     * OTHERS("其他学习行为","OTHERS");
     */
    @Field(name = "learn_behavior")
    private String learnBehavior;

    /**
     * 学习时长 单位min
     */
    @Field(name = "learn_times")
    private int learnTimes;

    /**
     * 锚点所在的目录
     * 规划范围目录列表，如: 章、节、小节、小小节、课时
     * 以目录维度存储规划结果
     */
    @Field(name = "catalog_id")
    private String catalogId;

    /**
     * 创建时间
     */
    @Field(name = "create_time")
    private Instant createTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @Field(name = "is_delete")
    private Integer isDelete;
}
