//package com.iflytek.skylab.core.dataapi.data;
//
//import com.iflytek.skylab.core.constant.CatalogTypeEnum;
//import com.iflytek.skylab.core.constant.FlagEnum;
//import com.iflytek.skylab.core.constant.StudyCodeEnum;
//import lombok.AccessLevel;
//import lombok.Getter;
import lombok.Setter;
//import lombok.Setter;
//import org.springframework.data.mongodb.core.query.Criteria;
//
//import javax.validation.constraints.NotBlank;
//import javax.validation.constraints.NotEmpty;
//import javax.validation.constraints.NotNull;
//import java.util.List;
//
///**
// * 应学点查询条件 和罗俊确认，废弃业务字段 查询 用户指定目录下的 应学点
// *
// * <AUTHOR>
// */
//@Getter
//@Setter
//public class ShouldLearnPointQuery extends AbstractQuery {
//
//    /**
//     * 用户id
//     */
//    @NotBlank
//    private String userId;
//
//    /**
//     * 查询应学点，固定为true
//     */
//    @Setter(AccessLevel.PRIVATE)
//    private Boolean shouldFlag = FlagEnum.TRUE.boolValue();
//
//    /**
//     * 学习场景
//     */
//    private StudyCodeEnum studyCode;
//
//    /**
//     * 目录类型
//     */
//    private CatalogTypeEnum catalogType;
//
//    /**
//     * 目录ID
//     */
//    @NotEmpty
//    private List<String> catalogIds;
//
//
//    @Override
//    public Criteria buildCriteria() {
//        Criteria criteria =  Criteria.where("user_id").is(userId)
//                .and("should_flag").is(shouldFlag)
//                .and("catalog_id").in(catalogIds);
//
//        if (catalogType != null) {
//            criteria.and("catalog_type").is(catalogType);
//        }
//
//        if (studyCode != null) {
//            criteria.and("study_code").is(studyCode);
//        }
//
//        return criteria;
//    }
//
//    // @Override
//    // public String toString() {
//    //     return super.toString();
//    // }
//}
