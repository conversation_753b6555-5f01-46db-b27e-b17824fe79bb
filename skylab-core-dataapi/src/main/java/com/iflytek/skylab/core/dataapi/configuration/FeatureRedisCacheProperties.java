package com.iflytek.skylab.core.dataapi.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ConfigurationProperties(prefix = "skylab.feature.redis")
public class FeatureRedisCacheProperties {

    /**
     * 过期时间配置
     */
    private Expire expire = new Expire();

    private List<String> clearFeatureName;

    /**
     * 批量操作配置
     */
    private Batch batch = new Batch();

    /**
     * 内存监控配置
     */
    private Memory memory = new Memory();

    /**
     * 线程池配置
     */
    private ThreadPool threadPool = new ThreadPool();

    /**
     * 是否启用特征缓存
     */
    private boolean enabled = false;

    /**
     * 获取特征的过期时间
     */
    public Long getExpire(String featureName) {
        if (expire.getFeatureExpireSecond() != null && expire.getFeatureExpireSecond().containsKey(featureName)) {
            return expire.getFeatureExpireSecond().get(featureName);
        }
        return null;
    }


    /**
     * 过期时间配置
     */
    @Getter
@Setter
    public static class Expire {
        /**
         * 各特征自定义失效时间（second）
         */
        private Map<String, Long> featureExpireSecond;

    }

    /**
     * 批量操作配置
     */
    @Getter
@Setter
    public static class Batch {
        /**
         * 批量操作的分批大小
         */
        private int size = 500;

    }

    /**
     * 内存监控配置
     */
    @Getter
@Setter
    public static class Memory {
        /**
         * 是否启用内存监控
         */
        private boolean monitorEnabled = true;
        /**
         * 内存使用率阈值，超过此值跳过写入
         */
        private double threshold = 0.6;
    }

    /**
     * 线程池配置
     */
    @Getter
@Setter
    public static class ThreadPool {
        /**
         * 核心线程数
         */
        private int corePoolSize = 64;
        /**
         * 最大线程数
         */
        private int maxPoolSize = 1000;
        /**
         * 线程空闲时间，单位毫秒
         */
        private long keepAliveTime = 6000;
        /**
         * 队列容量
         */
        private int queueCapacity = 1024;
        /**
         * 线程名前缀
         */
        private String threadNamePrefix = "redis-fea-pool-";
        /**
         * 任务最长运行时间，单位毫秒
         */
        private long maxExecutionTime = 500;

    }

    /**
     * 热key配置
     */
    @Getter
@Setter
    public static class HotKey {

        /**
         * 热key本地缓存失效时间，单位分钟
         */
        private int localExpireMinutes = 30;
        /**
         * 热key LRU缓存容量，最大支持多少个热key
         */
        private int lruSize = 500_000;
        /**
         * 热key判定阈值，单个key访问次数超过此值即为热key
         */
//        private int threshold = 200;
        private int threshold = 200;
        /**
         * 热key续期降频，每N次访问才续期一次
         */
        private int expireUpdateInterval = 100;
//        private int expireUpdateInterval = 5;
    }
} 