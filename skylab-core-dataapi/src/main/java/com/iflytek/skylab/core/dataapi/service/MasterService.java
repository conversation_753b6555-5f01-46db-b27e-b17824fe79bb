package com.iflytek.skylab.core.dataapi.service;

import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.domain.MasterInfo;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户画像 数据工具入口
 */
@Validated
public interface MasterService {


    /***
     * 是否存在画像
     * @param traceId  跟踪id
     * @param masterQuery  画像查询对象
     * @return
     */
    boolean exists(@NotBlank String traceId, @NotNull @Valid MasterQuery masterQuery);

    /**
     * 保存UserMasteryRecord,
     * 按场景（将非OS场景复制到OS场景）进行数据迁移使用，废弃接口
     * @param userMasteryRecordList
     * @return
     */
    @Deprecated
    void batchSaveUserMasteryRecord(@NotNull List<UserMasteryRecord> userMasteryRecordList, @NotNull String userId);

    /**
     * 画像查询
     *
     * @param traceId     跟踪id
     * @param masterQuery 画像查询对象
     * @return {@link MasterData}
     */
    MasterData queryMasterData(@NotBlank String traceId, @NotNull @Valid MasterQuery masterQuery);

    /**
     * 分页画像查询
     *
     * @param traceId     跟踪id
     * @param masterQuery 画像查询对象
     * @return {@link MasterData}
     */
    List<UserMasteryRecord> queryMasterDataBatch(@NotBlank String traceId, @NotNull @Valid MasterQuery masterQuery);

//    /**
//     * 查询 用户指定目录下的 应学点，和罗俊确认，废弃业务字段
//     * 本期版本先注释，下个版本移出相关代码
//     *
//     * @param traceId
//     * @param query
//     * @return 应学点ID
//     */
//    List<String> queryLearnPoints(@NotBlank String traceId, @NotNull @Valid ShouldLearnPointQuery query);

    /**
     * 保存小学精准学数据迁移UserMasteryRecord
     *
     * @param userId
     * @param userMasteryRecordList
     * @return
     */
    List<String> savePrimaryMigrationUserMasteryRecordAndGet(@NotBlank String userId, @NotNull List<UserMasteryRecord> userMasteryRecordList);


    /**
     * 用户画像更新
     * @param traceId
     * @param masterData
     * @return
     */
    List<UserMasteryRecord> updateMasterDataAndGet(@NotBlank String traceId, @NotNull @Valid MasterData masterData);

    /**
     * 用户画像更新
     * @param traceId
     * @param userId
     * @param masterItem
     * @return
     */
    UserMasteryRecord updateMasterDataAndGet(@NotBlank String traceId, @NotBlank String userId, @NotNull MasterItem masterItem);

    /**
     * 批量更新(会更新匹配的所有数据，暂时使用updateMasterData代替)
     *
     * @param traceId
     * @param masterData
     */
    void upsertMasterDataBatch(@NotBlank String traceId, @NotNull @Valid MasterData masterData);

    /**
     * 用户画像更新（可以操作一个用户的多条记录）
     *
     * @param traceId    跟踪id
     * @param masterData 主数据
     * @return List 更新成功的id
     */
    List<String> updateMasterData(@NotBlank String traceId, @NotNull @Valid MasterData masterData);

    /**
     * 用户画像更新
     *
     * @param traceId
     * @param userId
     * @param masterItem
     * @return
     */
    String updateMasterData(@NotBlank String traceId, @NotBlank String userId, @NotNull MasterItem masterItem);

    /**
     * 画像删除
     *
     * @param traceId 跟踪id
     * @param userId  用户id
     */
    Long deleteMasterDataByUserId(@NotBlank String traceId, @NotEmpty String userId);


    /**
     * 获取老平台画像。新平台就是同步学场景的获取画像
     *
     * @return
     */
    List<MasterInfo> computePortraitByCatalogCode(String traceId, ComputePortraitParam param);
}
