package com.iflytek.skylab.core.dataapi.mongo.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * 锚点映射关系，用于进行学情兼容
 * 索引（主键）：anchorPointId
 */
@Getter
@Setter
@Document(collection = "anchor_cast")
public class AnchorCast implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * mongo唯一标识
     */
    @Id
    private String id;

    /**
     * 原锚点id
     */
    private String anchorPointId;

    /**
     * 变化后锚点id列表。变化可包括：复制
     */
    private List<String> castAnchorPointIds;

    /**
     * 记录创建时间
     */
    @CreatedDate
    private Instant createTime;

    /**
     * 记录更新时间
     */
    @LastModifiedDate
    private Instant updateTime;
}
