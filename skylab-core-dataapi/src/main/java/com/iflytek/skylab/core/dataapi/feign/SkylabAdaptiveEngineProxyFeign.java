package com.iflytek.skylab.core.dataapi.feign;

import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/9/8 20:17
 */
@Deprecated
@FeignClient("skylab-engine-proxy")
public interface SkylabAdaptiveEngineProxyFeign {

    @PostMapping("/skylab/proxy/v1/master/diagnose")
    DispatchApiResponse diagnose(@RequestBody DispatchApiRequest apiRequest);

}
