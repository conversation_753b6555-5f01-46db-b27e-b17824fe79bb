package com.iflytek.skylab.core.dataapi.metric;

import com.iflytek.skylab.core.annotation.MetricTagField;
import com.iflytek.skylab.core.constant.EnumDescable;
import com.iflytek.skylab.core.constant.RecFuncEnum;
import com.iflytek.skylab.core.data.SkylabRequest;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.brave.metric.SkylineMetricProvider;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.List;

/**
 * SkylineMetric desc tag修改扩展
 *
 * <AUTHOR>
 * @date 2022/4/11 8:59 下午
 */
@Slf4j
@Deprecated
public class SceneInfoMetricProvider implements SkylineMetricProvider {

    private final static List<Field> METRIC_TAG_FIELD_LIST;

    static {
        METRIC_TAG_FIELD_LIST = FieldUtils.getFieldsListWithAnnotation(SceneInfo.class, MetricTagField.class);
        METRIC_TAG_FIELD_LIST.forEach(x -> x.setAccessible(true));
    }

    /**
     * 指标名称
     *
     * @return
     */
    @Override
    public String getName() {
        return "skylab.scene";
    }

    /**
     * 自定义 tags
     *
     * @param args
     * @return
     */
    @Override
    public Tags getTags(Object[] args) {
        Tags tags = Tags.empty();
        if (args != null && args.length > 0 && args[0] != null) {
            SceneInfo sceneInfo = null;
            if (args[0] instanceof DispatchApiRequest) {
                sceneInfo = ((DispatchApiRequest) args[0]).getScene(SceneInfo.class);
            } else if (args[0] instanceof SkylabRequest) {
                SkylabRequest skylabRequest = ((SkylabRequest) args[0]);
                sceneInfo = skylabRequest.getScene();
                sceneInfo.setFunctionCode(skylabRequest.getPayload().getFuncCode());
            }
            if (sceneInfo == null) {
                log.warn("sceneInfo is null");
                sceneInfo = new SceneInfo();
            }
            for (Field field : METRIC_TAG_FIELD_LIST) {
                Object value = ReflectionUtils.getField(field, sceneInfo);
                tags = tags.and(field.getName(), value == null ? "" : String.valueOf(value));
                if (field.getType().isEnum()) {
                    String desc = "";
                    EnumDescable enumDescable = (EnumDescable) value;
                    if (enumDescable != null) {
                        desc = enumDescable.getDesc();
                    }
                    tags = tags.and(field.getName() + "Desc", desc);
                }
            }
            
            //对FunctionCode 单独处理
            tags = tags.and("functionCode", sceneInfo.getFunctionCode());
            RecFuncEnum recFuncEnum = RecFuncEnum.parse(sceneInfo.getFunctionCode());
            String desc = recFuncEnum != null ? recFuncEnum.getDesc() : sceneInfo.getFunctionCode();
            tags = tags.and("functionCodeDesc", StringUtils.hasText(desc) ? desc.trim() : "");
        }
        return tags;
    }
}
