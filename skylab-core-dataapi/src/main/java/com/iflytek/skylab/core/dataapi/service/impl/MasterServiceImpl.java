package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Stopwatch;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.data.MasterFetch4CatalogParam;
import com.iflytek.skylab.core.data.MasterFetchResult;
import com.iflytek.skylab.core.dataapi.configuration.MasteryProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.feign.SkylabAdaptiveEngineProxyFeign;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.mongo.dao.UserMasteryRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.dataapi.service.MasterService;
import com.iflytek.skylab.core.dataapi.util.MasteryUtil;
import com.iflytek.skylab.core.dataapi.util.SubCollectionUtils;
import com.iflytek.skylab.core.domain.MasterInfo;
import com.iflytek.skylab.core.domain.SceneInfo;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.dispatcher.api.DispatchApiPayload;
import com.iflytek.skyline.dispatcher.api.DispatchApiRequest;
import com.iflytek.skyline.dispatcher.api.DispatchApiResponse;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.util.CollectionUtils;
import skynet.boot.logging.LoggingCost;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户画像数据访问实现
 */
@Slf4j
public class MasterServiceImpl implements MasterService {

    @Lazy
    @Autowired
    private SkylabAdaptiveEngineProxyFeign skylabAdaptiveEngineProxyFeign;

    private final UserMasteryRecordRepository userMasteryRecordRepository;

    private final MasteryProperties masteryProperties;

    public MasterServiceImpl(UserMasteryRecordRepository userMasteryRecordRepository, MasteryProperties masteryProperties) {
        this.userMasteryRecordRepository = userMasteryRecordRepository;
        this.masteryProperties = masteryProperties;
    }


    /**
     * 画像查询
     *
     * @param traceId     跟踪id
     * @param masterQuery 画像查询对象
     * @return {@link MasterData}
     */
    @Override
    @SkylineMetric(value = "DataHub-3.画像查询", meterProviders = SkylabDataApiMeterProvider.class)
    public MasterData queryMasterData(String traceId, MasterQuery masterQuery) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {};masterQuery= {}", traceId, masterQuery);
        }
        // 构造查询条件，并查询结果
        Criteria criteria = masterQuery.buildCriteria();
        if (masteryProperties.isUseDeleteFlag()){
            criteria.and("delete_flag").ne(1);
        }
        List<UserMasteryRecord> records = userMasteryRecordRepository.search(criteria, masterQuery.getUserId());

        // 全局画像为空的，赋值为单一画像
        for(UserMasteryRecord mastery : records){
            if (null == mastery.getGlobalMastery()){
                GlobalMasteryItem global = new GlobalMasteryItem();
                global.setMasterScore(mastery.getMasteryScore());
                global.setReal(mastery.getReal());
                global.setPredict(mastery.getPredict());
                global.setFusion(mastery.getFusion());
                global.setAssociativePoint(new HashMap<>());
                mastery.setGlobalMastery(global);
            }
        }

        // 将查询结果类型转换
        List<MasterItem> items = records.stream().map(UserMasteryRecord::toMasterItem).collect(Collectors.toList());

        // 20241211-过滤重复场景的数据
        items = this.filterMasterItem(items);

        return new MasterData(masterQuery.getUserId(), items);
    }

    /**
     * 过滤掉不需要的画像数据（按nodeId作为唯一属性，如果存在重复的数据，按updateTime降序取1条）
     *
     * @param items 原始数据
     * @return 过滤后的数据
     */
    public List<MasterItem> filterMasterItem(List<MasterItem> items) {
        if (items == null || items.isEmpty()) {
            return new ArrayList<>();
        }
        // 使用Map按nodeId分组，并且只保留updateTime最新的条目
        Map<String, MasterItem> map = items.stream().collect(
                Collectors.toMap(MasterItem::getNodeId,
                        item -> item,
                        (existing, replacement) -> {
                            if (null == existing.getUpdateTime()){
                                return replacement;
                            }

                            if (null == replacement.getUpdateTime()){
                                return existing;
                            }

                            return existing.getUpdateTime().isAfter(replacement.getUpdateTime()) ? existing : replacement;
                        })
        );
        return new ArrayList<>(map.values());
    }

    /**
     * 过滤掉不需要的画像数据（按nodeId和userId作为唯一属性，如果存在重复的数据，按updateTime降序取1条）
     *
     * @param items 原始数据
     * @return 过滤后的数据
     */
    public List<UserMasteryRecord> filterMasterRecordItem(List<UserMasteryRecord> items) {
        if (items == null || items.isEmpty()) {
            return new ArrayList<>();
        }
        // 使用Map按userId和nodeId的组合作为键，并且只保留updateTime最新的条目
        Map<String, UserMasteryRecord> map = items.stream()
                .collect(Collectors.toMap(
                        record -> record.getUserId() + "-" + record.getNodeId(),
                        record -> record,
                                (existing, replacement) -> {
                                    if (null == existing.getUpdateTime()){
                                        return replacement;
                                    }

                                    if (null == replacement.getUpdateTime()){
                                        return existing;
                                    }

                                    return existing.getUpdateTime().isAfter(replacement.getUpdateTime()) ? existing : replacement;
                                })
                        );
        return new ArrayList<>(map.values());
    }

    /**
     * 画像分批查询
     *
     * @param traceId     跟踪id
     * @param masterQuery 画像查询对象
     * @return {@link MasterData}
     */
    @Override
    @SkylineMetric(value = "DataHub-3.画像分批查询", meterProviders = SkylabDataApiMeterProvider.class)
    public List<UserMasteryRecord> queryMasterDataBatch(String traceId, MasterQuery masterQuery) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        if (log.isDebugEnabled()) {
            log.debug("traceId= {};masterQuery= {}", traceId, masterQuery);
        }    // 构造查询条件，并查询结果
        Criteria criteria = masterQuery.buildCriteria();
        if (masteryProperties.isUseDeleteFlag()){
            criteria.and("delete_flag").ne(1);
        }
        List<UserMasteryRecord> masteryRecords = userMasteryRecordRepository.findAllByCursor(criteria, masterQuery.getUserId());
        // 20241211-过滤重复场景的数据
        masteryRecords = this.filterMasterRecordItem(masteryRecords);

        // 全局画像为空的，赋值为单一画像
        for(UserMasteryRecord mastery : masteryRecords){
            if (null == mastery.getGlobalMastery()){
                GlobalMasteryItem global = new GlobalMasteryItem();
                global.setMasterScore(mastery.getMasteryScore());
                global.setReal(mastery.getReal());
                global.setPredict(mastery.getPredict());
                global.setFusion(mastery.getFusion());
                global.setAssociativePoint(new HashMap<>());
                mastery.setGlobalMastery(global);
            }
        }

        long cost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        log.info("【dataapi-cost】画像分批查询mongo耗时:{}ms 数量:{}", cost, masteryRecords.size());
        return masteryRecords;
    }

//    /**
//     * 和罗俊确认，废弃业务字段 查询 用户指定目录下的 应学点
//     *
//     * @param traceId
//     * @param query
//     * @return
//     */
//    @Override
//    @SkylineMetric(value = "DataHub-3.查询应学点", meterProviders = SkylabDataApiMeterProvider.class)
//    public List<String> queryLearnPoints(String traceId, ShouldLearnPointQuery query) {
//        if (log.isDebugEnabled()) {
//            log.debug("traceId= {};shouldLearnPointQuery= {}", traceId, query);
//        }
//        List<UserMasteryRecord> records = userMasteryRecordRepository.search(query.buildCriteria(), query.getUserId());
//        return records.stream().map(UserMasteryRecord::getNodeId).collect(Collectors.toList());
//    }

    @Override
    public List<String> savePrimaryMigrationUserMasteryRecordAndGet(String userId, List<UserMasteryRecord> userMasteryRecordList) {
        log.info("Starting to save user mastery records for userId = {}", userId);
        if (CollectionUtils.isEmpty(userMasteryRecordList)) {
            return new ArrayList<>();
        }
        Set<String> nodeIds = userMasteryRecordList.stream()
                .map(UserMasteryRecord::getNodeId)
                .collect(Collectors.toSet());
        // 构建查询条件
        Criteria criteria = Criteria.where("user_id").is(userId).and("node_id").in(nodeIds);
        if (masteryProperties.isUseDeleteFlag()){
            criteria.and("delete_flag").ne(1);
        }
        List<UserMasteryRecord> existingRecords = userMasteryRecordRepository.search(criteria, userId);
        Set<String> existingNodeIds = existingRecords.stream()
                .map(UserMasteryRecord::getNodeId)
                .collect(Collectors.toSet());
        // 筛选出需要保存的记录
        List<UserMasteryRecord> recordsToSave = userMasteryRecordList.stream()
                .filter(record -> {
                    if(existingNodeIds.contains(record.getNodeId())){
                        return false;
                    }
                    if(!MasteryUtil.validMastery(record)){
//                        log.debug("5.validMastery userId: {}, record: {}", userId, JSON.toJSONString(record));
                        return false;
                    }
                    return true;
                }).peek(record -> record.setUpdateTime(Instant.now())).collect(Collectors.toList());
        List<String> savedIds = new ArrayList<>();
        for (UserMasteryRecord record : recordsToSave) {
            userMasteryRecordRepository.save(record, userId);
            String id = record.getId();
            if (id != null) {
                savedIds.add(id);
            }
        }
        return savedIds;
    }

    @Override
    public List<UserMasteryRecord> updateMasterDataAndGet(String traceId, MasterData masterData) {
        return masterData.getItems().stream().map(item -> updateMasterDataAndGet(traceId, masterData.getUserId(), item)).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
    }

    @LoggingCost
    @Override
    public void upsertMasterDataBatch(String traceId, MasterData masterData) {
        String userId = masterData.getUserId();
        List<Pair<Query, Update>> updates = new ArrayList<>();

        for (MasterItem masterItem : masterData.getItems()) {
            if (log.isDebugEnabled()) {
                log.debug("traceId= {};userId = {}, masterItem= {}", traceId, userId, masterItem);
            }
            masterItem.setUpdateTime(Instant.now());
            // 构造查询条件 userId - nodeId
            // 本期改造后，按userId-nodeId查询，可能存在多条记录，如果存在多条记录就取最新的一条，需要按updateTime降序排序更新最新的一条记录
            Criteria criteria = Criteria.where("user_id").is(userId).and("node_id").is(masterItem.getNodeId());
            if (masteryProperties.isUseDeleteFlag()){
                criteria.and("delete_flag").ne(1);
            }
            // 构造查询对象，并添加按 updateTime 降序排序的条件
            Query query = new Query(criteria);
            query.with(Sort.by(Sort.Direction.DESC, "update_time"));

            // 构造 要更新的数据
            Update update = new Update();
            Document document = new Document();
            MongoOperations mongoOperations = userMasteryRecordRepository.getMongoOperations();
            mongoOperations.getConverter().write(new UserMasteryRecord(userId, masterItem), document);
            document.forEach((key, val) -> {
                if ("_id".equals(key)) {
                    return;
                }
                if (val != null) {
                    update.set(key, val);
                }
            });
            updates.add(Pair.of(new Query(criteria), update));
        }
        userMasteryRecordRepository.upsertMasterDataBatch(updates, userId);
    }

    /**
     * 画像更新
     *
     * @param traceId    跟踪id
     * @param masterData 主数据
     * @return List 更新成功的id
     */
    @Override
    @SkylineMetric(value = "DataHub-3.画像更新", meterProviders = SkylabDataApiMeterProvider.class)
    public List<String> updateMasterData(String traceId, MasterData masterData) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {};masterData= {}", traceId, masterData);
        }
        return masterData.getItems().stream().map(item -> updateMasterData(traceId, masterData.getUserId(), item)).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    @Override
    @SkylineMetric(value = "DataHub-3.用户画像更新", meterProviders = SkylabDataApiMeterProvider.class)
    public String updateMasterData(String traceId, String userId, MasterItem masterItem) {
        UserMasteryRecord modified = updateMasterDataAndGet(traceId, userId, masterItem);
        return Optional.ofNullable(modified).map(UserMasteryRecord::getId).orElse(null);
    }

    @Override
    public UserMasteryRecord updateMasterDataAndGet(String traceId, String userId, MasterItem masterItem) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {};userId = {}, masterItem= {}", traceId, userId, masterItem);
        }
        masterItem.setUpdateTime(Instant.now());
        // 构造查询条件 userId - nodeId
        // 本期改造后，按userId-nodeId查询，可能存在多条记录，如果存在多条记录就取最新的一条，需要按updateTime降序排序更新最新的一条记录
        Criteria criteria = Criteria.where("user_id").is(userId).and("node_id").is(masterItem.getNodeId());
        if (masteryProperties.isUseDeleteFlag()){
            criteria.and("delete_flag").ne(1);
        }
        // 构造查询对象，并添加按 updateTime 降序排序的条件
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.DESC, "update_time"));

        // 构造 要更新的数据
        Update update = new Update();
        Document document = new Document();
        MongoOperations mongoOperations = userMasteryRecordRepository.getMongoOperations();
        mongoOperations.getConverter().write(new UserMasteryRecord(userId, masterItem), document);
        document.forEach((key, val) -> {
            if (val != null) {
                update.set(key, val);
            }
        });

        // 设置 更新参数
        FindAndModifyOptions options = new FindAndModifyOptions().upsert(true).remove(false).returnNew(true);
        // findAndModify 方法在 MongoDB 中是一个原子操作，它只会根据提供的查询条件找到一条匹配的记录进行更新。
        // 即使查询条件可能匹配多条记录，findAndModify 也只会选择其中一条进行更新，具体选择哪一条取决于排序条件。
        // 更新，返回更新后的ID
        return userMasteryRecordRepository.findAndModify(query, update, options, userId);
    }


    /**
     * 画像删除
     *
     * @param traceId 跟踪id
     * @param userId  用户id
     */
    @SkylineMetric(value = "DataHub-3.用户画像删除", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public Long deleteMasterDataByUserId(String traceId, String userId) {
        log.info("traceId={}, deleteMasterDataByUserId userId= {}", traceId, userId);
        if (CharSequenceUtil.isEmpty(userId)) {
            return 0L;
        }
        Criteria criteria = Criteria.where("user_id").is(userId);
        DeleteResult remove = userMasteryRecordRepository.getMongoOperations().remove(new Query(criteria), UserMasteryRecord.class, SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId));
        long deletedCount = remove.getDeletedCount();
        log.info("traceId={}, deleteMasterDataByUserId userId= {},delete count={}", traceId, userId, deletedCount);
        return deletedCount;
    }

    @Override
    public List<MasterInfo> computePortraitByCatalogCode(String traceId, ComputePortraitParam param) {

        SceneInfo sceneInfo = new SceneInfo().setUserId(param.getUserId()).setBizCode(param.getBizCode()).setSubjectCode(param.getSubjectCode()).setPhaseCode(param.getPhaseCode()).setAreaCode(param.getAreaCode()).setCatalogCode(param.getCatalogCode()).setStudyCode(StudyCodeEnum.SYNC_LEARN).setFunctionCode(MasterFetch4CatalogParam.FUNC_CODE);

        MasterFetch4CatalogParam masterParam = new MasterFetch4CatalogParam();
        List<String> catalogIds = new ArrayList<>();
        catalogIds.add(param.getCatalogCode());
        masterParam.setCatalogIds(catalogIds);
        JSONObject masterFetchExt = new JSONObject();
        masterFetchExt.fluentPut("evalNum", param.getEvalNum()).fluentPut("photoEvaluationNum", param.getPhotoEvaluationNum()).fluentPut("engineEvaluationNum", param.getEngineEvaluationNum());
        masterParam.setMasterFetchExt(masterFetchExt);
        DispatchApiPayload payload = new DispatchApiPayload();
        payload.putData("data", (JSONObject) JSON.toJSON(masterParam));

        DispatchApiRequest dispatchApiRequest = new DispatchApiRequest();
        dispatchApiRequest.setTraceId(traceId);
        dispatchApiRequest.setObjectScene(sceneInfo);
        dispatchApiRequest.setPayload(payload);

        DispatchApiResponse dispatchApiResponse = skylabAdaptiveEngineProxyFeign.diagnose(dispatchApiRequest);

        return Optional.ofNullable(dispatchApiResponse).map(d -> JSON.parseObject(JSON.toJSONString(d.getPayload()), MasterFetchResult.class)).map(MasterFetchResult::getMasterInfos).orElse(new ArrayList<>());
    }

    @Override
    public boolean exists(String traceId, MasterQuery masterQuery) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {};masterQuery= {}", traceId, masterQuery);
        }
        Criteria criteria = masterQuery.buildCriteria();
        return userMasteryRecordRepository.exists(criteria, masterQuery.getUserId());
    }

    @Override
    public void batchSaveUserMasteryRecord(List<UserMasteryRecord> userMasteryRecordList, String userId) {
        //long l = System.currentTimeMillis();
        //userMasteryRecordRepository.saveBatch(userMasteryRecordList, userId);
        //log.info("用户ID：{},迁移数量：{},数据入库耗时：{}", userId, userMasteryRecordList.size(), System.currentTimeMillis() - l);
    }
}
