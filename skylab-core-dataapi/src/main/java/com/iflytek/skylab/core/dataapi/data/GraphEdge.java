package com.iflytek.skylab.core.dataapi.data;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 图的边
 */
@Getter
@Setter
@Accessors(chain = true)
public class GraphEdge {
    
    @NotBlank
    private String traceId;

    /**
     * 图谱版本
     */
    @NotBlank
    private String graphVersion;

    /**
     * 边的类型
     */
    @NotBlank
    private String label;
    
    /**
     * 边的起始点
     */
    @NotBlank
    private String source;

    /**
     * 边的结束点
     */
    @NotBlank
    private String target;

    /**
     * 边的属性
     */
    private JSONObject properties;
}
