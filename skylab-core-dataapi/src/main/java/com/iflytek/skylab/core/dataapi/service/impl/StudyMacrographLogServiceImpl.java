package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.dataapi.configuration.StudyLogProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.mongo.dao.StudyLogRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.dao.StudyMacrographRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyMacrographLogRecordEntity;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skylab.core.dataapi.service.StudyMacrographLogService;
import com.iflytek.skylab.core.dataapi.util.UserAnchorAnswerRecordDelegate;
import com.iflytek.skylab.core.dataapi.util.UserErrorAnswerDelegate;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大图谱-存储隔离；查询需要合并精准学学情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/30 15:01
 */
@Slf4j
public class StudyMacrographLogServiceImpl implements StudyMacrographLogService {

    private final StudyLogProperties studyLogProperties;
    private final FeatureService featureService;
    private final StudyMacrographRecordRepository macrographRecordRepository;
    private final StudyLogRecordRepository studyLogRecordRepository;

    public StudyMacrographLogServiceImpl(StudyLogProperties studyLogProperties, FeatureService featureService, StudyMacrographRecordRepository macrographRecordRepository, StudyLogRecordRepository studyLogRecordRepository) {
        this.studyLogProperties = studyLogProperties;
        this.featureService = featureService;
        this.macrographRecordRepository = macrographRecordRepository;
        this.studyLogRecordRepository = studyLogRecordRepository;
    }

    @Override
    @SkylineMetric(value = "DataHub-3.推荐记录批量存储", meterProviders = SkylabDataApiMeterProvider.class)
    public List<String> saveRecLogList(String traceId, List<RecLog> recLogs) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, recLogs={}", traceId, recLogs);
        }
        List<StudyMacrographLogRecordEntity> entities = recLogs.stream().map(recLog -> new StudyMacrographLogRecordEntity(traceId, recLog)).collect(Collectors.toList());

        List<StudyMacrographLogRecordEntity> savedEntities = macrographRecordRepository.saveAll(entities);
        return savedEntities.stream().map(StudyMacrographLogRecordEntity::getId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
    }

    @SkylineMetric(value = "DataHub-3.答题记录存储(并返回答题记录)", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public List<FeedbackLog> saveFeedbackLogListAndGet(String traceId, List<FeedbackLog> feedbackLogs) {
        return feedbackLogs.stream().map(feedbackLog -> saveFeedbackLogAndGet(traceId, feedbackLog)).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
    }


    private FeedbackLog saveFeedbackLogAndGet(String traceId, FeedbackLog feedbackLog) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, feedbackLog={}", traceId, feedbackLog);
        }
        try {
            StudyLogRecordEntity studyLog = saveFeedbackLogAndGetStudyLog(traceId, feedbackLog);
            feedbackLog.setId(studyLog.getId());
            feedbackLog.setFuncCode(studyLog.getFuncCode());
            return feedbackLog;
        } catch (Exception e) {
            log.error("save feedbackLog error, traceId：{}", traceId, e);
        }
        return null;
    }

    private StudyLogRecordEntity saveFeedbackLogAndGetStudyLog(String traceId, FeedbackLog feedbackLog) {
        StudyMacrographLogRecordEntity record = findOriginalStudyLogRecord(feedbackLog);
        if (record == null) {
            record = new StudyMacrographLogRecordEntity(traceId, feedbackLog);
        } else {
            record.setFrom(feedbackLog.getFrom());
            record.setTimeCost(feedbackLog.getTimeCost());
            record.setScore(feedbackLog.getScore());
            record.setStandardScore(feedbackLog.getStandardScore());
            record.setScoreRatio(feedbackLog.getScoreRatio());
            record.setFeedbackExt(feedbackLog.getFeedbackExt());
            record.setFeedbackTime(feedbackLog.getFeedbackTime());
            record.setCorrectTraceId(feedbackLog.getCorrectTraceId());
        }
        return macrographRecordRepository.save(record);
    }

    private StudyMacrographLogRecordEntity findOriginalStudyLogRecord(FeedbackLog feedbackLog) {
        String refTraceId = feedbackLog.getRefTraceId();
        if (StrUtil.isBlank(refTraceId)) {
            return null;
        }
        // 根据refTraceId、userId、studyCode、nodeId、resNodeId查找
        Criteria criteria = Criteria.where("trace_id").is(refTraceId).and("user_id").is(feedbackLog.getUserId()).and("study_code").is(feedbackLog.getStudyCode()).and("node_id").is(feedbackLog.getNodeId()).and("res_node_id").is(feedbackLog.getResNodeId());

        List<StudyMacrographLogRecordEntity> list = macrographRecordRepository.search(criteria);
        return CollectionUtil.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 查询答题记录 （大图谱studyCode）
     * <p>
     * 从数仓获取历史答题记录时，特征名：user_answer_topics；主键：user_id#biz_code[ZSY_XXJ]#anchorpoint_code
     * 其他未列字段无。
     *
     * @param traceId
     * @param studyLogQuery
     * @param num
     * @return
     */
    @SkylineMetric(value = "DataHub-3.查询答题记录", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public List<StudyLogData> queryFeedbackLogs(String traceId, StudyMacrographLogQuery studyLogQuery, Integer num) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, studyLogQuery={}, num={}", traceId, studyLogQuery, num);
        }
        final int target = (num == null || num < 1) ? 50 : num;

        // key:nodeId， val: 点对应的答题记录
        MultiValueMap<String, StudyLogData> multiValueMap = new LinkedMultiValueMap<>();

        // 从mongo查询近两天数据
        Date yesterday = DateUtil.yesterday().toJdkDate();
        Criteria criteria = studyLogQuery.buildCriteria().and("feedback_time").gte(DateUtil.beginOfDay(yesterday)).and("time_cost").ne(null);


        Stopwatch stopwatch = Stopwatch.createStarted();

        // 查询作答日志-大图谱(精品密卷+同步习题) 错题本待定
        List<StudyMacrographLogRecordEntity> recordsMacrograph = macrographRecordRepository.search(criteria);
        if (log.isDebugEnabled()) {
            log.debug("queryFeedbackLogs recordsMacrograph result is : {}", JSON.toJSONString(recordsMacrograph));
        }
        // 查询作答日志-精准学（所有）
        List<StudyLogRecordEntity> recordsSync = studyLogRecordRepository.search(criteria);
        if (log.isDebugEnabled()) {
            log.debug("queryFeedbackLogs recordsSync result is : {}", JSON.toJSONString(recordsSync));
        }


        List<StudyLogRecordEntity> records = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(recordsMacrograph)) {
            records.addAll(recordsMacrograph);
        }
        if (CollectionUtil.isNotEmpty(recordsSync)) {
            records.addAll(recordsSync);
        }

        //换一换
        if (studyLogQuery.getChange() != null && studyLogQuery.getChange()) {
            criteria = studyLogQuery.buildCriteria().and("feedback_time").gte(DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -8))).and("time_cost").isNull();
            //补充-未作答的推荐记录
            List<StudyMacrographLogRecordEntity> recommedRecordsMacrograph = macrographRecordRepository.searchWithLimit(criteria, 1000);
            if (log.isDebugEnabled()) {
                log.debug("queryFeedbackLogs recommedRecordsMacrograph result is : {}", JSON.toJSONString(recommedRecordsMacrograph));
            }

            log.info("queryFeedbackLogs recommedRecordsMacrograph result is : {}", recommedRecordsMacrograph == null ? 0 : recommedRecordsMacrograph.size());
            if (CollectionUtil.isNotEmpty(recommedRecordsMacrograph)) {
                /**
                 * 遍历推荐的macrograph日志记录列表。
                 * 对于每个日志记录，进行处理以确保只有最新的记录被保留，并且设置其评分为0，反馈时间为更新时间。
                 */
                for (StudyMacrographLogRecordEntity macrographLogRecord : recommedRecordsMacrograph) {
                    if (macrographLogRecord != null) {
                        macrographLogRecord.setScoreRatio(0D);
                        //feedback_time设置成更新时间
                        macrographLogRecord.setFeedbackTime(macrographLogRecord.getUpdateTime());
                    }
                }
                records.addAll(recommedRecordsMacrograph);
            }
        }

        long mongoCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);

        if (CollectionUtil.isNotEmpty(records)) {
            Map<String, List<StudyLogData>> mongoDataMap = records.stream().map(StudyLogRecordEntity::toStudyLogData).sorted((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime())).collect(Collectors.groupingBy(StudyLogData::getNodeId));
            multiValueMap.putAll(mongoDataMap);
        }

        // 在mongo中已经查到足够数据的nodeId，不再从数仓补充查询
        List<String> additional = studyLogQuery.getNodeIdList();
        if (CollectionUtil.isNotEmpty(multiValueMap)) {
            additional.removeIf(nodeId -> multiValueMap.getOrDefault(nodeId, Lists.newArrayList()).size() >= target);
        }

        // 从数仓补充历史数据
        stopwatch.reset().start();
        MultiValueMap<String, StudyLogData> appendMultiValueMap = userAnchorAnswerRecord(traceId, additional, studyLogQuery);
        long odeonCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        if (CollectionUtil.isNotEmpty(appendMultiValueMap)) {
            additional.forEach(nodeId -> multiValueMap.addAll(nodeId, appendMultiValueMap.getOrDefault(nodeId, Lists.newArrayList())));
        }

        // 统一截取目标长度
        List<StudyLogData> result = Lists.newArrayList();
        multiValueMap.values().forEach(list -> result.addAll(list.subList(0, Math.min(list.size(), target))));

        //换一换-去重
        if (studyLogQuery.getChange() != null && studyLogQuery.getChange() && CollectionUtil.isNotEmpty(result)) {
            Map<String, StudyLogData> uMaps = new HashMap<>();

            for (StudyLogData macrographLogRecord : result) {
                //   构建唯一键，由节点ID和资源节点ID组成。
                String key = macrographLogRecord.getNodeId() + macrographLogRecord.getResNodeId();
//                         * 检查map中是否已存在相同的键。
//                         * 如果存在，比较当前记录和map中记录的更新时间，保留更新时间最新的记录。
//                         * 如果不存在，直接将当前记录添加到map中。
                if (uMaps.containsKey(key)) {
                    StudyLogData studyMacrographLogRecordEntity = uMaps.get(key);
//                              比较两个记录的更新时间，保留最新的记录。
                    if (studyMacrographLogRecordEntity.getUpdateTime() != null && macrographLogRecord.getUpdateTime() != null) {
                        if (studyMacrographLogRecordEntity.getUpdateTime().compareTo(macrographLogRecord.getUpdateTime()) < 0) {
                            uMaps.put(key, macrographLogRecord);
                        }
                    }
                } else {
                    uMaps.put(key, macrographLogRecord);
                }
            }
            log.info("【dataapi-cost】换一换 答题记录mongo耗时:{} 数量:{}; 答题记录odeon耗时:{} 数量：{}", mongoCost, records.size(), odeonCost, result.size() - records.size());
            return Lists.newArrayList(uMaps.values());
        }
        log.info("【dataapi-cost】答题记录mongo耗时:{} 数量:{}; 答题记录odeon耗时:{} 数量：{}", mongoCost, records.size(), odeonCost, result.size() - records.size());
        return result;
    }

    @Override
    public List<UserErrorAnswerLog> queryWrongLogs(String traceId, ErrorLogQuery studyLogQuery, Integer num) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, studyLogQuery={}, num={}", traceId, studyLogQuery, num);
        }
        final int target = (num == null || num < 1) ? 100 : num;

        List<UserErrorAnswerLog> userErrorAnswerLogs = new ArrayList<>();
        // 从mongo查询近两天数据
        Date yesterday = DateUtil.yesterday().toJdkDate();
        Criteria criteria = studyLogQuery.buildCriteria().and("feedback_time").gte(DateUtil.beginOfDay(yesterday)).and("time_cost").ne(null);

        Stopwatch stopwatch = Stopwatch.createStarted();

        // 查询作答日志-大图谱(精品密卷+同步习题) 错题本待定
        List<StudyMacrographLogRecordEntity> recordsMacrograph = macrographRecordRepository.search(criteria);
        if (log.isDebugEnabled()) {
            log.debug("queryFeedbackLogs recordsMacrograph result is : {}", JSON.toJSONString(recordsMacrograph));
        }
        // 查询作答日志-精准学（所有）
        List<StudyLogRecordEntity> recordsSync = studyLogRecordRepository.search(criteria);
        if (log.isDebugEnabled()) {
            log.debug("queryFeedbackLogs recordsSync result is : {}", JSON.toJSONString(recordsSync));
        }

        List<StudyLogRecordEntity> records = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(recordsMacrograph)) {
            records.addAll(recordsMacrograph);
        }
        if (CollectionUtil.isNotEmpty(recordsSync)) {
            records.addAll(recordsSync);
        }
        long mongoCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        //如果满足数量条件 直接去重返回
        List<StudyLogRecordEntity> uniqueAndLatestRecords = records.stream().filter(x -> Objects.nonNull(x.getUpdateTime()) && Objects.nonNull(x.getScoreRatio()) && 0 == x.getScoreRatio() && Objects.nonNull(x.getResNodeId())).collect(Collectors.toMap(StudyLogRecordEntity::getResNodeId, Function.identity(), (existing, replacement) -> replacement.getUpdateTime().compareTo(existing.getUpdateTime()) > 0 ? replacement : existing)).values().stream().collect(Collectors.toList());

        if (uniqueAndLatestRecords.size() >= target) {
            List<StudyLogRecordEntity> studyLogRecordEntities = uniqueAndLatestRecords.subList(0, target);
            userErrorAnswerLogs = UserErrorAnswerDelegate.studyLogToUserErrorAnswerLog(studyLogRecordEntities);
            return userErrorAnswerLogs;
        } else {
            userErrorAnswerLogs = UserErrorAnswerDelegate.studyLogToUserErrorAnswerLog(uniqueAndLatestRecords);
        }

        // 数量不足 从数仓补充
        stopwatch.reset().start();
        List<UserErrorAnswerLog> result = userErrorAnswerRecord(traceId, studyLogQuery);
        long odeonCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);

        if (CollectionUtil.isNotEmpty(result)) {
            result.sort(Comparator.comparing(UserErrorAnswerLog::getUpdateTime).reversed());
            List<String> collect = userErrorAnswerLogs.stream().map(UserErrorAnswerLog::getResNodeId).collect(Collectors.toList());
            Map<String, UserErrorAnswerLog> logMap = userErrorAnswerLogs.stream().collect(Collectors.toMap(UserErrorAnswerLog::getResNodeId, Function.identity()));
            for (UserErrorAnswerLog userErrorAnswerLog : result) {
                if (!collect.contains(userErrorAnswerLog.getResNodeId())) {
                    userErrorAnswerLogs.add(userErrorAnswerLog);
                } else {
                    UserErrorAnswerLog userErrorAnswerLog1 = logMap.get(userErrorAnswerLog.getResNodeId());
                    if (userErrorAnswerLog1.getUpdateTime().compareTo(userErrorAnswerLog.getUpdateTime()) < 0) {
                        userErrorAnswerLogs.remove(userErrorAnswerLog1);
                        userErrorAnswerLogs.add(userErrorAnswerLog);
                    }
                }
                if (userErrorAnswerLogs.size() >= target) {
                    break;
                }
            }
            log.info("【dataapi-cost】错题记录mongo耗时:{} 数量:{}; 错题记录odeon耗时:{} 数量：{}", mongoCost, records.size(), odeonCost, result.size() - records.size());
        }
        return userErrorAnswerLogs;
    }


    /**
     * 查询特征 user_error_answers
     *
     * @return
     */
    private List<UserErrorAnswerLog> userErrorAnswerRecord(String traceId, ErrorLogQuery query) {
        try {
            FeatureQuery featureQuery = UserErrorAnswerDelegate.generateFeatureQuery(query, studyLogProperties);
            FeatureData featureData = featureService.query(traceId, featureQuery);
            return UserErrorAnswerDelegate.parseFeatureData(featureData);
        } catch (Exception e) {
            log.error("TraceId:{},query user_answer_topics error", traceId, e);
        }
        return null;
    }

    /**
     * 查询特征 user_anchor_exam_recently_answer_record
     *
     * @param traceId
     * @param additional
     * @param query
     * @return
     */
    private MultiValueMap<String, StudyLogData> userAnchorAnswerRecord(String traceId, List<String> additional, StudyLogQuery query) {
        if (CollectionUtil.isEmpty(additional)) {
            return null;
        }
        try {
            FeatureQuery featureQuery = UserAnchorAnswerRecordDelegate.generateFeatureQuery(additional, query, studyLogProperties);
            FeatureData featureData = featureService.query(traceId, featureQuery);
            return UserAnchorAnswerRecordDelegate.parseFeatureData(featureData);
        } catch (Exception e) {
            log.error("query user_answer_topics error", e);
        }
        return null;
    }
}
