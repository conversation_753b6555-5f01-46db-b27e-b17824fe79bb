package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.constant.BizActionEnum;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.ResourceTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/24 16:20
 */
@Getter
@Setter
public class UserRecently200AnswerRecord {
    private String userId;
    private String subjectCode;
    private String phaseCode;
    private BizCodeEnum bizCode;
    private String lastAnchorPoint;
    private TopicInfo lastTopicInfo;
    private String topicLimitPerAnchor;
    private List<AnchorTopicInfo> recentTopicInfos;
    private Long createTime;
    private Long updateTime;


    @Getter
@Setter
    public static class TopicInfo {
        private String topicId;
        private Long updateTime;
        private Double scoreRatio;
        private String source;
        private StudyCodeEnum studyCode;
        private BizActionEnum bizAction;

        public StudyLogData toStudyLogData(BizCodeEnum bizCode) {
            // 只给这几个字段
            StudyLogData studyLogData = new StudyLogData();
            studyLogData.setUpdateTime(Instant.ofEpochMilli(updateTime));
            studyLogData.setFeedbackTime(Instant.ofEpochMilli(updateTime));
            studyLogData.setResNodeId(topicId);
            studyLogData.setResNodeType(ResourceTypeEnum.TOPIC);
            studyLogData.setBizCode(bizCode);
            return studyLogData;
        }
    }

    @Getter
@Setter
    public static class AnchorTopicInfo {
        private String anchorPointId;
        private TopicInfo topicInfos;
    }
}
