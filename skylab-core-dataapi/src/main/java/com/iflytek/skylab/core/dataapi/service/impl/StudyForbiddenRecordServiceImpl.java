package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Stopwatch;
import com.iflytek.skylab.core.dataapi.mongo.dao.StudyForbiddenRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyForbiddenRecordEntity;
import com.iflytek.skylab.core.dataapi.service.StudyForbiddenRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class StudyForbiddenRecordServiceImpl implements StudyForbiddenRecordService {

    private final StudyForbiddenRecordRepository studyForbiddenRecordRepository;

    public StudyForbiddenRecordServiceImpl(StudyForbiddenRecordRepository studyForbiddenRecordRepository) {
        this.studyForbiddenRecordRepository = studyForbiddenRecordRepository;
    }

    @Override
    public void save(String userId, String catalogId, List<String> nodeIds) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            catalogId = getUnit(catalogId);
            // 参数校验
            if (StringUtils.isAnyBlank(userId, catalogId)) {
                log.warn("保存禁用题数据参数无效，userId: {}, catalogId: {}", userId, catalogId);
                return;
            }

            if (CollectionUtils.isEmpty(nodeIds)) {
                log.info("保存禁用题数据为空，userId: {}, catalogId: {}", userId, catalogId);
                return;
            }

            // 过滤掉空值
            List<String> validNodeIds = nodeIds.stream()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            if (validNodeIds.isEmpty()) {
                log.warn("保存禁用题数据中所有nodeId都为空，userId: {}, catalogId: {}", userId, catalogId);
                return;
            }

            log.info("开始保存禁用题数据，userId: {}, catalogId: {}, 待保存禁用题数量: {}", userId, catalogId, validNodeIds.size());

            // 获取已存在的nodeId集合
            Set<String> existingNodeIds = getExistingNodeIds(userId, catalogId);
            log.info("已存在的禁用题数量: {}", existingNodeIds.size());

            // 构建并保存新记录
            List<StudyForbiddenRecordEntity> toSaveList = buildNewEntities(userId, catalogId, validNodeIds, existingNodeIds);
            log.info("需要新增的禁用题数量: {}", toSaveList.size());

            if (!toSaveList.isEmpty()) {
                studyForbiddenRecordRepository.saveAll(toSaveList);
                log.info("禁用题数据保存完成，userId: {}, catalogId: {}, 新增禁用题数量: {}", userId, catalogId, toSaveList.size());
            } else {
                log.info("没有需要新增的禁用题，userId: {}, catalogId: {}", userId, catalogId);
            }
        } catch (Exception e) {
            log.error("保存禁用题数据异常，userId: {}, catalogId: {}, error: {}", userId, catalogId, e.getMessage(), e);
        } finally {
            log.info("保存禁用题数据总耗时: {}, userId: {}, catalogId: {}", stopwatch.stop(), userId, catalogId);
        }
    }

    @Override
    public List<StudyForbiddenRecordEntity> findByUserIdAndCatalogId(String userId, String catalogId) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            catalogId = getUnit(catalogId);
            // 参数校验
            if (StringUtils.isAnyBlank(userId, catalogId)) {
                log.warn("查询禁用题数据参数无效，userId: {}, catalogId: {}", userId, catalogId);
                return Collections.emptyList();
            }

            Query query = Query.query(Criteria.where("user_id").is(userId)
                    .and("catalog_id").is(catalogId));
            List<StudyForbiddenRecordEntity> result = studyForbiddenRecordRepository.search(query);
            log.info("查询禁用题结果数量: {}, userId: {}, catalogId: {}", result.size(), userId, catalogId);
            return result;
        } catch (Exception e) {
            log.error("查询禁用题数据异常，userId: {}, catalogId: {}, error: {}", userId, catalogId, e.getMessage(), e);
            return Collections.emptyList();
        } finally {
            log.info("查询禁用题数据总耗时: {}, userId: {}, catalogId: {}", stopwatch.stop(), userId, catalogId);
        }
    }

    /**
     * 获取已存在的nodeId集合
     */
    private Set<String> getExistingNodeIds(String userId, String catalogId) {
        List<StudyForbiddenRecordEntity> records = findByUserIdAndCatalogId(userId, catalogId);
        return records.stream()
                .filter(Objects::nonNull)
                .map(StudyForbiddenRecordEntity::getNodeId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    /**
     * 构建新的实体列表
     */
    private List<StudyForbiddenRecordEntity> buildNewEntities(String userId, String catalogId,
                                                              List<String> nodeIds, Set<String> existingNodeIds) {
        return nodeIds.stream()
                .filter(StringUtils::isNotBlank)
                .filter(nodeId -> !existingNodeIds.contains(nodeId))
                .map(nodeId -> createEntity(userId, catalogId, nodeId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 创建实体对象
     */
    private StudyForbiddenRecordEntity createEntity(String userId, String catalogId, String nodeId) {
        if (StringUtils.isAnyBlank(userId, catalogId, nodeId)) {
            return null;
        }
        StudyForbiddenRecordEntity entity = new StudyForbiddenRecordEntity();
        entity.setUserId(userId);
        entity.setCatalogId(catalogId);
        entity.setNodeId(nodeId);
        return entity;
    }

    private String getUnit(String cataLog) {
//        教材_书_章_节_课时
        List<String> split = StrUtil.split(cataLog, "_");
        if (split.size() < 3) {
            return cataLog;
        } else {
//            教材_书_章
            return split.get(0) + "_" + split.get(1) + "_" + split.get(2);
        }
    }
} 