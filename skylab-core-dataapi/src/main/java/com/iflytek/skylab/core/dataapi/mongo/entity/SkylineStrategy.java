package com.iflytek.skylab.core.dataapi.mongo.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.alibaba.fastjson2.JSONObject;

@Getter
@Setter
@Document(collection = "skyline_strategy")
public class SkylineStrategy {
    
    @Id
    private String id;
    
    /**
     * 名称
     */
    @Field(name = "name")
    private String name;

    /**
     * 版本号
     */
    @Field(name = "version_number")
    private Integer versionNumber;

    /**
     * 配置参数
     */
    @Field(name = "parameter")
    private JSONObject parameter;
}
