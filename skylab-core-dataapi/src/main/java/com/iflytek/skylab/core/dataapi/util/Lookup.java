package com.iflytek.skylab.core.dataapi.util;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/4 16:08
 */
public class Lookup {

    private final String label;

    private String whereKey;
    private Object whereValue;

    private final Map<String, Object> andMap = new HashMap<>();

    public Lookup(String label) {
        this.label = label;
        // this.ngql = new StringBuffer(String.format("LOOKUP ON %s ", label));
    }

    public Lookup where(String propKey, Object propVal) {
        this.whereKey = propKey;
        this.whereValue = propVal;
        return this;
    }

    public Lookup and(String propKey, Object propVal) {
        andMap.put(propKey, propVal);
        return this;
    }

    public String ngql() {
        StringBuffer ngql = new StringBuffer(String.format("LOOKUP ON %s ", label));
        // where
        String whereFormat = whereValue instanceof Integer ? "WHERE {}.{} == {} " : "WHERE {}.{} == \"{}\" ";
        ngql.append(StrUtil.format(whereFormat, label, whereKey, whereValue));
        // and
        andMap.forEach((k,v) -> {
            String andFormat = v instanceof Integer ? "AND {}.{} == {} " : "AND {}.{} == \"{}\" ";
            ngql.append(StrUtil.format(andFormat, label, k, v));
        });
        // yield
        return ngql.append(" YIELD id(vertex);").toString();
    }

}
