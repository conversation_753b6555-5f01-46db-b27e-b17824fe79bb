package com.iflytek.skylab.core.dataapi.service.impl;

import com.iflytek.skylab.core.constant.GraphVertexType;
import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.service.cache.GraphCache;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 图谱查询服务，提供 Nebula 图数据库的子图查询、节点查询等功能
 * <p>
 * 缓存废弃原因：1. 不缓存也挺快的，不是瓶颈。2.代码有缺陷，压测偶现超时（未定位原因）3.图谱过大500M+，多版本图谱开销不起
 */
@Slf4j
public class CachedGraphServiceImpl extends GraphServiceImpl {

    private final GraphCache graphCache;

    public CachedGraphServiceImpl(NebulaPool nebulaPool, GraphProperties graphProperties, GraphCache graphCache) {
        super(nebulaPool, graphProperties);
        this.graphCache = graphCache;
    }

    /**
     * 查询子图，因为 考点-题、锚点-题、复习点-题 已经缓存，所以涉及到这些的查询可以直接从缓存中读取
     */
    @Override
    @SkylineMetric(value = "DataHub-1.图谱-查询子图", meterProviders = SkylabDataApiMeterProvider.class)
    public GraphData querySubGraph(SubGraphQuery subGraphQuery) {

        // 缓存未加载完成，直接查询图谱
        if (!graphCache.isInitialized()) {
            log.debug("Graph cache is not initialized");
            return super.querySubGraph(subGraphQuery);
        }

        // 缓存加载完成，查询缓存+图谱，并合并结果
        SubGraphQuery notCachedSubGraphQuery = removeCachedEdge(subGraphQuery);
        if (log.isDebugEnabled()) {
            log.debug("After remove cached edge, subGraphQuery={}", notCachedSubGraphQuery);
        }
        GraphData result = super.querySubGraph(notCachedSubGraphQuery);
        GraphData checkPointData = queryCheckPointData(subGraphQuery, result);
        GraphData anchorPointData = queryAnchorPointData(subGraphQuery, result);
        GraphData reviewPointData = queryReviewPointData(subGraphQuery, result);
        return mergeGraphData(subGraphQuery.getGraphVersion(), result, checkPointData, anchorPointData, reviewPointData);
    }

    /**
     * 合并图谱+缓存的查询结果
     */
    private GraphData mergeGraphData(String graphVersion, GraphData result, GraphData checkPointData, GraphData anchorPointData, GraphData reviewPointData) {
        GraphData data = result;
        data = super.mergeGraphData(graphVersion, data, checkPointData);
        data = super.mergeGraphData(graphVersion, data, anchorPointData);
        data = super.mergeGraphData(graphVersion, data, reviewPointData);
        return data;
    }

    /**
     * 查询 考点-题
     */
    private GraphData queryCheckPointData(SubGraphQuery subGraphQuery, GraphData result) {
        GraphData checkPointData = null;
        if (hasCheckPointTopic(subGraphQuery) && result.getVertices() != null) {
            List<String> checkPointIds = result.getVertices().stream()
                    .filter(vertex -> GraphVertexType.CHECK_POINT.equals(vertex.getLabel()))
                    .map(GraphData.GraphVertex::getId)
                    .collect(Collectors.toList());
            checkPointData = graphCache.queryCheckPoint(checkPointIds);
        }
        return checkPointData;
    }

    /**
     * 查询 锚点-题
     */
    private GraphData queryAnchorPointData(SubGraphQuery subGraphQuery, GraphData result) {
        GraphData anchorPointData = null;
        if (hasAnchorPointTopic(subGraphQuery) && result.getVertices() != null) {
            List<String> anchorPointIds = result.getVertices().stream()
                    .filter(vertex -> GraphVertexType.ANCHOR_POINT.equals(vertex.getLabel()))
                    .map(GraphData.GraphVertex::getId)
                    .collect(Collectors.toList());
            anchorPointData = graphCache.queryAnchorPoint(anchorPointIds);
        }
        return anchorPointData;
    }

    /**
     * 查询 复习点-题
     */
    private GraphData queryReviewPointData(SubGraphQuery subGraphQuery, GraphData result) {
        GraphData reviewPointData = null;
        if (hasReviewPointTopic(subGraphQuery) && result.getVertices() != null) {
            List<String> reviewPointIds = result.getVertices().stream()
                    .filter(vertex -> GraphVertexType.REVIEW_POINT.equals(vertex.getLabel()))
                    .map(GraphData.GraphVertex::getId)
                    .collect(Collectors.toList());
            reviewPointData = graphCache.queryReviewPoint(reviewPointIds);
        }
        return reviewPointData;
    }

    /**
     * 判断查询条件中是否包含 考点-题
     *
     * @param subGraphQuery
     * @return
     */
    private boolean hasCheckPointTopic(SubGraphQuery subGraphQuery) {
        return subGraphQuery.getEdgeLabels().stream().anyMatch(this::isCheckPointTopic);
    }

    /**
     * 判断边类型是否为 考点-题
     *
     * @param edge
     * @return
     */
    private boolean isCheckPointTopic(SubGraphQuery.EdgeLabel edge) {
        return GraphVertexType.CHECK_POINT.equals(edge.getSource()) && GraphVertexType.TOPIC.equals(edge.getTarget());
    }

    /**
     * 判断查询条件中是否包含 锚点-题
     *
     * @param subGraphQuery
     * @return
     */
    private boolean hasAnchorPointTopic(SubGraphQuery subGraphQuery) {
        return subGraphQuery.getEdgeLabels().stream().anyMatch(this::isAnchorPointTopic);
    }

    /**
     * 判断边类型是否为 锚点-题
     *
     * @param edge
     * @return
     */
    private boolean isAnchorPointTopic(SubGraphQuery.EdgeLabel edge) {
        return GraphVertexType.ANCHOR_POINT.equals(edge.getSource()) && GraphVertexType.TOPIC.equals(edge.getTarget());
    }

    /**
     * 判断查询条件中是否包含 复习点-题
     *
     * @param subGraphQuery
     * @return
     */
    private boolean hasReviewPointTopic(SubGraphQuery subGraphQuery) {
        return subGraphQuery.getEdgeLabels().stream().anyMatch(this::isReviewPointTopic);
    }

    /**
     * 判断边类型是否为 复习点-题
     *
     * @param edge
     * @return
     */
    private boolean isReviewPointTopic(SubGraphQuery.EdgeLabel edge) {
        return GraphVertexType.REVIEW_POINT.equals(edge.getSource()) && GraphVertexType.TOPIC.equals(edge.getTarget());
    }

    /**
     * 删除查询条件中的已缓存的边
     *
     * @param subGraphQuery
     * @return
     */
    private SubGraphQuery removeCachedEdge(SubGraphQuery subGraphQuery) {
        SubGraphQuery result = new SubGraphQuery();
        result.setTraceId(subGraphQuery.getTraceId());
        result.setGraphVersion(subGraphQuery.getGraphVersion());
        result.setRootVertexLabel(subGraphQuery.getRootVertexLabel());
        result.setRootVertexIdList(subGraphQuery.getRootVertexIdList());
        result.setEdgeLabels(subGraphQuery.getEdgeLabels().stream()
                .filter(edge -> !isCheckPointTopic(edge) && !isAnchorPointTopic(edge) && !isReviewPointTopic(edge))
                .collect(Collectors.toList()));
        return result;
    }
}
