package com.iflytek.skylab.core.dataapi.service;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 学情日志 数据工具入口
 * <p>
 *  TODO  返回结果 进一步转换
 */
@Validated
public interface StudyLogService {

    /**
     * 推荐记录存储
     * <p>
     * userId
     * subjectCode
     * phaseCode
     *
     * @param traceId 跟踪id
     * @param recLog  推荐记录
     * @return {@link String}记录Id
     */
    String saveRecLog(@NotBlank String traceId, @NotNull @Valid RecLog recLog);

    /**
     * 保存答题记录
     *
     * @param traceId
     * @param studyLogRecordEntity
     * @return
     */
    String saveStudyLogRecord(@NotBlank String traceId, @NotNull @Valid StudyLogRecordEntity studyLogRecordEntity);

    /**
     * 推荐记录批量存储
     *
     * @param traceId
     * @param recLogs
     * @return
     */
    List<String> saveRecLogList(@NotBlank String traceId, @NotEmpty List<@Valid RecLog> recLogs);

    /**
     * 答题记录存储
     *
     * @param traceId     跟踪id
     * @param feedbackLog 答题记录反馈
     * @return {@link String}记录Id
     */
    String saveFeedbackLog(@NotBlank String traceId, @NotNull @Valid FeedbackLog feedbackLog);

    FeedbackLog saveFeedbackLogAndGet(@NotBlank String traceId, @NotNull @Valid FeedbackLog feedbackLog);

    /**
     * 批量保存答题记录
     *
     * @param traceId
     * @param feedbackLogs
     * @return
     */
    List<String> saveFeedbackLogList(@NotBlank String traceId, @NotEmpty List<@Valid FeedbackLog> feedbackLogs);

    List<FeedbackLog> saveFeedbackLogListAndGet(@NotBlank String traceId, @NotEmpty List<@Valid FeedbackLog> feedbackLogs);

    List<FeedbackLog> saveStudyCorrectLogList(@NotBlank String traceId, @NotEmpty List<@Valid FeedbackLog> feedbackLogs);

    /**
     * 学情数据查询
     *
     * @param traceId       跟踪id
     * @param studyLogQuery 学情数据查询对象
     * @return {@link List}<{@link StudyLogData}>
     *     TODO 限定点下记录数量 200 、具体看之前业务细化。
     */
    @Deprecated
    List<StudyLogData> queryStudyLog(@NotBlank String traceId, @NotNull @Valid StudyLogQuery studyLogQuery);


    /**
     * 学情数据查询
     *
     * @param traceId       跟踪id
     * @param studyLogQuery 学情数据查询对象
     * @return {@link List}<{@link StudyLogData}>
     */
    List<StudyLogData> queryStudyLog5min(@NotBlank String traceId, @NotNull @Valid StudyLogQuery studyLogQuery);


    /**
     * 通用
     * <p>
     * 查询答题记录
     * <p>
     * 从数仓获取历史答题记录时，
     * 必有字段：userId、subjectCode、phaseCode、bizCode、nodeId、nodeType、resNodeId、resNodeType、updateTime、scoreRatio
     * 或有字段：bizAction、studyCode
     * 其他未列字段无。
     *
     * @param traceId
     * @param studyLogQuery
     * @return
     */
    List<StudyLogData> queryFeedbackLogs(@NotBlank String traceId, @NotNull @Valid StudyLogQuery studyLogQuery, Integer num);


    /**
     * 当天 近10条
     * 区分场景值 精准学os使用  所有答题日志
     * 查询答题记录  + 错题本答题
     * <p>
     *
     * @param traceId
     * @param studyLogQuery
     * @return
     */
    List<StudyLogData> queryFeedbackLogsToday(@NotBlank String traceId, @NotNull @Valid StudyLogQuery studyLogQuery, Integer num);


    /**
     * 查询用户最近答题记录。不区分点
     *
     * @param traceId
     * @param query
     * @return
     */
    List<StudyLogData> queryUserRecentlyAnswerRecord(@NotBlank String traceId, @Valid UserRecentlyStudyLogQuery query);


    /**
     * 学情日志删除（逻辑删除）
     *
     * @param traceId
     * @param idList
     * @return 逻辑删除的记录ID
     */
    List<String> deleteStudyLog(@NotBlank String traceId, @NotEmpty List<String> idList);

    /**
     * 学情日志删除（逻辑删除）by 用户id
     *
     * @param traceId
     * @param userId
     * @return count
     */
    Long deleteStudyLog(@NotBlank String traceId, @NotEmpty String userId);

    /**
     * 根据cacheKey查询推荐缓存
     *
     * @param traceId
     * @param cacheKey
     * @return
     */
    JSONObject queryRecommendRecordCache(@NotBlank String traceId, @NotBlank String cacheKey);

    /**
     * 保存推荐记录缓存
     *
     * @param traceId
     * @param cacheKey
     * @return
     */
    String saveRecommendRecordCache(@NotBlank String traceId, @NotBlank String cacheKey, @NotNull JSONObject content);


    /**
     * 存在答题记录
     *
     * @param traceId
     * @return studyLogQuery
     */
    boolean existStudentLog(@NotBlank String traceId, @NotNull @Valid StudyLogQuery studyLogQuery);
}
