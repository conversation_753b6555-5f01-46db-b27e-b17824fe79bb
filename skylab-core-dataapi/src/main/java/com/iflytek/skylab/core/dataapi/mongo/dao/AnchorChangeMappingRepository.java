package com.iflytek.skylab.core.dataapi.mongo.dao;

import com.iflytek.skylab.core.dataapi.mongo.DataApiMongoRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.AnchorChangeMapping;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AnchorChangeMappingRepository extends DataApiMongoRepository<AnchorChangeMapping, String> {

    /**
     * 分页查找
     *
     * @param pageable
     * @return
     */
    Page<AnchorChangeMapping> findPagedBy(Pageable pageable);

    /**
     * 查找排序后前 2(可以是N) 条记录
     *
     * @param sort
     * @return
     */
    List<AnchorChangeMapping> findTop2By(Sort sort);


    /**
     * 查找按ObjectId降序排列后的前2(可以是N) 条记录
     *
     * @return
     */
    List<AnchorChangeMapping> findTop2ByOrderByIdDesc();

}
