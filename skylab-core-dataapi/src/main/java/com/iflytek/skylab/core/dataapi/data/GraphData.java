package com.iflytek.skylab.core.dataapi.data;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.data.Jsonable;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 图查询返回结果
 */
@Getter
@Setter
public class GraphData extends Jsonable {

    /**
     * 图谱版本
     */
    private String graphVersion;

    /**
     * 点列表
     */
    private List<GraphVertex> vertices;

    /**
     * 边列表
     */
    private List<GraphEdge> edges;

    /**
     * 点
     */
    @Getter
@Setter
    @Builder
    public static class GraphVertex implements Serializable {
        public static final long serialVersionUID = -5401347991557731805L;

        /**
         * 点ID
         */
        private String id;

        /**
         * 点类型
         */
        private String label;

        /**
         * 点属性
         */
        private JSONObject properties;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            GraphVertex that = (GraphVertex) o;
            return Objects.equals(id, that.id);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id);
        }
    }

    /**
     * 边
     */
    @Getter
@Setter
    @Builder
    public static class GraphEdge implements Serializable {
        public static final long serialVersionUID = 934730721544975579L;
        /**
         * 边ID
         */
        private String id;

        /**
         * 边类型
         */
        private String label;

        /**
         * 源点
         */
        private String source;

        /**
         * 目标点
         */
        private String target;

        /**
         * 边属性
         */
        private JSONObject properties;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
