package com.iflytek.skylab.core.dataapi.data;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.data.Jsonable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 图查询入参，包括点查询和路径查询，如果是子图查询请使用 {@link SubGraphQuery} 类
 */
@Getter
@Setter
@Accessors(chain = true)
public class GraphQuery extends Jsonable {

    @NotBlank
    private String traceId;

    /**
     * 图谱版本
     */
    @NotBlank
    private String graphVersion;

    /**
     * 要查询的边列表，由这些边组成这个子图
     */
    @NotEmpty
    private List<EdgeLabel> edgeLabels;

    /**
     * 根节点类型
     *
     * <p>
     * 教材版本：PRESS
     * 教材书 ：BOOK
     * 目录章 ：UNIT
     * 目录节 ：COURSE
     * 图谱锚点：ANCHOR_POINT
     * 图谱考点：CHECK_POINT
     * 图谱知识点：KNOWLEDGE_POINT
     * 复习点：REVIEW_POINT
     * 资源题：TOPIC
     * 资源卡片：CARD
     * 资源视频：VIDEO
     * 学习路径：LEARN_PATH
     */
    @ApiModelProperty(allowableValues = "<br/>教材版本：PRESS<br/>" +
            "教材书 ：BOOK<br/>" +
            "目录章 ：UNIT<br/>" +
            "目录节 ：COURSE<br/>" +
            "图谱锚点：ANCHOR_POINT<br/>" +
            "图谱考点：CHECK_POINT<br/>" +
            "图谱知识点：KNOWLEDGE_POINT<br/>" +
            "复习点：REVIEW_POINT<br/>" +
            "资源题：TOPIC<br/>" +
            "资源卡片：CARD<br/>" +
            "资源视频：VIDEO<br/>" +
            "学习路径：LEARN_PATH")
    @NotBlank
    private String rootVertexLabel;

    /**
     * 根节点列表，从这些节点开始遍历子图
     */
    @NotEmpty
    private List<String> rootVertexIdList;

    /**
     * 尾节点类型（用于路径查询）
     */
    private String tailVertexLabel;

    /**
     * 尾节点列表（用于路径查询）
     */
    private List<String> tailVertexIdList;

    /**
     * 要查询的边
     */
    @Getter
@Setter
    @Accessors(chain = true)
    public static class EdgeLabel extends Jsonable {

        /**
         * 源节点类型
         *
         * <p>
         * 教材版本：PRESS
         * 教材书 ：BOOK
         * 目录章 ：UNIT
         * 目录节 ：COURSE
         * 图谱锚点：ANCHOR_POINT
         * 图谱考点：CHECK_POINT
         * 图谱知识点：KNOWLEDGE_POINT
         * 复习点：REVIEW_POINT
         * 资源题：TOPIC
         * 资源卡片：CARD
         * 资源视频：VIDEO
         * 学习路径：LEARN_PATH
         */
        @ApiModelProperty(allowableValues = "<br/>教材版本：PRESS<br/>" +
                "教材书 ：BOOK<br/>" +
                "目录章 ：UNIT<br/>" +
                "目录节 ：COURSE<br/>" +
                "图谱锚点：ANCHOR_POINT<br/>" +
                "图谱考点：CHECK_POINT<br/>" +
                "图谱知识点：KNOWLEDGE_POINT<br/>" +
                "复习点：REVIEW_POINT<br/>" +
                "资源题：TOPIC<br/>" +
                "资源卡片：CARD<br/>" +
                "资源视频：VIDEO<br/>" +
                "学习路径：LEARN_PATH")
        @NotBlank
        private String source;

        /**
         * 目标节点类型
         *
         * <p>
         * 教材版本：PRESS
         * 教材书 ：BOOK
         * 目录章 ：UNIT
         * 目录节 ：COURSE
         * 图谱锚点：ANCHOR_POINT
         * 图谱考点：CHECK_POINT
         * 图谱知识点：KNOWLEDGE_POINT
         * 复习点：REVIEW_POINT
         * 资源题：TOPIC
         * 资源卡片：CARD
         * 资源视频：VIDEO
         * 学习路径：LEARN_PATH
         */
        @ApiModelProperty(allowableValues = "<br/>教材版本：PRESS<br/>" +
                "教材书 ：BOOK<br/>" +
                "目录章 ：UNIT<br/>" +
                "目录节 ：COURSE<br/>" +
                "图谱锚点：ANCHOR_POINT<br/>" +
                "图谱考点：CHECK_POINT<br/>" +
                "图谱知识点：KNOWLEDGE_POINT<br/>" +
                "复习点：REVIEW_POINT<br/>" +
                "资源题：TOPIC<br/>" +
                "资源卡片：CARD<br/>" +
                "资源视频：VIDEO<br/>" +
                "学习路径：LEARN_PATH")
        @NotBlank
        private String target;

        /**
         * 边的过滤条件
         */
        private String filter;

        /**
         * 边的属性
         */
        private JSONObject properties;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
