package com.iflytek.skylab.core.dataapi.configuration;

import com.iflytek.skylab.core.dataapi.metric.SceneInfoMetricProvider;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * dataapi固定要加载的配置
 *
 * <AUTHOR>
 * @date 2022/3/23 17:44
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@PropertySource("classpath:/skylab-dataapi/application.properties")
@EnableFeignClients("com.iflytek.skylab.core.dataapi.feign")
public class DataApiAutoConfiguration {

    @Bean
    SkylabDataApiMeterProvider skylabDataApiMeterProvider(){
        return new SkylabDataApiMeterProvider();
    }
}
