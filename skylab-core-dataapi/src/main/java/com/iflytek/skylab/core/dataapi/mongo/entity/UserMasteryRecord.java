package com.iflytek.skylab.core.dataapi.mongo.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.CatalogTypeEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import com.iflytek.skylab.core.dataapi.data.GlobalMasteryItem;
import com.iflytek.skylab.core.dataapi.data.MasterItem;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;
import java.util.HashMap;

/**
 * 用户画像集合
 * userId - studyCode - nodeId - catalogId
 */
@Getter
@Setter
@Document(collection = "xxj_user_mastery_record")
@NoArgsConstructor
@AllArgsConstructor
public class UserMasteryRecord {
    private static CopyOptions copyOptions = CopyOptions.create();

    /**
     * mongo唯一标识
     */
    @Id
    private String id;

    /**
     * 用户Id
     */
    @Field(name = "user_id")
    private String userId;

    /**
     * 业务编码
     */
    @Field(name = "biz_code")
    private BizCodeEnum bizCode;

    /**
     * 学习场景
     */
    @Field(name = "study_code")
    private StudyCodeEnum studyCode;

    /**
     * 图谱版本
     */
    @Field(name = "graph_version")
    private String graphVersion;

    /**
     * 学科编码
     */
    @Field(name = "subject_code")
    private String subjectCode;

    /**
     * 学段编码
     */
    @Field(name = "phase_code")
    private String phaseCode;

    /**
     * 教材版本编码
     */
    @Field(name = "book_code")
    private String bookCode;

    /**
     * 父目录
     */
    @Field(name = "catalog_id")
    private String catalogId;

    /**
     * 父目录类型（章、节、复习点一级目录）
     */
    @Field(name = "catalog_type")
    private CatalogTypeEnum catalogType;

    /**
     * 点标识，点id，平铺存储数据
     */
    @Field(name = "node_id")
    private String nodeId;

    /**
     * 点类型（考点、锚点、复习点）
     */
    @Field(name = "node_type")
    private NodeTypeEnum nodeType;

    /**
     * 是否是应学点，如果没有计算则不存在该字段
     */
    @Field(name = "should_flag")
    private Boolean shouldFlag;

    /**
     * 融合画像
     */
    @Field(name = "fusion")
    private Double fusion;

    /**
     * 真实画像
     */
    @Field(name = "real")
    private Double real;

    /**
     * 预测画像
     */
    @Field(name = "predict")
    private Double predict;

    /**
     * 算法融合画像值
     */
    @Field(name = "algofusion")
    private Double algoFusion;

    /**
     * 算法真实画像值
     */
    @Field(name = "algoreal")
    private Double algoReal;

    /**
     * 算法预测画像值
     */
    @Field(name = "algopredict")
    private Double algoPredict;

    /**
     * 创建时间
     */
    @CreatedDate
    @Field(name = "create_time")
    private Instant createTime;

    /**
     * 更新时间，采用答题记录时间
     */
    @Field(name = "update_time")
    private Instant updateTime;

    /**
     * 画像得分
     */
    @Field(name = "mastery_score")
    private Double masteryScore;

    /**
     * 画像类型
     */
    @Field(name = "mastery_type")
    private String masteryType;

    /**
     * 最后一题答题时间
     */
    @Field(name = "last_answer_time")
    private Instant lastAnswerTime;

    /**
     * 全局畫像值
     */
    @Field(name = "global_mastery")
    private GlobalMasteryItem globalMastery;

    @Field(name = "delete_flag")
    private Integer deleteFlag = 0;


    public UserMasteryRecord(String userId, MasterItem item) {
        this.userId = userId;
        BeanUtil.copyProperties(item, this, copyOptions);
        this.deleteFlag = 0;
    }


    public MasterItem toMasterItem() {
        MasterItem item = new MasterItem();
        BeanUtil.copyProperties(this, item, copyOptions);
        if (this.getGlobalMastery() == null){
            GlobalMasteryItem global = new GlobalMasteryItem();
            global.setMasterScore(this.getMasteryScore());
            global.setFusion(this.getFusion());
            global.setPredict(this.getPredict());
            global.setReal(this.getReal());
            global.setAssociativePoint(new HashMap<>());
            item.setGlobalMastery((JSONObject) JSON.toJSON(global));
        } else {
            item.setGlobalMastery((JSONObject) JSON.toJSON(this.getGlobalMastery()));
        }
        return item;
    }

}
