package com.iflytek.skylab.core.dataapi.mongo.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Getter
@Setter
@Document(collection = "skyline_plan_version")
public class SkylinePlanVersion {
    
    @Id
    private String id;

    /**
     * 方案ID
     */
    @Field(name = "plan_id")
    private String planId;
    
    /**
     * 名称
     */
    @Field(name = "name")
    private String name;

    /**
     * 状态
     */
    @Field(name = "status")
    private Integer status;

    /**
     * 拓扑数据
     */
    @Field(name = "design_data")
    private JSONObject designData;
}
