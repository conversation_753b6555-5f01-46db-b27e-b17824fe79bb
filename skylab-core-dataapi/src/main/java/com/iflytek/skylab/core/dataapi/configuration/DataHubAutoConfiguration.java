package com.iflytek.skylab.core.dataapi.configuration;

import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.annotation.EnableBizDataAPI;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.annotation.EnableFeatureAPI;
import com.iflytek.skylab.core.dataapi.annotation.EnableGraphAPI;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 数据句柄JavaConfig
 */

@EnableFeatureAPI
@EnableBizDataAPI
@EnableGraphAPI
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableDataHub.class)
public class DataHubAutoConfiguration {
    @Bean
    public DataHub dataHub() {
        return new DataHub();
    }
}
