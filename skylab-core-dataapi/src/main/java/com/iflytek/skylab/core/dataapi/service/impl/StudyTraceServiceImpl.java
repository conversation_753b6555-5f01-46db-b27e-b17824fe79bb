package com.iflytek.skylab.core.dataapi.service.impl;

import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.mongo.dao.StudyTraceRecordRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyTraceRecord;
import com.iflytek.skylab.core.dataapi.service.StudyTraceService;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.annotation.Async;

/**
 * 学习行为追踪服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
public class StudyTraceServiceImpl implements StudyTraceService, ApplicationContextAware {

    private final StudyTraceRecordRepository studyTraceRecordRepository;
    private ApplicationContext applicationContext;

    public StudyTraceServiceImpl(StudyTraceRecordRepository studyTraceRecordRepository) {
        this.studyTraceRecordRepository = studyTraceRecordRepository;
    }

    @Override
    @SkylineMetric(value = "DataHub-3.学习行为追踪存储", meterProviders = SkylabDataApiMeterProvider.class)
    public String saveStudyTraceRecord(StudyTraceRecord studyTraceRecord) {
        if (log.isDebugEnabled()) {
            log.debug("studyTraceRecord= {}", studyTraceRecord);
        }
        // 入库   
        StudyTraceRecord saved = studyTraceRecordRepository.save(studyTraceRecord);
        return saved.getId();
    }

    @Async("asyncExecutor")
    @Override
    public void saveStudyTraceRecordAsync(StudyTraceRecord studyTraceRecord) {
        //防止 SkylineMetric AOP 失效
        this.applicationContext.getBean(StudyTraceService.class).saveStudyTraceRecord(studyTraceRecord);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
