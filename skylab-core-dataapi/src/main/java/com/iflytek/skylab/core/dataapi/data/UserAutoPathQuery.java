package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.data.Jsonable;
import lombok.Getter;
import lombok.Setter;

/**
 * 学习路径用户试题信息
 *
 * <AUTHOR>
 * @version 1.0
 * @Getter
@Setter
 * @EqualsAndHashCode(callSuper = true)
 * @ToString(callSuper = true)
 * @date 2022/9/16 15:02
 */
@Getter
@Setter
public class UserAutoPathQuery extends Jsonable {


    /**
     * 用户id
     */
    private String userId;
    /**
     * 学科
     */
    private String subjectCode;
    /**
     * 学段
     */
    private String phaseCode;


}
