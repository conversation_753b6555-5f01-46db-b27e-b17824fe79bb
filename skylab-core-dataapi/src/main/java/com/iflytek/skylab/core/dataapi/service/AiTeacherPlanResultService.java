package com.iflytek.skylab.core.dataapi.service;


import com.iflytek.skylab.core.dataapi.mongo.entity.PlanResultEntity;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AiTeacherPlanResultService {


    /**
     * 规划结果表查询接口
     *
     * @return
     */
    List<PlanResultEntity> query(@NotEmpty String userId, @NotEmpty String catalogId);

}
