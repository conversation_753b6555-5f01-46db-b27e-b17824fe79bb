package com.iflytek.skylab.core.dataapi.endpoint;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.dataapi.configuration.AsyncProperties;
import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;

import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2022/3/25 12:04 下午
 */
@Endpoint(id = "skyline-dataapi")
public class DataApiEndpoint {

    @Autowired(required = false)
    private AsyncProperties asyncProperties;

    @Autowired(required = false)
    private GraphProperties graphProperties;


    @ReadOperation
    public Object invoke() {
        Map<String, Object> status = new TreeMap<>();

        if (asyncProperties != null) {
            status.put("async-properties", asyncProperties);
        }
        if (graphProperties != null) {
            status.put("graph-properties", graphProperties);
        }
        return JSON.toJSONString(status);
    }
}
