package com.iflytek.skylab.core.dataapi.configuration;

import com.iflytek.cog2.feaflow.sdk.annotation.EnableSkylabZion;
import com.iflytek.skylab.core.dataapi.annotation.EnableBizDataAPI;
import com.iflytek.skylab.core.dataapi.annotation.EnableFeatureAPI;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skylab.core.dataapi.service.UserAutoPathService;
import com.iflytek.skylab.core.dataapi.service.impl.UserAutoPathServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/16 16:45
 */
@EnableFeatureAPI
@EnableSkylabZion
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableBizDataAPI.class)
public class UserAutoPathAutoConfiguration {

    @Bean
    @ConfigurationProperties("skylab.data.api.auto-path")
    public UserAutoPathProperties userAutoPathProperties() {
        return new UserAutoPathProperties();
    }

    @Bean
    @ConfigurationProperties("skylab.data.api.auto-path-topicht")
    public TopicHtProperties topicHtProperties() {
        return new TopicHtProperties();
    }

    @Bean
    @ConditionalOnBean(FeatureService.class)
    public UserAutoPathService userAutoPathService(FeatureService featureService, UserAutoPathProperties userAutoPathProperties, TopicHtProperties topicHtProperties) {
        return new UserAutoPathServiceImpl(featureService, userAutoPathProperties, topicHtProperties);
    }
}
