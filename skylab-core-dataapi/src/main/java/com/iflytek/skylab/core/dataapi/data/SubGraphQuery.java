package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.data.Jsonable;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 子图查询入参
 */
@Getter
@Setter
@Accessors(chain = true)
public class SubGraphQuery extends Jsonable {

    @NotBlank
    private String traceId;

    /**
     * 图谱版本
     */
    @NotBlank
    private String graphVersion;

    /**
     * 要查询的边列表，由这些边组成这个子图
     */
    @NotEmpty
    private List<EdgeLabel> edgeLabels;

    /**
     * 根节点类型
     */
    @NotBlank
    private String rootVertexLabel;

    /**
     * 根节点列表，从这些节点开始遍历子图
     */
    @NotEmpty
    private List<String> rootVertexIdList;

    /**
     * 返回类型 VERTICES点 或者 边 EDGES
     */
    private List<String> includeFields;

    /**
     * 点指定的属性
     * nodeProps.put("ANCHOR_POINT", Arrays.asList("anchorPointType", "difficulty", "evaluatePoint", "evaluations", "examPoint", "phaseCode", "realLastLevelRelationCatas", "subjectCode", "tracePoints"));
     */
    private Map<String, List<String>> nodeProps;

    /**
     * 要查询的边
     */
    @Getter
@Setter
    @Builder
    public static class EdgeLabel {

        /**
         * 源节点类型
         */
        @NotBlank
        private String source;

        /**
         * 目标节点类型
         */
        @NotBlank
        private String target;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
