package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iflytek.skylab.core.dataapi.DataHub;
import com.iflytek.skylab.core.dataapi.configuration.TopicHtProperties;
import com.iflytek.skylab.core.dataapi.configuration.UserAutoPathProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skylab.core.dataapi.service.UserAutoPathService;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 自动化学习路径查询实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/16 16:01
 */
@Slf4j
public class UserAutoPathServiceImpl implements UserAutoPathService {

    public static final String USER_ID = "user_id";
    public static final String SUBJECT_CODE = "subject_code";
    public static final String PHASE_CODE = "phase_code";
    public static final String TOPIC_ID = "topic_id";
    /**
     * 特征查询类
     */
    private final FeatureService featureService;
    /**
     * 学习路径默认属性
     */
    private final UserAutoPathProperties userAutoPathProperties;

    private final TopicHtProperties topicHtProperties;

    public UserAutoPathServiceImpl(FeatureService featureService, UserAutoPathProperties userAutoPathProperties, TopicHtProperties topicHtProperties) {
        this.featureService = featureService;
        this.userAutoPathProperties = userAutoPathProperties;
        this.topicHtProperties = topicHtProperties;
    }

    /**
     * 查询用户自动化路径数据
     *
     * @param traceId           链路id
     * @param userAutoPathQuery 用户自动化路径入参
     * @return UserAutoPathData
     */
    @Override
    @SkylineMetric(value = "DataHub-3.用户自动化学习路径查询", meterProviders = SkylabDataApiMeterProvider.class)
    public UserAutoPathData queryUserAutoPath(String traceId, UserAutoPathQuery userAutoPathQuery) {
        if (log.isDebugEnabled()) {
            log.debug("traceId={}, queryUserAutoPath query= {}", traceId, userAutoPathQuery);
        }
        try {
            FeatureQueryItem queryItem = new FeatureQueryItem();
            queryItem.setFeatureName(userAutoPathProperties.getFeatureName());
            queryItem.setFeatureVersion(userAutoPathProperties.getFeatureVersion());
            queryItem.setGraphVersion(userAutoPathProperties.getGraphVersion());

            List<Map<String, String>> params = Lists.newArrayList();
            Map<String, String> map = Maps.newHashMap();
            map.put(USER_ID, userAutoPathQuery.getUserId());
            map.put(SUBJECT_CODE, userAutoPathQuery.getSubjectCode());
            map.put(PHASE_CODE, userAutoPathQuery.getPhaseCode());
            params.add(map);
            queryItem.setParams(params);

            FeatureQuery featureQuery = new FeatureQuery();
            featureQuery.setItems(Lists.newArrayList(queryItem));

            FeatureData featureData = featureService.query(traceId, featureQuery);

            if (featureData == null || CollUtil.isEmpty(featureData.getItems())) {
                return null;
            }

            List<Map<String, String>> values = featureData.getItems().get(0).getValues();
            if (CollUtil.isEmpty(values)) {
                return null;
            }

            String featureName = userAutoPathProperties.getFeatureName();
            Map<String, String> resultMap = values.get(0);

            return JSON.parseObject(resultMap.get(featureName), UserAutoPathData.class);

        } catch (Exception e) {
            log.error("query queryUserAutoPath error", e);
        }

        return null;
    }

    /**
     * 查询试题ht
     *
     * @param traceId
     * @param topicIds
     * @return
     */
    @Override
    @SkylineMetric(value = "DataHub-3.试题难度与HT查询", meterProviders = SkylabDataApiMeterProvider.class)
    public Map<String, TopicInfo> queryTopicInfo(String traceId, List<String> topicIds) {
        Map<String, TopicInfo> result = Maps.newHashMap();
        try {
            List<Map<String, String>> params = new ArrayList<>();
            for (String topic : topicIds) {
                Map<String, String> map = Maps.newHashMap();
                map.put(TOPIC_ID, topic);
                params.add(map);
            }
            FeatureQueryItem ht = new FeatureQueryItem();
            ht.setFeatureName(topicHtProperties.getHtFeatureName());
            ht.setFeatureVersion(topicHtProperties.getFeatureVersion());
            ht.setGraphVersion(topicHtProperties.getGraphVersion());
            ht.setParams(params);

            FeatureQueryItem diff = new FeatureQueryItem();
            diff.setFeatureName(topicHtProperties.getDiffFeatureName());
            diff.setFeatureVersion(topicHtProperties.getFeatureVersion());
            diff.setGraphVersion(topicHtProperties.getGraphVersion());
            diff.setParams(params);

            FeatureQuery featureQuery = new FeatureQuery();
            featureQuery.setItems(Arrays.asList(ht, diff));

            FeatureData featureData = DataHub.getFeatureService().query(traceId, featureQuery);

            if (featureData == null || CollUtil.isEmpty(featureData.getItems())) {
                return Maps.newHashMap();
            }


            for (FeatureDataItem item : featureData.getItems()) {

                //ht
                String featureName = item.getFeatureName();
                if (topicHtProperties.getHtFeatureName().equals(featureName)) {
                    List<Map<String, String>> values = item.getValues();

                    for (Map<String, String> value : values) {
                        String topicId = MapUtil.getStr(value, TOPIC_ID);
                        if (StrUtil.isEmpty(topicId)) {
                            continue;
                        }
                        //初始化
                        if (!result.containsKey(topicId)) {
                            result.put(topicId, new TopicInfo(topicId));
                        }

                        List<Float> htFloat = Lists.newArrayList();
                        String htV = MapUtil.getStr(value, featureName);
                        if (StrUtil.isNotEmpty(htV)) {
                            JSONArray array = JSON.parseArray(htV);
                            for (Object o : array) {
                                float v = Float.parseFloat(o.toString());
                                htFloat.add(v);
                            }
                            //设置ht
                            result.get(topicId).setFeatures(htFloat);
                        }

                    }
                }
                //难度
                if (topicHtProperties.getDiffFeatureName().equals(featureName)) {
                    List<Map<String, String>> values = item.getValues();

                    for (Map<String, String> value : values) {
                        String topicId = MapUtil.getStr(value, TOPIC_ID);
                        if (StrUtil.isEmpty(topicId)) {
                            continue;
                        }
                        //初始化
                        if (!result.containsKey(topicId)) {
                            result.put(topicId, new TopicInfo(topicId));
                        }
                        String diffV = MapUtil.getStr(value, featureName);
                        if (StrUtil.isNotEmpty(diffV)) {
                            Integer v = Integer.parseInt(diffV);
                            //设置难度
                            result.get(topicId).setTopicDiff(v);
                        }
                    }
                }

            }

        } catch (Exception e) {
            log.error("auto path queryTopicInfo error", e);
        }
        return result;
    }


}
