package com.iflytek.skylab.core.dataapi.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserErrorData
 * @description TODO
 * @date 2024/6/17 19:21
 */
@Getter
@Setter
public class UserErrorData {

    @JSONField(name = "topic_id")
    private String topicId;

    @JSONField(name = "anchor_id")
    private String anchorId;

    @JSONField(name = "feedback_time")
    private Date feedbackTime;


}
