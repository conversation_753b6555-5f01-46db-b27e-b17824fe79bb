package com.iflytek.skylab.core.dataapi.mongo.dao;

import com.iflytek.skylab.core.dataapi.mongo.DataApiMongoRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.SkylinePlanVersion;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SkylinePlanVersionRepository extends DataApiMongoRepository<SkylinePlanVersion, String> {
    
    List<SkylinePlanVersion> findAllByPlanIdAndStatus(String planId, Integer status);
}
