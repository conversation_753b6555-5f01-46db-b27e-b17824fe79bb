package com.iflytek.skylab.core.dataapi.service.cache;

import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.GraphLabelQuery;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 图谱缓存
 */
@Slf4j
public class GraphCacheImpl implements GraphCache {

    private final Map<String, GraphData.GraphVertex> vertexMap = new HashMap<>(1024);
    private final Map<String, List<GraphData.GraphEdge>> edgeMap = new HashMap<>(1024);
    private final GraphService graphService;
    private final GraphProperties graphProperties;
    private volatile boolean initialized = false;

    public GraphCacheImpl(GraphService graphService, GraphProperties graphProperties) {
        this.graphService = graphService;
        this.graphProperties = graphProperties;

        if (graphProperties.isCacheEnabled()) {
            startInitializeThread();
        }
    }

    /**
     * 异步加载缓存
     */
    public void startInitializeThread() {
        new Thread(() -> {
            this.initialized = false;
            this.initialize();
            this.initialized = true;
        }).start();
    }

    /**
     * 判断缓存是否已完成初始化
     */
    @Override
    public boolean isInitialized() {
        return initialized;
    }

    /**
     * 查询考点
     */
    @Override
    public GraphData queryCheckPoint(List<String> checkPointIds) {
        return queryVertexAndRelatedEdge(checkPointIds);
    }

    /**
     * 查询锚点
     */
    @Override
    public GraphData queryAnchorPoint(List<String> anchorPointIds) {
        return queryVertexAndRelatedEdge(anchorPointIds);
    }

    /**
     * 查询复习点
     */
    @Override
    public GraphData queryReviewPoint(List<String> reviewPointIds) {
        return queryVertexAndRelatedEdge(reviewPointIds);
    }

    /**
     * 查询点和关联的边
     *
     * @param ids
     * @return
     */
    private GraphData queryVertexAndRelatedEdge(List<String> ids) {
        List<GraphData.GraphVertex> vertices = new ArrayList<>();
        List<GraphData.GraphEdge> edges = new ArrayList<>();
        for (String id : ids) {
            // 点
            if (vertexMap.containsKey(id)) {
                vertices.add(vertexMap.get(id));
            }
            // 点下的边
            if (edgeMap.containsKey(id)) {
                edges.addAll(edgeMap.get(id));
                // 边关联的点
                edgeMap.get(id).forEach(edge -> {
                    if (vertexMap.containsKey(edge.getTarget())) {
                        vertices.add(vertexMap.get(edge.getTarget()));
                    }
                });
            }
        }
        GraphData result = new GraphData();
        result.setVertices(vertices);
        result.setEdges(edges);
        return result;
    }

    /**
     * 初始化图谱缓存
     */
    private void initialize() {
        StopWatch sw = new StopWatch();
        log.info("Start to initialize Graph cache...");
        sw.start();
        initializeCheckPoint(graphProperties.getCacheVersion());
        initializeAnchorPoint(graphProperties.getCacheVersion());
        initializeReviewPoint(graphProperties.getCacheVersion());
        sw.stop();
        log.info("Graph cache initialized. Total {}s cost.", sw.getTime(TimeUnit.SECONDS));
    }

    /**
     * 加载考点数据，及考点下的题
     */
    private void initializeCheckPoint(String version) {
        loadVertexAndRelatedEdge(version, "CHECK_POINT", "TOPIC");
    }

    /**
     * 加载锚点数据，及锚点下的题
     */
    private void initializeAnchorPoint(String version) {
        loadVertexAndRelatedEdge(version, "ANCHOR_POINT", "TOPIC");
    }

    /**
     * 加载复习点数据，及复习点下的题
     */
    private void initializeReviewPoint(String version) {
        loadVertexAndRelatedEdge(version, "REVIEW_POINT", "TOPIC");
    }

    /**
     * 加载点和关联的边
     */
    private void loadVertexAndRelatedEdge(String version, String sourceLabel, String targetLabel) {
        GraphData graphData = graphService.lookup(new GraphLabelQuery()
                .setGraphVersion(version)
                .setTraceId(UUID.randomUUID().toString())
                .setLabel(sourceLabel));
        if (graphData != null && graphData.getVertices() != null) {
            log.info(String.format("Loading %s 0/%s", sourceLabel, graphData.getVertices().size()));
            for (int i = 0; i < graphData.getVertices().size(); i++) {
                log.info(String.format("Loading %s %s/%s", sourceLabel, (i + 1), graphData.getVertices().size()));
                GraphData.GraphVertex vertex = graphData.getVertices().get(i);
                loadVertexAndRelatedEdge(version, sourceLabel, targetLabel, vertex.getId());
            }
        }
    }

    /**
     * 加载点和关联的边
     */
    private void loadVertexAndRelatedEdge(String version, String sourceLabel, String targetLabel, String sourceId) {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion(version);
        subGraphQuery.setRootVertexLabel(sourceLabel);
        subGraphQuery.setRootVertexIdList(Collections.singletonList(sourceId));
        subGraphQuery.setEdgeLabels(Collections.singletonList(SubGraphQuery.EdgeLabel.builder().source(sourceLabel).target(targetLabel).build()));
        GraphData graphData = graphService.querySubGraph(subGraphQuery);
        fillCache(graphData);
    }

    /**
     * 填充缓存
     *
     * @param graphData
     */
    private void fillCache(GraphData graphData) {
        if (graphData != null && graphData.getVertices() != null) {
            graphData.getVertices().forEach(vertex -> {
                vertexMap.put(vertex.getId(), vertex);
            });
        }
        if (graphData != null && graphData.getEdges() != null) {
            graphData.getEdges().forEach(edge -> {
                if (!edgeMap.containsKey(edge.getSource())) {
                    edgeMap.put(edge.getSource(), new ArrayList<>());
                }
                if (edgeMap.get(edge.getSource()).stream().noneMatch(item -> item.getTarget().equals(edge.getTarget()))) {
                    edgeMap.get(edge.getSource()).add(edge);
                }
            });
        }
    }
}
