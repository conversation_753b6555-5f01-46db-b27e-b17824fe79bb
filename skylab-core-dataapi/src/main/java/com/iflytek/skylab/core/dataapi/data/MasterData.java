package com.iflytek.skylab.core.dataapi.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 画像查询 返回数据
 */
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@AllArgsConstructor
@Builder
public class MasterData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户Id
     */
    @NotBlank
    private String userId;

    /**
     * 数据列表
     */
    @NotEmpty
    @Valid
    private List<MasterItem> items = new ArrayList<>();

}
