package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.data.Jsonable;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/19 17:48
 */
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
public class TopicInfo extends Jsonable {

    public TopicInfo(String topicId) {
        this.topicId = topicId;
    }

    /**
     * 试题id
     */
    private String topicId;
    /**
     * 试题难度
     */
    private Integer topicDiff;
    /**
     * 试题HT
     */
    private List<Float> features;
}
