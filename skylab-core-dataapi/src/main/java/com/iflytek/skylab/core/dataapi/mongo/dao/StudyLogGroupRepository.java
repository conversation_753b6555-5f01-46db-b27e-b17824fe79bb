package com.iflytek.skylab.core.dataapi.mongo.dao;

import com.iflytek.skylab.core.dataapi.mongo.DataApiMongoRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyCorrectLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyMacrographLogRecordEntity;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 *
 */
@Repository
public interface StudyLogGroupRepository {

    /**
     * 按Nodeid分组查询作答记录
     * @param criteria
     * @param maxNum
     * @return
     */
    List<StudyLogRecordEntity> findStudyLogGroupByNodeId(Criteria criteria, int maxNum);

    /**
     * 按Nodeid分组查询批改记录
     * @param criteria
     * @param maxNum
     * @return
     */
    List<StudyCorrectLogRecordEntity> findStudyCorrectLogGroupByNodeId(Criteria criteria, int maxNum);

    /**
     * 按Nodeid分组查询错题本记录
     * @param criteria
     * @param maxNum
     * @return
     */
    List<StudyMacrographLogRecordEntity> findMacroGraphStudyLogGroupByNodeId(Criteria criteria, int maxNum);
}
