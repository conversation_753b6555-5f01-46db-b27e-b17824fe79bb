package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skyline.common.api.ApiResponseGeneric;
import com.iflytek.skyline.common.api.ApiResponseHeader;

/***
 * skylab 服务响应对象
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2022-02-08 16:35
 */
public class SkylabApiResponse<T> extends ApiResponseGeneric<T> {

    public SkylabApiResponse() {
    }

    public SkylabApiResponse(ApiResponseHeader header) {
        this(header, null);
    }

    public SkylabApiResponse(T payload) {
        this(new ApiResponseHeader(), payload);
    }

    public SkylabApiResponse(ApiResponseHeader header, T payload) {
        super(header, payload);
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
