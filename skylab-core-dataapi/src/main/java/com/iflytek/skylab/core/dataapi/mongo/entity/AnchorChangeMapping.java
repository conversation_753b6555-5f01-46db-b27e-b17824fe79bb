package com.iflytek.skylab.core.dataapi.mongo.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;

/**
 * 锚点映射关系，用于防止锚点变更后推出的锚点不可用的情况
 * 替换主要场景为：
 * 1、AI诊断锚点预测引擎（查库投票）
 * 2、DKT学情画像引擎
 */
@Getter
@Setter
@Document(collection = "anchor_change_mapping")
public class AnchorChangeMapping {

    /**
     * mongo唯一标识
     */
    @Id
    private String id;

    /**
     * 功能类型:
     * anchorPredict包含拍搜画像点识别和锚点预测引擎
     * userState包含应学点、同水平掌握度和DKT
     */
    private String type;

    /**
     * 学科code
     */
    private String subjectCode;

    /**
     * 学段code
     */
    private String phaseCode;

    /**
     * 教材版本code
     */
    private String bookVersionCode;
    /**
     * 章节code
     */
    private String catalogCode;
    /**
     * 老锚点ID
     */
    private String oldAnchorPointId;
    /**
     * 新锚点ID
     */
    private String newAnchorPointId;
    /**
     * 新锚点ID
     */
    @LastModifiedDate
    private Instant updateTime;
}
