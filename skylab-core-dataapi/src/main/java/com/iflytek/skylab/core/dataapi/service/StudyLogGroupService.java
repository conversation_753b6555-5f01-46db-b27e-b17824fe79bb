package com.iflytek.skylab.core.dataapi.service;

import com.iflytek.skylab.core.dataapi.data.StudyLogData;
import com.iflytek.skylab.core.dataapi.data.StudyLogQuery;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

public interface StudyLogGroupService {

    /**
     * 通用
     * <p>
     * 查询答题记录
     * <p>
     * 从数仓获取历史答题记录时，
     * 必有字段：userId、subjectCode、phaseCode、bizCode、nodeId、nodeType、resNodeId、resNodeType、updateTime、scoreRatio
     * 或有字段：bizAction、studyCode
     * 其他未列字段无。
     *
     * @param traceId
     * @param studyLogQuery
     * @return
     */
    List<StudyLogData> queryFeedbackLogsWithFeedBackTime(@NotBlank String traceId, @NotNull @Valid StudyLogQuery studyLogQuery, Long startTime, Long endTime, Integer num);
}
