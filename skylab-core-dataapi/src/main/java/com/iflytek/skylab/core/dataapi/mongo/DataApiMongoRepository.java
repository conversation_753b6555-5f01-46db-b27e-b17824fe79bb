package com.iflytek.skylab.core.dataapi.mongo;

import com.mongodb.client.result.UpdateResult;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.UpdateDefinition;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/25 10:26
 */
@NoRepositoryBean
public interface DataApiMongoRepository<T, ID extends Serializable> extends MongoRepository<T, Serializable> {


    /**
     * 按{@link Query}找到记录并更新{@link UpdateDefinition}中的数据
     * {@link FindAndModifyOptions} 控制更新时的要求
     *
     * @param query
     * @param update
     * @param options
     * @return
     */
    T findAndModify(Query query, UpdateDefinition update, FindAndModifyOptions options);

    /**
     * 删除符合条件的所有记录
     *
     * @param query
     * @return
     */
    List<T> findAllAndRemove(Query query);


    MongoOperations getMongoOperations();

    Class<T> getEntityClass();

    String getCollectionName();


    List<T> search(Criteria criteria);

    List<T> search(Query query);

    List<T> searchWithLimit(Criteria criteria, int limit);

    boolean exists(Criteria criteria);


    UpdateResult update(T entity);
}
