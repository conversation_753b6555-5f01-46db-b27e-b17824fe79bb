package com.iflytek.skylab.core.dataapi.mongo.entity;

import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PortraitMigrationRecord
 * @description TODO
 * @date 2024/4/23 16:11
 */
@Getter
@Setter
@Accessors(chain = true)
@Document(collection = "xxj_portrait_migration_record")
@NoArgsConstructor
public class PortraitMigrationRecord {

    /**
     * mongo唯一标识
     */
    @Id
    private String id;

    /**
     * 用户Id
     */
    @Field(name = "user_id")
    private String userId;

    /**
     * 学科编码
     */
    @Field(name = "subject_code")
    private String subjectCode;

    /**
     * 学段编码
     */
    @Field(name = "phase_code")
    private String phaseCode;

    /**
     * 学习场景
     */
    @Field(name = "study_code")
    private StudyCodeEnum studyCode;

    @Field(name = "create_time")
    private Instant createTime;

    /**
     * 点类型（考点、锚点、复习点）
     */
    @Field(name = "node_type")
    private NodeTypeEnum nodeType;
}
