package com.iflytek.skylab.core.dataapi.data;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.query.Criteria;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 画像查询对象
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MasterQuery extends AbstractQuery {

    /**
     * 用户id
     */
    @NotBlank
    private String userId;

    /**
     * 点类型，这个还是需要的，可能存在一个学习场景，获取多种点类型画像数据的情况
     */
    private NodeTypeEnum nodeType;
    /**
     * 点类型列表
     */
    private List<NodeTypeEnum> nodeTypes;
    /**
     * 范围列表
     * <p>
     * 锚点/考点/复习点
     */
    private List<String> nodeIdList;

    @Override
    public Criteria buildCriteria() {
        Criteria criteria = new Criteria();
        criteria.and("user_id").is(userId);
        if (nodeType != null) {
            criteria.and("node_type").is(nodeType);
        }
        if (CollUtil.isNotEmpty(nodeTypes)) {
            criteria.and("node_type").in(nodeTypes);
        }
        if (CollUtil.isNotEmpty(nodeIdList)) {
            criteria.and("node_id").in(nodeIdList);
        }
        return criteria;
    }
}
