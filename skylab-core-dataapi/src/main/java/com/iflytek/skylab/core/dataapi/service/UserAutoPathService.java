package com.iflytek.skylab.core.dataapi.service;

import com.iflytek.skylab.core.dataapi.data.TopicInfo;
import com.iflytek.skylab.core.dataapi.data.UserAutoPathData;
import com.iflytek.skylab.core.dataapi.data.UserAutoPathQuery;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 停用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/16 14:55
 */
@Deprecated
@Validated
public interface UserAutoPathService {


    /**
     * 查询用户自动化路径数据
     *
     * @param userAutoPathQuery 用户自动化路径入参
     * @return UserAutoPathData
     */
    @Deprecated
    UserAutoPathData queryUserAutoPath(@NotBlank String traceId, @NotNull @Valid UserAutoPathQuery userAutoPathQuery);

    /**
     * 查询试题信息（难度+ht）
     *
     * @param traceId
     * @param topicIds
     * @return
     */
    @Deprecated
    Map<String, TopicInfo> queryTopicInfo(@NotBlank String traceId, List<String> topicIds);

}
