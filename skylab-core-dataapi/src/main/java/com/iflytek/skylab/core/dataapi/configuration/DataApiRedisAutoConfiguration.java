package com.iflytek.skylab.core.dataapi.configuration;

import com.iflytek.skylab.core.dataapi.annotation.EnableDataApiRedis;
import com.iflytek.skylab.core.dataapi.redis.FeatureRedisCacheService;
import com.iflytek.skylab.core.dataapi.redis.RedisMemoryMonitorService;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 特征API自动配置
 */
@EnableScheduling
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableDataApiRedis.class)
public class DataApiRedisAutoConfiguration {

    @Bean
    public RedisMemoryMonitorService redisMemoryMonitorService(RedisTemplate<String, byte[]> binaryRedisTemplate, FeatureRedisCacheProperties featureCacheProperties) {
        return new RedisMemoryMonitorService(binaryRedisTemplate, featureCacheProperties);
    }

    @Bean
    public FeatureRedisCacheService featureCacheService(RedisTemplate<String, byte[]> binaryRedisTemplate, RedisMemoryMonitorService redisMemoryMonitorService, FeatureRedisCacheProperties featureCacheProperties) {
        return new FeatureRedisCacheService(binaryRedisTemplate, redisMemoryMonitorService, featureCacheProperties);
    }

    /**
     * 配置二进制RedisTemplate，用于高性能缓存存储
     */
    @Bean
    public RedisTemplate<String, byte[]> binaryRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, byte[]> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用StringRedisSerializer作为key序列化器
        template.setKeySerializer(new org.springframework.data.redis.serializer.StringRedisSerializer());

        // 使用自定义的字节数组序列化器作为value序列化器
        RedisSerializer<byte[]> byteArraySerializer = new RedisSerializer<byte[]>() {
            @Override
            public byte[] serialize(byte[] bytes) throws org.springframework.data.redis.serializer.SerializationException {
                return bytes;
            }

            @Override
            public byte[] deserialize(byte[] bytes) throws org.springframework.data.redis.serializer.SerializationException {
                return bytes;
            }
        };

        template.setValueSerializer(byteArraySerializer);
        template.setHashKeySerializer(new org.springframework.data.redis.serializer.StringRedisSerializer());
        template.setHashValueSerializer(byteArraySerializer);

        template.afterPropertiesSet();
        return template;
    }

}
