package com.iflytek.skylab.core.dataapi.data;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.FlagEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.query.Criteria;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 学情数据查询对象
 */
@Getter
@Setter
@Accessors(chain = true)
public class StudyLogQuery extends AbstractQuery {

    /**
     * 用户id
     */
    @NotBlank
    private String userId;

    /**
     * 学科编码
     */
    @NotBlank
    private String subjectCode;

    /**
     * 学段编码
     */
    @NotBlank
    private String phaseCode;

    /**
     * 点类型
     */
    private NodeTypeEnum nodeType;

    /**
     * 关联点Id列表
     */
    @NotEmpty
    private List<String> nodeIdList;

    /**
     * 资源点Id列表，可选条件
     */
    private List<String> resNodeIdList;

    /**
     * 业务编码
     */
    @NotEmpty
    private List<BizCodeEnum> bizCodeList;

    /**
     * 轮标识，可选参数，查询特定测评下的数据
     */
    private String roundId;

    /**
     * 功能编码
     */
    private String funcCode;

    /**
     * 学习场景
     */
    private StudyCodeEnum studyCode;

    /**
     * 是否包含已经逻辑删除。默认不查已逻辑删除的
     */
    private boolean containsDeleted;


    //    @JSONField(serialize = false)
    @Override
    public Criteria buildCriteria() {
        Criteria criteria = new Criteria()
                .and("user_id").is(userId)
                .and("node_id").in(nodeIdList)
                .and("biz_code").in(bizCodeList.stream()
                        .map(BizCodeEnum::name)
                        .collect(Collectors.toList())
                );

        if (nodeType != null) {
            criteria.and("node_type").is(nodeType);
        }

        if (CollectionUtil.isNotEmpty(resNodeIdList)) {
            criteria.and("res_node_id").in(resNodeIdList);
        }

//        if (StrUtil.isNotEmpty(roundId)) {
//            criteria.and("mission_id").is(roundId);
//        }

        if (StrUtil.isNotEmpty(funcCode)) {
            criteria.and("func_code").is(funcCode);
        }

        if (StrUtil.isNotEmpty(subjectCode)) {
            criteria.and("subject_code").is(subjectCode);
        }

        if (StrUtil.isNotEmpty(phaseCode)) {
            criteria.and("phase_code").is(phaseCode);
        }

        if (studyCode != null) {
            criteria.and("study_code").is(studyCode.name());
        }

        if (!containsDeleted) {
            criteria.and("delete_flag").in(FlagEnum.FALSE.intValue(), null);
        }

        return criteria;
    }

    // @Override
    // public String toString() {
    //     return super.toString();
    // }
}
