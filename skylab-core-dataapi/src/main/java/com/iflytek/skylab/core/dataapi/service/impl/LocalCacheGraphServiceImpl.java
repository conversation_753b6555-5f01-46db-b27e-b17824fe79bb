package com.iflytek.skylab.core.dataapi.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.service.cache.LocalFileGraphCache;
import com.iflytek.skylab.core.dataapi.util.GraphUtils;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
public class LocalCacheGraphServiceImpl implements GraphService {

    private final LocalFileGraphCache graphCache;

    private final GraphService graphService;

    private final GraphProperties graphProperties;

    public LocalCacheGraphServiceImpl(LocalFileGraphCache graphCache, GraphProperties graphProperties, GraphService graphService) {
        this.graphCache = graphCache;
        this.graphService = graphService;
        this.graphProperties = graphProperties;
    }

    @Override
    public GraphData querySubGraph(SubGraphQuery subGraphQuery) {
        if (useCache(subGraphQuery)) {
            return queryCachedSubGraph(subGraphQuery);
        } else {
            return graphService.querySubGraph(subGraphQuery);
        }
    }

    @Override
    public GraphData queryCachedSubGraph(SubGraphQuery subGraphQuery) {
        Set<String> tagTypeOrEdgeType = new HashSet<>();

        if (null != subGraphQuery.getRootVertexLabel()) {
            tagTypeOrEdgeType.add(subGraphQuery.getRootVertexLabel());
        }

        if (null != subGraphQuery.getEdgeLabels()) {
            subGraphQuery.getEdgeLabels().forEach(e ->
            {
                tagTypeOrEdgeType.add(e.getSource());
                tagTypeOrEdgeType.add(e.getTarget());
                tagTypeOrEdgeType.add(e.getSource() + "_" + e.getTarget());
            });
        }

        List<GraphData.GraphVertex> vertices = new ArrayList<>();
        List<GraphData.GraphEdge> edges = new ArrayList<>();
        // 获取子图最长路径
        Integer longestPath = GraphUtils.getDPHLongestPath(subGraphQuery.getEdgeLabels());
        graphCache.bfs(subGraphQuery.getRootVertexIdList(), tagTypeOrEdgeType, vertices, edges, longestPath);

        GraphData result = new GraphData();
        result.setVertices(vertices);
        result.setEdges(edges);
        return result;
    }

    @Override
    public GraphData querySubGraphNeedProps(SubGraphQuery subGraphQuery) {
        return this.querySubGraph(subGraphQuery);
    }

    @SkylineMetric(value = "DataHub-1.图谱-查询某个顶点详情", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public GraphData queryVertex(GraphVertexQuery graphVertexQuery) {
        if (useCache(graphVertexQuery)) {
            return graphCache.queryVertexes(Lists.newArrayList(graphVertexQuery.getId()));
        } else {
            return graphService.queryVertex(graphVertexQuery);
        }
    }

    @Override
    public GraphData queryVertexByIds(GraphVertexesQuery query) {
        if (useCache(query)) {
            return graphCache.queryVertexes(query.getIds());
        } else {
            return graphService.queryVertexByIds(query);
        }
    }

    @Override
    public GraphData lookup(GraphLabelQuery graphLabelQuery) {
        return graphService.lookup(graphLabelQuery);
    }

    @Override
    public GraphData lookup(GraphLabelQuery query, JSONObject props) {
        return graphService.lookup(query, props);
    }


    @Override
    public void saveVertex(GraphVertex graphVertex) {
        throw new RuntimeException("不允许的操作 添加点缓存");
    }

    @Override
    public void deleteVertex(GraphVertex graphVertex) {
        throw new RuntimeException("不允许的操作 删除点缓存");
    }

    @Override
    public void saveEdge(GraphEdge graphEdge) {
        throw new RuntimeException("不允许的操作 添加边缓存");
    }

    @Override
    public void deleteEdge(GraphEdge graphEdge) {
        throw new RuntimeException("不允许的操作 删除边缓存");
    }

    @Override
    public GraphData queryPath(GraphQuery graphQuery) {
        return graphService.queryPath(graphQuery);
    }

    @Override
    public GraphData queryPathReverse(GraphQuery graphQuery) {
        return graphService.queryPathReverse(graphQuery);
    }

    @Override
    public GraphData queryVertices(GraphQuery graphQuery) {
        return graphService.queryVertices(graphQuery);
    }

    @Override
    public GraphData queryVerticesReverse(GraphQuery graphQuery) {
        return graphService.queryVerticesReverse(graphQuery);
    }

    @Override
    public List<GraphData.GraphVertex> queryVertexByIdsWithPropsCache(GraphVertexesQuery query) {
        return this.queryVertexByIds(query).getVertices();
    }

    @Override
    public GraphData findUpperLayer(UpperLayerQuery query) {
        return graphService.findUpperLayer(query);
    }


    private boolean useCache(SubGraphQuery query) {
        return graphProperties.isLocalCacheEnabled() && (StringUtils.isBlank(query.getGraphVersion()) || graphProperties.getCacheVersion().equals(query.getGraphVersion()));
    }

    private boolean useCache(GraphVertexesQuery query) {
        return graphProperties.isLocalCacheEnabled() && (StringUtils.isBlank(query.getGraphVersion()) || graphProperties.getCacheVersion().equals(query.getGraphVersion()));
    }

    private boolean useCache(GraphVertexQuery query) {
        return graphProperties.isLocalCacheEnabled() && (StringUtils.isBlank(query.getGraphVersion()) || graphProperties.getCacheVersion().equals(query.getGraphVersion()));
    }
}
