package com.iflytek.skylab.core.dataapi.util;

import com.iflytek.skylab.core.constant.NodeTypeEnum;

/**
 * <AUTHOR>
 * @date 2022/5/6 11:30
 */
public class FeatureNodeTypeUtils {

    public static final String ANCHOR_POINT = "ANCHOR_POINT";
    public static final String CHECK_POINT = "CHECK_POINT";
    public static final String REVIEW_POINT = "REVIEW_POINT";


    public static String transform(NodeTypeEnum nodeTypeEnum) {
        if (nodeTypeEnum == NodeTypeEnum.ANCHOR_POINT) {
            return ANCHOR_POINT;
        }
        if (nodeTypeEnum == NodeTypeEnum.CHECK_POINT) {
            return CHECK_POINT;
        }
        if (nodeTypeEnum == NodeTypeEnum.REVIEW_POINT) {
            return REVIEW_POINT;
        }
        return null;
    }

    public static NodeTypeEnum transform(String pointType) {
        if (ANCHOR_POINT.equalsIgnoreCase(pointType)) {
            return NodeTypeEnum.ANCHOR_POINT;
        }

        if (CHECK_POINT.equalsIgnoreCase(pointType)) {
            return NodeTypeEnum.CHECK_POINT;
        }

        if (REVIEW_POINT.equalsIgnoreCase(pointType)) {
            return NodeTypeEnum.CHECK_POINT;
        }

        return null;
    }
}
