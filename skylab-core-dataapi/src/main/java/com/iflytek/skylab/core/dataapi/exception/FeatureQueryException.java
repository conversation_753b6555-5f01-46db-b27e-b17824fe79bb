package com.iflytek.skylab.core.dataapi.exception;

/**
 * 特征查询异常
 *
 * <AUTHOR>
 * @date 2022/3/1 11:14 上午
 */
public class FeatureQueryException extends SkylabException {
    public FeatureQueryException(int code, String message, Object... args) {
        super(code, message, args);
    }

    public FeatureQueryException(int code, String message) {
        super(code, message);
    }

    public FeatureQueryException(int code, String message, Throwable e) {
        super(code, message, e);
    }
}
