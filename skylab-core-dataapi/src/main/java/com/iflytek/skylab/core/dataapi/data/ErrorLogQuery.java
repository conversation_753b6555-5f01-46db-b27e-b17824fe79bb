package com.iflytek.skylab.core.dataapi.data;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.query.Criteria;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ErrorLogQuery
 * @description TODO
 * @date 2024/6/17 20:01
 */
@Getter
@Setter
public class ErrorLogQuery extends AbstractQuery {

    /**
     * 用户id
     */
    @NotBlank
    private String userId;

    /**
     * 学科编码
     */
    @NotBlank
    private String subjectCode;

    /**
     * 学段编码
     */
    @NotBlank
    private String phaseCode;

    @Override
    public Criteria buildCriteria() {
        Criteria criteria = new Criteria()
                .and("user_id").is(userId)
                .and("subject_code").is(subjectCode)
                .and("phase_code").is(phaseCode);
        return criteria;
    }
}
