package com.iflytek.skylab.core.dataapi.data;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.Instant;

/**
 * 学情数据对象
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StudyLogData extends StudyLogBase {

    /**
     * 推荐记录对象
     */
    private StudyLogRecordEntity.RecProps recProps;


    /**
     * 答题耗时(单位：秒)
     */
    @NotNull
    private Integer timeCost;

    /**
     * 得分
     */
    @NotNull
    private Double score;

    /**
     * 标准得分
     */
    @NotNull
    private Double standardScore;

    /**
     * 真实得分率
     */
    @NotNull
    private Double scoreRatio;

    /**
     * 反馈扩展
     */
    private JSONObject feedbackExt;

    /**
     * 反馈时间，客户端提交时间
     */
    private Instant feedbackTime;

    /**
     * 更新时间，采用答题记录时间
     */
    private Instant updateTime;

    /**
     * 逻辑删除标志
     */
    private Integer deleteFlag;

    // @Override
    // public String toString() {
    //     return super.toString();
    // }
}
