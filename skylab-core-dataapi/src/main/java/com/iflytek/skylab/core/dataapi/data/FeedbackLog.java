package com.iflytek.skylab.core.dataapi.data;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.Instant;

/**
 * 答题记录反馈数据对象
 * <p>
 * 根据  以下属性 查找 推荐记录
 * refTraceId（如果 roundId、roundIndex 数据比较准确，后续可以采用这个） by lyhu
 * userId
 * studyCode
 * nodeId
 * resNodeId
 */
@Getter
@Setter
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FeedbackLog extends StudyLogBase {

    /**
     * 关联的推荐记录的traceId
     */
    private String refTraceId;

    /**
     * 批改TraceId
     */
    private String correctTraceId;

    /**
     * 答题耗时(单位：秒)
     */
    @NotNull
    private Integer timeCost;

    /**
     * 得分
     */
    @NotNull
    private Double score;

    /**
     * 标准得分
     */
    @NotNull
    private Double standardScore;

    /**
     * 真实得分率
     */
    @NotNull
    private Double scoreRatio;

    /**
     * 反馈扩展
     */
    private JSONObject feedbackExt;

    /**
     * 反馈时间，客户端提交时间
     */
    @NotNull
    private Instant feedbackTime;

    /**
     * 测评是否结束
     */
    private boolean evalEnd = false;


    private String  correctType;


    // @Override
    // public String toString() {
    //     return super.toString();
    // }
}
