package com.iflytek.skylab.core.dataapi.configuration;

import com.iflytek.skylab.core.dataapi.annotation.EnableGraphAPI;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.service.cache.GraphCache;
import com.iflytek.skylab.core.dataapi.service.cache.GraphCacheImpl;
import com.iflytek.skylab.core.dataapi.service.cache.LocalFileGraphCache;
import com.iflytek.skylab.core.dataapi.service.impl.CachedGraphServiceImpl;
import com.iflytek.skylab.core.dataapi.service.impl.GraphServiceImpl;
import com.iflytek.skylab.core.dataapi.service.impl.LocalCacheGraphServiceImpl;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableGraphAPI.class)
public class GraphAutoConfiguration {

    @Bean
    @ConfigurationProperties("skylab.data.api.graph")
    public GraphProperties graphProperties() {
        return new GraphProperties();
    }

    @Bean
    public NebulaPool nebulaPool(GraphProperties graphProperties) {
        try {
            List<HostAddress> addresses = Arrays.stream(graphProperties.getHosts().split(",")).map(item -> {
                String ip = item.split(":")[0];
                int port = Integer.parseInt(item.split(":")[1]);
                return new HostAddress(ip, port);
            }).collect(Collectors.toList());

            NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
            nebulaPoolConfig.setMaxConnSize(graphProperties.getMaxConnSize());
            nebulaPoolConfig.setMinConnSize(graphProperties.getMinConnSize());
            nebulaPoolConfig.setTimeout(graphProperties.getTimeout());
            nebulaPoolConfig.setIdleTime(graphProperties.getIdleTime());
            nebulaPoolConfig.setIntervalIdle(graphProperties.getIntervalIdle());
            nebulaPoolConfig.setWaitTime(graphProperties.getWaitTime());
            NebulaPool pool = new NebulaPool();
            pool.init(addresses, nebulaPoolConfig);
            return pool;
        } catch (Exception e) {
            log.error("Nebula pool init error", e);
            return null;
        }
    }

    @Bean
    @Primary
    public GraphCache graphCache(NebulaPool nebulaPool, GraphProperties graphProperties) {
        return new GraphCacheImpl(new GraphServiceImpl(nebulaPool, graphProperties), graphProperties);
    }

    @Bean
    public LocalFileGraphCache graphLocalFileGraphCache(GraphProperties graphProperties) throws Exception {
        return new LocalFileGraphCache(graphProperties);
    }

    @Bean
    @Primary
    public GraphService graphService(NebulaPool nebulaPool, GraphProperties graphProperties, GraphCache graphCache) {
        return new CachedGraphServiceImpl(nebulaPool, graphProperties, graphCache);
    }

    @Bean("localGraphService")
    public GraphService localGraphService(GraphService graphService, GraphProperties graphProperties, LocalFileGraphCache graphCache) {
        return new LocalCacheGraphServiceImpl(graphCache, graphProperties, graphService);
    }
}
