package com.iflytek.skylab.core.dataapi.data;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.CatalogTypeEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

/**
 * 用户画像掌握度Item
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MasterItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 记录Id
     */
    // @NotBlank
    private String id;

    /**
     * 业务编码
     */
    private BizCodeEnum bizCode;

    /**
     * 学习场景
     */
    private StudyCodeEnum studyCode;

    /**
     * 教程版本
     */
    private String bookCode;

    /**
     * 图谱版本
     */
    private String graphVersion;

    /**
     * 父目录
     */
    private String catalogId;

    /**
     * 父目录类型
     */
    private CatalogTypeEnum catalogType;

    /**
     * 点标识
     */
    @NotBlank
    private String nodeId;

    /**
     * 点类型
     */
    @NotNull
    private NodeTypeEnum nodeType;

    /**
     * 融合画像值
     */
    private Double fusion;

    /**
     * 真实画像
     */
    private Double real;

    /**
     * 预测画像
     */
    private Double predict;

    /**
     * 是否是应学点
     */
    private Boolean shouldFlag;

    /**
     * 算法融合画像值
     */
    private Double algoFusion;

    /**
     * 算法真实画像值
     */
    private Double algoReal;

    /**
     * 算法预测画像值
     */
    private Double algoPredict;

    /**
     * 创建时间，时间戳
     */
    private Instant createTime;

    /**
     * 更新时间，时间戳
     */
//    @NotNull
    private Instant updateTime;

    /**
     * 画像得分
     */
    private Double masteryScore;

    /**
     * 画像类型
     */
    private String masteryType;

    /**
     * 最后一题答题时间
     */
    private Instant lastAnswerTime;

    /**
     * 全局畫像值
     */
    private JSONObject globalMastery;

    // @Override
    // public String toString() {
    //     return super.toString();
    // }
}
