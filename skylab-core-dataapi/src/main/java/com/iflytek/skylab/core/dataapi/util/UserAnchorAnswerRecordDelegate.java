package com.iflytek.skylab.core.dataapi.util;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.NodeTypeEnum;
import com.iflytek.skylab.core.constant.ResourceTypeEnum;
import com.iflytek.skylab.core.dataapi.configuration.StudyLogProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/24 17:12
 */
@Slf4j
public class UserAnchorAnswerRecordDelegate {
    // user_id#biz_code[ZSY_XXJ]#anchorpoint_code
    private static final String FEATURE_NAME = "user_answer_topics";

    private static final CopyOptions copyOptionsIgnoreError = CopyOptions.create().ignoreNullValue().ignoreError();

    public static FeatureQuery generateFeatureQuery(List<String> nodeIds, StudyLogQuery query, StudyLogProperties studyLogProperties) {
        List<BizCodeEnum> bizCodes = query.getBizCodeList();

        List<Map<String, String>> params = Lists.newArrayList();
        // user_id#biz_code[ZSY_XXJ]#anchorpoint_code
        for (String nodeId : nodeIds) {
            for (BizCodeEnum bizCode : bizCodes) {
                params.add(buildParam(query, bizCode, nodeId));
            }
        }

        FeatureQueryItem queryItem = new FeatureQueryItem();
        queryItem.setFeatureName(FEATURE_NAME);
        queryItem.setFeatureVersion(studyLogProperties.getFeatureVersion());
        queryItem.setGraphVersion(studyLogProperties.getGraphVersion());
        queryItem.setParams(params);

        FeatureQuery featureQuery = new FeatureQuery();
        featureQuery.setItems(Lists.newArrayList(queryItem));
        return featureQuery;
    }

    public static MultiValueMap<String, StudyLogData> parseFeatureData(FeatureData featureData) {
        if (featureData == null || CollectionUtil.isEmpty(featureData.getItems())) {
            return null;
        }

        List<Map<String, String>> values = featureData.getItems().get(0).getValues();
        if (CollectionUtil.isEmpty(values)) {
            return null;
        }

        Date yesterday = DateUtil.yesterday().toJdkDate();
        Instant yesterdayZeroClock = DateUtil.beginOfDay(yesterday).toInstant();

        MultiValueMap<String, StudyLogData> multiValueMap = new LinkedMultiValueMap<>();
        // 遍历查询结果
        for (Map<String, String> valueMap : values) {
            String json = valueMap.get(FEATURE_NAME);
            if (StrUtil.isBlank(json)) {
                continue;
            }
            // json为对象
            List<UserAnswerTopic> topicDetails = JSON.parseArray(json, UserAnswerTopic.class);


            if (CollectionUtil.isEmpty(topicDetails)) {
                log.warn("user_answer_topics no data find");
                return null;
            }
            if (log.isDebugEnabled()) {
                log.debug("user_answer_topics result size()={}", topicDetails.size());
                log.debug("user_answer_topics result : {}", JSON.toJSONString(topicDetails));
                // 组装数据
            }
            for (UserAnswerTopic topic : topicDetails) {
                // 过滤昨天的数据。因为近两天从mongo查
                StudyLogData studyLogData = new StudyLogData();
                studyLogData.setNodeId(valueMap.get("anchorpoint_code"));
                studyLogData.setNodeType(NodeTypeEnum.ANCHOR_POINT);
                studyLogData.setUserId(valueMap.get("user_id"));
                //默认值-开始
                studyLogData.setSubjectCode("02");
                studyLogData.setPhaseCode("04");
                studyLogData.setGraphVersion("bg-1");
                studyLogData.setBizCode(BizCodeEnum.ZSY_XXJ);
                //默认值-结束
                studyLogData.setResNodeId(topic.getTopicId());
                studyLogData.setResNodeType(ResourceTypeEnum.TOPIC);
                studyLogData.setScoreRatio(topic.getScoreRatio());
                if (topic.getFeedbackTime() == null) {
                    continue;
                }
                studyLogData.setFeedbackTime(Instant.ofEpochMilli(topic.getFeedbackTime().getTime()));
                studyLogData.setUpdateTime(studyLogData.getFeedbackTime());
                // 从数仓查前天及之前的，mongo近两天的
                if (studyLogData.getFeedbackTime() != null && studyLogData.getFeedbackTime().isBefore(yesterdayZeroClock)) {
                    multiValueMap.add(studyLogData.getNodeId(), studyLogData);
                }
            }
        }

        // 按updateTime倒序
        multiValueMap.forEach((k, v) -> v.sort((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime())));
        return multiValueMap;
    }


    private static Map<String, String> buildParam(StudyLogQuery query, BizCodeEnum bizCode, String pointCode) {
        Map<String, String> featureParam = new HashMap<>();
        featureParam.put("user_id", query.getUserId());
        featureParam.put("biz_code", bizCode.name());
        featureParam.put("anchorpoint_code", pointCode);
        return featureParam;
    }

}
