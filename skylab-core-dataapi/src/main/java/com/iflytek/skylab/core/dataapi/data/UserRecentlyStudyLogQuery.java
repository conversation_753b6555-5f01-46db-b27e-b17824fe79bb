package com.iflytek.skylab.core.dataapi.data;

import cn.hutool.core.date.DateUtil;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.FlagEnum;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;
import org.springframework.data.mongodb.core.query.Criteria;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/24 09:51
 */
@Getter
@Setter
public class UserRecentlyStudyLogQuery extends AbstractQuery {

    @NotBlank
    private String userId;
    @NotBlank
    private String subjectCode;
    @NotBlank
    private String phaseCode;
    @NotNull
    private BizCodeEnum bizCode;
    @Range(min = 1, max = 200)
    private int limit = 1;

    /**
     * 是否包含已经逻辑删除。默认不查已逻辑删除的
     */
    private boolean containsDeleted;

    @Override
    public Criteria buildCriteria() {
        Date yesterday = DateUtil.yesterday().toJdkDate();

        Criteria criteria = new Criteria()
                .and("time_cost").ne(null)
                .and("feedback_time").gte(DateUtil.beginOfDay(yesterday))
                .and("user_id").is(userId)
                .and("subject_code").is(subjectCode)
                .and("phase_code").is(phaseCode)
                .and("biz_code").is(bizCode.name());

        if (!containsDeleted) {
            criteria.and("delete_flag").in(FlagEnum.FALSE.intValue(), null);
        }

        return criteria;
    }
}
