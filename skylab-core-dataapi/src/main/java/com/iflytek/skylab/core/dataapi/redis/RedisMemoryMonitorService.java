package com.iflytek.skylab.core.dataapi.redis;

import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.dataapi.configuration.FeatureRedisCacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


@Slf4j
/**
 * Redis内存监控服务
 * 负责定时监控Redis内存使用率，控制缓存写入，定期打印缓存命中率统计，并支持定时重置命中率统计。
 * <p>
 * 主要功能：
 * - 定时刷新Redis内存使用率，防止写满
 * - 定时打印全局及分特征缓存命中率
 * - 定时重置缓存命中率统计
 * - 提供内存阈值判断接口
 * </p>
 */
public class RedisMemoryMonitorService {
    /**
     * 当前Redis内存使用率（0~1之间）
     */
    private volatile double redisMemoryUsage = 0.0;

    private static final String REDIS_MEMORY_MONITOR_LOCK_KEY = "skylab:dataapi:redis_memory_monitor_lock";
    private static final String REDIS_MEMORY_USAGE_KEY = "skylab:dataapi:redis_memory_usage";
    private static final String INSTANCE_ID = UUID.randomUUID().toString();

    /**
     * Redis二进制模板
     */
    private final RedisTemplate<String, byte[]> binaryRedisTemplate;
    /**
     * 缓存配置属性
     */
    private final FeatureRedisCacheProperties cacheProperties;

    /**
     * 构造方法，注入依赖
     *
     * @param binaryRedisTemplate Redis二进制模板
     * @param cacheProperties     缓存配置
     */
    public RedisMemoryMonitorService(RedisTemplate<String, byte[]> binaryRedisTemplate, FeatureRedisCacheProperties cacheProperties) {
        this.binaryRedisTemplate = binaryRedisTemplate;
        this.cacheProperties = cacheProperties;
    }

    /**
     * 定时刷新Redis内存使用率（每分钟执行一次）
     * <p>
     * 采用分布式锁机制，确保在高并发的分布式环境中，只有一个实例会执行{@code INFO memory}命令来查询Redis的内存状态。
     * 其它实例则从一个共享的Redis键中读取内存使用率。
     * 这种方式可以显著降低对Redis服务器的{@code INFO}命令请求压力，尤其是在部署了大量服务实例的场景下。
     * <p>
     * <b>工作流程:</b>
     * <ol>
     *   <li>每个实例在调度周期开始时，尝试使用{@code SETNX}获取一个分布式锁。</li>
     *   <li>成功获取锁的实例（“leader”）负责执行{@code INFO memory}命令，并将计算出的内存使用率写入一个共享的Redis键中，并设置一个较短的TTL。</li>
     *   <li>未获取锁的实例（“followers”）则从该共享键中读取内存使用率。</li>
     *   <li>为保证健壮性，如果follower无法读取到共享键（例如leader故障），它会回退到自行查询内存（但不发布结果），以确保监控数据的可用性。</li>
     * </ol>
     */
    @Scheduled(fixedRate = 60_000)
    public void refreshRedisMemoryUsage() {
        if (!cacheProperties.isEnabled() || !cacheProperties.getMemory().isMonitorEnabled()) {
            return;
        }

        try {
            // 尝试获取分布式锁，TTL为55秒，略小于执行周期，避免任务重叠
            Boolean lockAcquired = binaryRedisTemplate.opsForValue().setIfAbsent(
                    REDIS_MEMORY_MONITOR_LOCK_KEY,
                    INSTANCE_ID.getBytes(StandardCharsets.UTF_8),
                    55,
                    TimeUnit.SECONDS
            );

            if (Boolean.TRUE.equals(lockAcquired)) {
                // 获取锁成功，作为leader执行内存查询并发布
                log.debug("[RedisMemoryMonitor] 本实例获得leader-lock，开始获取并发布Redis内存使用率");
                fetchAndStoreRedisMemoryUsage(true);
            } else {
                // 获取锁失败，作为follower从Redis读取使用率
                log.debug("[RedisMemoryMonitor] 本实例为follower，尝试从Redis读取内存使用率");
                readRemoteRedisMemoryUsage();
            }
        } catch (Exception e) {
            log.warn("[RedisMemoryMonitor] 刷新Redis内存信息时发生未知异常，将尝试直接获取", e);
            // 异常情况下，降级为本实例直接获取，保证可用性
            fetchAndStoreRedisMemoryUsage(false);
        }
    }

    private void fetchAndStoreRedisMemoryUsage(boolean storeRemote) {
        try {
            Properties info = binaryRedisTemplate.execute((RedisCallback<Properties>) (connection) -> connection.info("memory"));
            if (info == null) {
                log.warn("[RedisMemoryMonitor] 获取Redis内存信息失败，返回为空");
                return;
            }

            long used = parseLongFromProperties(info, "used_memory");
            long max = parseLongFromProperties(info, "maxmemory");
            double usedMB = used / 1024.0 / 1024.0;
            double maxMB = max / 1024.0 / 1024.0;

            if (max > 0) {
                redisMemoryUsage = (double) used / max;
            } else {
                redisMemoryUsage = 0.0;
            }

            log.info("[RedisMemoryMonitor] {}: used_memory={}MB, maxmemory={}MB, usage={},canWrite={}",
                    storeRemote ? "Leader" : "Fallback",
                    String.format("%.2f", usedMB),
                    String.format("%.2f", maxMB),
                    String.format("%.4f", redisMemoryUsage), canWrite());

            if (storeRemote) {
                binaryRedisTemplate.opsForValue().set(
                        REDIS_MEMORY_USAGE_KEY,
                        String.valueOf(redisMemoryUsage).getBytes(StandardCharsets.UTF_8),
                        70, // TTL略长于任务周期，确保follower能读到
                        TimeUnit.SECONDS
                );
            }
        } catch (Exception e) {
            log.warn("[RedisMemoryMonitor] 获取并存储Redis内存使用率失败", e);
        }
    }

    private void readRemoteRedisMemoryUsage() {
        try {
            byte[] usageBytes = binaryRedisTemplate.opsForValue().get(REDIS_MEMORY_USAGE_KEY);
            if (usageBytes != null) {
                String usageStr = new String(usageBytes, StandardCharsets.UTF_8);
                this.redisMemoryUsage = Double.parseDouble(usageStr);
                log.info("[RedisMemoryMonitor] Follower: 从 {} 读取到内存使用率: {},canWrite={}", REDIS_MEMORY_USAGE_KEY, usageStr, canWrite());
            } else {
                // 共享Key不存在，可能leader执行慢或首次运行，降级为自己获取
                log.warn("[RedisMemoryMonitor] Follower: 未从 {} 读取到内存使用率，执行本地获取.", REDIS_MEMORY_USAGE_KEY);
                fetchAndStoreRedisMemoryUsage(false);
            }
        } catch (NumberFormatException e) {
            log.warn("[RedisMemoryMonitor] Follower: 解析Redis内存使用率失败", e);
            // 数据损坏，降级为自己获取
            fetchAndStoreRedisMemoryUsage(false);
        } catch (Exception e) {
            log.warn("[RedisMemoryMonitor] Follower: 读取Redis内存使用率异常", e);
            fetchAndStoreRedisMemoryUsage(false);
        }
    }


    /**
     * 从Properties中解析long类型的内存信息
     *
     * @param info Redis info属性
     * @param key  需要解析的key
     * @return long值，解析失败返回0
     */
    private long parseLongFromProperties(Properties info, String key) {
        String value = info.getProperty(key);
        if (value != null) {
            try {
                return Long.parseLong(value.trim());
            } catch (NumberFormatException e) {
                log.warn("[RedisMemoryMonitor] 解析内存信息失败: key={}, value={}", key, value);
            }
        }
        return 0L;
    }

    /**
     * 判断当前Redis内存是否可写（未超过阈值）
     *
     * @return true-可写，false-超阈值不可写
     */
    public boolean canWrite() {
        return redisMemoryUsage < cacheProperties.getMemory().getThreshold();
    }
} 