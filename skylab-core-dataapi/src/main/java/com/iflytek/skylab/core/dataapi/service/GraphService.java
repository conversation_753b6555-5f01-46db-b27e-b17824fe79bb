package com.iflytek.skylab.core.dataapi.service;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.dataapi.data.*;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Validated
public interface GraphService {

    /**
     * 查询子图，如果给定的 root 是根，直接返回子图查询结果
     * 如果不是根，则分为两次查询：反方向线查询和正方向子图查询，返回合并后结果
     *
     * @param subGraphQuery
     * @return
     */
    GraphData querySubGraph(@NotNull @Valid SubGraphQuery subGraphQuery);

    GraphData queryCachedSubGraph(@NotNull @Valid SubGraphQuery subGraphQuery);

    /**
     * 图谱查询优化 ，指定点放回字段
     *
     * @param subGraphQuery
     * @return
     */
    GraphData querySubGraphNeedProps(@NotNull @Valid SubGraphQuery subGraphQuery);

    /**
     * 查询路径，给定入参里的边列表必须构成一条路径
     *
     * @param graphQuery
     * @return
     */
    GraphData queryPath(@NotNull @Valid GraphQuery graphQuery);

    /**
     * 逆推根节点
     * 查询路径，给定入参里的边列表必须构成一条路径
     *
     * @param graphQuery
     * @return
     */
    GraphData queryPathReverse(@NotNull @Valid GraphQuery graphQuery);

    /**
     * 查询顶点列表
     *
     * @param graphQuery
     * @return
     */
    GraphData queryVertices(@NotNull @Valid GraphQuery graphQuery);

    /**
     * 根据路径逆推根节点
     *
     * @param graphQuery
     * @return
     */
    GraphData queryVerticesReverse(@NotNull @Valid GraphQuery graphQuery);

    /**
     * 根据点的id 查询节点详细信息
     *
     * @param graphVertexQuery
     * @return
     */
    GraphData queryVertex(@NotNull @Valid GraphVertexQuery graphVertexQuery);


    GraphData queryVertexByIds(@NotNull @Valid GraphVertexesQuery query);

    List<GraphData.GraphVertex> queryVertexByIdsWithPropsCache(@NotNull @Valid GraphVertexesQuery query);

    /**
     * 保存点（新增或更新）
     *
     * @param graphVertex
     */
    void saveVertex(@NotNull @Valid GraphVertex graphVertex);

    /**
     * 删除点
     *
     * @param graphVertex
     */
    void deleteVertex(@NotNull @Valid GraphVertex graphVertex);

    /**
     * 保存边（新增或更新）
     *
     * @param graphEdge
     */
    void saveEdge(@NotNull @Valid GraphEdge graphEdge);

    /**
     * 删除边
     *
     * @param graphEdge
     */
    void deleteEdge(@NotNull @Valid GraphEdge graphEdge);

    /**
     * 遍历所有顶点
     *
     * @param graphLabelQuery
     * @return
     */
    GraphData lookup(@NotNull @Valid GraphLabelQuery graphLabelQuery);

    /**
     * 根据属性查询
     *
     * @param query
     * @param props
     * @return
     */
    GraphData lookup(@NotNull @Valid GraphLabelQuery query, JSONObject props);

    /**
     * 按传入的边，查询某个节点的上一层。并可按范围过滤
     *
     * @param query
     * @return
     */
    GraphData findUpperLayer(@NotNull @Valid UpperLayerQuery query);
}
