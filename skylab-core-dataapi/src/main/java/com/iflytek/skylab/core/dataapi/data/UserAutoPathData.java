package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.data.Jsonable;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 学习路径用户试题信息
 *
 * <AUTHOR>
 * @version 1.0
 * @Getter
@Setter
 * @EqualsAndHashCode(callSuper = true)
 * @ToString(callSuper = true)
 * @date 2022/9/16 15:02
 */
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
public class UserAutoPathData extends Jsonable {

    /**
     * 用户id
     */
    private String userId;
    /**
     * 学科
     */
    private String subjectCode;
    /**
     * 学段
     */
    private String phaseCode;

    /**
     * 答题记录
     */
    private List<RecordInfo> answerRecordSeq;


    @Getter
@Setter
    @Builder
    public static class RecordInfo implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 试题id
         */
        private String topicId;
        private String nodeId;
        private String roundId;
        private Long feedBackTime;
        private Long duration;
        private Integer topicDiff;
        /**
         * 得分率
         */
        private Float scoreRate;
        /**
         * 试题HT
         */
        private List<Float> features;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            RecordInfo that = (RecordInfo) o;
            return Objects.equals(topicId, that.topicId) && Objects.equals(feedBackTime, that.feedBackTime);
        }

        @Override
        public int hashCode() {
            return Objects.hash(topicId, feedBackTime);
        }
    }
}
