package com.iflytek.skylab.core.dataapi.mongo;


import cn.hutool.core.bean.BeanUtil;
import com.mongodb.client.result.UpdateResult;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.query.UpdateDefinition;
import org.springframework.data.mongodb.repository.query.MongoEntityInformation;
import org.springframework.data.mongodb.repository.support.SimpleMongoRepository;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/25 10:27
 */
public class DataApiMongoRepositoryImpl<T, ID extends Serializable> extends SimpleMongoRepository<T, Serializable> implements DataApiMongoRepository<T, Serializable> {

    private final MongoOperations mongoOperations;
    private final MongoEntityInformation<T, ID> entityInformation;

    public DataApiMongoRepositoryImpl(MongoEntityInformation<T, ID> metadata, MongoOperations mongoOperations) {
        super((MongoEntityInformation<T, Serializable>) metadata, mongoOperations);
        entityInformation = metadata;
        this.mongoOperations = mongoOperations;
    }

    @Override
    public T findAndModify(Query query, UpdateDefinition update, FindAndModifyOptions options) {
        return mongoOperations.findAndModify(query, update, options, getEntityClass(), getCollectionName());
    }

    @Override
    public List<T> findAllAndRemove(Query query) {
        return mongoOperations.findAllAndRemove(query, getEntityClass(), getCollectionName());
    }


    @Override
    public MongoOperations getMongoOperations() {
        return mongoOperations;
    }

    @Override
    public Class<T> getEntityClass() {
        return entityInformation.getJavaType();
    }

    @Override
    public String getCollectionName() {
        return entityInformation.getCollectionName();
    }


    @Override
    public List<T> search(Criteria criteria) {
        return mongoOperations.find(new Query(criteria), entityInformation.getJavaType(), entityInformation.getCollectionName());
    }

    @Override
    public List<T> search(Query query) {
        return mongoOperations.find(query, entityInformation.getJavaType(), entityInformation.getCollectionName());
    }

    @Override
    public List<T> searchWithLimit(Criteria criteria, int limit) {
        Query query = new Query(criteria);
        query.limit(limit);
        return mongoOperations.find(new Query(criteria), entityInformation.getJavaType(), entityInformation.getCollectionName());
    }

    /**
     * @param criteria
     * @return
     */
    @Override
    public boolean exists(Criteria criteria) {
        return mongoOperations.exists(new Query(criteria), entityInformation.getJavaType(), entityInformation.getCollectionName());
    }

    @Override
    public UpdateResult update(T entity) {
        Assert.notNull(entity, "Entity must not be null!");

        Criteria criteria = Criteria.where(entityInformation.getIdAttribute()).is(entityInformation.getId(entity));
        return mongoOperations.updateFirst(new Query(criteria), buildUpdate(entity), entityInformation.getJavaType());
    }

    /**
     * 将需要更新的字段，放入Update对象，仅保留k,v都不为空的字段和值
     *
     * @param entity
     * @return
     */
    private Update buildUpdate(T entity) {
        Map<String, Object> entityMap = BeanUtil.beanToMap(entity, false, true);

        Update update = new Update();
        entityMap.forEach((k, v) -> {
            if (!ObjectUtils.isEmpty(k) && !ObjectUtils.isEmpty(v)) {
                update.set(k, v);
            }
        });
        return update;
    }
}
