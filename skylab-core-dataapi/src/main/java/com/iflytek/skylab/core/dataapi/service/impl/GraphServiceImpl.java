package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.dataapi.configuration.GraphProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.exception.SkylabException;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.util.GraphUtils;
import com.iflytek.skylab.core.dataapi.util.GraphVertexCache;
import com.iflytek.skylab.core.dataapi.util.Lookup;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.iflytek.skyline.common.api.ApiErrorCode;
import com.vesoft.nebula.client.graph.data.Node;
import com.vesoft.nebula.client.graph.data.Relationship;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 图谱查询服务，提供 Nebula 图数据库的子图查询、节点查询等功能
 */
@Slf4j
public class GraphServiceImpl implements GraphService, AutoCloseable {

    private final NebulaPool nebulaPool;
    private final GraphProperties graphProperties;

    private final String QUERY_PATH = "QUERY_PATH";
    private final String QUERY_PATH_REVERSE = "QUERY_PATH_REVERSE";
    private final String QUERY_VERTICES = "QUERY_VERTICES";
    private final String QUERY_VERTICES_REVERSE = "QUERY_VERTICES_REVERSE";

    /**
     * Constructor
     *
     * @param nebulaPool
     * @param graphProperties
     */
    public GraphServiceImpl(NebulaPool nebulaPool, GraphProperties graphProperties) {
        this.nebulaPool = nebulaPool;
        this.graphProperties = graphProperties;
        if (log.isInfoEnabled()) {
            log.info("graphProperties.nodeProps:{}", JSONArray.toJSONString(graphProperties.getNodeProps()));
        }
    }

    /**
     * 查询子图-指定属性
     */
    @Override
    @SkylineMetric(value = "DataHub-1.图谱-查询子图", meterProviders = SkylabDataApiMeterProvider.class)
    public GraphData querySubGraphNeedProps(SubGraphQuery subGraphQuery) {
        if (log.isDebugEnabled()) {
            log.debug("nodeProps:{}", JSONArray.toJSONString(graphProperties.getNodeProps()));
        }
        if (MapUtil.isEmpty(subGraphQuery.getNodeProps())) {
            subGraphQuery.setNodeProps(graphProperties.getNodeProps());
        }
        if (CollectionUtils.isEmpty(subGraphQuery.getEdgeLabels())) {
            GraphData result = new GraphData();
            result.setGraphVersion(subGraphQuery.getGraphVersion());
            result.setVertices(subGraphQuery.getRootVertexIdList().stream()
                    .map(id -> GraphData.GraphVertex.builder().id(id).label(subGraphQuery.getRootVertexLabel()).build())
                    .collect(Collectors.toList()));
            return result;
        }

        SubGraphQuery subgraphQueryConvert = convertSubgraphQuery(subGraphQuery);
        String realRoot = getRealRootLabel(subgraphQueryConvert.getEdgeLabels());
        String maskRoot = subgraphQueryConvert.getRootVertexLabel();
        if (log.isDebugEnabled()) {
            log.debug("use nodeProps:{}", JSONArray.toJSONString(subgraphQueryConvert.getNodeProps()));
        }
        // 如果 rootVertex 和 realRoot 一致，就直接子图查询，返回结果
        // 如果 rootVertex 和 realRoot 不一致，就需要根据伪根 maskRoot 为界限，两次查询
        if (maskRoot.equals(realRoot)) {
            return querySubGraphByRootNeedProps(subgraphQueryConvert);
        } else {
            return querySubGraphByNonRoot(subgraphQueryConvert, realRoot);
        }
    }

    /**
     * 查询子图
     */
    @Override
    @SkylineMetric(value = "DataHub-1.图谱-查询子图", meterProviders = SkylabDataApiMeterProvider.class)
    public GraphData queryCachedSubGraph(SubGraphQuery subGraphQuery) {
        return null;
    }

    @Override
    @SkylineMetric(value = "DataHub-1.图谱-查询子图", meterProviders = SkylabDataApiMeterProvider.class)
    public GraphData querySubGraph(SubGraphQuery subGraphQuery) {

        if (CollectionUtils.isEmpty(subGraphQuery.getEdgeLabels())) {
            GraphData result = new GraphData();
            result.setGraphVersion(subGraphQuery.getGraphVersion());
            result.setVertices(subGraphQuery.getRootVertexIdList().stream()
                    .map(id -> GraphData.GraphVertex.builder().id(id).label(subGraphQuery.getRootVertexLabel()).build())
                    .collect(Collectors.toList()));
            return result;
        }

        SubGraphQuery subgraphQueryConvert = convertSubgraphQuery(subGraphQuery);
        String realRoot = getRealRootLabel(subgraphQueryConvert.getEdgeLabels());
        String maskRoot = subgraphQueryConvert.getRootVertexLabel();

        // 如果 rootVertex 和 realRoot 一致，就直接子图查询，返回结果
        // 如果 rootVertex 和 realRoot 不一致，就需要根据伪根 maskRoot 为界限，两次查询
        if (maskRoot.equals(realRoot)) {
            return querySubGraphByRoot(subgraphQueryConvert);
        } else {
            return querySubGraphByNonRoot(subgraphQueryConvert, realRoot);
        }
    }

    /**
     * 子图查询对象转换，主要是将查询对象中的 label 进行排序
     *
     * @param subGraphQuery
     * @return
     */
    private SubGraphQuery convertSubgraphQuery(SubGraphQuery subGraphQuery) {

        // 获取子图的真实根节点，和 rootVertex 比较
        List<SubGraphQuery.EdgeLabel> edgeLabels = subGraphQuery.getEdgeLabels();
        List<SubGraphQuery.EdgeLabel> sortEdgeLabels = GraphUtils.sortGraphEdge(edgeLabels);

        SubGraphQuery subGraphQueryWrap = new SubGraphQuery();
        subGraphQueryWrap.setTraceId(subGraphQuery.getTraceId());
        subGraphQueryWrap.setGraphVersion(subGraphQuery.getGraphVersion());
        subGraphQueryWrap.setRootVertexLabel(subGraphQuery.getRootVertexLabel());
        subGraphQueryWrap.setRootVertexIdList(subGraphQuery.getRootVertexIdList());
        subGraphQueryWrap.setEdgeLabels(sortEdgeLabels);
        subGraphQueryWrap.setIncludeFields(subGraphQuery.getIncludeFields());

        Map<String, List<String>> nodeProps = subGraphQuery.getNodeProps();
        Map<String, List<String>> nodePropsWrap = new HashMap<>();
        //过滤，非查询点类型
        if (CollUtil.isNotEmpty(subGraphQuery.getEdgeLabels()) && CollUtil.isNotEmpty(nodeProps)) {
            subGraphQuery.getEdgeLabels().forEach(edgeLabel -> {
                if (nodeProps.containsKey(edgeLabel.getSource())) {
                    nodePropsWrap.put(edgeLabel.getSource(), nodeProps.get(edgeLabel.getSource()));
                }
                if (nodeProps.containsKey(edgeLabel.getTarget())) {
                    nodePropsWrap.put(edgeLabel.getTarget(), nodeProps.get(edgeLabel.getTarget()));
                }
            });
        }
        subGraphQueryWrap.setNodeProps(nodePropsWrap);

        return subGraphQueryWrap;

    }

    /**
     * 根据根节点查询子图
     *
     * @param subGraphQuery
     * @return
     */
    private GraphData querySubGraphByRoot(SubGraphQuery subGraphQuery) {

        // 注意只有一个根节点，没有边的情况
        if (subGraphQuery.getEdgeLabels().isEmpty()) {
            return new GraphData();
        }

        // 获取子图最长路径
        Integer longestPath = GraphUtils.getDPHLongestPath(subGraphQuery.getEdgeLabels());
        // 获取根节点ID列表
        String rootVertexIdList = convertVertexIdListToString(subGraphQuery.getRootVertexIdList());
        // 获取子图边中所有边的名字
        String edgeLabelList = convertEdgeLabelsToString(subGraphQuery.getEdgeLabels());

        // 组装 NGQL
        String ngql = String.format("GET SUBGRAPH WITH PROP %s STEPS FROM %s OUT %s YIELD ",
                longestPath, rootVertexIdList, edgeLabelList);

        String result = "VERTICES AS nodes, EDGES AS relationships;";

        if (!CollUtil.isEmpty(subGraphQuery.getIncludeFields())) {

            if (subGraphQuery.getIncludeFields().contains("VERTICES") && subGraphQuery.getIncludeFields().size() == 1) {
                result = "VERTICES AS nodes;";
            }

            if (subGraphQuery.getIncludeFields().contains("EDGES") && subGraphQuery.getIncludeFields().size() == 1) {
                result = "EDGES AS relationships;";
            }

        }


        ngql = ngql + result;
        return executeAndReturnGraphData(subGraphQuery.getTraceId(), ngql, subGraphQuery.getGraphVersion());
    }

    /**
     * 根据根节点查询子图
     *
     * @param subGraphQuery
     * @return
     */
    private GraphData querySubGraphByRootNeedProps(SubGraphQuery subGraphQuery) {

        Map<String, List<String>> nodeProps = subGraphQuery.getNodeProps();

        // 注意只有一个根节点，没有边的情况
        if (subGraphQuery.getEdgeLabels().isEmpty()) {
            return new GraphData();
        }

        // 获取子图最长路径
        Integer longestPath = GraphUtils.getDPHLongestPath(subGraphQuery.getEdgeLabels());
        // 获取根节点ID列表
        String rootVertexIdList = convertVertexIdListToString(subGraphQuery.getRootVertexIdList());
        // 获取子图边中所有边的名字
        String edgeLabelList = convertEdgeLabelsToString(subGraphQuery.getEdgeLabels());

        // 组装 NGQL -只返回关系
        String ngql = String.format("GET SUBGRAPH WITH PROP %s STEPS FROM %s OUT %s YIELD EDGES AS relationships;",
                longestPath, rootVertexIdList, edgeLabelList);

        //获取边信息
        GraphData graphData = executeAndReturnGraphData(subGraphQuery.getTraceId(), ngql, subGraphQuery.getGraphVersion());
        List<GraphData.GraphEdge> edges = graphData.getEdges();
        Set<String> vid = new HashSet<>();

        List<GraphData.GraphVertex> topicVertices = new ArrayList<>();
        if (CollUtil.isNotEmpty(edges)) {

            for (GraphData.GraphEdge edge : edges) {
                vid.add(edge.getSource());

                // TOPIC不查询
                if (!edge.getLabel().endsWith("_TOPIC")) {
                    vid.add(edge.getTarget());
                } else {
                    GraphData.GraphVertex vertex = GraphData.GraphVertex.builder()
                            .id(edge.getTarget())
                            .label("TOPIC")
                            .properties(new JSONObject())
                            .build();
                    topicVertices.add(vertex);
                }

            }
        }
        log.debug("querySubGraphByRootNeedProps查询vid.size={}，跳过查询TOPIC.size()={}", vid.size(), topicVertices.size());
        //查询点信息
        GraphVertexesQuery query = new GraphVertexesQuery();
        query.setTraceId(subGraphQuery.getTraceId());
        query.setIds(Lists.newArrayList(vid));
        query.setNodeProps(nodeProps);
        query.setGraphVersion(subGraphQuery.getGraphVersion());
        List<GraphData.GraphVertex> graphVertices = queryVertexByIdsWithPropsCache(query);
        graphData.setVertices(graphVertices);
        if (CollUtil.isEmpty(graphData.getVertices())) {
            graphData.setVertices(topicVertices);
        } else {
            graphData.getVertices().addAll(topicVertices);
        }
        return graphData;
    }

    /**
     * 根据非根节点查询子图
     *
     * @param subGraphQuery
     * @return
     */
    private GraphData querySubGraphByNonRoot(SubGraphQuery subGraphQuery, String realRoot) {

        // 将查询条件分成两部分
        Tuple graphQueries = splitGraphQuery(subGraphQuery, realRoot);

        // maskRoot 入度反方向进行线查询
        GraphData maskRootReverseResult = queryPathReverse(graphQueries.get(0));
        log.trace("伪根入度反向线查询结果: {}", maskRootReverseResult);

        // maskRoot 出度方向进行子图查询
        GraphData subGraphResult = querySubGraphByRoot(graphQueries.get(1));
        log.trace("伪根出度子图查询结果: {}", subGraphResult);

        // 组装返回值
        return mergeGraphData(subGraphQuery.getGraphVersion(), maskRootReverseResult, subGraphResult);
    }

    /**
     * 合并两个 GraphData
     */
    protected GraphData mergeGraphData(String graphVersion, GraphData data1, GraphData data2) {
        GraphData result = new GraphData();
        result.setGraphVersion(graphVersion);
        result.setVertices(mergeResultVertex(data1, data2));
        result.setEdges(mergeResultEdges(data1, data2));
        return result;
    }

    /**
     * 获取组装后的边
     */
    private List<GraphData.GraphEdge> mergeResultEdges(GraphData data1, GraphData data2) {
        List<GraphData.GraphEdge> resultEdges = new ArrayList<>();
        if (data1 != null && data1.getEdges() != null) {
            resultEdges.addAll(data1.getEdges());
        }
        if (data2 != null && data2.getEdges() != null) {
            resultEdges.addAll(data2.getEdges());
        }
        return resultEdges;
    }

    /**
     * 获取组装后的点
     */
    private List<GraphData.GraphVertex> mergeResultVertex(GraphData data1, GraphData data2) {
        HashMap<String, GraphData.GraphVertex> map = new HashMap<>();
        if (data1 != null && data1.getVertices() != null) {
            for (GraphData.GraphVertex vertex : data1.getVertices()) {
                map.put(vertex.getId(), vertex);
            }
        }
        if (data2 != null && data2.getVertices() != null) {
            for (GraphData.GraphVertex vertex : data2.getVertices()) {
                map.put(vertex.getId(), vertex);
            }
        }
        return new ArrayList<>(map.values());
    }

    /**
     * 获取伪根入度反向查询入参，并删除了 edgeLabels 中伪根入度反方向的边和点
     *
     * @param subGraphQuery
     * @param realRoot
     * @return
     */
    private Tuple splitGraphQuery(SubGraphQuery subGraphQuery, String realRoot) {

        String realRootDuplicate = realRoot;
        // 找到 realRoot 和 maskRoot 之间的所有边
        List<GraphQuery.EdgeLabel> maskRootReverseLabelList = new ArrayList<>();
        List<SubGraphQuery.EdgeLabel> maskRootExcludeSubGraphLabelList = new ArrayList<>();
        for (SubGraphQuery.EdgeLabel edge : subGraphQuery.getEdgeLabels()) {
            if (edge.getSource().equals(realRoot)) {
                maskRootReverseLabelList.add(new GraphQuery.EdgeLabel().setSource(edge.getSource()).setTarget(edge.getTarget()));
                maskRootExcludeSubGraphLabelList.add(edge);
                realRoot = edge.getTarget();
                if (realRoot.equals(subGraphQuery.getRootVertexLabel())) {
                    break;
                }
            }
        }

        // 构造反向查询条件
        GraphQuery graphQuery = new GraphQuery();
        graphQuery.setTraceId(subGraphQuery.getTraceId());
        graphQuery.setGraphVersion(subGraphQuery.getGraphVersion());
        graphQuery.setRootVertexLabel(realRootDuplicate);
        graphQuery.setRootVertexIdList(subGraphQuery.getRootVertexIdList());
        graphQuery.setEdgeLabels(maskRootReverseLabelList);

        // 构造子图查询条件
        SubGraphQuery excludeSubGraphQuery = new SubGraphQuery();
        excludeSubGraphQuery.setTraceId(subGraphQuery.getTraceId());
        excludeSubGraphQuery.setGraphVersion(subGraphQuery.getGraphVersion());
        excludeSubGraphQuery.setRootVertexLabel(subGraphQuery.getRootVertexLabel());
        excludeSubGraphQuery.setRootVertexIdList(subGraphQuery.getRootVertexIdList());
        excludeSubGraphQuery.setEdgeLabels(subGraphQuery.getEdgeLabels().stream()
                .filter(edge -> !maskRootExcludeSubGraphLabelList.contains(edge)).collect(Collectors.toList()));
        return new Tuple(graphQuery, excludeSubGraphQuery);
    }

    /**
     * 获取有向图中真正的根
     *
     * @param edgeLabels
     * @return
     */
    private String getRealRootLabel(List<SubGraphQuery.EdgeLabel> edgeLabels) {
        HashSet<String> outSets = new HashSet<>();
        HashSet<String> inSets = new HashSet<>();
        for (SubGraphQuery.EdgeLabel edge : edgeLabels) {
            outSets.add(edge.getSource());
            inSets.add(edge.getTarget());
        }
        outSets.removeAll(inSets);
        if (outSets.size() != 1) {
            throw new SkylabException(-1, "查询子图根节点异常");
        }
        return outSets.iterator().next();
    }

    /**
     * 查询路径
     */
    @SkylineMetric(value = "DataHub-1.图谱-查询路径", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public GraphData queryPath(GraphQuery graphQuery) {
        return doMatchQuery(graphQuery, QUERY_PATH);
    }

    /**
     * 反向查询路径
     *
     * @param graphQuery
     * @return
     */
    @SkylineMetric(value = "DataHub-1.图谱-查询路径", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public GraphData queryPathReverse(GraphQuery graphQuery) {
        return doMatchQuery(graphQuery, QUERY_PATH_REVERSE);
    }

    /**
     * 查询顶点列表
     */
    @SkylineMetric(value = "DataHub-1.图谱-查询顶点列表", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public GraphData queryVertices(GraphQuery graphQuery) {
        return doMatchQuery(graphQuery, QUERY_VERTICES);
    }

    /**
     * 给定一个点，查询根
     */
    @SkylineMetric(value = "DataHub-1.图谱-查询顶点的根", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public GraphData queryVerticesReverse(GraphQuery graphQuery) {
        return doMatchQuery(graphQuery, QUERY_VERTICES_REVERSE);
    }

    /**
     * 匹配 match 查询结果
     *
     * @param graphQuery
     * @param type
     * @return
     */
    private GraphData doMatchQuery(GraphQuery graphQuery, String type) {
        GraphQuery convertGraphQuery = convertGraphQuery(graphQuery);
        // 判断各个节点是否在一条线上
        if (!GraphUtils.isLine(convertGraphQuery)) {
            log.error("查询的节点有误，不在一条线上：{}", graphQuery);
            throw new SkylabException(-1, "查询的节点有误，不在一条线上");
        }
        // 点的个数 = 边的个数 + 1
        int vCount = convertGraphQuery.getEdgeLabels().size() + 1;
        // 获取 MATCH 子句
        String matchString = convertEdgeLabelsToMatchString(convertGraphQuery);
        // 获取根节点ID列表
        String rootVertexIdList = convertVertexIdListToString(convertGraphQuery.getRootVertexIdList());
        // 拼接边的过滤条件
        String filterStr = getFilterStr(convertGraphQuery);
        // 尾节点查询
        String tailStr = "";
        if (convertGraphQuery.getTailVertexIdList() != null) {
            tailStr = String.format("AND id(v%s) IN [%s]", vCount, convertVertexIdListToString(convertGraphQuery.getTailVertexIdList()));
        }

        String ngql = null;
        switch (type) {
            case "QUERY_PATH":
                ngql = String.format("MATCH p=%s WHERE id(v1) IN [%s] %s RETURN DISTINCT nodes(p),relationships(p);",
                        matchString, rootVertexIdList, tailStr);
                break;
            case "QUERY_PATH_REVERSE":
                ngql = String.format("MATCH p=%s WHERE id(v%s) IN [%s] RETURN DISTINCT nodes(p),relationships(p);",
                        matchString, vCount, rootVertexIdList);
                break;
            case "QUERY_VERTICES":
                ngql = String.format("MATCH %s WHERE id(v1) IN [%s] %s RETURN DISTINCT v%s;",
                        matchString, rootVertexIdList, filterStr, vCount);
                break;
            case "QUERY_VERTICES_REVERSE":
                ngql = String.format("MATCH %s WHERE id(v%s) IN [%s] %s RETURN DISTINCT v1;",
                        matchString, vCount, rootVertexIdList, filterStr);
                break;
        }
        return executeAndReturnGraphData(convertGraphQuery.getTraceId(), ngql, convertGraphQuery.getGraphVersion());

    }


    /**
     * 获取边的过滤条件
     *
     * @param convertGraphQuery
     * @return
     */
    private String getFilterStr(GraphQuery convertGraphQuery) {
        List<GraphQuery.EdgeLabel> edgeLabels = convertGraphQuery.getEdgeLabels();

        ArrayList<String> strings = new ArrayList<>();
        for (int i = 0; i < edgeLabels.size(); i++) {
            String filter = edgeLabels.get(i).getFilter();
            if (StringUtils.isNotEmpty(filter)) {
                String replaced = filter.replace("$", "e" + (i + 1));
                strings.add(replaced);
            }

        }

        return strings.isEmpty() ? "" : String.format("AND (%s)", String.join(" AND ", strings));
    }

    /**
     * 转换线查询对象，主要是给定的边进行排序
     *
     * @param graphQuery
     * @return
     */
    private GraphQuery convertGraphQuery(GraphQuery graphQuery) {
        GraphQuery graphQueryConvert = new GraphQuery();
        graphQueryConvert.setTraceId(graphQuery.getTraceId());
        graphQueryConvert.setEdgeLabels(GraphUtils.sortVertexEdge(graphQuery.getEdgeLabels()));
        graphQueryConvert.setRootVertexLabel(graphQuery.getRootVertexLabel());
        graphQueryConvert.setRootVertexIdList(graphQuery.getRootVertexIdList());
        graphQueryConvert.setTailVertexLabel(graphQuery.getTailVertexLabel());
        graphQueryConvert.setTailVertexIdList(graphQuery.getTailVertexIdList());
        graphQueryConvert.setGraphVersion(graphQuery.getGraphVersion());
        return graphQueryConvert;
    }

    /**
     * 查询某个顶点详情
     */
    @SkylineMetric(value = "DataHub-1.图谱-查询某个顶点详情", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public GraphData queryVertex(GraphVertexQuery graphVertexQuery) {
        String ngql = String.format("MATCH (v) WHERE id(v) == '%s' RETURN v;", graphVertexQuery.getId());
        return executeAndReturnGraphData(graphVertexQuery.getTraceId(), ngql, graphVertexQuery.getGraphVersion());
    }

    @Override
    public GraphData queryVertexByIds(GraphVertexesQuery query) {
        String ids = query.getIds().stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
        String ngql = String.format("MATCH (v) WHERE id(v) IN [%s] RETURN v;", ids);
        return executeAndReturnGraphData(query.getTraceId(), ngql, query.getGraphVersion());
    }

    @Override
    public List<GraphData.GraphVertex> queryVertexByIdsWithPropsCache(GraphVertexesQuery query) {

        //添加点缓存 逻辑
        List<String> ids = query.getIds();

        //获取缓存点信息
        List<GraphData.GraphVertex> graphVertices = GraphVertexCache.get(query.getGraphVersion(), ids);

        if (ids.size() != graphVertices.size()) {
            List<String> cacheIds = graphVertices.stream().map(GraphData.GraphVertex::getId).collect(Collectors.toList());

            //缓存不存在，数据库查询
            List<String> addIds = CollUtil.subtractToList(ids, cacheIds);
            query.setIds(addIds);
            List<GraphData.GraphVertex> vertex = queryVertexByIdsWithProps(query);
            if (CollUtil.isNotEmpty(vertex)) {
                graphVertices.addAll(vertex);
                //添加缓存
                GraphVertexCache.put(query.getGraphVersion(), vertex);
            }

        }

        return graphVertices;
    }

    private List<GraphData.GraphVertex> queryVertexByIdsWithProps(GraphVertexesQuery query) {
        String ids = query.getIds().stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
        List<String> props = new ArrayList<>();
        if (query.getNodeProps() != null) {
            for (Map.Entry<String, List<String>> entry : query.getNodeProps().entrySet()) {
                if (CollUtil.isEmpty(entry.getValue())) {
                    continue;
                }
                for (String s : entry.getValue()) {
                    props.add(String.format("v.%s.%s", entry.getKey(), s));
                }
            }
        }

        String join = StrUtil.join(",", props);
        if (props.size() > 0) {
            join = "," + join;
        }
        String ngql = String.format("MATCH (v) WHERE id(v) IN [%s] RETURN id(v) as id, labels(v) as label %s;", ids, join);

        GraphData graphData = executeAndReturnGraphDataWithProps(query.getTraceId(), ngql, query.getGraphVersion());

        return graphData.getVertices();
    }

    /**
     * 保存点（新增或更新）
     *
     * @param graphVertex
     */
    @Override
    public void saveVertex(GraphVertex graphVertex) {
        String propertyKeys = getPropertyKeys(graphVertex.getProperties());
        String propertyValues = getPropertyValues(graphVertex.getProperties());
        String vid = String.format("\"%s\"", graphVertex.getId());
        String ngql = String.format("INSERT VERTEX %s(%s) VALUES %s: (%s)", graphVertex.getLabel(), propertyKeys, vid, propertyValues);
        execute(graphVertex.getTraceId(), ngql, graphVertex.getGraphVersion());
    }

    /**
     * 删除点
     *
     * @param graphVertex
     */
    @Override
    public void deleteVertex(GraphVertex graphVertex) {
        String vid = String.format("\"%s\"", graphVertex.getId());
        String ngql = String.format("DELETE VERTEX %s WITH EDGE", vid);
        execute(graphVertex.getTraceId(), ngql, graphVertex.getGraphVersion());
    }

    /**
     * 保存边（新增或更新）
     *
     * @param graphEdge
     */
    @Override
    public void saveEdge(GraphEdge graphEdge) {
        String propertyKeys = getPropertyKeys(graphEdge.getProperties());
        String propertyValues = getPropertyValues(graphEdge.getProperties());
        String vid = String.format("\"%s\" -> \"%s\"", graphEdge.getSource(), graphEdge.getTarget());
        String ngql = String.format("INSERT EDGE %s(%s) VALUES %s: (%s)", graphEdge.getLabel(), propertyKeys, vid, propertyValues);
        execute(graphEdge.getTraceId(), ngql, graphEdge.getGraphVersion());
    }

    /**
     * 删除边
     *
     * @param graphEdge
     */
    @Override
    public void deleteEdge(GraphEdge graphEdge) {
        String vid = String.format("\"%s\" -> \"%s\"", graphEdge.getSource(), graphEdge.getTarget());
        String ngql = String.format("DELETE EDGE %s %s", graphEdge.getLabel(), vid);
        execute(graphEdge.getTraceId(), ngql, graphEdge.getGraphVersion());
    }

    /**
     * 遍历所有顶点
     *
     * @param graphLabelQuery
     * @return
     */
    @Override
    public GraphData lookup(GraphLabelQuery graphLabelQuery) {
        String traceId = graphLabelQuery.getTraceId();
        String ngql = String.format("LOOKUP ON %s YIELD id(vertex);", graphLabelQuery.getLabel());
        String version = graphLabelQuery.getGraphVersion();
        String label = graphLabelQuery.getLabel();
        return executeAndBuildLookupGraphData(traceId, ngql, version, label);
    }

    @Override
    public GraphData lookup(GraphLabelQuery query, JSONObject props) {
        if (CollUtil.isEmpty(props)) {
            return new GraphData();
        }

        String label = query.getLabel();
        List<String> keys = new ArrayList<>(props.keySet());

        Lookup lookup = new Lookup(label).where(keys.get(0), props.get(keys.get(0)));
        for (int i = 1; i < keys.size(); i++) {
            lookup.and(keys.get(i), props.get(keys.get(i)));
        }
        return executeAndBuildLookupGraphData(query.getTraceId(), lookup.ngql(), query.getGraphVersion(), label);
    }

    @Override
    public GraphData findUpperLayer(UpperLayerQuery query) {
        // 检查目标节点是否一致
        Set<String> targets = query.getEdgeLabels()
                .stream()
                .map(UpperLayerQuery.EdgeLabel::getTarget)
                .collect(Collectors.toSet());

        if (targets.size() != 1) {
            throw new IllegalArgumentException("所有边的目标节点不一致!");
        }

        String target = new ArrayList<>(targets).get(0);
        String targetId = String.join("','", query.getTargetIds());
        // 构建所有边类型，去重，并按格式拼接
        String edges = query.getEdgeLabels()
                .stream()
                .map(el -> String.join("_", el.getSource(), el.getTarget()))
                .collect(Collectors.joining("|"));

        // 拼接查询语句
        String pattern = "MATCH p=(v1) -[e:{}*1]-> (v2:{}) WHERE id(v2) IN ['{}'] RETURN DISTINCT nodes(p), relationships(p);";
        String nqgl = StrUtil.format(pattern, edges, target, targetId);
        // 执行语句并返回结果
        GraphData graphData = executeAndReturnGraphData(query.getTraceId(), nqgl, query.getGraphVersion());
        // 按要求对结果进行过滤
        if (StrUtil.isNotBlank(query.getResultLimitId())) {
            graphData.getEdges().removeIf(edge -> !StrUtil.startWith(edge.getId(), query.getResultLimitId()));
            graphData.getVertices().removeIf(vertex -> !StrUtil.startWith(vertex.getId(), query.getResultLimitId()));
        }
        return graphData;
    }

    private GraphData executeAndBuildLookupGraphData(String traceId, String ngql, String version, String label) {
        try {
            ResultSet resultSet = execute(traceId, ngql, version);
            return buildLookupGraphData(resultSet, label, version);
        } catch (Exception e) {
            log.error(String.format("traceId=%s;Execute: `%s`, failed: %s", traceId, ngql, e.getMessage()));
            throw new SkylabException(ApiErrorCode.ERROR.getCode(), e.getMessage(), e);
        }
    }

    /**
     * 构造 Lookup 语句查询出来的顶点列表
     *
     * @param resultSet
     * @param label
     * @param version
     * @return
     * @throws Exception
     */
    private GraphData buildLookupGraphData(ResultSet resultSet, String label, String version) throws Exception {
        List<GraphData.GraphVertex> vertices = new ArrayList<>();
        for (String name : resultSet.keys()) {
            for (ValueWrapper value : resultSet.colValues(name)) {
                if (value.isString()) {
                    vertices.add(GraphData.GraphVertex.builder().label(label).id(value.asString()).build());
                }
            }
        }
        GraphData graphData = new GraphData();
        graphData.setGraphVersion(version);
        graphData.setVertices(vertices);
        return graphData;
    }

    /**
     * 获取 JSONObject 的所有 Key，并使用逗号分隔
     *
     * @param properties
     * @return
     */
    private String getPropertyKeys(JSONObject properties) {
        if (properties == null) {
            return "";
        }
        return String.join(", ", properties.keySet());
    }

    /**
     * 获取 JSONObject 的所有 Value，并使用逗号分隔
     *
     * @param properties
     * @return
     */
    private String getPropertyValues(JSONObject properties) {
        if (properties == null) {
            return "";
        }
        return String.join(", ", properties.values().stream()
                .map(v -> {
                    if (v == null) {
                        return "\"\"";
                    }
                    if (v instanceof Integer) {
                        return String.format("%s", v);
                    } else {
                        return String.format("\"%s\"", ((String) v).replace("\"", "\\\""));
                    }
                }).collect(Collectors.toList()));
    }

    /**
     * 将要遍历的边列表转换为 MATCH 子句，比如要遍历的边为：a -> b -> c
     * 转换为 MATCH 语句为：(v1:a)-[e1:a_b]->(v2:b)-[e2:b_c]->(v3:c)
     *
     * @param graphQuery 查询参数
     * @return 转换后的 MATCH 子句
     */
    private String convertEdgeLabelsToMatchString(GraphQuery graphQuery) {
        int count = 1;
        String rootLabel = graphQuery.getRootVertexLabel();
        StringBuffer matchWhereBuffer = new StringBuffer();
        for (GraphQuery.EdgeLabel edgeLabel : graphQuery.getEdgeLabels()) {
            matchWhereBuffer.append(String.format("(v%d:%s)-", count, edgeLabel.getSource()));
            if (edgeLabel.getProperties() == null || edgeLabel.getProperties().isEmpty()) {
                matchWhereBuffer.append(String.format("[e%d:%s_%s]->", count, edgeLabel.getSource(), edgeLabel.getTarget()));
            } else {
                List<String> kv = new ArrayList<>();
                edgeLabel.getProperties().forEach((k, v) -> kv.add(StrUtil.format(v instanceof Integer ? "{}:{}" : "{}:\"{}\"", k, v)));
                String props = ArrayUtil.join(kv.toArray(new String[]{}), ",");
                matchWhereBuffer.append(String.format("[e%d:%s_%s{%s}]->", count, edgeLabel.getSource(), edgeLabel.getTarget(), props));
            }
            rootLabel = edgeLabel.getTarget();
            count++;
        }
        return matchWhereBuffer.append(String.format("(v%d:%s)", count, rootLabel)).toString();
    }

    /**
     * 将边的 label 列表拼接为字符串，使用逗号分隔，每个边的 source 和 target 之间使用下划线连接
     *
     * @param labels 边的 label 列表
     * @return
     */
    private String convertEdgeLabelsToString(List<SubGraphQuery.EdgeLabel> labels) {
        return String.join(",",
                labels.stream()
                        .map(label -> String.format("%s_%s", label.getSource(), label.getTarget()))
                        .collect(Collectors.toList()));
    }

    /**
     * 将节点 ID 列表拼接为字符串，使用逗号分隔，注意每个节点 ID 使用单引号包围
     *
     * @param vertexIdList 节点 ID 列表
     * @return
     */
    private String convertVertexIdListToString(List<String> vertexIdList) {
        return String.join(",", vertexIdList.stream()
                .map(id -> String.format("'%s'", id))
                .collect(Collectors.toList()));
    }

    /**
     * 执行 nGQL
     */
    private ResultSet execute(String traceId, String nGQL, String graphVersion) {
//        log.debug("traceId={}, nGQL: {}, version: {}", traceId, nGQL, graphVersion);
        Session session = null;
        try {
            session = nebulaPool.getSession(graphProperties.getUsername(), graphProperties.getPassword(), false);

            nGQL = String.format("USE %s;%s", getVersionedNamespace(graphVersion), nGQL);
            if (log.isDebugEnabled()) {
                log.debug("traceId={};Exec nGQL= {}", traceId, nGQL);
            }
            ResultSet resp = session.execute(nGQL);
            if (!resp.isSucceeded()) {
                throw new Exception(resp.getErrorMessage());
            }
            return resp;
        } catch (Exception e) {
            log.error(String.format("traceId=%s;Execute: `%s`, failed: %s", traceId, nGQL, e.getMessage()));
            throw new SkylabException(ApiErrorCode.ERROR.getCode(), e.getMessage(), e);
        } finally {
            if (session != null) {
                session.release();
            }
        }
    }

    /**
     * 执行 nGQL，返回图谱结果
     */
    private GraphData executeAndReturnGraphData(String traceId, String nGQL, String graphVersion) {
        try {
            ResultSet resp = execute(traceId, nGQL, graphVersion);
            return buildGraphData(resp, graphVersion);
        } catch (Exception e) {
            log.error(String.format("traceId=%s;Execute: `%s`, failed: %s", traceId, nGQL, e.getMessage()));
            throw new SkylabException(ApiErrorCode.ERROR.getCode(), e.getMessage(), e);
        }
    }

    /**
     * 执行 nGQL，返回图谱结果
     */
    private GraphData executeAndReturnGraphDataWithProps(String traceId, String nGQL, String graphVersion) {
        try {
            ResultSet resultSet = execute(traceId, nGQL, graphVersion);


            if (log.isTraceEnabled()) {
                log.trace("进入组装图谱结果接口, 请求参数 resultSet:{}, graphVersion:{}", resultSet, graphVersion);
            }
            // 从 resultSet 里解析出点和边的列表

            List<GraphData.GraphVertex> vertices = new ArrayList<>();
            JSONArray jsonArray = null;
            for (String name : resultSet.keys()) {
//初始化
                if (jsonArray == null) {
                    jsonArray = new JSONArray();
                    for (int j = 0; j < resultSet.colValues(name).size(); j++) {
                        jsonArray.add(new JSONObject());
                    }
                }

                for (int i = 0; i < resultSet.colValues(name).size(); i++) {
                    JSONObject properties = jsonArray.getJSONObject(i);

                    ValueWrapper value = resultSet.colValues(name).get(i);
                    //类型特殊处理拉倒
                    if ("label".equals(name)) {
                        properties.put(name, value.asList().get(0).asString());
                    }
                    //数据类型处理
                    if (value.isLong()) {
                        properties.put(name, value.asLong());
                    }
                    if (value.isBoolean()) {
                        properties.put(name, value.asBoolean());
                    }
                    if (value.isDouble()) {
                        properties.put(name, value.asDouble());
                    }
                    if (value.isString()) {
                        properties.put(name, value.asString());
                    }
                    if (value.isTime()) {
                        properties.put(name, value.asTime());
                    }
                    if (value.isDate()) {
                        properties.put(name, value.asDate());
                    }
                    if (value.isDateTime()) {
                        properties.put(name, value.asDateTime());
                    }


                }
            }
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject properties = jsonArray.getJSONObject(i);
                    JSONObject renameProperties = new JSONObject();
                    String key = "v." + properties.getString("label") + ".";
                    for (Map.Entry<String, Object> entry : properties.entrySet()) {
                        if ("id".equals(entry.getKey()) || "label".equals(entry.getKey())) {
                            continue;
                        }
                        renameProperties.put(entry.getKey().replace(key, ""), entry.getValue());
                    }
                    GraphData.GraphVertex vertex = GraphData.GraphVertex.builder()
                            .id(properties.getString("id"))
                            .label(properties.getString("label"))
                            .properties(renameProperties)
                            .build();
                    vertices.add(vertex);
                }
            }


            // 组装 GraphData
            GraphData graphData = new GraphData();
            graphData.setGraphVersion(graphVersion);
            graphData.setVertices(vertices);
            if (log.isTraceEnabled()) {
                log.trace("GraphData= {}", graphData);
            }
            return graphData;

        } catch (Exception e) {
            e.printStackTrace();
            log.error(String.format("traceId=%s;Execute: `%s`, failed: %s", traceId, nGQL, e.getMessage()));
            throw new SkylabException(ApiErrorCode.ERROR.getCode(), e.getMessage(), e);
        }
    }

    /**
     * 返回带版本号的图谱命名空间，如果版本号为空，返回默认的命名空间
     *
     * @return
     */
    private String getVersionedNamespace(String graphVersion) {
        if (StringUtils.isNotBlank(graphVersion)) {
            return String.format("%s_%s", graphProperties.getNamespace(), graphVersion);
        } else {
            return graphProperties.getNamespace();
        }
    }

    /**
     * 组装图谱结果
     */
    private GraphData buildGraphData(ResultSet resultSet, String graphVersion) throws Exception {
        if (log.isTraceEnabled()) {
            log.trace("进入组装图谱结果接口, 请求参数 resultSet:{}, graphVersion:{}", resultSet, graphVersion);
        }
        // 从 resultSet 里解析出点和边的列表
        List<GraphData.GraphVertex> vertices = new ArrayList<>();
        List<GraphData.GraphEdge> edges = new ArrayList<>();
        formatEdgeAndVertices(resultSet, edges, vertices);

        // 组装 GraphData
        GraphData graphData = new GraphData();
        graphData.setGraphVersion(graphVersion);
        graphData.setVertices(vertices);
        graphData.setEdges(edges);
        if (log.isTraceEnabled()) {
            log.trace("GraphData= {}", graphData);
        }
        return graphData;
    }

    /**
     * 从 resultSet 里解析出点和边的列表
     *
     * @param resultSet
     * @param edges
     * @param vertices
     * @throws Exception
     */
    private void formatEdgeAndVertices(ResultSet resultSet,
                                       List<GraphData.GraphEdge> edges, List<GraphData.GraphVertex> vertices) throws Exception {
        for (String name : resultSet.keys()) {
            for (ValueWrapper value : resultSet.colValues(name)) {
                // 如果是节点
                if (value.isVertex()) {
                    formatVertex(vertices, value);
                }
                // 如果是个list集合
                if (value.isList()) {
                    ArrayList<ValueWrapper> valueList = value.asList();
                    for (ValueWrapper valueWrapper : valueList) {
                        // 如果是节点
                        if (valueWrapper.isVertex()) {
                            formatVertex(vertices, valueWrapper);
                        }
                        // 如果是边
                        if (valueWrapper.isEdge()) {
                            formatEdge(edges, valueWrapper);
                        }
                    }
                }
            }
        }
    }

    /**
     * @Description: 格式化边
     * @Param:
     * @return:
     * @Author: xuYao2
     * @Date: 2022/3/14
     */
    private void formatEdge(List<GraphData.GraphEdge> edges, ValueWrapper valueWrapper) throws UnsupportedEncodingException {
        Relationship relationship = valueWrapper.asRelationship();
        edges.add(
                GraphData.GraphEdge.builder()
                        .id(relationship.srcId().asString() + "->" + relationship.dstId().asString())
                        .source(relationship.srcId().asString())
                        .target(relationship.dstId().asString())
                        .label(relationship.edgeName())
                        .properties(formatProperties(relationship.properties()))
                        .build());
    }

    /**
     * @Description: 格式化节点
     * @Param:
     * @return:
     * @Author: xuYao2
     * @Date: 2022/3/14
     */
    private void formatVertex(List<GraphData.GraphVertex> vertices, ValueWrapper value) throws UnsupportedEncodingException {

        Node node = value.asNode();
        JSONObject properties = new JSONObject();

        // 需要注意 tagNames 为0 的情况
        if (!node.tagNames().isEmpty()) {
            HashMap<String, ValueWrapper> map = node.properties(node.tagNames().get(0));
            properties = formatProperties(map);
        }


        // 将节点放入 GraphData 中的 点列表: vertices
        vertices.add(GraphData.GraphVertex.builder()
                .id(node.getId().asString())
                .label(!node.labels().isEmpty() ? node.labels().get(0) : null)
                .properties(properties)
                .build());
    }

    /**
     * 格式化节点或边的属性
     *
     * @param map
     * @return
     */
    private JSONObject formatProperties(HashMap<String, ValueWrapper> map) throws UnsupportedEncodingException {

        JSONObject properties = new JSONObject();
        if (map == null) {
            return properties;
        }

        for (Map.Entry<String, ValueWrapper> entry : map.entrySet()) {
            ValueWrapper value = entry.getValue();
            if (value.isLong()) {
                properties.put(entry.getKey(), value.asLong());
            }
            if (value.isBoolean()) {
                properties.put(entry.getKey(), value.asBoolean());
            }
            if (value.isDouble()) {
                properties.put(entry.getKey(), value.asDouble());
            }
            if (value.isString()) {
                properties.put(entry.getKey(), value.asString());
            }
            if (value.isTime()) {
                properties.put(entry.getKey(), value.asTime());
            }
            if (value.isDate()) {
                properties.put(entry.getKey(), value.asDate());
            }
            if (value.isDateTime()) {
                properties.put(entry.getKey(), value.asDateTime());
            }
        }
        return properties;
    }

    @Override
    public void close() throws Exception {
        if (nebulaPool != null) {
            nebulaPool.close();
        }
    }
}
