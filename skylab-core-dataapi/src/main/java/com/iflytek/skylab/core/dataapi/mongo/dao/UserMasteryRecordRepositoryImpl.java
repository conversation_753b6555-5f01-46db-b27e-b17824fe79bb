package com.iflytek.skylab.core.dataapi.mongo.dao;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.skylab.core.dataapi.mongo.entity.UserMasteryRecord;
import com.iflytek.skylab.core.dataapi.util.SubCollectionUtils;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.query.UpdateDefinition;
import org.springframework.data.util.Pair;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-03-06
 * UserMasteryRecord 用户画像 mongodb仓库
 */
@Slf4j
public class UserMasteryRecordRepositoryImpl {

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 按条件分表查询
     *
     * @param criteria
     * @param userId
     * @return
     */
    public List<UserMasteryRecord> search(Criteria criteria, String userId) {
        return mongoTemplate.find(new Query(criteria), UserMasteryRecord.class, SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId));
    }


    public List<UserMasteryRecord> findAllByCursor(Criteria criteria, String userId) {
        List<UserMasteryRecord> userMasteryRecords = new ArrayList<>();
        Query query = new Query(criteria);
        try (MongoCursor<Document> cursor =
                     //指定查询集合
                     mongoTemplate.getCollection(SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId))
                             //组装查询条件
                             .find(query.getQueryObject())
                             // 设置超时时间为1000毫秒
                             .maxTime(1000, TimeUnit.MILLISECONDS)
                             //设置游标查询不超时
//                             .noCursorTimeout(true)
                             //设置批量从数据库中获取的数据量
                             .batchSize(1000)
                             .cursor()) {
            Document doc;
            while (cursor.hasNext()) {
                doc = cursor.next();
                UserMasteryRecord to = JSON.to(UserMasteryRecord.class, doc);
                userMasteryRecords.add(to);
            }
        } catch (Exception e) {
            log.error("query mastery with cursor error" + e);
        }
        return userMasteryRecords;
    }


    /**
     * 按条件分表查询count
     *
     * @param criteria
     * @param userId
     * @return
     */
    public long searchCount(Criteria criteria, String userId) {
        return mongoTemplate.count(new Query(criteria), UserMasteryRecord.class, SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId));
    }

    /**
     * 查找和更新操作
     *
     * @param query
     * @param update
     * @param options
     * @param userId
     * @return
     */
    public UserMasteryRecord findAndModify(Query query, UpdateDefinition update, FindAndModifyOptions options, String userId) {
        return mongoTemplate.findAndModify(query, update, options, UserMasteryRecord.class, SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId));
    }

    /**
     * 批量更新
     *
     * @param updates
     * @param userId
     */
    public void upsertMasterDataBatch(List<Pair<Query, Update>> updates, String userId) {
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, UserMasteryRecord.class, SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId));
        bulkOps.upsert(updates);
        bulkOps.execute();
    }

    public UserMasteryRecord save(UserMasteryRecord userMasteryRecord, String userId) {
        return mongoTemplate.save(userMasteryRecord, SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId));
    }

    public boolean exists(Criteria criteria, String userId) {
        return mongoTemplate.exists(new Query(criteria), SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId));
    }

    public void saveBatch(List<UserMasteryRecord> userMasteryRecordList, String userId) {
        List<? extends List<?>> split = CollUtil.split(userMasteryRecordList, 1500);
        String collectionName = SubCollectionUtils.getSubUserMasteryRecordCollectionName(userId);
        int count = 0;
        try {
            BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collectionName);
            for (List<?> list : split) {
                bulkOps.insert(list);
                log.debug("同步{}，进度：{}", collectionName, count + "/" + userMasteryRecordList.size());
            }
            bulkOps.execute();
        } catch (Exception e) {
            log.warn("异常处理，走单个save保存" + e);
            count = 0;
            for (UserMasteryRecord masteryRecord : userMasteryRecordList) {
                count++;
                mongoTemplate.save(masteryRecord, collectionName);
                log.debug("同步{}，进度：{}", collectionName, count + "/" + userMasteryRecordList.size());
            }

        }

    }

}
