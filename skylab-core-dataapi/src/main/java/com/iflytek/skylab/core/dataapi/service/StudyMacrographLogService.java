package com.iflytek.skylab.core.dataapi.service;

import com.iflytek.skylab.core.dataapi.data.*;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 学情日志 数据工具入口
 * <p>
 */
@Validated
public interface StudyMacrographLogService {

    /**
     * 推荐记录批量存储
     *
     * @param traceId
     * @param recLogs
     * @return
     */
    List<String> saveRecLogList(@NotBlank String traceId, @NotEmpty List<@Valid RecLog> recLogs);

    List<FeedbackLog> saveFeedbackLogListAndGet(@NotBlank String traceId, @NotEmpty List<@Valid FeedbackLog> feedbackLogs);

    /**
     * 查询答题记录
     * <p>
     * 从数仓获取历史答题记录时，
     * 必有字段：userId、subjectCode、phaseCode、bizCode、nodeId、nodeType、resNodeId、resNodeType、updateTime、scoreRatio
     * 或有字段：bizAction、studyCode
     * 其他未列字段无。
     *
     * @param traceId
     * @param studyMacrographLogQuery
     * @return
     */
    List<StudyLogData> queryFeedbackLogs(@NotBlank String traceId, @NotNull @Valid StudyMacrographLogQuery studyMacrographLogQuery, Integer num);


    /**
     * 两张实时作答记录 + 历史记录 ， 实时记录优先级 》 历史记录
     * 用户id + 学科学段
     * 查询时间范围近两天  （每个用户一百条）
     */
    List<UserErrorAnswerLog> queryWrongLogs(@NotBlank String traceId, @NotNull @Valid ErrorLogQuery studyLogQuery, Integer num);

}
