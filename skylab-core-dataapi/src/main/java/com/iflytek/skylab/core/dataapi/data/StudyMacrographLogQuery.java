package com.iflytek.skylab.core.dataapi.data;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.FlagEnum;
import com.iflytek.skylab.core.constant.StudyCodeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 学情数据查询对象
 */
@Getter
@Setter
@Accessors(chain = true)
public class StudyMacrographLogQuery extends StudyLogQuery {

    /**
     * 学习场景
     */
    private List<StudyCodeEnum> studyCodes;

    /**
     * 用户无论是否作答，都支持“换一换
     * <p>
     * 换一换标识
     */
    private Boolean change = false;


    @Override
    public Criteria buildCriteria() {
        Criteria criteria = new Criteria()
                .and("user_id").is(getUserId())
                .and("node_id").in(getNodeIdList());

        if (CollectionUtil.isNotEmpty(studyCodes)) {
            criteria.and("study_code").in(studyCodes.stream()
                    .map(StudyCodeEnum::name)
                    .collect(Collectors.toList())
            );
        }

        if (CollectionUtil.isNotEmpty(getBizCodeList())) {
            criteria.and("biz_code").in(getBizCodeList().stream()
                    .map(BizCodeEnum::name)
                    .collect(Collectors.toList()));
        }

        if (getNodeType() != null) {
            criteria.and("node_type").is(getNodeType());
        }

        if (CollectionUtil.isNotEmpty(getResNodeIdList())) {
            criteria.and("res_node_id").in(getResNodeIdList());
        }

        if (StrUtil.isNotEmpty(getRoundId())) {
            criteria.and("mission_id").is(getRoundId());
        }

        if (StrUtil.isNotEmpty(getFuncCode())) {
            criteria.and("func_code").is(getFuncCode());
        }

        if (StrUtil.isNotEmpty(getSubjectCode())) {
            criteria.and("subject_code").is(getSubjectCode());
        }

        if (StrUtil.isNotEmpty(getPhaseCode())) {
            criteria.and("phase_code").is(getPhaseCode());
        }

        if (getStudyCode() != null) {
            criteria.and("study_code").is(getStudyCode().name());
        }

        if (!isContainsDeleted()) {
            criteria.and("delete_flag").in(FlagEnum.FALSE.intValue(), null);
        }

        return criteria;
    }

    @Override
    public String toString() {
        return "StudyMacrographLogQuery{" +
                "studyCodes=" + studyCodes +
                "} " + super.toString();
    }
}
