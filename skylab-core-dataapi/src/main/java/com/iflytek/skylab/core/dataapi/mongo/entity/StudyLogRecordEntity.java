package com.iflytek.skylab.core.dataapi.mongo.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.skylab.core.constant.*;
import com.iflytek.skylab.core.dataapi.data.FeedbackLog;
import com.iflytek.skylab.core.dataapi.data.RecLog;
import com.iflytek.skylab.core.dataapi.data.StudyLogData;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;

/**
 * 学情日志document
 * <p>
 * refTraceId - userId - studyCode - nodeId - resNodeId
 */
@Getter
@Setter
@Document(collection = "xxj_study_log_record")
@NoArgsConstructor
public class StudyLogRecordEntity {
    private static CopyOptions copyOptions = CopyOptions.create();
    // region推荐、答题共用字段

    @Id
    private String id;

    @Field(name = "trace_id")
    private String traceId;

    /**
     * 批改行为TraceId
     */
    @Field(name = "correct_trace_id")
    private String correctTraceId;

    /**
     * 用户Id
     */
    @Field(name = "user_id")
    private String userId;

    /**
     * 学段编码
     */
    @Field(name = "phase_code")
    private String phaseCode;

    /**
     * 学校Id
     */
    @Field(name = "school_id")
    private String schoolId;

    /**
     * 年级编码
     */
    @Field(name = "grade_code")
    private String gradeCode;

    /**
     * 班级id
     */
    @Field(name = "class_id")
    private String classId;

    /**
     * 学科编码
     */
    @Field(name = "subject_code")
    private String subjectCode;

    /**
     * 教材版本编码
     */
    @Field(name = "book_code")
    private String bookCode;

    /**
     * 图谱版本
     */
    @Field(name = "graph_version")
    private String graphVersion;

    /**
     * 学习场景
     */
    @Field(name = "study_code")
    private StudyCodeEnum studyCode;

    /**
     * 业务编码，详见编码表
     */
    @Field(name = "biz_code")
    private BizCodeEnum bizCode;

    /**
     * 业务功能枚举
     */
    @Field(name = "biz_action")
    private BizActionEnum bizAction;

    /**
     * 关联点id
     */
    @Field(name = "node_id")
    private String nodeId;

    /**
     * 关联点类型 （锚点、考点、复习点）
     */
    @Field(name = "node_type")
    private NodeTypeEnum nodeType;

    /**
     * 资源id （题目id 、其他资源id）
     */
    @Field(name = "res_node_id")
    private String resNodeId;

    /**
     * 资源类型 （题、视频、卡片）
     */
    @Field(name = "res_node_type")
    private ResourceTypeEnum resNodeType;

    /**
     * 轮标识，如测评任务、推荐任务
     */
    @Field(name = "mission_id")
    private String roundId;

    /**
     * 轮序号，一次测评任务中的序号
     */
    @Field(name = "mission_index")
    private Integer roundIndex;

    /**
     * 功能编码
     */
    @Field(name = "func_code")
    private String funcCode;

    /**
     * 学情记录来源
     */
    @Field(name = "from")
    private String from;

    /**
     * 创建时间
     */
    @CreatedDate
    @Field(name = "create_time")
    private Instant createTime;

    /**
     * 更新时间，采用答题记录时间
     */
    @LastModifiedDate
    @Field(name = "update_time")
    private Instant updateTime;


    // endregion


    // region 推荐的相关字段

    /**
     * 推荐记录对象
     */
    @Field(name = "rec_props")
    private RecProps recProps;

    // endregion


    // region 答题反馈的相关字段

    /**
     * 得分
     */
    @Field(name = "score")
    private Double score;

    /**
     * 标准得分
     */
    @Field(name = "standard_score")
    private Double standardScore;

    /**
     * 答题耗时(单位：秒)
     */
    @Field(name = "time_cost")
    private Integer timeCost;
    /**
     * 真实得分率
     */
    @Field(name = "score_ratio")
    private Double scoreRatio;

    /**
     * 反馈扩展
     */
    @Field(name = "feedback_ext")
    private JSONObject feedbackExt;

    /**
     * 反馈时间
     */
    @Field(name = "feedback_time")
    private Instant feedbackTime = Instant.now();

    // endregion


    // region 标志位字段

    /**
     * 是否克隆
     */
    @Field(name = "clone_flag")
    private Integer cloneFlag;

    /**
     * 逻辑删除标志
     */
    @Field(name = "delete_flag")
    private Integer deleteFlag;

    // endregion


    /**
     * 推荐记录对象
     *
     * <AUTHOR>
     * @date 2022/3/25 14:30
     */
    @Getter
@Setter
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class RecProps {

        /**
         * 推荐追踪标识
         */
        private String recTraceId;
        /**
         * 推荐时间
         */
        private Long recTime;
        /**
         * 推荐时的上下文
         */
        private JSONObject recContext;
        /**
         * 推荐建议终止时间戳
         */
        private Long recEndTime;

        /**
         * 章节测试总时长-单位秒 (单元限时测使用)
         */
        private Long unitTimes;

    }


    public StudyLogRecordEntity(String traceId) {
        this.traceId = traceId;
    }

    /**
     * 根据 traceId， RecLog 构造StudyLogRecordEntity
     *
     * @param traceId
     * @param log
     */
    public StudyLogRecordEntity(String traceId, RecLog log) {
        this.traceId = traceId;
        BeanUtil.copyProperties(log, this, copyOptions);
    }

    /**
     * 根据 traceId， FeedbackLog 构造StudyLogRecordEntity
     *
     * @param traceId
     * @param log
     */
    public StudyLogRecordEntity(String traceId, FeedbackLog log) {
        this.traceId = traceId;
        BeanUtil.copyProperties(log, this, copyOptions);
    }

    public StudyLogRecordEntity(FeedbackLog log) {
        this(null, log);
    }


    /**
     * 转化为StudyLogData对象
     *
     * @return
     */
    public StudyLogData toStudyLogData() {
        StudyLogData data = new StudyLogData();
        BeanUtil.copyProperties(this, data, copyOptions);
        return data;
    }

    public static void main(String[] args) {
        Instant instant = Instant.now();

        System.out.println("" + instant);
    }
}