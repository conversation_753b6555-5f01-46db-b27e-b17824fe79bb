package com.iflytek.skylab.core.dataapi.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.dataapi.configuration.StudyLogProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/24 17:12
 */
@Slf4j
public class UserAnchorExamRecentlyAnswerRecordDelegate {
    // user_id#biz_code#subject_code#phase_code#point_code#point_type
    private static final String FEATURE_NAME = "user_anchor_exam_recently_answer_record";
    /**
     * b版作答日志归档
     */
    private static final String BYOD_FEATURE_NAME = "user_byod_recently_answer_record";

    private static final CopyOptions copyOptionsIgnoreError = CopyOptions.create().ignoreNullValue().ignoreError();

    public static FeatureQuery generateFeatureQuery(List<String> nodeIds, StudyLogQuery query, StudyLogProperties studyLogProperties) {
        List<BizCodeEnum> bizCodes = query.getBizCodeList();
        String point_type = FeatureNodeTypeUtils.transform(query.getNodeType());

        List<Map<String, String>> params = Lists.newArrayList();
        // user_id#biz_code#subject_code#phase_code#point_code#point_type
        for (String nodeId : nodeIds) {
            for (BizCodeEnum bizCode : bizCodes) {
                if (StrUtil.isNotBlank(point_type)) {
                    params.add(buildParam(query, bizCode, nodeId, point_type));
                } else {
                    log.info("not point_type value, try my best");
                    params.add(buildParam(query, bizCode, nodeId, FeatureNodeTypeUtils.ANCHOR_POINT));
                    params.add(buildParam(query, bizCode, nodeId, FeatureNodeTypeUtils.CHECK_POINT));
                    params.add(buildParam(query, bizCode, nodeId, FeatureNodeTypeUtils.REVIEW_POINT));
                }

            }
        }


        FeatureQueryItem queryItem = new FeatureQueryItem();

        String featureName = getFeatureName(query);

        queryItem.setFeatureName(featureName);

        queryItem.setFeatureVersion(studyLogProperties.getFeatureVersion());
        queryItem.setGraphVersion(studyLogProperties.getGraphVersion());
        queryItem.setParams(params);

        FeatureQuery featureQuery = new FeatureQuery();
        featureQuery.setItems(Lists.newArrayList(queryItem));
        return featureQuery;
    }

    private static String getFeatureName(StudyLogQuery query) {
        if (CollectionUtil.isNotEmpty(query.getBizCodeList()) && BizCodeEnum.ZSY_BYOD.equals(query.getBizCodeList().get(0))) {
            return BYOD_FEATURE_NAME;
        } else {
            return FEATURE_NAME;
        }
    }


    public static MultiValueMap<String, StudyLogData> parseFeatureData(FeatureData featureData, StudyLogQuery query) {

        String featureName = getFeatureName(query);

        if (featureData == null || CollectionUtil.isEmpty(featureData.getItems())) {
            return null;
        }

        List<Map<String, String>> values = featureData.getItems().get(0).getValues();
        if (CollectionUtil.isEmpty(values)) {
            return null;
        }

        Date yesterday = DateUtil.yesterday().toJdkDate();
        Instant yesterdayZeroClock = DateUtil.beginOfDay(yesterday).toInstant();

        MultiValueMap<String, StudyLogData> multiValueMap = new LinkedMultiValueMap<>();
        // 遍历查询结果
        for (Map<String, String> valueMap : values) {
            String json = StrUtil.cleanBlank(valueMap.get(featureName));
            if (StrUtil.isBlank(json)) {
                continue;
            }
            // json为对象
            UserAnchorExamRecentlyAnswerRecord record = JSON.parseObject(json, UserAnchorExamRecentlyAnswerRecord.class);
            List<UserAnchorExamRecentlyAnswerRecord.TopicInfo> topicDetails = record.getTopicDetails();
            if (CollectionUtil.isEmpty(topicDetails)) {
                log.warn("no data find");
                return null;
            }
            // funcCode过滤
            if (StrUtil.isNotBlank(query.getFuncCode())) {
                topicDetails.removeIf(topic -> StrUtil.equalsIgnoreCase(query.getFuncCode(), topic.getFuncCode()));
            }
            // 组装数据
            for (UserAnchorExamRecentlyAnswerRecord.TopicInfo topic : topicDetails) {
                // 过滤昨天的数据。因为近两天从mongo查
                StudyLogData studyLogData = new StudyLogData();
                BeanUtil.copyProperties(record, studyLogData, copyOptionsIgnoreError);
                BeanUtil.copyProperties(topic, studyLogData, copyOptionsIgnoreError);
                studyLogData.setUpdateTime(studyLogData.getFeedbackTime());
                // 从数仓查前天及之前的，mongo近两天的
                if (studyLogData.getFeedbackTime() != null && studyLogData.getFeedbackTime().isBefore(yesterdayZeroClock)) {
                    multiValueMap.add(studyLogData.getNodeId(), studyLogData);
                }
            }
        }

        // 按updateTime倒序
        multiValueMap.forEach((k, v) -> v.sort((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime())));
        return multiValueMap;
    }


    private static Map<String, String> buildParam(StudyLogQuery query, BizCodeEnum bizCode, String pointCode, String pointType) {
        Map<String, String> featureParam = new HashMap<>();
        featureParam.put("user_id", query.getUserId());
        featureParam.put("biz_code", bizCode.name());
        featureParam.put("subject_code", query.getSubjectCode());
        featureParam.put("phase_code", query.getPhaseCode());
        featureParam.put("point_code", pointCode);
        featureParam.put("point_type", pointType);
        return featureParam;
    }

}
