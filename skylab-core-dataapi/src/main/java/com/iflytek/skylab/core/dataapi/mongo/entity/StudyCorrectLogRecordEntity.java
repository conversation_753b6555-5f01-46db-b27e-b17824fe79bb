package com.iflytek.skylab.core.dataapi.mongo.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.iflytek.skylab.core.dataapi.data.FeedbackLog;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2023/3/16 15:22
 * @description 批改行为日志
 */

@Getter
@Setter
@Document(collection = "xxj_study_correct_log_record")
@NoArgsConstructor
public class StudyCorrectLogRecordEntity extends StudyLogRecordEntity {

    private static CopyOptions copyOptions = CopyOptions.create();


    /**
     * 根据 FeedbackLog StudyCorrectLogRecordEntity
     *
     * @param log
     */
    public StudyCorrectLogRecordEntity(FeedbackLog log) {
        BeanUtil.copyProperties(log, this, copyOptions);
    }
}


