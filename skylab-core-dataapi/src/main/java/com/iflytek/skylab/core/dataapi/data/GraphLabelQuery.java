package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skylab.core.data.Jsonable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 图顶点查询参数
 */
@Getter
@Setter
@Accessors(chain = true)
public class GraphLabelQuery extends Jsonable {

    @NotBlank
    private String traceId;

    /**
     * 顶点Label
     */
    @NotBlank
    private String label;


    /**
     * 图谱版本
     */
    @NotBlank
    private String graphVersion;


    @Override
    public String toString() {
        return super.toString();
    }
}
