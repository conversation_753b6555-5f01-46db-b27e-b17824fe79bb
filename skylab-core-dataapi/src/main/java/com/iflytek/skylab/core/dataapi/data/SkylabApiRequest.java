package com.iflytek.skylab.core.dataapi.data;

import com.iflytek.skyline.common.api.ApiRequestGeneric;
import lombok.Getter;
import lombok.Setter;

/**
 * skylab 服务请求对象
 *
 * @param <T> 请求数据类型
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2022-02-08 16:35
 */


@Getter
@Setter
public class SkylabApiRequest<T> extends ApiRequestGeneric<T> {


    public SkylabApiRequest() {
    }

    public SkylabApiRequest(T payload) {
        super(payload);
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
