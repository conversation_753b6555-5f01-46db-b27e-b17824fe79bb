package com.iflytek.skylab.core.dataapi.data;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/***
 *
 * 特征请求数据
 *
 * {"traceId": "asdgagasdfdsafd", "featureQuery":[{
 *     "featureName": "fc_score",
 *     "params": [{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"a"},{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"b"}],
 *     "featureVersion": 2,
 *     "graphVersion": "v2022-01"
 *
 *   },{
 *     "featureName": "dev_score",
 *     "params": [{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"a"},{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"b"}],
 *     "featureVersion": 2,
 *     "graphVersion": "v2022-01"
 *   }]
 * }
 *
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class FeatureQueryItem {

    /**
     * 图谱版本
     */
    @NotBlank
    private String graphVersion;

    /**
     * 特征名称
     */
    @NotEmpty
    private String featureName;

    /**
     * 查询参数
     * [{"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"a"},
     * {"user_id":"15000000","biz_code":"xxj","subject_code":"02","phase_code":"05","anchor_id":"b"}],
     */
    @NotEmpty
    private List<Map<String, String>> params = new ArrayList<>();

    /**
     * 特征版本
     */
    private int featureVersion;
}
