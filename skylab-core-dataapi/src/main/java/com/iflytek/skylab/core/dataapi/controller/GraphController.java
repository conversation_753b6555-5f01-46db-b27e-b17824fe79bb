package com.iflytek.skylab.core.dataapi.controller;

import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;

import javax.validation.Valid;

/**
 * 图谱查询服务
 */
@Slf4j
@Api(tags = "X.图谱查询服务")
@RestController
@RequestMapping(value = "/skylab/api/v1/graph", consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
@EnableSkynetSwagger2
@ConditionalOnBean(GraphService.class)
public class GraphController {

    private final GraphService graphService;

    public GraphController(GraphService graphService) {
        this.graphService = graphService;
    }

//    @SkylineMetric
    @ApiOperation(value = "1.查询子图")
    @PostMapping("/querySubGraph")
    public GraphData querySubGraph(@RequestBody @Valid SubGraphQuery subGraphQuery) {
        return graphService.querySubGraph(subGraphQuery);
    }

//    @SkylineMetric
    @ApiOperation(value = "2.查询顶点列表")
    @PostMapping("/queryVertices")
    public GraphData queryVertices(@RequestBody @Valid GraphQuery graphQuery) {
        return graphService.queryVertices(graphQuery);
    }

//    @SkylineMetric
    @ApiOperation(value = "3.根据路径逆推根节点")
    @PostMapping("/queryVerticesReverse")
    public GraphData queryVerticesReverse(@RequestBody @Valid GraphQuery graphQuery) {
        return graphService.queryVerticesReverse(graphQuery);
    }

//    @SkylineMetric
    @ApiOperation(value = "4.根据点的id 查询节点详细信息")
    @PostMapping("/queryVertex")
    public GraphData queryVertex(@RequestBody @Valid GraphVertexQuery graphVertexQuery) {
        return graphService.queryVertex(graphVertexQuery);
    }

//    @SkylineMetric
    @ApiOperation(value = "5.保存点（新增或更新）")
    @PostMapping("/saveVertex")
    public boolean saveVertex(@RequestBody @Valid GraphVertex graphVertex) {
        graphService.saveVertex(graphVertex);
        return true;
    }

//    @SkylineMetric
    @ApiOperation(value = "6.删除点")
    @PostMapping("/deleteVertex")
    public boolean deleteVertex(@RequestBody @Valid GraphVertex graphVertex) {
        graphService.deleteVertex(graphVertex);
        return true;
    }

//    @SkylineMetric
    @ApiOperation(value = "7.保存边（新增或更新）")
    @PostMapping("/saveEdge")
    public boolean saveEdge(@RequestBody @Valid GraphEdge graphEdge) {
        graphService.saveEdge(graphEdge);
        return true;
    }

//    @SkylineMetric
    @ApiOperation(value = "8.删除边")
    @PostMapping("/deleteEdge")
    public boolean deleteEdge(@RequestBody @Valid GraphEdge graphEdge) {
        graphService.deleteEdge(graphEdge);
        return true;
    }
}
