package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.FlagEnum;
import com.iflytek.skylab.core.dataapi.configuration.StudyLogProperties;
import com.iflytek.skylab.core.dataapi.data.FeatureData;
import com.iflytek.skylab.core.dataapi.data.FeatureQuery;
import com.iflytek.skylab.core.dataapi.data.StudyLogData;
import com.iflytek.skylab.core.dataapi.data.StudyLogQuery;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.mongo.dao.StudyLogGroupRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyCorrectLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyMacrographLogRecordEntity;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skylab.core.dataapi.service.StudyLogGroupService;
import com.iflytek.skylab.core.dataapi.util.UserAnchorExamRecentlyAnswerRecordDelegate;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class StudyLogGroupServiceImpl implements StudyLogGroupService {

    private final FeatureService featureService;
    private final StudyLogProperties studyLogProperties;
    private final StudyLogGroupRepository studyLogGroupRepository;


    public StudyLogGroupServiceImpl(StudyLogProperties studyLogProperties, FeatureService featureService,StudyLogGroupRepository studyLogGroupRepository) {
        this.studyLogProperties = studyLogProperties;
        this.featureService = featureService;
        this.studyLogGroupRepository = studyLogGroupRepository;
    }

    /**
     * 查询作答记录
     * 限制查询的条数
     * ！！ studyCode不作为查询条件
     * @param traceId
     * @param studyLogQuery
     * @param startTime
     * @param endTime
     * @param num
     * @return
     */
    @Override
    @SkylineMetric(value = "DataHub-3.查询答题记录", meterProviders = SkylabDataApiMeterProvider.class)
    public List<StudyLogData> queryFeedbackLogsWithFeedBackTime(String traceId, StudyLogQuery studyLogQuery, Long startTime, Long endTime, Integer num) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, studyLogQuery={}, num={}", traceId, studyLogQuery, num);
        }
        final int target = (num == null || num < 1) ? 10 : num;

        // key:nodeId， val: 点对应的答题记录
        MultiValueMap<String, StudyLogData> multiValueMap = new LinkedMultiValueMap<>();

        // 从mongo查询数据
        Criteria criteria = new Criteria()
                .and("user_id").is(studyLogQuery.getUserId())
                .and("node_id").in(studyLogQuery.getNodeIdList())
                .and("biz_code").in(studyLogQuery.getBizCodeList().stream()
                        .map(BizCodeEnum::name)
                        .collect(Collectors.toList())
                );

        if (studyLogQuery.getNodeType() != null) {
            criteria.and("node_type").is(studyLogQuery.getNodeType());
        }

        if (CollectionUtil.isNotEmpty(studyLogQuery.getResNodeIdList())) {
            criteria.and("res_node_id").in(studyLogQuery.getResNodeIdList());
        }

        if (StrUtil.isNotEmpty(studyLogQuery.getRoundId())) {
            criteria.and("mission_id").is(studyLogQuery.getRoundId());
        }

        if (StrUtil.isNotEmpty(studyLogQuery.getSubjectCode())) {
            criteria.and("subject_code").is(studyLogQuery.getSubjectCode());
        }

        if (StrUtil.isNotEmpty(studyLogQuery.getPhaseCode())) {
            criteria.and("phase_code").is(studyLogQuery.getPhaseCode());
        }

        criteria.and("delete_flag").in(FlagEnum.FALSE.intValue(), null);

        if (null != startTime) {
            criteria.and("feedback_time").gte(DateTime.of(startTime));
        }

        if (null != endTime) {
            criteria.and("feedback_time").lte(DateTime.of(endTime));
        }

        criteria.and("time_cost").ne(null);

        Stopwatch stopwatch = Stopwatch.createStarted();

        // 查询作答日志和批改日志，进行merge
//        List<StudyCorrectLogRecordEntity> studyCorrectLogRecordEntityList = studyCorrectLogRecordRepository.search(criteria);
//        List<StudyLogRecordEntity> studyLogRecordEntityList = studyLogRecordRepository.search(criteria);
        List<StudyCorrectLogRecordEntity> studyCorrectLogRecordEntityList = studyLogGroupRepository.findStudyCorrectLogGroupByNodeId(criteria,target);
        List<StudyLogRecordEntity> studyLogRecordEntityList = studyLogGroupRepository.findStudyLogGroupByNodeId(criteria,target);
        List<StudyLogRecordEntity> records = mergeStudyCorrectLogAndStudyLog(studyCorrectLogRecordEntityList, studyLogRecordEntityList);


        if (log.isDebugEnabled()) {
            log.debug("queryFeedbackLogs mergeStudyLog result is : {}", JSON.toJSONString(records));
        }
//        List<StudyLogRecordEntity> records = studyLogRecordRepository.search(criteria);
        long mongoCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);


        // 查询作答日志-大图谱(精品密卷+同步习题)/ 错题本
        List<StudyMacrographLogRecordEntity> recordsMacrograph = studyLogGroupRepository.findMacroGraphStudyLogGroupByNodeId(criteria,target);
        if (log.isDebugEnabled()) {
            log.debug("queryFeedbackLogs recordsMacrograph result is : {}", JSON.toJSONString(recordsMacrograph));
        }
        if (CollectionUtil.isNotEmpty(recordsMacrograph)) {
            records.addAll(recordsMacrograph);
        }


        if (CollectionUtil.isNotEmpty(records)) {
            Map<String, List<StudyLogData>> mongoDataMap = records.stream()
                    .map(StudyLogRecordEntity::toStudyLogData)
                    .sorted((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()))
                    .collect(Collectors.groupingBy(StudyLogData::getNodeId));

            multiValueMap.putAll(mongoDataMap);
        }

        // 在mongo中已经查到足够数据的nodeId，不再从数仓补充查询
        // 結果沒有查到足够数据的nodeId，从数仓补充历史数据
        List<String> additional = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(multiValueMap)) {
            for (String nodeId : studyLogQuery.getNodeIdList()){
                if (multiValueMap.getOrDefault(nodeId, Lists.newArrayList()).size() < target) {
                    additional.add(nodeId);
                }
            }
        }

        // 从数仓补充历史数据
        stopwatch.reset().start();
        MultiValueMap<String, StudyLogData> appendMultiValueMap = userAnchorExamRecentlyAnswerRecord(traceId, additional, studyLogQuery);
        if (log.isDebugEnabled()) {
            log.debug("query odeon studyLog result is : {}", JSON.toJSONString(appendMultiValueMap));
        }
        long odeonCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        if (CollectionUtil.isNotEmpty(appendMultiValueMap)) {
            additional.forEach(nodeId -> multiValueMap.addAll(nodeId, appendMultiValueMap.getOrDefault(nodeId, Lists.newArrayList())));
        }

        // 统一截取目标长度
        List<StudyLogData> result = Lists.newArrayList();
        multiValueMap.values().forEach(list -> result.addAll(list.subList(0, Math.min(list.size(), target))));

        log.info("【dataapi-cost】答题记录mongo耗时:{} 数量:{}; 答题记录odeon耗时:{} 数量：{}", mongoCost, records.size(), odeonCost, result.size() - records.size());
        return result;
    }

    /**
     * 合并作答日志和批改日志
     *
     * @param studyCorrectLogRecordEntityList
     * @param studyLogRecordEntityList
     * @return
     */
    private List<StudyLogRecordEntity> mergeStudyCorrectLogAndStudyLog(List<StudyCorrectLogRecordEntity> studyCorrectLogRecordEntityList, List<StudyLogRecordEntity> studyLogRecordEntityList) {
        if (log.isDebugEnabled()) {
            log.debug("mergeStudyLog param : studyCorrectLogRecordEntityList is {},studyLogRecordEntityList is  {}", JSON.toJSONString(studyCorrectLogRecordEntityList), JSON.toJSONString(studyLogRecordEntityList));
        }
        if (CollectionUtil.isEmpty(studyCorrectLogRecordEntityList) && CollectionUtil.isEmpty(studyLogRecordEntityList)) {
            return new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(studyCorrectLogRecordEntityList) && CollectionUtil.isNotEmpty(studyLogRecordEntityList)) {
            return studyLogRecordEntityList;
        }
        if (CollectionUtil.isNotEmpty(studyCorrectLogRecordEntityList) && CollectionUtil.isEmpty(studyLogRecordEntityList)) {
            return studyCorrectLogRecordEntityList.stream().map(studyCorrectLogRecordEntity -> (StudyLogRecordEntity) studyCorrectLogRecordEntity).collect(Collectors.toList());
        }
        //有作答行为就一定有批改行为，但有批改行为不一定会有作答行为
        //两者的traceId都是会话id
        //作答日志的correctTraceId为对应的批改日志的traceId，批改日志的correctTraceId为null
        Set<String> correctTraceIdSet = new HashSet<>();
        for (StudyLogRecordEntity studyLogRecordEntity : studyLogRecordEntityList) {
            correctTraceIdSet.add(studyLogRecordEntity.getCorrectTraceId());
        }
        for (StudyCorrectLogRecordEntity studyCorrectLogRecordEntity : studyCorrectLogRecordEntityList) {
            if (!correctTraceIdSet.contains(studyCorrectLogRecordEntity.getTraceId())) {
                studyLogRecordEntityList.add(studyCorrectLogRecordEntity);
            }
        }
        return studyLogRecordEntityList;
    }

    /**
     * 查询特征 user_anchor_exam_recently_answer_record
     *
     * @param traceId
     * @param additional
     * @param query
     * @return
     */
    private MultiValueMap<String, StudyLogData> userAnchorExamRecentlyAnswerRecord(String traceId, List<String> additional, StudyLogQuery query) {
        if (CollectionUtil.isEmpty(additional)) {
            return null;
        }
        try {
            FeatureQuery featureQuery = UserAnchorExamRecentlyAnswerRecordDelegate.generateFeatureQuery(additional, query, studyLogProperties);
            FeatureData featureData = featureService.query(traceId, featureQuery);
            return UserAnchorExamRecentlyAnswerRecordDelegate.parseFeatureData(featureData, query);
        } catch (Exception e) {
            log.error("query user_anchor_exam_recently_answer_record error", e);
        }
        return null;
    }
}
