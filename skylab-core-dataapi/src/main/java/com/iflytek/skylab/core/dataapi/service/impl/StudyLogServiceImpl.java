package com.iflytek.skylab.core.dataapi.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.BizCodeEnum;
import com.iflytek.skylab.core.constant.FlagEnum;
import com.iflytek.skylab.core.dataapi.configuration.StudyLogProperties;
import com.iflytek.skylab.core.dataapi.data.*;
import com.iflytek.skylab.core.dataapi.metric.SkylabDataApiMeterProvider;
import com.iflytek.skylab.core.dataapi.mongo.dao.*;
import com.iflytek.skylab.core.dataapi.mongo.entity.DataCacheRecord;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyCorrectLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyLogRecordEntity;
import com.iflytek.skylab.core.dataapi.mongo.entity.StudyMacrographLogRecordEntity;
import com.iflytek.skylab.core.dataapi.service.FeatureService;
import com.iflytek.skylab.core.dataapi.service.StudyLogService;
import com.iflytek.skylab.core.dataapi.util.UserAnchorExamRecentlyAnswerRecordDelegate;
import com.iflytek.skylab.core.dataapi.util.UserRecently200AnswerRecordDelegate;
import com.iflytek.skyline.brave.annotation.SkylineMetric;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * 学情日志数据访问实现
 */
@Slf4j
public class StudyLogServiceImpl implements StudyLogService {

    private final StudyLogProperties studyLogProperties;
    private final FeatureService featureService;
    private final StudyLogRecordRepository studyLogRecordRepository;
    private final StudyCorrectLogRecordRepository studyCorrectLogRecordRepository;
    private final StudyMacrographRecordRepository macrographRecordRepository;
    private final DataCacheRecordRepository dataCacheRecordRepository;


    private final CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();
    private final CopyOptions copyOptionsIgnoreError = CopyOptions.create().ignoreNullValue().ignoreError();

    public StudyLogServiceImpl(StudyLogProperties studyLogProperties,
                               FeatureService featureService,
                               StudyLogRecordRepository studyLogRecordRepository,
                               StudyCorrectLogRecordRepository studyCorrectLogRecordRepository,
                               DataCacheRecordRepository dataCacheRecordRepository,
                               StudyMacrographRecordRepository macrographRecordRepository) {
        this.studyLogProperties = studyLogProperties;
        this.featureService = featureService;
        this.studyLogRecordRepository = studyLogRecordRepository;
        this.studyCorrectLogRecordRepository = studyCorrectLogRecordRepository;
        this.dataCacheRecordRepository = dataCacheRecordRepository;
        this.macrographRecordRepository = macrographRecordRepository;
    }

    @Override
    @SkylineMetric(value = "DataHub-3.推荐记录存储", meterProviders = SkylabDataApiMeterProvider.class)
    public String saveRecLog(String traceId, RecLog recLog) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, recLog={}", traceId, recLog);
        }
        // 类中转换
        StudyLogRecordEntity studyLogRecord = new StudyLogRecordEntity(traceId, recLog);
        // 保存，并返回结果
        StudyLogRecordEntity saved = studyLogRecordRepository.save(studyLogRecord);
        return saved.getId();
    }

    @Override
    public String saveStudyLogRecord(String traceId, StudyLogRecordEntity studyLogRecordEntity) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, studyLogRecordEntity={}", traceId, studyLogRecordEntity);
        }
        // 保存，并返回结果
        StudyLogRecordEntity saved = studyLogRecordRepository.save(studyLogRecordEntity);
        return saved.getId();
    }

    @Override
    @SkylineMetric(value = "DataHub-3.推荐记录批量存储", meterProviders = SkylabDataApiMeterProvider.class)
    public List<String> saveRecLogList(String traceId, List<RecLog> recLogs) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, recLogs={}", traceId, recLogs);
        }

        List<StudyLogRecordEntity> entities = recLogs.stream()
                .map(recLog -> new StudyLogRecordEntity(traceId, recLog))
                .collect(Collectors.toList());

        List<StudyLogRecordEntity> savedEntities = studyLogRecordRepository.saveAll(entities);
        return savedEntities.stream()
                .map(StudyLogRecordEntity::getId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    @SkylineMetric(value = "DataHub-3.答题记录存储", meterProviders = SkylabDataApiMeterProvider.class)
    public String saveFeedbackLog(String traceId, FeedbackLog feedbackLog) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, feedbackLog={}", traceId, feedbackLog);
        }
        try {
            return saveFeedbackLogAndGetStudyLog(traceId, feedbackLog).getId();
        } catch (Exception e) {
            log.error("saveFeedbackLog error, traceId：{}", traceId, e);
        }
        return null;
    }


    @Override
    @SkylineMetric(value = "DataHub-3.答题记录存储(并返回答题记录)", meterProviders = SkylabDataApiMeterProvider.class)
    public FeedbackLog saveFeedbackLogAndGet(String traceId, FeedbackLog feedbackLog) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, feedbackLog={}", traceId, feedbackLog);
        }
        try {
            StudyLogRecordEntity studyLog = saveFeedbackLogAndGetStudyLog(traceId, feedbackLog);
            feedbackLog.setId(studyLog.getId());
            feedbackLog.setFuncCode(studyLog.getFuncCode());
            return feedbackLog;
        } catch (Exception e) {
            log.error("save feedbackLog error, traceId：{}", traceId, e);
        }
        return null;
    }

    private StudyLogRecordEntity saveFeedbackLogAndGetStudyLog(String traceId, FeedbackLog feedbackLog) {
        StudyLogRecordEntity record = findOriginalStudyLogRecord(feedbackLog);
        if (record == null) {
            record = new StudyLogRecordEntity(traceId, feedbackLog);
        } else {
            record.setFrom(feedbackLog.getFrom());
            record.setTimeCost(feedbackLog.getTimeCost());
            record.setScore(feedbackLog.getScore());
            record.setStandardScore(feedbackLog.getStandardScore());
            record.setScoreRatio(feedbackLog.getScoreRatio());
            record.setFeedbackExt(feedbackLog.getFeedbackExt());
            record.setFeedbackTime(feedbackLog.getFeedbackTime());
            record.setCorrectTraceId(feedbackLog.getCorrectTraceId());
        }
        return studyLogRecordRepository.save(record);
    }

    private StudyLogRecordEntity findOriginalStudyLogRecord(FeedbackLog feedbackLog) {
        String refTraceId = feedbackLog.getRefTraceId();
        if (StrUtil.isBlank(refTraceId)) {
            return null;
        }
        // 根据refTraceId、userId、studyCode、nodeId、resNodeId查找
        Criteria criteria = Criteria.where("trace_id").is(refTraceId)
                .and("user_id").is(feedbackLog.getUserId())
                .and("study_code").is(feedbackLog.getStudyCode())
                .and("node_id").is(feedbackLog.getNodeId())
                .and("res_node_id").is(feedbackLog.getResNodeId());

        List<StudyLogRecordEntity> list = studyLogRecordRepository.search(criteria);
        return CollectionUtil.isEmpty(list) ? null : list.get(0);
    }

    @Override
    @SkylineMetric(value = "DataHub-3.批量保存答题记录", meterProviders = SkylabDataApiMeterProvider.class)
    public List<String> saveFeedbackLogList(String traceId, List<FeedbackLog> feedbackLogs) {
        return feedbackLogs.stream()
                .map(feedbackLog -> saveFeedbackLog(traceId, feedbackLog))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    @SkylineMetric(value = "DataHub-3.批量保存答题记录(并返回答题记录)", meterProviders = SkylabDataApiMeterProvider.class)
    public List<FeedbackLog> saveFeedbackLogListAndGet(String traceId, List<FeedbackLog> feedbackLogs) {
        return feedbackLogs.stream()
                .map(feedbackLog -> saveFeedbackLogAndGet(traceId, feedbackLog))
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<FeedbackLog> saveStudyCorrectLogList(@NotBlank String traceId, @NotEmpty List<@Valid FeedbackLog> feedbackLogs) {
        return feedbackLogs.stream()
                .map(feedbackLog -> {
                    StudyCorrectLogRecordEntity studyCorrectLogRecordEntity = saveStudyCorrectLogAndGet(traceId, feedbackLog);
                    feedbackLog.setId(studyCorrectLogRecordEntity.getId());
                    feedbackLog.setFuncCode(studyCorrectLogRecordEntity.getFuncCode());
                    return feedbackLog;
                })
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.toList());
    }

    private StudyCorrectLogRecordEntity saveStudyCorrectLogAndGet(String traceId, FeedbackLog feedbackLog) {
        StudyCorrectLogRecordEntity studyCorrectLogRecordEntity = new StudyCorrectLogRecordEntity(feedbackLog);
        studyCorrectLogRecordEntity.setTraceId(traceId);
        return studyCorrectLogRecordRepository.save(studyCorrectLogRecordEntity);
    }


    @Override
    @SkylineMetric(value = "DataHub-3.学情数据查询", meterProviders = SkylabDataApiMeterProvider.class)
    public List<StudyLogData> queryStudyLog(String traceId, StudyLogQuery studyLogQuery) {
        Criteria criteria = studyLogQuery.buildCriteria();
        List<StudyLogRecordEntity> records = studyLogRecordRepository.search(criteria);
        // TODO featureService 查询数仓数据

        return records.stream()
                .map(StudyLogRecordEntity::toStudyLogData)
                .collect(Collectors.toList());
    }

    @Override
    @SkylineMetric(value = "DataHub-3.5分钟学情数据查询", meterProviders = SkylabDataApiMeterProvider.class)
    public List<StudyLogData> queryStudyLog5min(String traceId, StudyLogQuery studyLogQuery) {
        //最近5分钟
        Date newDate = DateUtil.offsetMinute(DateUtil.date(), -5).toJdkDate();
        Criteria criteria = studyLogQuery.buildCriteria()
                .and("feedback_time").gte(newDate)
                .and("time_cost").ne(null);

        Stopwatch stopwatch = Stopwatch.createStarted();
        List<StudyLogRecordEntity> records = studyLogRecordRepository.search(criteria);
        long cost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        log.info("【dataapi-cost】答题记录mongo耗时:{} 数量:{}", cost, records.size());

        return records.stream()
                .map(StudyLogRecordEntity::toStudyLogData)
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        Date yesterday = DateUtil.yesterday().toJdkDate();
        DateTime dateTime = DateUtil.beginOfDay(yesterday);
        String format = DateUtil.format(new Date(dateTime.getTime()), NORM_DATETIME_PATTERN);
        System.err.println(format);
    }

    @Override
    @SkylineMetric(value = "DataHub-3.查询答题记录", meterProviders = SkylabDataApiMeterProvider.class)
    public List<StudyLogData> queryFeedbackLogsToday(String traceId, StudyLogQuery studyLogQuery, Integer num) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, studyLogQuery={}, num={}", traceId, studyLogQuery, num);
        }
        final int target = (num == null || num < 1) ? 10 : num;

        // key:nodeId， val: 点对应的答题记录
        MultiValueMap<String, StudyLogData> multiValueMap = new LinkedMultiValueMap<>();

        // 从mongo查询近当天数据
        Criteria criteria = studyLogQuery.buildCriteria()
                .and("time_cost").ne(null);
        criteria.and("feedback_time").gte(DateUtil.beginOfDay(DateUtil.beginOfDay(new Date())));

        Stopwatch stopwatch = Stopwatch.createStarted();

        // 查询作答日志和批改日志，进行merge
        List<StudyCorrectLogRecordEntity> studyCorrectLogRecordEntityList = studyCorrectLogRecordRepository.search(criteria);
        List<StudyLogRecordEntity> studyLogRecordEntityList = studyLogRecordRepository.search(criteria);
        List<StudyLogRecordEntity> recordsSync = mergeStudyCorrectLogAndStudyLog(studyCorrectLogRecordEntityList, studyLogRecordEntityList);
        if (log.isDebugEnabled()) {
            log.debug("queryFeedbackLogs mergeStudyLog result is : {}", JSON.toJSONString(recordsSync));
        }

        List<StudyLogRecordEntity> records = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(recordsSync)) {
            records.addAll(recordsSync);
        }

        //如果是精准学os，查询所有
        // 查询作答日志-大图谱(精品密卷+同步习题) 错题本待定
        List<StudyMacrographLogRecordEntity> recordsMacrograph = macrographRecordRepository.search(criteria);
        if (log.isDebugEnabled()) {
            log.debug("queryFeedbackLogs recordsMacrograph result is : {}", JSON.toJSONString(recordsMacrograph));
        }
        if (CollectionUtil.isNotEmpty(recordsMacrograph)) {
            records.addAll(recordsMacrograph);
        }

        if (CollectionUtil.isNotEmpty(records)) {
            Map<String, List<StudyLogData>> mongoDataMap = records.stream()
                    .map(StudyLogRecordEntity::toStudyLogData)
                    .sorted((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()))
                    .collect(Collectors.groupingBy(StudyLogData::getNodeId));

            multiValueMap.putAll(mongoDataMap);
        }
        // 统一截取目标长度
        List<StudyLogData> result = Lists.newArrayList();
        multiValueMap.values().forEach(list -> result.addAll(list.subList(0, Math.min(list.size(), target))));

        // 按 用户id，点id、题id、feedback时间确定唯一值
        Set<String> studyLogKeys = result.stream().map(log -> log.getUserId() + log.getNodeId() + log.getResNodeId() + log.getFeedbackTime()).collect(Collectors.toSet());
        // 如果存在roundId，需要补充roundId下所有记录
        if(StringUtils.isNotBlank(studyLogQuery.getRoundId())){
            List<StudyLogData> roundStudyLogs = queryFeedbackLogsWithRound(traceId, studyLogQuery, studyLogQuery.getRoundId());
            if (CollectionUtil.isNotEmpty(roundStudyLogs)){
                // 按 用户id，点id、题id、feedback时间过滤
                roundStudyLogs.stream()
                        .filter(log -> !studyLogKeys.contains(log.getUserId() + log.getNodeId() + log.getResNodeId() + log.getFeedbackTime()))
                        .forEach(log -> result.add(log));
            }
        }

        long mongoCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        log.info("【dataapi-cost】答题记录mongo耗时:{} 数量:{}; ", mongoCost, records.size());
        return result;
    }


    @Override
    @SkylineMetric(value = "DataHub-3.查询答题记录", meterProviders = SkylabDataApiMeterProvider.class)
    public List<StudyLogData> queryFeedbackLogs(String traceId, StudyLogQuery studyLogQuery, Integer num) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, studyLogQuery={}, num={}", traceId, studyLogQuery, num);
        }
        final int target = (num == null || num < 1) ? 50 : num;

        // key:nodeId， val: 点对应的答题记录
        MultiValueMap<String, StudyLogData> multiValueMap = new LinkedMultiValueMap<>();

        // 从mongo查询近两天数据
        Date yesterday = DateUtil.yesterday().toJdkDate();
        Criteria criteria = studyLogQuery.buildCriteria()
                .and("time_cost").ne(null);
        criteria.and("feedback_time").gte(DateUtil.beginOfDay(yesterday));

        Stopwatch stopwatch = Stopwatch.createStarted();

        // 查询作答日志和批改日志，进行merge
        List<StudyCorrectLogRecordEntity> studyCorrectLogRecordEntityList = studyCorrectLogRecordRepository.search(criteria);
        List<StudyLogRecordEntity> studyLogRecordEntityList = studyLogRecordRepository.search(criteria);
        List<StudyLogRecordEntity> records = mergeStudyCorrectLogAndStudyLog(studyCorrectLogRecordEntityList, studyLogRecordEntityList);
        if (log.isDebugEnabled()) {
            log.debug("queryFeedbackLogs mergeStudyLog result is : {}", JSON.toJSONString(records));
        }
//        List<StudyLogRecordEntity> records = studyLogRecordRepository.search(criteria);
        long mongoCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);

        if (CollectionUtil.isNotEmpty(records)) {
            Map<String, List<StudyLogData>> mongoDataMap = records.stream()
                    .map(StudyLogRecordEntity::toStudyLogData)
                    .sorted((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()))
                    .collect(Collectors.groupingBy(StudyLogData::getNodeId));

            multiValueMap.putAll(mongoDataMap);
        }

        // 在mongo中已经查到足够数据的nodeId，不再从数仓补充查询
        List<String> additional = studyLogQuery.getNodeIdList();
        if (CollectionUtil.isNotEmpty(multiValueMap)) {
            additional.removeIf(nodeId -> multiValueMap.getOrDefault(nodeId, Lists.newArrayList()).size() >= target);
        }

        // 从数仓补充历史数据
        stopwatch.reset().start();
        MultiValueMap<String, StudyLogData> appendMultiValueMap = userAnchorExamRecentlyAnswerRecord(traceId, additional, studyLogQuery);
        if (log.isDebugEnabled()) {
            log.debug("query odeon studyLog result is : {}", JSON.toJSONString(appendMultiValueMap));
        }
        long odeonCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
        if (CollectionUtil.isNotEmpty(appendMultiValueMap)) {
            additional.forEach(nodeId -> multiValueMap.addAll(nodeId, appendMultiValueMap.getOrDefault(nodeId, Lists.newArrayList())));
        }

        // 统一截取目标长度
        List<StudyLogData> result = Lists.newArrayList();
        multiValueMap.values().forEach(list -> result.addAll(list.subList(0, Math.min(list.size(), target))));

        // 按 用户id，点id、题id、feedback时间确定唯一值
        Set<String> studyLogKeys = result.stream().map(log -> log.getUserId() + log.getNodeId() + log.getResNodeId() + log.getFeedbackTime()).collect(Collectors.toSet());
        // 如果存在roundId，需要补充roundId下所有记录
        if(StringUtils.isNotBlank(studyLogQuery.getRoundId())){
            List<StudyLogData> roundStudyLogs = queryFeedbackLogsWithRound(traceId, studyLogQuery, studyLogQuery.getRoundId());
            if (CollectionUtil.isNotEmpty(roundStudyLogs)){
                // 按 用户id，点id、题id、feedback时间过滤
                roundStudyLogs.stream()
                        .filter(log -> !studyLogKeys.contains(log.getUserId() + log.getNodeId() + log.getResNodeId() + log.getFeedbackTime()))
                        .forEach(log -> result.add(log));
            }
        }

        log.info("【dataapi-cost】答题记录mongo耗时:{} 数量:{}; 答题记录odeon耗时:{} 数量：{}", mongoCost, records.size(), odeonCost, result.size() - records.size());
        return result;
    }

    /**
     * 查询round中所有所答记录，不限制时间和条数
     * 用于引擎过滤用户本轮已作答记录
     * @param traceId
     * @param studyLogQuery
     * @param roundId
     * @return
     */
    private List<StudyLogData> queryFeedbackLogsWithRound(String traceId, StudyLogQuery studyLogQuery, String roundId){
        if (StringUtils.isNotBlank(roundId)) {
            Criteria criteria = studyLogQuery.buildCriteria()
                    .and("time_cost").ne(null);
            criteria.and("mission_id").is(roundId);

            // 查询作答日志和批改日志，进行merge
            List<StudyCorrectLogRecordEntity> studyCorrectLogRecordEntityList = studyCorrectLogRecordRepository.search(criteria);
            List<StudyLogRecordEntity> studyLogRecordEntityList = studyLogRecordRepository.search(criteria);
            List<StudyLogRecordEntity> records = mergeStudyCorrectLogAndStudyLog(studyCorrectLogRecordEntityList, studyLogRecordEntityList);
            if (log.isDebugEnabled()) {
                log.debug("queryFeedbackLogs mergeStudyLog result is : {}", JSON.toJSONString(records));
            }

            MultiValueMap<String, StudyLogData> multiValueMap = new LinkedMultiValueMap<>();

            if (CollectionUtil.isNotEmpty(records)) {
                Map<String, List<StudyLogData>> mongoDataMap = records.stream()
                        .map(StudyLogRecordEntity::toStudyLogData)
                        .sorted((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()))
                        .collect(Collectors.groupingBy(StudyLogData::getNodeId));

                multiValueMap.putAll(mongoDataMap);
            }

            List<String> additional = studyLogQuery.getNodeIdList();
            MultiValueMap<String, StudyLogData> appendMultiValueMap = userAnchorExamRecentlyAnswerRecord(traceId, additional, studyLogQuery);
            if (CollectionUtil.isNotEmpty(appendMultiValueMap)) {
                additional.forEach(nodeId -> multiValueMap.addAll(nodeId,
                        appendMultiValueMap.getOrDefault(nodeId, Lists.newArrayList()).stream().filter(rec -> roundId.equals(rec.getResNodeId())).collect(Collectors.toList())
                ));
            }

            return multiValueMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
        }



        return Lists.newArrayListWithExpectedSize(0);
    }

    /**
     * 合并作答日志和批改日志
     *
     * @param studyCorrectLogRecordEntityList
     * @param studyLogRecordEntityList
     * @return
     */
    private List<StudyLogRecordEntity> mergeStudyCorrectLogAndStudyLog(List<StudyCorrectLogRecordEntity> studyCorrectLogRecordEntityList, List<StudyLogRecordEntity> studyLogRecordEntityList) {
        if (log.isDebugEnabled()) {
            log.debug("mergeStudyLog param : studyCorrectLogRecordEntityList is {},studyLogRecordEntityList is  {}", JSON.toJSONString(studyCorrectLogRecordEntityList), JSON.toJSONString(studyLogRecordEntityList));
        }
        if (CollectionUtil.isEmpty(studyCorrectLogRecordEntityList) && CollectionUtil.isEmpty(studyLogRecordEntityList)) {
            return new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(studyCorrectLogRecordEntityList) && CollectionUtil.isNotEmpty(studyLogRecordEntityList)) {
            return studyLogRecordEntityList;
        }
        if (CollectionUtil.isNotEmpty(studyCorrectLogRecordEntityList) && CollectionUtil.isEmpty(studyLogRecordEntityList)) {
            return studyCorrectLogRecordEntityList.stream().map(studyCorrectLogRecordEntity -> (StudyLogRecordEntity) studyCorrectLogRecordEntity).collect(Collectors.toList());
        }
        //有作答行为就一定有批改行为，但有批改行为不一定会有作答行为
        //两者的traceId都是会话id
        //作答日志的correctTraceId为对应的批改日志的traceId，批改日志的correctTraceId为null
        Set<String> correctTraceIdSet = new HashSet<>();
        for (StudyLogRecordEntity studyLogRecordEntity : studyLogRecordEntityList) {
            correctTraceIdSet.add(studyLogRecordEntity.getCorrectTraceId());
        }
        for (StudyCorrectLogRecordEntity studyCorrectLogRecordEntity : studyCorrectLogRecordEntityList) {
            if (!correctTraceIdSet.contains(studyCorrectLogRecordEntity.getTraceId())) {
                studyLogRecordEntityList.add(studyCorrectLogRecordEntity);
            }
        }
        return studyLogRecordEntityList;
    }

    /**
     * 查询特征 user_anchor_exam_recently_answer_record
     *
     * @param traceId
     * @param additional
     * @param query
     * @return
     */
    private MultiValueMap<String, StudyLogData> userAnchorExamRecentlyAnswerRecord(String traceId, List<String> additional, StudyLogQuery query) {
        if (CollectionUtil.isEmpty(additional)) {
            return null;
        }
        try {
            FeatureQuery featureQuery = UserAnchorExamRecentlyAnswerRecordDelegate.generateFeatureQuery(additional, query, studyLogProperties);
            FeatureData featureData = featureService.query(traceId, featureQuery);
            return UserAnchorExamRecentlyAnswerRecordDelegate.parseFeatureData(featureData, query);
        } catch (Exception e) {
            log.error("query user_anchor_exam_recently_answer_record error", e);
        }
        return null;
    }

    @Deprecated
    @Override
    @SkylineMetric(value = "DataHub-3.查询用户最近答题记录，不区分点", meterProviders = SkylabDataApiMeterProvider.class)
    public List<StudyLogData> queryUserRecentlyAnswerRecord(String traceId, UserRecentlyStudyLogQuery query) {
        if (log.isDebugEnabled()) {
            log.debug("traceId= {}, StudyLogRecentQuery={}", traceId, query);
        }
        List<StudyLogData> studyLogDataList = new ArrayList<>();

        // mongo查询数据
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<StudyLogRecordEntity> records = studyLogRecordRepository.search(query.buildCriteria());
        long mongoCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);

        if (CollectionUtil.isNotEmpty(records)) {
            List<StudyLogData> mongoDataList = records.stream()
                    .map(StudyLogRecordEntity::toStudyLogData)
                    .sorted((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()))
                    .collect(Collectors.toList());

            studyLogDataList.addAll(mongoDataList);
        }

        // 从数仓补充历史数据
        stopwatch.reset().start();
        if (studyLogDataList.size() < query.getLimit()) {
            studyLogDataList.addAll(userRecently200AnswerRecord(traceId, query));
        }
        long odeonCost = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);

        // 统一截取目标长度
        List<StudyLogData> result = studyLogDataList.subList(0, Math.min(studyLogDataList.size(), query.getLimit()));
        log.info("【dataapi-cost】答题记录mongo耗时:{} 数量:{}; 答题记录odeon耗时:{} 数量：{}", mongoCost, records.size(), odeonCost, result.size() - records.size());
        return result;
    }

    /**
     * 查询特征 user_recently_200_answer_record
     *
     * @param traceId
     * @param query
     * @return
     */
    private List<StudyLogData> userRecently200AnswerRecord(String traceId, UserRecentlyStudyLogQuery query) {
        try {
            FeatureQuery featureQuery = UserRecently200AnswerRecordDelegate.generateFeatureQuery(query, studyLogProperties);
            FeatureData featureData = featureService.query(traceId, featureQuery);
            return UserRecently200AnswerRecordDelegate.parseFeatureData(featureData);
        } catch (Exception e) {
            log.error("query user_recently_200_answer_record error", e);
        }
        return new ArrayList<>();
    }


    @Override
    public List<String> deleteStudyLog(String traceId, List<String> idList) {
        if (log.isDebugEnabled()) {
            log.debug("traceId={}, deleteStudyLog idList= {}", traceId, idList);
        }
        if (CollectionUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }

        List<String> deletedIds = new ArrayList<>(idList.size());
        for (String id : idList) {
            StudyLogRecordEntity entity = new StudyLogRecordEntity();
            entity.setId(id);
            entity.setDeleteFlag(FlagEnum.TRUE.intValue());
            UpdateResult result = studyLogRecordRepository.update(entity);
            if (result.wasAcknowledged()) {
                deletedIds.add(id);
            }
        }
        return deletedIds;
    }

    /**
     * 学情日志删除（逻辑删除）by 用户id
     *
     * @param traceId
     * @param userId
     * @return count
     */
    @SkylineMetric(value = "DataHub-3.用户学情删除", meterProviders = SkylabDataApiMeterProvider.class)
    @Override
    public Long deleteStudyLog(String traceId, String userId) {
        log.info("traceId={}, deleteStudyLog userId= {}", traceId, userId);
        if (CharSequenceUtil.isEmpty(userId)) {
            return 0L;
        }
        Criteria criteria = Criteria.where("user_id").is(userId);
        DeleteResult remove = studyLogRecordRepository.getMongoOperations().remove(new Query(criteria), StudyLogRecordEntity.class);
        long deletedCount = remove.getDeletedCount();

        log.info("traceId={}, deleteStudyLog userId= {},delete count={}", traceId, userId, deletedCount);
        return deletedCount;
    }

    @Override
    @SkylineMetric(value = "DataHub-3.根据cacheKey查询推荐缓存", meterProviders = SkylabDataApiMeterProvider.class)
    public JSONObject queryRecommendRecordCache(String traceId, String cacheKey) {
        if (log.isDebugEnabled()) {
            log.debug("traceId={}, recommend cacheKey= {}", traceId, cacheKey);
        }
        DataCacheRecord dataCacheRecord = dataCacheRecordRepository.findFirstByCacheKeyOrderByCreateTimeDesc(cacheKey);
        return Optional.ofNullable(dataCacheRecord).map(DataCacheRecord::getData).orElse(null);
    }

    @Override
    @SkylineMetric(value = "DataHub-3.保存推荐记录缓存", meterProviders = SkylabDataApiMeterProvider.class)
    public String saveRecommendRecordCache(String traceId, String cacheKey, JSONObject content) {
        if (log.isDebugEnabled()) {
            log.debug("traceId={}, recommend cacheKey= {}， recommend content={}", traceId, cacheKey, content);
        }
        DataCacheRecord saved = dataCacheRecordRepository.save(new DataCacheRecord(cacheKey, content));
        return saved.getId();
    }

    /**
     * 存在答题记录
     *
     * @param traceId
     * @param studyLogQuery
     * @return
     */
    @Deprecated
    @Override
    @SkylineMetric(value = "DataHub-3.存在答题记录", meterProviders = SkylabDataApiMeterProvider.class)
    public boolean existStudentLog(String traceId, StudyLogQuery studyLogQuery) {

        Criteria criteria = studyLogQuery.buildCriteria()
                .and("time_cost").ne(null);
        boolean exists = studyLogRecordRepository.exists(criteria);
        if (exists) {
            return true;
        } else {
            log.info("traceId={},近7天不存在答题记录", traceId);
            //查询odeon历史
            MultiValueMap<String, StudyLogData> appendMultiValueMap = userAnchorExamRecentlyAnswerRecord(traceId, studyLogQuery.getNodeIdList(), studyLogQuery);

            if (CollectionUtil.isNotEmpty(appendMultiValueMap)) {
                return true;
            }
            log.info("traceId={},不存在历史作答记录", traceId);
            return false;
        }

    }
}
