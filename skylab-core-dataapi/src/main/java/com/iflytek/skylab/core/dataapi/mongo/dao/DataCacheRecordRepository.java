package com.iflytek.skylab.core.dataapi.mongo.dao;

import com.iflytek.skylab.core.dataapi.mongo.DataApiMongoRepository;
import com.iflytek.skylab.core.dataapi.mongo.entity.DataCacheRecord;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/3/30 17:35
 */
@Repository
public interface DataCacheRecordRepository extends DataApiMongoRepository<DataCacheRecord, String> {

    DataCacheRecord findFirstByCacheKeyOrderByCreateTimeDesc(String cacheKey);

}
