# Enable Auto Configuration
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
    com.iflytek.skylab.core.dataapi.configuration.DataApiAutoConfiguration,\
    com.iflytek.skylab.core.dataapi.configuration.DataApiRedisAutoConfiguration,\
    com.iflytek.skylab.core.dataapi.configuration.GraphAutoConfiguration,\
    com.iflytek.skylab.core.dataapi.configuration.FeatureAutoConfiguration,\
    com.iflytek.skylab.core.dataapi.configuration.SkylabMongoAutoConfiguration,\
    com.iflytek.skylab.core.dataapi.configuration.AsyncApiAutoConfiguration,\
    com.iflytek.skylab.core.dataapi.configuration.DataHubAutoConfiguration,\
    com.iflytek.skylab.core.dataapi.configuration.UserAutoPathAutoConfiguration,\
    com.iflytek.skylab.core.dataapi.exception.ApiExceptionAutoConfiguration
