# skylab-core-dataapi

数据访问句柄 模块

---



## 0 主要功能

- 图谱访问、特征访问以及其他业务数据访问



## 1 使用指南

POM依赖

```xml
<dependency>
    <groupId>com.iflytek.skylab</groupId>
    <artifactId>skylab-core-dataapi</artifactId>
    <version>1.0.1-SNAPSHOT</version>
</dependency>
```



提供的功能：

- `@EnableBusDataAPI`    启用**业务数据访问**功能

- `@EnableFeatureAPI`    启用**特征访问**功能

- `@EnableGraphAPI`        启用**图谱数据访问**功能

- `@EnableDataHub`          启用**完整功能**，包含上述的业务数据访问、特征访问、XX访问功能

  

在启动类或配置类添加对应注解，以启用相应的功能。通过**入口类`com.iflytek.skylab.core.dataapi.DataHub`**获得service。

```java
/**
 * 使用演示类。通过添加 @EnableDataHub 获得完整功能
 *
 * <AUTHOR>
 * @date 2022/3/23 13:58
 */
@SpringBootApplication
@EnableDataHub
public class DemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);

        // 通过DataHub获得图谱service
        GraphService graphService = DataHub.getGraphService();
        // 查询图谱相关数据
        GraphData data = graphService.queryVertices(new GraphQuery());
        // ...
        
    }
}
```



## 2 服务列表

- `AnchorRelatedService`        锚点体系相关数据服务
- `FeatureService`                     特征服务
- `GraphService`                         图谱服务
- `MasteryService`                     画像服务
- `StudyBehaviorService`        学习行为服务
- `StudyLogService`                   学情日志服务
- `StudyTraceService`               学习行为追踪服务



### 2.1 锚点数据服务

方法列表

- <span id="findAllAnchorCast">AnchorRelatedService</span>s#**findAllAnchorCast()**                          查询所有锚点的转换关系
- <span id="findAllAnchorChangeMapping">AnchorRelatedService</span>#**findAllAnchorChangeMapping()**     查询所有锚点变更映射关系



#### 2.1.1 [查询所有锚点的转换关系](#findAllAnchorCast)

**描述**：查询库中所有锚点的转换关系

**入参**：无

**出参**：List\<AnchorCast> 。其中AnchorCast字段描述如下

| 序号 |      字段名称      | 类型          | 说明             |
| :--: | :----------------: | ------------- | ---------------- |
|  1   |      objectId      | ObjectId      | 主键ID           |
|  2   |   anchorPointId    | String        | 原锚点           |
|  3   | castAnchorPointIds | List\<String> | 变化后锚点id列表 |
|  4   |     createTime     | Date          | 记录创建时间     |
|  5   |     updateTime     | Date          | 记录更新时间     |



#### 2.1.2 [查询所有锚点变更映射关系](#findAllAnchorChangeMapping)

**描述**：查询库中所有锚点变更映射关系

**入参**：无

**出参**：List\<AnchorChangeMapping> 。其中AnchorChangeMapping字段描述如下

| 序号 |     字段名称     | 类型     | 说明         |
| :--: | :--------------: | -------- | ------------ |
|  1   |     objectId     | ObjectId | 主键ID       |
|  2   |       type       | String   | 功能类型     |
|  3   |   subjectCode    | String   | 学科         |
|  4   |    phaseCode     | String   | 学段         |
|  5   | bookVersionCode  | String   | 教材版本code |
|  6   |   catalogCode    | String   | 章节         |
|  7   | oldAnchorPointId | String   | 老锚点ID     |
|  8   | newAnchorPointId | String   | 新锚点ID     |
|  9   |    updateTime    | Date     | 更新时间     |



---

### 2.2 特征服务

方法列表

- <span id="query">FeatureService</span>#**query(String traceId, FeatureQuery featureQuery)**    查询特征数据
- <span id="querySchema">FeatureService</span>#**querySchema(String traceId, String dictRowKey)**        查询特征数据字典



#### 2.2.1 [查询特征数据](#query)

**描述**：查询特征数据

**入参**：

1. 跟踪ID

   | 序号 | 字段名称 | 必须 | 类型   | 说明   |
   | :--: | :------: | :--: | ------ | ------ |
   |  1   | traceId  |  Y   | String | 跟踪ID |

2. FeatureQuery 对象

   | 序号 |  字段名称   | 必须 | 类型                    | 说明                                          |
   | :--: | :---------: | :--: | ----------------------- | --------------------------------------------- |
   |  1   | dictRowKey  |  N   | String                  | 特征字典版本， 如果为空，将采用系统配置默认的 |
   |  2   | dataApiItem |  Y   | DataApiItem             | 数据源版本                                    |
   |  3   |    items    |  Y   | List\<FeatureQueryItem> | 查询参数                                      |

   其中DataApiItem为com.iflytek.skyline.dataapi.sdk.data包下类型。// TODO ？

   其中FeatureQueryItem字段如下

   | 序号 |    字段名称    | 必须 | 类型                        | 说明     |
   | :--: | :------------: | :--: | --------------------------- | -------- |
   |  1   |  graphVersion  |  N   | String                      | 图谱版本 |
   |  2   |  featureName   |  Y   | String                      | 特征名称 |
   |  3   | featureVersion |  N   | int                         | 特征版本 |
   |  4   |     params     |  Y   | List\<Map\<String, String>> | 查询参数 |

**出参**：FeatureData对象。

| 序号 | 字段名称 | 类型                   | 说明         |
| :--: | :------: | ---------------------- | ------------ |
|  1   |  items   | List\<FeatureDataItem> | 特征数据列表 |

其中FeatureDataItem字段如下

| 序号 |  字段名称   | 类型                        | 说明     |
| :--: | :---------: | --------------------------- | -------- |
|  1   | featureName | String                      | 特征名称 |
|  2   |   values    | List\<Map\<String, String>> | 特征数据 |



#### 2.2.2  [查询特征数据字典](#querySchema)

**描述**：查询特征数据字典

**入参**：

1. 跟踪ID

   | 序号 | 字段名称 | 必须 | 类型   | 说明   |
   | :--: | :------: | :--: | ------ | ------ |
   |  1   | traceId  |  Y   | String | 跟踪ID |

2. 特征字典版本

   | 序号 |  字段名称  | 必须 | 类型   | 说明   |
   | :--: | :--------: | :--: | ------ | ------ |
   |  1   | dictRowKey |  N   | String | 跟踪ID |

**出参**：ZionDictModel对象

| 序号 |  字段名称   | 类型                       | 说明 |
| :--: | :---------: | -------------------------- | ---- |
|  1   |     key     | String                     |      |
|  2   |    value    | List\<ZionDictItem>        |      |
|  3   |    meta     | ZionDictMeta               |      |
|  4   | dictInfoMap | Map\<String, ZionDictItem> |      |

其中ZionDictItem字段如下

| 序号 |   字段名称    | 类型                | 说明                           |
| :--: | :-----------: | ------------------- | ------------------------------ |
|  1   |  featureName  | String              | 特征名称                       |
|  2   |  primaryKey   | String              | 该特征名称所查询的表的主键构成 |
|  3   |  featureType  | String              |                                |
|  4   | featureEntity | String              |                                |
|  5   |  createTime   | Long                |                                |
|  6   |  modifyTime   | Long                |                                |
|  7   |     tags      | Map<String, Object> |                                |
|  8   |   describe    | String              |                                |
|  9   |   keyArray    | String[]            |                                |

其中ZionDictMeta字段如下

| 序号 |     字段名称     | 类型   | 说明 |
| :--: | :--------------: | ------ | ---- |
|  1   |     version      | String |      |
|  2   |    createTime    | Long   |      |
|  3   | lastModifiedTime | Long   |      |
|  4   |     creator      | String |      |



---

### 2.3 图谱服务

方法列表

- <span id="querySubGraph">GraphService</span>#**querySubGraph(SubGraphQuery subGraphQuery)**    查询子图
- <span id="queryVertices">GraphService</span>#**queryVertices(GraphQuery graphQuery)**                      查询顶点列表
- <span id="queryVerticesReverse">GraphService</span>#**queryVerticesReverse(GraphQuery graphQuery)**       根据路径逆推根节点
- <span id="queryVertex">GraphService</span>#**queryVertex(String id,  String graphVersion)**               根据点的id 查询节点详细信息



#### 2.3.1 [查询子图](#querySubGraph)

**描述**：根据条件查询子图

**入参**：SubGraphQuery对象

| 序号 |     字段名称     | 必须 | 类型             | 说明       |
| :--: | :--------------: | :--: | ---------------- | ---------- |
|  1   |   graphVersion   |  Y   | String           | 图谱版本   |
|  2   |    edgeLabels    |  Y   | List\<EdgeLabel> | 要查询的边 |
|  3   | rootVertexLabel  |  Y   | String           | 根节点类型 |
|  4   | rootVertexIdList |  Y   | List\<String>    | 根节点列表 |

其中EdgeLabel对象字段如下

| 序号 | 字段名称 | 必须 | 类型   | 说明         |
| :--: | :------: | :--: | ------ | ------------ |
|  1   |  source  |  Y   | String | 源节点类型   |
|  2   |  target  |  Y   | String | 目标节点类型 |

**出参**：GraphData对象

| 序号 |   字段名称   | 类型               | 说明     |
| :--: | :----------: | ------------------ | -------- |
|  1   | graphVersion | String             | 图谱版本 |
|  2   |   vertices   | List\<GraphVertex> | 点列表   |
|  3   |    edges     | List\<GraphEdge>   | 边列表   |

其中GraphVertex字段如下

| 序号 |  字段名称  | 类型       | 说明   |
| :--: | :--------: | ---------- | ------ |
|  1   |     id     | String     | 点ID   |
|  2   |   label    | String     | 点类型 |
|  3   | properties | JSONObject | 点属性 |

其中GraphEdge字段如下

|  号  |  字段名称  | 类型       | 说明   |
| :--: | :--------: | ---------- | ------ |
|  1   |     id     | String     | 边ID   |
|  2   |   label    | String     | 边类型 |
|  3   |   source   | String     | 源点   |
|  4   |   target   | String     | 目标点 |
|  5   | properties | JSONObject | 边属性 |

##### 图谱节点

**教材版本**：PRESS

| 序号 | 字段名称 | 必须 | 类型   | 说明   |
| :--: | :------: | :--: | ------ | ------ |
|  1   | vid |  Y   | String | ID |
| 2 | name | Y | String | 教材版本名称 |
| 3 | code | Y | String | 教材版本编码 |

**教材书本**：BOOK

| 序号 | 字段名称 | 必须 | 类型   | 说明         |
| :--: | :------: | :--: | ------ | ------------ |
|  1   |   vid    |  Y   | String | ID           |
|  2   |   name   |  Y   | String | 教材书本名称 |
|  3   |   code   |  Y   | String | 教材书本编码 |

**章**：UNIT

| 序号 | 字段名称 | 必须 | 类型   | 说明   |
| :--: | :------: | :--: | ------ | ------ |
|  1   |   vid    |  Y   | String | ID     |
|  2   |   name   |  Y   | String | 章名称 |
|  3   |   code   |  Y   | String | 章编码 |

**节**：COURSE

| 序号 | 字段名称 | 必须 | 类型   | 说明   |
| :--: | :------: | :--: | ------ | ------ |
|  1   |   vid    |  Y   | String | ID     |
|  2   |   name   |  Y   | String | 节名称 |
|  3   |   code   |  Y   | String | 节编码 |

**考点**：CHECK_POINT

| 序号 |    字段名称    | 必须 | 类型    | 说明                |
| :--: | :------------: | :--: | ------- | ------------------- |
|  1   |      vid       |  Y   | String  | ID                  |
|  2   | checkPointName |  Y   | String  | 考点名称            |
|  3   |   phaseCode    |  Y   | String  | 学段编码            |
|  4   |   phaseName    |  Y   | String  | 学段名称            |
|  5   |  subjectCode   |  Y   | String  | 学科编码            |
|  6   |  subjectName   |  Y   | String  | 学科名称            |
|  7   |     level      |  Y   | Integer | 深度                |
|  8   |   difficulty   |  Y   | String  | 难度：1，2，3，4，5 |
|  9   |  deleteStatus  |  Y   | Integer | 删除状态            |

**锚点**：ANCHOR_POINT

| 序号 |    字段名称     | 必须 | 类型    | 说明                                                         |
| :--: | :-------------: | :--: | ------- | ------------------------------------------------------------ |
|  1   |       vid       |  Y   | String  | ID                                                           |
|  2   |      name       |  Y   | String  | 锚点名称                                                     |
|  3   |    phaseCode    |  Y   | String  | 学段编码                                                     |
|  4   |    phaseName    |  Y   | String  | 学段名称                                                     |
|  5   |   subjectCode   |  Y   | String  | 学科编码                                                     |
|  6   |   subjectName   |  Y   | String  | 学科名称                                                     |
|  7   |   topicLayer    |  Y   | String  | 分层信息，definite("必会题")，raise("拔高题")，higher("高阶题") |
|  8   |  anchorTopicId  |  Y   | String  |                                                              |
|  9   |    isDelete     |  Y   | Integer | 删除状态                                                     |
|  10  |    isRelease    |  Y   | Integer | 发布状态                                                     |
|  11  |   difficulty    |  Y   | String  | 难度：1，2，3，4，5                                          |
|  12  | anchorPointType |  Y   | String  | 锚点类型，conventional("常规锚点")，fundamental("基础概念")，innovative("综合创新")，pentagram("综合提升")，highScoreAdvanced("高分进阶")，thinkingExpansion("思维拓展") |

**知识卡片**：CARD

| 序号 | 字段名称 | 必须 | 类型   | 说明     |
| :--: | :------: | :--: | ------ | -------- |
|  1   |   vid    |  Y   | String | ID       |
|  2   |   name   |  Y   | String | 卡片名称 |

**视频**：VIDEO

| 序号 | 字段名称 | 必须 | 类型   | 说明     |
| :--: | :------: | :--: | ------ | -------- |
|  1   |   vid    |  Y   | String | ID       |
|  2   |   name   |  Y   | String | 视频名称 |

**题**：TOPIC

| 序号 | 字段名称 | 必须 | 类型   | 说明 |
| :--: | :------: | :--: | ------ | ---- |
|  1   |   vid    |  Y   | String | ID   |

**学习路径**：LEARN_PATH

| 序号 | 字段名称 | 必须 | 类型   | 说明 |
| :--: | :------: | :--: | ------ | ---- |
|  1   |   vid    |  Y   | String | ID   |
|  2   |   pathType   |  Y   | String | 路径类型，actual 或 virtual |

**复习点目录树**：TREE

| 序号 | 字段名称 | 必须 | 类型   | 说明 |
| :--: | :------: | :--: | ------ | ---- |
|  1   |   vid    |  Y   | String | ID   |
|  2   |   name   |  Y   | String | 复习点树名称 |

**复习点一级目录**：REVISE

| 序号 | 字段名称 | 必须 | 类型   | 说明 |
| :--: | :------: | :--: | ------ | ---- |
|  1   |   vid    |  Y   | String | ID   |
|  2   |   name   |  Y   | String | 复习点一级目录名称 |

**复习点二级目录**：L2_MAP_TREE_NODE

| 序号 | 字段名称 | 必须 | 类型   | 说明 |
| :--: | :------: | :--: | ------ | ---- |
|  1   |   vid    |  Y   | String | ID   |
|  2   |   name   |  Y   | String | 复习点二级目录名称 |

**复习点三级目录**：L3_MAP_TREE_NODE

| 序号 | 字段名称 | 必须 | 类型   | 说明 |
| :--: | :------: | :--: | ------ | ---- |
|  1   |   vid    |  Y   | String | ID   |
|  2   |   name   |  Y   | String | 复习点三级目录名称 |

**复习点（四级目录）**：REVIEW_POINT

| 序号 | 字段名称 | 必须 | 类型   | 说明 |
| :--: | :------: | :--: | ------ | ---- |
|  1   |   vid    |  Y   | String | ID   |
|  2   |   name   |  Y   | String | 复习点名称 |

##### 图谱关系

* 教材版本 -> 教材书本
* 教材书本 -> 章
* 章 -> 节
* 章 -> 题（topicType：1：概念梳理，2：拔高卷）
* 章 -> 考点
* 考点 -> 考点
* 考点 -> 锚点
* 考点 -> 知识卡片
* 考点 -> 视频
* 考点 -> 题（topicType：1：互动题，2：单元出门测，3：期中出门测，4：期末出门测，5：考点资源题，6：锚点资源题，7：单元入门测）
* 节 -> 锚点
* 节 -> 题（topicType：1：概念梳理，2：拔高卷）
* 锚点 -> 锚点
* 锚点 -> 知识卡片
* 锚点 -> 视频
* 锚点 -> 题
* 节 -> 学习路径（indexNum：排序）
* 学习路径 -> 锚点（indexNum：排序）

![](./assets/graph-schema.png)

* 复习点目录树 -> 复习点一级目录
* 复习点目录树 -> 复习点二级目录
* 复习点目录树 -> 复习点三级目录
* 复习点目录树 -> 复习点（四级目录）
* 复习点一级目录 -> 复习点二级目录
* 复习点二级目录 -> 复习点三级目录
* 复习点三级目录 -> 复习点（四级目录）
* 复习点（四级目录） -> 题（topicType：typical：典型题，normal：正常题）
* 复习点一级目录 -> 复习点（四级目录）

#### 2.3.2 [查询顶点列表](#queryVertices)

**描述**：查询顶点列表

**入参**：GraphQuery对象

| 序号 |     字段名称     | 必须 | 类型             | 说明       |
| :--: | :--------------: | :--: | ---------------- | ---------- |
|  1   |   graphVersion   |  Y   | String           | 图谱版本   |
|  2   |    edgeLabels    |  Y   | List\<EdgeLabel> | 要查询的边 |
|  3   | rootVertexLabel  |  Y   | String           | 根节点类型 |
|  4   | rootVertexIdList |  Y   | List\<String>    | 根节点列表 |

其中EdgeLabel字段

| 序号 | 字段名称 | 必须 | 类型   | 说明         |
| :--: | :------: | :--: | ------ | ------------ |
|  1   |  source  |  Y   | String | 源节点类型   |
|  2   |  target  |  Y   | String | 目标节点类型 |
|  3   |  filter  |  N   | String | 边的过滤条件 |





**出参**：GraphData对象，字段描述见 2.3.1出参。



#### 2.3.3 [根据路径逆推根节点](#queryVerticesReverse)

**描述**：根据路径逆推根节点

**入参**：GraphQuery对象，字段描述见 2.3.2入参。

**出参**：GraphData对象，字段描述见 2.3.1出参。



#### 2.3.4 [根据点的id 查询节点详细信息](#queryVertex)

**描述**：根据点的id 查询节点详细信息

**入参**：

1. 点ID

   | 序号 | 字段名称 | 必须 | 类型   | 说明 |
   | :--: | :------: | :--: | ------ | ---- |
   |  1   |    id    |  Y   | String | 点ID |

2. 图谱版本

   | 序号 |   字段名称   | 必须 | 类型   | 说明     |
   | :--: | :----------: | :--: | ------ | -------- |
   |  1   | graphVersion |  Y   | String | 图谱版本 |

**出参**：GraphData对象，字段描述见 2.3.1出参。



---

### 2.4 画像服务

方法列表

- <span id="queryMasterData">MasteryService</span>#**queryMasterData(String traceId,  MasterQuery query)**                画像查询
- <span id="updateMasterData">MasteryService</span>#**updateMasterData(String traceId, MasterData masterData)**      画像更新
- <span id="deleteMasterData">MasteryService</span>#**deleteMasterData(String traceId, List\<String\> idList) **                     画像删除



#### 2.4.1 [画像查询](#queryMasterData)

**描述**：根据画像查询条件，查询对应的画像数据

**入参**：

1. 跟踪ID

   | 序号 | 字段名称 | 必须 | 类型   | 说明   |
   | :--: | :------: | :--: | ------ | ------ |
   |  1   | traceId  |  Y   | String | 跟踪ID |

2.  MasterQuery 对象

| 序号 |   字段名称   | 必须 | 类型           | 说明     |
| :--: | :----------: | :--: | -------------- | -------- |
|  1   |    userId    |  Y   | String         | 用户id   |
|  2   |  studyCode   |  N   | StudyCodeEnum  | 学习场景 |
|  3   |   nodeType   |  Y   | NodeTypeEnum   | 点类型   |
|  4   |  nodeIdList  |  Y   | List\<String\> | 点ID列表 |
|  5   | graphVersion |  N   | String         | 图谱版本 |

**出参**：MasterData 对象

| 序号 | 字段名称 | 类型               | 说明     |
| :--: | :------: | ------------------ | -------- |
|  1   |  userId  | String             | 用户Id   |
|  2   |  items   | List\<MasterItem\> | 数据列表 |

其中MasterItem对象的字段参见 2.4.2中入参的描述



#### 2.4.2 [画像更新](#updateMasterData)

**描述**：根据主键ID，更新用户画像数据

**入参**：

1. 跟踪ID

   | 序号 | 字段名称 | 必须 | 类型   | 说明   |
   | :--: | :------: | :--: | ------ | ------ |
   |  1   | traceId  |  Y   | String | 跟踪ID |

2. MasterData 对象

   | 序号 | 字段名称 | 必须 | 类型               | 说明     |
   | :--: | :------: | :--: | ------------------ | -------- |
   |  1   |  userId  |  Y   | String             | 用户Id   |
   |  2   |  items   |  Y   | List\<MasterItem\> | 数据列表 |

   其中MasterItem字段

   | 序号 |   字段名称   | 必须 | 类型            | 说明           |
   | :--: | :----------: | :--: | --------------- | -------------- |
   |  1   |      id      |  Y   | String          | 用户Id         |
   |  2   |   bizCode    |  N   | BizCodeEnum     | 业务编码       |
   |  3   |  studyCode   |  N   | StudyCodeEnum   | 学习场景       |
   |  4   | graphVersion |  N   | String          | 图谱版本       |
   |  5   |  catalogId   |  Y   | String          | 父目录         |
   |  6   | catalogType  |  Y   | CatalogTypeEnum | 父目录类型     |
   |  7   |    nodeId    |  Y   | String          | 点ID           |
   |  8   |   nodeType   |  Y   | NodeTypeEnum    | 点类型         |
   |  9   |    fusion    |  N   | Double          | 融合画像值     |
   |  10  |     real     |  N   | Double          | 真实画像       |
   |  11  |   predict    |  N   | Double          | 预测画像       |
   |  12  |  shouldFlag  |  N   | Boolean         | 是否是应学点   |
   |  13  |  algoFusion  |  N   | Double          | 算法融合画像值 |
   |  14  |   algoReal   |  N   | Double          | 算法真实画像值 |
   |  15  | algoPredict  |  N   | Double          | 算法预测画像值 |
   |  16  |  createTime  |  N   | Long            | 创建时间       |
   |  17  |  updateTime  |  Y   | Long            | 更新时间       |
   |  18  | masteryScore |  N   | Double          | 画像得分       |
   |  19  | masteryType  |  N   | String          | 画像类型       |

**出参**：更新成功的记录的主键ID。List\<String> 



#### 2.4.3 [画像删除](#deleteMasterData)

**描述**：根据主键ID，删除用户画像数据

**入参**：

1. 跟踪ID

   | 序号 | 字段名称 | 必须 | 类型   | 说明   |
   | :--: | :------: | :--: | ------ | ------ |
   |  1   | traceId  |  Y   | String | 跟踪ID |

2. 要删除的主键ID集合

   | 序号 | 字段名称 | 必须 | 类型          | 说明               |
   | :--: | :------: | :--: | ------------- | ------------------ |
   |  1   |  idList  |  Y   | List\<String> | 要删除的主键ID集合 |

**出参**：无



---

### 2.5 学习行为服务 // TODO



---

### 2.6 学情日志服务 

方法列表

- <span id="saveRecLog">StudyLogService</span>#**saveRecLog(String traceId, RecLog recLog) **                                                       保存推荐记录
- <span id="saveRecLogList">StudyLogService</span>#**saveRecLogList(String traceId, List\<RecLog> recLogs)**                                    批量保存推荐记录
- <span id="saveFeedbackLog">StudyLogService</span>#**saveFeedbackLog(String traceId, FeedbackLog feedbackLog)**                      保存答题记录
- <span id="saveFeedbackLogList">StudyLogService</span>#**saveFeedbackLogList(String traceId,List\<FeedbackLog> feedbackLogs)**   批量保存答题记录
- <span id="queryStudyLog">StudyLogService</span>#**queryStudyLog(String traceId, StudyLogQuery studyLogQuery)**                  查询学情日志
- <span id="deleteStudyLog">StudyLogService</span>#**deleteStudyLog(String traceId, List\<String> idList)**                                           逻辑删除学情日志



#### 2.6.1 [保存推荐记录](#saveRecLog)

**描述**：推荐记录存储

**入参**：

1. 跟踪id

   | 序号 | 字段名称 | 必须 | 类型   | 说明   |
   | :--: | :------: | :--: | ------ | ------ |
   |  1   | traceId  |  Y   | String | 跟踪ID |

2. RecLog对象

   | 序号 |    字段名称    | 必须 | 类型             | 说明         |
   | :--: | :------------: | :--: | ---------------- | ------------ |
   |  1   |      *id*      |  N   | String           | 主键ID       |
   |  2   |    *userId*    |  Y   | String           | 用户Id       |
   |  3   |  *phaseCode*   |  Y   | String           | 学段编码     |
   |  4   |   *schoolId*   |  N   | String           | 学校Id       |
   |  5   |  *gradeCode*   |  Y   | String           | 年级编码     |
   |  6   |   *classId*    |  N   | String           | 班级id       |
   |  7   | *subjectCode*  |  Y   | String           | 学科编码     |
   |  8   |   *bookCode*   |  N   | String           | 教材版本编码 |
   |  9   | *graphVersion* |  N   | String           | 图谱版本     |
   |  10  |  *studyCode*   |  Y   | StudyCodeEnum    | 学习场景     |
   |  11  |   *bizCode*    |  Y   | BizCodeEnum      | 业务编码     |
   |  12  |    *nodeId*    |  Y   | String           | 关联点id     |
   |  13  |   *nodeType*   |  Y   | NodeTypeEnum     | 关联点类型   |
   |  14  |  *resNodeId*   |  Y   | String           | 资源id       |
   |  15  | *resNodeType*  |  Y   | ResourceTypeEnum | 资源类型     |
   |  16  |   *resProps*   |  N   | JSONObject       | 资源扩展属性 |
   |  17  |  *roundId*   |  N   | String           | 轮标识       |
   |  18  | *roundIndex* |  N   | Integer          | 轮序号       |
   |  19  |   *funcCode*   |  N   | String           | 功能编码     |
   |  20  |     *from*     |  N   | String           | 学情记录来源 |
   |  21  |  *cloneFlag*   |  N   | Integer          | 是否克隆     |
   |  22  |    recProps    |  N   | JSONObject       | 推荐记录属性 |

**出参**：保存成功的推荐记录的主键ID。String



#### 2.6.2 [批量保存推荐记录](#saveRecLogList)

**描述**：批量存储，逻辑与2.6.1一致

**入参**：traceId、RecLog对像数组，字段参见2.6.1

**出参**：保存成功的推荐记录的主键ID集合。List\<String>



#### 2.6.3 [保存答题记录](#saveFeedbackLog)

**描述**：首先会查找此答题记录有无对应的推荐记录。如有推荐记录，则将答题数据更新到原推荐记录中；若无推荐			记录，则将答题记录数据插入数据库中。

**入参**：

1. 跟踪id

   | 序号 | 字段名称 | 必须 | 类型   | 说明   |
   | :--: | :------: | :--: | ------ | ------ |
   |  1   | traceId  |  Y   | String | 跟踪ID |

2. FeedbackLog对象

   | 序号 |    字段名称    | 必须 | 类型             | 说明                    |
   | :--: | :------------: | :--: | ---------------- | ----------------------- |
   |  1   |      *id*      |  N   | String           | 主键ID                  |
   |  2   |    *userId*    |  Y   | String           | 用户Id                  |
   |  3   |  *phaseCode*   |  Y   | String           | 学段编码                |
   |  4   |   *schoolId*   |  N   | String           | 学校Id                  |
   |  5   |  *gradeCode*   |  Y   | String           | 年级编码                |
   |  6   |   *classId*    |  N   | String           | 班级id                  |
   |  7   | *subjectCode*  |  Y   | String           | 学科编码                |
   |  8   |   *bookCode*   |  N   | String           | 教材版本编码            |
   |  9   | *graphVersion* |  N   | String           | 图谱版本                |
   |  10  |  *studyCode*   |  Y   | StudyCodeEnum    | 学习场景                |
   |  11  |   *bizCode*    |  Y   | BizCodeEnum      | 业务编码                |
   |  12  |    *nodeId*    |  Y   | String           | 关联点id                |
   |  13  |   *nodeType*   |  Y   | NodeTypeEnum     | 关联点类型              |
   |  14  |  *resNodeId*   |  Y   | String           | 资源id                  |
   |  15  | *resNodeType*  |  Y   | ResourceTypeEnum | 资源类型                |
   |  16  |   *resProps*   |  N   | JSONObject       | 资源扩展属性            |
   |  17  |  *roundId*   |  N   | String           | 轮标识                  |
   |  18  | *roundIndex* |  N   | Integer          | 轮序号                  |
   |  19  |   *funcCode*   |  N   | String           | 功能编码                |
   |  20  |     *from*     |  N   | String           | 学情记录来源            |
   |  21  |  *cloneFlag*   |  N   | Integer          | 是否克隆                |
   |  22  |   refTraceId   |  N   | String           | 关联的推荐记录的traceId |
   |  23  | feedbackResult |  N   | JSONObject       | 答题反馈结果            |
   |  24  |  feedbackTime  |  Y   | Long             | 客户端提交时间          |
   |  25  |   updateTime   |  Y   | Long             | 答题记录时间            |

**出参**：保存成功的答题记录的主键ID。String



#### 2.6.4 [批量保存答题记录](#saveFeedbackLogList)

**描述**：批量保存答题记录，逻辑与2.6.3一致

**入参**：traceId、FeedbackLog对像数组，字段参见2.6.3

**出参**：保存成功的答题记录的主键ID集合。List\<String>



#### 2.6.5 [查询学情日志](#queryStudyLog)

**描述**：学情数据查询

**入参**：

1. 跟踪id

   | 序号 | 字段名称 | 必须 | 类型   | 说明   |
   | :--: | :------: | :--: | ------ | ------ |
   |  1   | traceId  |  Y   | String | 跟踪ID |

2. StudyLogQuery对象

   | 序号 |   字段名称    | 必须 | 类型          | 说明         |
   | :--: | :-----------: | :--: | ------------- | ------------ |
   |  1   |    userId     |  Y   | String        | 用户id       |
   |  2   |  subjectCode  |  N   | String        | 学科         |
   |  3   |   phaseCode   |  N   | String        | 学段         |
   |  4   |  nodeIdList   |  Y   | List\<String> | 关联点Id集合 |
   |  5   | resNodeIdList |  N   | List\<String> | 资源点Id集合 |
   |  6   |  bizCodeList  |  Y   | List\<String> | 业务编码     |
   |  7   |   roundId   |  N   | String        | 轮标识       |
   |  8   |   funcCode    |  N   | String        | 功能编码     |

**出参**：StudyLogData对象集合。List\<StudyLogData>。字段如下

| 序号 |    字段名称    | 必须 | 类型             | 说明           |
| :--: | :------------: | :--: | ---------------- | -------------- |
|  1   |      *id*      |  N   | String           | 主键ID         |
|  2   |    *userId*    |  Y   | String           | 用户Id         |
|  3   |  *phaseCode*   |  Y   | String           | 学段编码       |
|  4   |   *schoolId*   |  N   | String           | 学校Id         |
|  5   |  *gradeCode*   |  Y   | String           | 年级编码       |
|  6   |   *classId*    |  N   | String           | 班级id         |
|  7   | *subjectCode*  |  Y   | String           | 学科编码       |
|  8   |   *bookCode*   |  N   | String           | 教材版本编码   |
|  9   | *graphVersion* |  N   | String           | 图谱版本       |
|  10  |  *studyCode*   |  Y   | StudyCodeEnum    | 学习场景       |
|  11  |   *bizCode*    |  Y   | BizCodeEnum      | 业务编码       |
|  12  |    *nodeId*    |  Y   | String           | 关联点id       |
|  13  |   *nodeType*   |  Y   | NodeTypeEnum     | 关联点类型     |
|  14  |  *resNodeId*   |  Y   | String           | 资源id         |
|  15  | *resNodeType*  |  Y   | ResourceTypeEnum | 资源类型       |
|  16  |   *resProps*   |  N   | JSONObject       | 资源扩展属性   |
|  17  |  *roundId*   |  N   | String           | 轮标识         |
|  18  | *roundIndex* |  N   | Integer          | 轮序号         |
|  19  |   *funcCode*   |  N   | String           | 功能编码       |
|  20  |     *from*     |  N   | String           | 学情记录来源   |
|  21  |  *cloneFlag*   |  N   | Integer          | 是否克隆       |
|  22  |    recProps    |  N   | JSONObject       | 推荐记录对象   |
|  23  | feedbackResult |  N   | JSONObject       | 反馈结果       |
|  24  |  feedbackTime  |  N   | Long             | 客户端提交时间 |
|  25  |   updateTime   |  N   | Long             | 答题记录时间   |
|  26  |   deleteFlag   |  N   | Integer          | 逻辑删除标志   |



#### 2.6.6 [逻辑删除学情日志](#deleteStudyLog)

**描述**：根据主键，**逻辑删除**。

**入参**：

1. 跟踪id

   | 序号 | 字段名称 | 必须 | 类型   | 说明   |
   | :--: | :------: | :--: | ------ | ------ |
   |  1   | traceId  |  Y   | String | 跟踪ID |

2. 要逻辑删除的主键ID集合

   | 序号 | 字段名称 | 必须 | 类型          | 说明       |
   | :--: | :------: | :--: | ------------- | ---------- |
   |  1   |  idList  |  Y   | List\<String> | 主键ID集合 |

**出参**：逻辑删除成功的主键ID集合。List\<String>



---

### 2.7 学习行为追踪服务

方法列表

- <span id="saveStudyTraceRecord"> </span>StudyTraceService#**saveStudyTraceRecord(StudyTraceData data) **             保存学习行为追踪记录
- <span id="saveStudyTraceRecordAsync"> </span>StudyTraceService#**saveStudyTraceRecordAsync(StudyTraceData data)**   异步保存学习行为追踪记录



#### 2.7.1 [保存学习行为追踪记录](#saveStudyTraceRecord)

**描述**：保存学习行为追踪记录到MongoDB数据库

入**参**：StudyTraceData 对象，字段说明如下

| 序号 |   字段名称   | 必须 | 类型            | 说明         |
| :--: | :----------: | :--: | --------------- | ------------ |
|  1   |      id      |  N   | String          | 主键ID       |
|  2   |     uuid     |  N   | String          | 记录唯一ID   |
|  3   |    userId    |  N   | String          | 用户ID       |
|  4   |   bizCode    |  N   | BizCodeEnum     | 业务编码     |
|  5   |  studyCode   |  N   | StudyCodeEnum   | 学习场景     |
|  6   | studyAction  |  N   | StudyActionEnum | 学习能力     |
|  7   | graphVersion |  N   | String          | 图谱版本     |
|  8   | subjectCode  |  N   | String          | 学科         |
|  9   |  phaseCode   |  N   | String          | 学段         |
|  10  |   bookCode   |  N   | String          | 教材版本     |
|  11  |  actionCode  |  N   | String          | 行为编码     |
|  12  | traceContext |  N   | JSONObject      | 行为上下文   |
|  13  |   traceId    |  N   | String          | 关联的追踪ID |
|  14  |  createTime  |  N   | Long            | 创建时间     |

**出参**：保存成功的记录的主键ID。String 



#### 2.7.2 [异步保存学习行为追踪记录](#saveStudyTraceRecordAsync)

**描述**：与2.7.1 逻辑一致，唯一区别为本方法通过线程池异步保存，无反值

**入参**：同 2.7.1 入参

**出参**：无

